package com.fishing.dto.settings;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 缓存信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CacheInfoDto {
    
    /**
     * 图片缓存大小
     */
    private String imageCache;
    
    /**
     * 视频缓存大小
     */
    private String videoCache;
    
    /**
     * 数据缓存大小
     */
    private String dataCache;
    
    /**
     * 总缓存大小
     */
    private String totalCache;
    
    /**
     * 图片缓存大小（字节）
     */
    private Long imageCacheBytes;
    
    /**
     * 视频缓存大小（字节）
     */
    private Long videoCacheBytes;
    
    /**
     * 数据缓存大小（字节）
     */
    private Long dataCacheBytes;
    
    /**
     * 总缓存大小（字节）
     */
    private Long totalCacheBytes;
    
    /**
     * 最后清理时间
     */
    private String lastClearTime;
}