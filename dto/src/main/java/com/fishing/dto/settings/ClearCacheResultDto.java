package com.fishing.dto.settings;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 清理缓存结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClearCacheResultDto {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 清理前总大小
     */
    private String beforeSize;
    
    /**
     * 清理后总大小
     */
    private String afterSize;
    
    /**
     * 释放的空间
     */
    private String freedSpace;
    
    /**
     * 释放的空间（字节）
     */
    private Long freedSpaceBytes;
    
    /**
     * 清理时间
     */
    private String clearTime;
    
    /**
     * 消息
     */
    private String message;
}