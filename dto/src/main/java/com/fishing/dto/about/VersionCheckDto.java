package com.fishing.dto.about;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 版本检查结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VersionCheckDto {
    
    /**
     * 是否有更新
     */
    private Boolean hasUpdate;
    
    /**
     * 最新版本号
     */
    private String latestVersion;
    
    /**
     * 更新描述
     */
    private String updateDescription;
    
    /**
     * 下载地址
     */
    private String downloadUrl;
    
    /**
     * 是否强制更新
     */
    private Boolean isForceUpdate;
    
    /**
     * 发布时间
     */
    private String releaseTime;
    
    /**
     * 更新包大小
     */
    private String updateSize;
    
    /**
     * 更新内容列表
     */
    private List<String> whatsNew;
    
    /**
     * 当前版本（无更新时返回）
     */
    private String currentVersion;
    
    /**
     * 检查时间
     */
    private String checkTime;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 是否有错误
     */
    private Boolean error;
}