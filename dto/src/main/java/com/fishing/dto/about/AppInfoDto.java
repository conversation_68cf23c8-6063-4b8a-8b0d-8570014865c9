package com.fishing.dto.about;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 应用信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppInfoDto {
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 构建号
     */
    private String buildNumber;
    
    /**
     * 应用描述
     */
    private String description;
    
    /**
     * 版权信息
     */
    private String copyright;
    
    /**
     * 官网地址
     */
    private String website;
    
    /**
     * 支持邮箱
     */
    private String supportEmail;
    
    /**
     * 客服电话
     */
    private String customerService;
    
    /**
     * 功能特性列表
     */
    private List<AppFeatureDto> features;
    
    /**
     * 团队信息
     */
    private List<TeamMemberDto> team;
    
    /**
     * 最后更新时间
     */
    private String lastUpdated;
}