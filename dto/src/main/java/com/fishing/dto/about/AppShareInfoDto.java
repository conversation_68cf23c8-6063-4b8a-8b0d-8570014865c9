package com.fishing.dto.about;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 应用分享信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppShareInfoDto {
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 应用描述
     */
    private String description;
    
    /**
     * 下载地址
     */
    private String downloadUrl;
    
    /**
     * 分享文本
     */
    private String shareText;
    
    /**
     * 二维码地址
     */
    private String qrCodeUrl;
    
    /**
     * 分享图片
     */
    private String shareImage;
    
    /**
     * 话题标签
     */
    private List<String> hashtags;
    
    /**
     * 推荐码（个性化分享）
     */
    private String referralCode;
    
    /**
     * 个性化分享地址
     */
    private String shareUrl;
}