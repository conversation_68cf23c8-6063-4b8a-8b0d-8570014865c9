package com.fishing.dto.about;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 应用统计信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppStatsDto {
    
    /**
     * 总用户数
     */
    private Integer totalUsers;
    
    /**
     * 总钓点数
     */
    private Integer totalSpots;
    
    /**
     * 总动态数
     */
    private Integer totalMoments;
    
    /**
     * 日活跃用户数
     */
    private Integer dailyActiveUsers;
    
    /**
     * 总下载量
     */
    private Integer totalDownloads;
    
    /**
     * 平均评分
     */
    private Double averageRating;
    
    /**
     * 总评论数
     */
    private Integer totalReviews;
    
    /**
     * 本月新用户
     */
    private Integer newUsersThisMonth;
    
    /**
     * 本月新钓点
     */
    private Integer newSpotsThisMonth;
    
    /**
     * 本月新动态
     */
    private Integer newMomentsThisMonth;
}