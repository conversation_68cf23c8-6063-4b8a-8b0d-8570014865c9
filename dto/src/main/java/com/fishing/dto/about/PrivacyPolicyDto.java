package com.fishing.dto.about;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 隐私政策DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PrivacyPolicyDto {
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 最后更新时间
     */
    private String lastUpdated;
    
    /**
     * 版本
     */
    private String version;
    
    /**
     * 生效日期
     */
    private String effectiveDate;
    
    /**
     * 内容
     */
    private String content;
    
    /**
     * 在线地址
     */
    private String url;
    
    /**
     * 联系邮箱
     */
    private String contactEmail;
}