package com.fishing.dto.help;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 帮助问题DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HelpQuestionDto {
    
    /**
     * 问题ID
     */
    private String id;
    
    /**
     * 问题内容
     */
    private String question;
    
    /**
     * 答案内容
     */
    private String answer;
    
    /**
     * 分类ID（搜索结果时需要）
     */
    private String categoryId;
    
    /**
     * 分类标题（搜索结果时需要）
     */
    private String categoryTitle;
}