package com.fishing.dto.help;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 问题统计DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionStatsDto {
    
    /**
     * 查看次数
     */
    private Integer viewCount;
    
    /**
     * 有用次数
     */
    private Long helpfulCount;
    
    /**
     * 无用次数
     */
    private Long notHelpfulCount;
    
    /**
     * 总反馈次数
     */
    private Long totalFeedback;
    
    /**
     * 有用比例
     */
    private Double helpfulRate;
}