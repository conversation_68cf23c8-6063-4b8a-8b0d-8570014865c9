package com.fishing.dto.help;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户反馈统计DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFeedbackStatsDto {
    
    /**
     * 总反馈次数
     */
    private Long totalFeedback;
    
    /**
     * 有用反馈次数
     */
    private Long helpfulFeedback;
    
    /**
     * 无用反馈次数
     */
    private Long notHelpfulFeedback;
    
    /**
     * 最后反馈时间
     */
    private String lastFeedbackTime;
}