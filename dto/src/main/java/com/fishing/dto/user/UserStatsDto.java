package com.fishing.dto.user;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户统计数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserStatsDto {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 粉丝数量
     */
    private Integer followersCount;
    
    /**
     * 关注数量
     */
    private Integer followingCount;
    
    /**
     * 动态数量
     */
    private Integer momentsCount;
    
    /**
     * 钓点数量
     */
    private Integer spotsCount;
    
    /**
     * 获赞数量
     */
    private Integer likesCount;
    
    /**
     * 评论数量
     */
    private Integer commentsCount;
    
    /**
     * 收藏数量
     */
    private Integer bookmarksCount;
    
    /**
     * 相册图片数量
     */
    private Integer albumCount;
    
    /**
     * 最后更新时间戳
     */
    private Long lastUpdated;
    
    /**
     * 创建空的统计对象
     */
    public static UserStatsDto empty(Long userId) {
        return UserStatsDto.builder()
                .userId(userId)
                .followersCount(0)
                .followingCount(0)
                .momentsCount(0)
                .spotsCount(0)
                .likesCount(0)
                .commentsCount(0)
                .bookmarksCount(0)
                .albumCount(0)
                .lastUpdated(System.currentTimeMillis())
                .build();
    }
}