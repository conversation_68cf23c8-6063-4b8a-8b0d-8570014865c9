package com.fishing.dto.album;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 相册统计DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlbumStatsDto {
    
    /**
     * 图片总数
     */
    private Long totalCount;
    
    /**
     * 占用空间（字节）
     */
    private Long totalSize;
    
    /**
     * 公开图片数量
     */
    private Long publicCount;
    
    /**
     * 私密图片数量
     */
    private Long privateCount;
}