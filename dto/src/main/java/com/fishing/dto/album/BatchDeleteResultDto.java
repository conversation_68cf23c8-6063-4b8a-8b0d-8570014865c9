package com.fishing.dto.album;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量删除结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchDeleteResultDto {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 请求删除数量
     */
    private Integer requestedCount;
    
    /**
     * 实际存在数量
     */
    private Integer existingCount;
    
    /**
     * 成功删除数量
     */
    private Integer deletedCount;
    
    /**
     * 失败数量
     */
    private Integer failedCount;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 失败的图片ID列表
     */
    private List<Long> failedImageIds;
}