package com.fishing.dto.album;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片详情DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageDetailsDto {
    
    /**
     * 图片ID
     */
    private Long id;
    
    /**
     * 图片URL
     */
    private String imageUrl;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 标签
     */
    private String tags;
    
    /**
     * 是否公开
     */
    private Boolean isPublic;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 更新时间
     */
    private String updateTime;
    
    /**
     * 文件大小
     */
    private String fileSize;
    
    /**
     * 图片尺寸
     */
    private String dimensions;
    
    /**
     * 图片格式
     */
    private String format;
    
    /**
     * 分享次数
     */
    private Integer shareCount;
    
    /**
     * 查看次数
     */
    private Integer viewCount;
}