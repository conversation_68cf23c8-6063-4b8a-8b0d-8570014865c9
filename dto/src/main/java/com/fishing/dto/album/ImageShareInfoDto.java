package com.fishing.dto.album;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片分享信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageShareInfoDto {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 图片ID
     */
    private Long imageId;
    
    /**
     * 图片URL
     */
    private String imageUrl;
    
    /**
     * 分享链接
     */
    private String shareUrl;
    
    /**
     * 分享文本
     */
    private String shareText;
    
    /**
     * 二维码地址
     */
    private String qrCodeUrl;
    
    /**
     * 分享标题（微信等）
     */
    private String title;
    
    /**
     * 小程序路径（微信）
     */
    private String miniProgramPath;
    
    /**
     * 话题标签（微博）
     */
    private String hashtags;
    
    /**
     * 错误消息
     */
    private String message;
}