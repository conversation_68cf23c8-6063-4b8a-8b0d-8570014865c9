package com.fishing.dto.album;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图片分享统计DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageShareStatsDto {
    
    /**
     * 总分享次数
     */
    private Integer totalShares;
    
    /**
     * 微信分享次数
     */
    private Integer wechatShares;
    
    /**
     * 微博分享次数
     */
    private Integer weiboShares;
    
    /**
     * QQ分享次数
     */
    private Integer qqShares;
    
    /**
     * 最后分享时间
     */
    private String lastShareTime;
    
    /**
     * 分享用户数
     */
    private Integer shareUsers;
}