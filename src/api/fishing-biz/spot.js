import request from '@/utils/request'

// 查询钓点列表
export function listSpot(query) {
  return request({
    url: '/fishing-biz/spot/list',
    method: 'get',
    params: query,
  })
}

// 获取所有钓点坐标（用于地图展示）
export function listAllSpotCoords(query) {
  return request({
    url: '/fishing-biz/spot/list-all-coordinates',
    method: 'get',
    params: query,
  })
}

// 查询钓点详细
export function getSpot(id) {
  return request({
    url: '/fishing-biz/spot/' + id,
    method: 'get',
  })
}

// 新增钓点
export function addSpot(data) {
  return request({
    url: '/fishing-biz/spot',
    method: 'post',
    data: data,
  })
}

// 修改钓点
export function updateSpot(data) {
  return request({
    url: '/fishing-biz/spot',
    method: 'put',
    data: data,
  })
}

// 修改钓点认证状态
export function verifySpot(id, level) {
  return request({
    url: `/fishing-biz/spot/verify/${id}/${level}`,
    method: 'put',
  })
}

// 删除钓点
export function delSpot(id) {
  return request({
    url: '/fishing-biz/spot/' + id,
    method: 'delete',
  })
}

// 获取钓点关联的鱼类ID列表
export function getSpotFishTypes(spotId) {
  return request({
    url: `/fishing-biz/spot/fish-types/${spotId}`,
    method: 'get',
  })
}

// 获取钓点图片
export function getSpotImages(spotId) {
  return request({
    url: `/fishing-biz/spot/images/${spotId}`,
    method: 'get',
  })
}

// 获取钓点设施
export function getSpotFacilities(spotId) {
  return request({
    url: `/fishing-biz/spot/facilities/${spotId}`,
    method: 'get',
  })
}

// 批量设置钓点状态
export function setSpotStatus(data) {
  return request({
    url: '/fishing-biz/spot/status',
    method: 'put',
    data: data,
  })
}

// 批量设置钓点可见性
export function setSpotVisibility(data) {
  return request({
    url: '/fishing-biz/spot/visibility',
    method: 'put',
    data: data,
  })
}

// 获取钓点统计信息
export function getSpotStats() {
  return request({
    url: '/fishing-biz/spot/stats',
    method: 'get',
  })
}

// 查询附近的钓点
export function findNearbySpots(latitude, longitude, radius = 5.0) {
  return request({
    url: '/fishing-biz/spot/nearby',
    method: 'get',
    params: { latitude, longitude, radius },
  })
}

// 获取热门钓点
export function getPopularSpots(limit = 10) {
  return request({
    url: '/fishing-biz/spot/popular',
    method: 'get',
    params: { limit },
  })
}
