import request from '@/utils/request'

// 查询通知列表
export function listNotification(query) {
  return request({
    url: '/fishing-biz/notification/list',
    method: 'get',
    params: query
  })
}

// 查询通知详细
export function getNotification(id) {
  return request({
    url: '/fishing-biz/notification/' + id,
    method: 'get'
  })
}

// 新增通知
export function addNotification(data) {
  return request({
    url: '/fishing-biz/notification',
    method: 'post',
    data: data
  })
}

// 修改通知
export function updateNotification(data) {
  return request({
    url: '/fishing-biz/notification',
    method: 'put',
    data: data
  })
}

// 删除通知
export function delNotification(ids) {
  return request({
    url: '/fishing-biz/notification/' + ids,
    method: 'delete'
  })
}

// 发送通知
export function sendNotification(data) {
  return request({
    url: '/fishing-biz/notification/send',
    method: 'post',
    data: data
  })
}

// 获取通知统计信息
export function getNotificationStatistics() {
  return request({
    url: '/fishing-biz/notification/statistics',
    method: 'get'
  })
}

// 获取通知模板
export function getNotificationTemplates() {
  return request({
    url: '/fishing-biz/notification/templates',
    method: 'get'
  })
}

// 批量标记为已读
export function markAsRead(ids) {
  return request({
    url: '/fishing-biz/notification/mark-read',
    method: 'put',
    data: ids
  })
}

// 撤回通知
export function recallNotification(id) {
  return request({
    url: '/fishing-biz/notification/recall/' + id,
    method: 'put'
  })
}

// 获取用户通知历史
export function getUserNotificationHistory(userId, query) {
  return request({
    url: '/fishing-biz/notification/user-history/' + userId,
    method: 'get',
    params: query
  })
}