import request from '@/utils/request'

// 获取审核队列
export function getModerationQueue(params) {
  return request({
    url: '/fishing-biz/content-moderation/queue',
    method: 'get',
    params: params
  })
}

// 审核内容
export function reviewContent(data) {
  return request({
    url: '/fishing-biz/content-moderation/review',
    method: 'post',
    data: data
  })
}

// 批量审核
export function batchReview(data) {
  return request({
    url: '/fishing-biz/content-moderation/batch-review',
    method: 'post',
    data: data
  })
}

// 获取敏感词列表
export function getSensitiveWords(params) {
  return request({
    url: '/fishing-biz/content-moderation/sensitive-words',
    method: 'get',
    params: params
  })
}

// 添加敏感词
export function addSensitiveWord(data) {
  return request({
    url: '/fishing-biz/content-moderation/sensitive-words',
    method: 'post',
    data: data
  })
}

// 删除敏感词
export function deleteSensitiveWords(ids) {
  return request({
    url: '/fishing-biz/content-moderation/sensitive-words/' + ids,
    method: 'delete'
  })
}

// 获取举报列表
export function getReports(params) {
  return request({
    url: '/fishing-biz/content-moderation/reports',
    method: 'get',
    params: params
  })
}

// 处理举报
export function handleReport(data) {
  return request({
    url: '/fishing-biz/content-moderation/handle-report',
    method: 'post',
    data: data
  })
}

// 获取审核统计
export function getModerationStatistics(days = 7) {
  return request({
    url: '/fishing-biz/content-moderation/statistics',
    method: 'get',
    params: { days }
  })
}

// 获取自动审核规则
export function getAutoModerationRules() {
  return request({
    url: '/fishing-biz/content-moderation/auto-rules',
    method: 'get'
  })
}

// 更新自动审核规则
export function updateAutoModerationRules(data) {
  return request({
    url: '/fishing-biz/content-moderation/auto-rules',
    method: 'put',
    data: data
  })
}

// 手动触发扫描
export function manualScan(data) {
  return request({
    url: '/fishing-biz/content-moderation/manual-scan',
    method: 'post',
    data: data
  })
}

// 获取黑名单
export function getBlacklist(params) {
  return request({
    url: '/fishing-biz/content-moderation/blacklist',
    method: 'get',
    params: params
  })
}

// 添加到黑名单
export function addToBlacklist(data) {
  return request({
    url: '/fishing-biz/content-moderation/blacklist',
    method: 'post',
    data: data
  })
}

// 从黑名单移除
export function removeFromBlacklist(userId) {
  return request({
    url: '/fishing-biz/content-moderation/blacklist/' + userId,
    method: 'delete'
  })
}