import request from '@/utils/request'

// 查询动态列表
export function listMoment(query) {
  return request({
    url: '/fishing-biz/moment/list',
    method: 'get',
    params: query,
  })
}

// 查询动态详细
export function getMoment(id) {
  return request({
    url: '/fishing-biz/moment/' + id,
    method: 'get',
  })
}

// 删除动态
export function delMoment(id) {
  return request({
    url: '/fishing-biz/moment/' + id,
    method: 'delete',
  })
}

// 批量审核动态
export function auditMoments(data) {
  return request({
    url: '/fishing-biz/moment/audit',
    method: 'put',
    data: data,
  })
}

// 批量设置动态可见性
export function setMomentVisibility(data) {
  return request({
    url: '/fishing-biz/moment/visibility',
    method: 'put',
    data: data,
  })
}

// 获取动态统计信息
export function getMomentStats() {
  return request({
    url: '/fishing-biz/moment/stats',
    method: 'get',
  })
}
