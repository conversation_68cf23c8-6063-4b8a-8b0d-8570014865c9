import request from '@/utils/request'

// 查询C端用户列表
export function listUserInfo(query) {
  return request({
    url: '/fishing-biz/user-info/list',
    method: 'get',
    params: query,
  })
}

// 查询C端用户详细
export function getUserInfo(id) {
  return request({
    url: '/fishing-biz/user-info/' + id,
    method: 'get',
  })
}

// 修改C端用户信息
export function updateUserInfo(data) {
  return request({
    url: '/fishing-biz/user-info',
    method: 'put',
    data: data,
  })
}

// 删除C端用户
export function delUserInfo(id) {
  return request({
    url: '/fishing-biz/user-info/' + id,
    method: 'delete',
  })
}

// 批量设置用户状态
export function setUserStatus(data) {
  return request({
    url: '/fishing-biz/user-info/status',
    method: 'put',
    data: data,
  })
}

// 重置用户密码
export function resetUserPassword(data) {
  return request({
    url: '/fishing-biz/user-info/resetPwd',
    method: 'put',
    data: data,
  })
}

// 获取用户统计信息
export function getUserStats() {
  return request({
    url: '/fishing-biz/user-info/stats',
    method: 'get',
  })
}

// 获取用户详细统计信息
export function getUserDetailStats(id) {
  return request({
    url: '/fishing-biz/user-info/' + id + '/detail-stats',
    method: 'get',
  })
}
