import request from '@/utils/request'

// 查询钓鱼计划列表
export function listFishingPlan(query) {
  return request({
    url: '/fishing-biz/fishing-plan/list',
    method: 'get',
    params: query
  })
}

// 查询钓鱼计划详细
export function getFishingPlan(id) {
  return request({
    url: '/fishing-biz/fishing-plan/' + id,
    method: 'get'
  })
}

// 查询计划参与者
export function getPlanParticipants(planId) {
  return request({
    url: '/fishing-biz/fishing-plan/participants/' + planId,
    method: 'get'
  })
}

// 修改计划状态
export function updatePlanStatus(id, status) {
  return request({
    url: '/fishing-biz/fishing-plan/status/' + id + '/' + status,
    method: 'put'
  })
}

// 批量修改计划状态
export function batchUpdatePlanStatus(ids, status) {
  return request({
    url: '/fishing-biz/fishing-plan/batch-status',
    method: 'put',
    data: ids,
    params: { status }
  })
}

// 删除钓鱼计划
export function delFishingPlan(ids) {
  return request({
    url: '/fishing-biz/fishing-plan/' + ids,
    method: 'delete'
  })
}

// 获取计划统计信息
export function getFishingPlanStatistics() {
  return request({
    url: '/fishing-biz/fishing-plan/statistics',
    method: 'get'
  })
}

// 移除计划参与者
export function removeParticipant(planId, userId) {
  return request({
    url: '/fishing-biz/fishing-plan/participant/' + planId + '/' + userId,
    method: 'delete'
  })
}