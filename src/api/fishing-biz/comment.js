import request from '@/utils/request'

// 查询评论列表
export function listComment(query) {
  return request({
    url: '/fishing-biz/comment/list',
    method: 'post',
    data: query
  })
}

// 查询评论详细
export function getComment(id) {
  return request({
    url: '/fishing-biz/comment/' + id,
    method: 'get'
  })
}

// 根据动态ID查询评论列表
export function getCommentsByMomentId(momentId, pageNum = 1, pageSize = 20) {
  return request({
    url: `/fishing-biz/comment/moment/${momentId}`,
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  })
}

// 根据用户ID查询评论列表
export function getCommentsByUserId(userId, pageNum = 1, pageSize = 20) {
  return request({
    url: `/fishing-biz/comment/user/${userId}`,
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  })
}

// 删除评论
export function delComment(id) {
  return request({
    url: '/fishing-biz/comment/' + id,
    method: 'delete'
  })
}

// 批量删除评论
export function batchDeleteComments(data) {
  return request({
    url: '/fishing-biz/comment/batch-delete',
    method: 'post',
    data: data
  })
}

// 获取评论统计信息
export function getCommentStats() {
  return request({
    url: '/fishing-biz/comment/stats',
    method: 'get'
  })
}

// 获取动态的评论列表
export function getMomentComments(momentId, pageNum = 1, pageSize = 10) {
  return request({
    url: `/fishing-biz/moment/${momentId}/comments`,
    method: 'get',
    params: {
      pageNum,
      pageSize
    }
  })
}