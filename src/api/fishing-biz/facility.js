import request from '@/utils/request'

// 查询设施列表
export function listFacility(query) {
  return request({
    url: '/fishing-biz/facility/list',
    method: 'get',
    params: query
  })
}

// 查询设施详细
export function getFacility(id) {
  return request({
    url: '/fishing-biz/facility/' + id,
    method: 'get'
  })
}

// 新增设施
export function addFacility(data) {
  return request({
    url: '/fishing-biz/facility',
    method: 'post',
    data: data
  })
}

// 修改设施
export function updateFacility(data) {
  return request({
    url: '/fishing-biz/facility',
    method: 'put',
    data: data
  })
}

// 删除设施
export function delFacility(ids) {
  return request({
    url: '/fishing-biz/facility/' + ids,
    method: 'delete'
  })
}

// 批量修改设施状态
export function batchUpdateFacilityStatus(ids, status) {
  return request({
    url: '/fishing-biz/facility/batch-status',
    method: 'put',
    data: ids,
    params: { status }
  })
}

// 上传设施图标
export function uploadFacilityIcon(id, file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/fishing-biz/facility/upload-icon/' + id,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取设施使用统计
export function getFacilityUsageStatistics() {
  return request({
    url: '/fishing-biz/facility/usage-statistics',
    method: 'get'
  })
}

// 获取热门设施排行
export function getFacilityPopularityRanking(limit = 10) {
  return request({
    url: '/fishing-biz/facility/popularity-ranking',
    method: 'get',
    params: { limit }
  })
}

// 获取设施分类统计
export function getFacilityCategoryStatistics() {
  return request({
    url: '/fishing-biz/facility/category-statistics',
    method: 'get'
  })
}

// 同步设施到钓点
export function syncFacilityToSpots(facilityId, spotIds) {
  return request({
    url: '/fishing-biz/facility/sync-to-spots',
    method: 'post',
    data: { facilityId, spotIds }
  })
}