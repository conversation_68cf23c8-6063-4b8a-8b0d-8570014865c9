import request from '@/utils/request'

// 查询鱼类列表
export function listFishType(query) {
  return request({
    url: '/fishing-biz/fish-type/list',
    method: 'get',
    params: query,
  })
}

// 查询鱼类详细信息
export function getFishType(id) {
  return request({
    url: '/fishing-biz/fish-type/' + id,
    method: 'get',
  })
}

// 新增鱼类
export function addFishType(data) {
  return request({
    url: '/fishing-biz/fish-type',
    method: 'post',
    data: data,
  })
}

// 修改鱼类
export function updateFishType(data) {
  return request({
    url: '/fishing-biz/fish-type',
    method: 'put',
    data: data,
  })
}

// 删除鱼类
export function delFishType(id) {
  return request({
    url: '/fishing-biz/fish-type/' + id,
    method: 'delete',
  })
}
