import request from '@/utils/request'

// 获取综合数据看板
export function getDashboard() {
  return request({
    url: '/fishing-biz/statistics/dashboard',
    method: 'get'
  })
}

// 获取用户统计数据
export function getUserStatistics(days = 7) {
  return request({
    url: '/fishing-biz/statistics/user',
    method: 'get',
    params: { days }
  })
}

// 获取钓点统计数据
export function getSpotStatistics(days = 7) {
  return request({
    url: '/fishing-biz/statistics/spot',
    method: 'get',
    params: { days }
  })
}

// 获取动态统计数据
export function getMomentStatistics(days = 7) {
  return request({
    url: '/fishing-biz/statistics/moment',
    method: 'get',
    params: { days }
  })
}

// 获取地域分布统计
export function getRegionStatistics() {
  return request({
    url: '/fishing-biz/statistics/region',
    method: 'get'
  })
}

// 获取用户行为分析
export function getUserBehaviorAnalysis(days = 30) {
  return request({
    url: '/fishing-biz/statistics/user-behavior',
    method: 'get',
    params: { days }
  })
}

// 获取钓点热度排行
export function getSpotRanking(type = 'checkin', limit = 10) {
  return request({
    url: '/fishing-biz/statistics/spot-ranking',
    method: 'get',
    params: { type, limit }
  })
}

// 获取实时统计数据
export function getRealtimeStatistics() {
  return request({
    url: '/fishing-biz/statistics/realtime',
    method: 'get'
  })
}

// 导出统计报表
export function exportStatistics(type, days = 30) {
  return request({
    url: '/fishing-biz/statistics/export',
    method: 'get',
    params: { type, days }
  })
}