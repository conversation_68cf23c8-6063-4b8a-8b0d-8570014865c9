import request from '@/utils/request'

// Query facility list
export function listFacility(query) {
  return request({
    url: '/fishing-biz/facility/list',
    method: 'get',
    params: query,
  })
}

// Query facility details by ID
export function getFacility(id) {
  return request({
    url: '/fishing-biz/facility/' + id,
    method: 'get',
  })
}

// Add new facility
export function addFacility(data) {
  return request({
    url: '/fishing-biz/facility',
    method: 'post',
    data: data,
  })
}

// Update facility
export function updateFacility(data) {
  return request({
    url: '/fishing-biz/facility',
    method: 'put',
    data: data,
  })
}

// Delete facility
export function delFacility(ids) {
  return request({
    url: '/fishing-biz/facility/' + ids,
    method: 'delete',
  })
}
