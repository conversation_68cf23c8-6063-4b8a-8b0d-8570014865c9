import request from '@/utils/request'

// 获取同步状态
export function getSyncStatus() {
  return request({
    url: '/system/algolia/status',
    method: 'get',
  })
}

// 获取同步进度
export function getSyncProgress() {
  return request({
    url: '/system/algolia/progress',
    method: 'get',
  })
}

// 手动触发增量同步
export function triggerIncrementalSync() {
  return request({
    url: '/system/algolia/sync/incremental',
    method: 'post',
  })
}

// 手动触发全量同步
export function triggerFullSync() {
  return request({
    url: '/system/algolia/sync/full',
    method: 'post',
  })
}

// 批量同步历史数据
export function batchSyncHistoricalData(data) {
  return request({
    url: '/system/algolia/sync/batch',
    method: 'post',
    params: data,
  })
}

// 同步指定动态
export function syncMoment(momentId) {
  return request({
    url: `/system/algolia/sync/moment/${momentId}`,
    method: 'post',
  })
}

// 同步指定用户
export function syncUser(userId) {
  return request({
    url: `/system/algolia/sync/user/${userId}`,
    method: 'post',
  })
}

// 同步指定钓点
export function syncFishingSpot(spotId) {
  return request({
    url: `/system/algolia/sync/spot/${spotId}`,
    method: 'post',
  })
}

// 删除指定动态
export function deleteMoment(momentId) {
  return request({
    url: `/system/algolia/moment/${momentId}`,
    method: 'delete',
  })
}

// 删除指定用户
export function deleteUser(userId) {
  return request({
    url: `/system/algolia/user/${userId}`,
    method: 'delete',
  })
}

// 删除指定钓点
export function deleteFishingSpot(spotId) {
  return request({
    url: `/system/algolia/spot/${spotId}`,
    method: 'delete',
  })
}

// 清空所有索引（危险操作）
export function clearAllIndexes() {
  return request({
    url: '/system/algolia/clear-indexes',
    method: 'delete',
  })
}
