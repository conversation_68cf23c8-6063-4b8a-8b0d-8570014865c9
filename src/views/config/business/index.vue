<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="card-title">业务配置</span>
        <small class="card-subtitle">配置系统业务相关参数</small>
      </div>

      <el-form ref="configForm" :model="form" :rules="rules" label-width="200px" size="small">
        <!-- 钓点动态统计配置 -->
        <el-card class="config-section" shadow="never">
          <div slot="header" class="section-header">
            <i class="el-icon-location-outline"></i>
            <span>钓点动态统计配置</span>
          </div>

          <el-form-item label="动态统计时间范围" prop="spotMomentStatDays">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-select v-model="form.spotMomentStatDays" placeholder="请选择时间范围" @change="onPresetChange">
                  <el-option
                    v-for="preset in timePresets"
                    :key="preset.value"
                    :label="preset.label"
                    :value="preset.value"
                  />
                  <el-option label="自定义" :value="0" />
                </el-select>
              </el-col>
              <el-col :span="8" v-if="form.spotMomentStatDays === 0">
                <el-input-number
                  v-model="customDays"
                  :min="1"
                  :max="365"
                  placeholder="请输入天数"
                  @change="onCustomDaysChange"
                />
              </el-col>
              <el-col :span="8">
                <span class="help-text">
                  当前设置：最近 {{ form.spotMomentStatDays || customDays }} 天
                </span>
              </el-col>
            </el-row>
            <div class="form-help">
              <i class="el-icon-info"></i>
              <span>设置钓点列表中显示的动态数量统计时间范围，影响C端用户看到的钓点动态数量</span>
            </div>
          </el-form-item>

          <!-- 配置预览 -->
          <el-form-item label="配置预览">
            <el-alert
              :title="`钓点列表将显示最近 ${form.spotMomentStatDays || customDays} 天内的动态数量`"
              type="info"
              :closable="false"
              show-icon
            />
          </el-form-item>
        </el-card>

        <!-- 操作按钮 -->
        <el-form-item class="form-actions">
          <el-button type="primary" @click="submitForm" :loading="loading">
            <i class="el-icon-check"></i>
            保存配置
          </el-button>
          <el-button @click="resetForm">
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
          <el-button @click="loadConfig">
            <i class="el-icon-download"></i>
            重新加载
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 配置说明 -->
    <el-card class="box-card config-help" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span class="card-title">配置说明</span>
      </div>
      <el-row :gutter="20">
        <el-col :span="12">
          <h4>钓点动态统计时间范围</h4>
          <ul class="help-list">
            <li>控制钓点列表中显示的动态数量统计时间范围</li>
            <li>例如设置为7天，则显示最近7天内的动态数量</li>
            <li>配置修改后立即生效，无需重启系统</li>
            <li>建议根据业务需求选择合适的时间范围</li>
          </ul>
        </el-col>
        <el-col :span="12">
          <h4>预设选项说明</h4>
          <ul class="help-list">
            <li><strong>7天</strong>：适合活跃度较高的钓点</li>
            <li><strong>15天</strong>：平衡选项，适合大多数场景</li>
            <li><strong>30天</strong>：适合动态较少的钓点</li>
            <li><strong>90天</strong>：长期统计，适合季节性钓点</li>
            <li><strong>自定义</strong>：可设置1-365天的任意值</li>
          </ul>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getBusinessConfig, updateBusinessConfig } from '@/api/config/business'

export default {
  name: 'BusinessConfig',
  data() {
    return {
      loading: false,
      form: {
        spotMomentStatDays: 7
      },
      customDays: 7,
      timePresets: [
        { label: '最近7天', value: 7 },
        { label: '最近15天', value: 15 },
        { label: '最近30天', value: 30 },
        { label: '最近90天', value: 90 },
        { label: '最近半年(180天)', value: 180 },
        { label: '最近一年(365天)', value: 365 }
      ],
      rules: {
        spotMomentStatDays: [
          { required: true, message: '请选择动态统计时间范围', trigger: 'change' },
          { type: 'number', min: 1, max: 365, message: '时间范围必须在1-365天之间', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.loadConfig()
  },
  methods: {
    // 加载配置
    async loadConfig() {
      try {
        this.loading = true
        const response = await getBusinessConfig()
        if (response.code === 200) {
          this.form = { ...response.data }
          // 如果不是预设值，设置为自定义
          const isPreset = this.timePresets.some(preset => preset.value === this.form.spotMomentStatDays)
          if (!isPreset) {
            this.customDays = this.form.spotMomentStatDays
            this.form.spotMomentStatDays = 0
          }
        }
      } catch (error) {
        this.$modal.msgError('加载配置失败')
        console.error('加载配置失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 预设值变化
    onPresetChange(value) {
      if (value !== 0) {
        this.customDays = value
      }
    },

    // 自定义天数变化
    onCustomDaysChange(value) {
      if (this.form.spotMomentStatDays === 0) {
        this.form.spotMomentStatDays = value
      }
    },

    // 提交表单
    async submitForm() {
      try {
        await this.$refs.configForm.validate()
        this.loading = true

        const submitData = {
          spotMomentStatDays: this.form.spotMomentStatDays === 0 ? this.customDays : this.form.spotMomentStatDays
        }

        const response = await updateBusinessConfig(submitData)
        if (response.code === 200) {
          this.$modal.msgSuccess('配置保存成功')
          await this.loadConfig()
        } else {
          this.$modal.msgError(response.msg || '配置保存失败')
        }
      } catch (error) {
        if (error !== false) { // 不是表单验证错误
          this.$modal.msgError('配置保存失败')
          console.error('配置保存失败:', error)
        }
      } finally {
        this.loading = false
      }
    },

    // 重置表单
    resetForm() {
      this.$refs.configForm.resetFields()
      this.customDays = 7
      this.loadConfig()
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.card-subtitle {
  color: #909399;
  margin-left: 10px;
}

.config-section {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  
  .section-header {
    font-weight: 600;
    color: #606266;
    
    i {
      margin-right: 8px;
      color: #409eff;
    }
  }
}

.form-help {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
  
  i {
    margin-right: 4px;
  }
}

.help-text {
  color: #606266;
  font-size: 13px;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
  border-top: 1px solid #ebeef5;
  padding-top: 20px;
}

.config-help {
  h4 {
    color: #303133;
    margin-bottom: 10px;
  }
  
  .help-list {
    color: #606266;
    line-height: 1.6;
    
    li {
      margin-bottom: 5px;
    }
  }
}
</style>
