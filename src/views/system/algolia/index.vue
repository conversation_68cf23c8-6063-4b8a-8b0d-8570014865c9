<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>Algolia 数据同步管理</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="refreshStatus"
          >刷新状态</el-button
        >
      </div>

      <!-- 同步状态概览 -->
      <el-row :gutter="20" class="status-overview">
        <el-col :span="4">
          <el-statistic title="动态总数" :value="syncStatus.momentCount" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="用户总数" :value="syncStatus.userCount" />
        </el-col>
        <el-col :span="4">
          <el-statistic title="钓点总数" :value="syncStatus.spotCount" />
        </el-col>
        <el-col :span="4">
          <el-statistic
            title="同步状态"
            :value="syncStatus.syncEnabled ? '启用' : '禁用'"
          />
        </el-col>
        <el-col :span="4">
          <el-statistic
            title="今日动态同步"
            :value="syncStatus.todayMomentSync || 0"
          />
        </el-col>
        <el-col :span="4">
          <el-statistic
            title="今日用户同步"
            :value="syncStatus.todayUserSync || 0"
          />
        </el-col>
      </el-row>

      <!-- 最后同步时间 -->
      <el-row v-if="syncStatus.lastSyncTime" style="margin-top: 10px">
        <el-col :span="24">
          <el-alert
            :title="`最后同步时间: ${syncStatus.lastSyncTime}`"
            type="info"
            :closable="false"
            show-icon
          />
        </el-col>
      </el-row>

      <!-- 同步进度 -->
      <el-divider content-position="left">同步进度</el-divider>
      <div v-if="syncProgress.isRunning" class="sync-progress">
        <el-progress
          :percentage="syncProgress.progress"
          :status="syncProgress.progress === 100 ? 'success' : null"
        />
        <el-row :gutter="20" style="margin-top: 10px">
          <el-col :span="12">
            <p>
              <strong>同步类型:</strong>
              {{ getTypeDisplayName(syncProgress.currentType) }}
            </p>
            <p>
              <strong>进度:</strong> {{ syncProgress.processedCount }} /
              {{ syncProgress.totalCount }} 条数据
            </p>
          </el-col>
          <el-col :span="12">
            <p v-if="syncProgress.currentBatch">
              <strong>批次进度:</strong> {{ syncProgress.currentBatch }} /
              {{ syncProgress.totalBatches }}
            </p>
            <p v-if="syncProgress.startTime">
              <strong>开始时间:</strong> {{ syncProgress.startTime }}
            </p>
            <p v-if="syncProgress.estimatedEndTime">
              <strong>预计完成:</strong> {{ syncProgress.estimatedEndTime }}
            </p>
          </el-col>
        </el-row>
      </div>
      <div v-else class="sync-progress">
        <el-alert
          title="当前没有正在执行的同步任务"
          type="info"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 操作按钮 -->
      <el-divider content-position="left">同步操作</el-divider>
      <el-row :gutter="20" class="sync-actions">
        <el-col :span="8">
          <el-card shadow="hover">
            <div slot="header">
              <span>增量同步</span>
            </div>
            <p>同步最近更新的数据到 Algolia</p>
            <el-button
              type="primary"
              :loading="loading.incremental"
              @click="handleIncrementalSync"
            >
              执行增量同步
            </el-button>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <div slot="header">
              <span>全量同步</span>
            </div>
            <p>同步所有数据到 Algolia（耗时较长）</p>
            <el-button
              type="warning"
              :loading="loading.full"
              @click="handleFullSync"
            >
              执行全量同步
            </el-button>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <div slot="header">
              <span>批量同步</span>
            </div>
            <p>按条件批量同步历史数据</p>
            <el-button type="success" @click="showBatchSyncDialog">
              批量同步设置
            </el-button>
          </el-card>
        </el-col>
      </el-row>

      <!-- 单条数据同步 -->
      <el-divider content-position="left">单条数据操作</el-divider>
      <el-row :gutter="20" class="single-sync">
        <el-col :span="8">
          <el-card shadow="hover">
            <div slot="header">
              <span>动态同步</span>
            </div>
            <el-input-number
              v-model="singleSync.momentId"
              placeholder="动态ID"
            />
            <div style="margin-top: 10px">
              <el-button size="small" @click="handleSyncMoment">同步</el-button>
              <el-button size="small" type="danger" @click="handleDeleteMoment"
                >删除</el-button
              >
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <div slot="header">
              <span>用户同步</span>
            </div>
            <el-input-number v-model="singleSync.userId" placeholder="用户ID" />
            <div style="margin-top: 10px">
              <el-button size="small" @click="handleSyncUser">同步</el-button>
              <el-button size="small" type="danger" @click="handleDeleteUser"
                >删除</el-button
              >
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <div slot="header">
              <span>钓点同步</span>
            </div>
            <el-input-number v-model="singleSync.spotId" placeholder="钓点ID" />
            <div style="margin-top: 10px">
              <el-button size="small" @click="handleSyncSpot">同步</el-button>
              <el-button size="small" type="danger" @click="handleDeleteSpot"
                >删除</el-button
              >
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 危险操作 -->
      <el-divider content-position="left">危险操作</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover">
            <div slot="header">
              <span style="color: #f56c6c">清空所有索引</span>
            </div>
            <p style="color: #909399">
              此操作将清空 Algolia 中的所有索引数据，请谨慎操作！
            </p>
            <el-button
              type="danger"
              :loading="loading.clear"
              @click="handleClearAllIndexes"
            >
              清空所有索引
            </el-button>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 批量同步对话框 -->
    <el-dialog
      title="批量同步设置"
      :visible.sync="batchSyncDialog"
      width="600px"
    >
      <el-form :model="batchSyncForm" label-width="100px">
        <el-form-item label="同步类型">
          <el-select v-model="batchSyncForm.type" placeholder="请选择同步类型">
            <el-option label="动态" value="moment" />
            <el-option label="用户" value="user" />
            <el-option label="钓点" value="spot" />
            <el-option label="全部" value="all" />
          </el-select>
        </el-form-item>
        <el-form-item label="页码">
          <el-input-number v-model="batchSyncForm.page" :min="1" />
        </el-form-item>
        <el-form-item label="每页数量">
          <el-input-number v-model="batchSyncForm.size" :min="1" :max="1000" />
        </el-form-item>
        <el-form-item label="开始日期">
          <el-date-picker
            v-model="batchSyncForm.startDate"
            type="date"
            placeholder="选择开始日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
        <el-form-item label="结束日期">
          <el-date-picker
            v-model="batchSyncForm.endDate"
            type="date"
            placeholder="选择结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchSyncDialog = false">取消</el-button>
        <el-button
          type="primary"
          :loading="loading.batch"
          @click="handleBatchSync"
          >开始同步</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSyncStatus,
  getSyncProgress,
  triggerIncrementalSync,
  triggerFullSync,
  batchSyncHistoricalData,
  syncMoment,
  syncUser,
  syncFishingSpot,
  deleteMoment,
  deleteUser,
  deleteFishingSpot,
  clearAllIndexes,
} from '@/api/system/algolia'

export default {
  name: 'AlgoliaSync',
  data() {
    return {
      // 同步状态
      syncStatus: {
        momentCount: 0,
        userCount: 0,
        spotCount: 0,
        syncEnabled: true,
        lastSyncTime: null,
      },
      // 同步进度
      syncProgress: {
        isRunning: false,
        currentType: '',
        processedCount: 0,
        totalCount: 0,
        progress: 0,
        startTime: null,
        estimatedEndTime: null,
      },
      // 加载状态
      loading: {
        incremental: false,
        full: false,
        batch: false,
        clear: false,
      },
      // 单条数据同步
      singleSync: {
        momentId: null,
        userId: null,
        spotId: null,
      },
      // 批量同步对话框
      batchSyncDialog: false,
      batchSyncForm: {
        type: 'moment',
        page: 1,
        size: 100,
        startDate: null,
        endDate: null,
      },
      // 定时器
      progressTimer: null,
    }
  },
  created() {
    this.loadSyncStatus()
    this.loadSyncProgress()
    this.startProgressPolling()
  },
  beforeDestroy() {
    this.stopProgressPolling()
  },
  methods: {
    // 加载同步状态
    async loadSyncStatus() {
      try {
        const response = await getSyncStatus()
        if (response.code === 200) {
          this.syncStatus = response.data
        }
      } catch (error) {
        this.$modal.msgError('获取同步状态失败')
      }
    },
    // 加载同步进度
    async loadSyncProgress() {
      try {
        const response = await getSyncProgress()
        if (response.code === 200) {
          this.syncProgress = response.data
        }
      } catch (error) {
        console.error('获取同步进度失败:', error)
      }
    },
    // 刷新状态
    refreshStatus() {
      this.loadSyncStatus()
      this.loadSyncProgress()
    },
    // 开始进度轮询
    startProgressPolling() {
      this.progressTimer = setInterval(() => {
        this.loadSyncProgress()
      }, 3000) // 每3秒刷新一次进度
    },
    // 停止进度轮询
    stopProgressPolling() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = null
      }
    },
    // 增量同步
    async handleIncrementalSync() {
      this.loading.incremental = true
      try {
        const response = await triggerIncrementalSync()
        if (response.code === 200) {
          this.$modal.msgSuccess('增量同步任务已启动')
          this.loadSyncProgress()
        } else {
          this.$modal.msgError(response.msg || '启动增量同步失败')
        }
      } catch (error) {
        this.$modal.msgError('启动增量同步失败')
      } finally {
        this.loading.incremental = false
      }
    },
    // 全量同步
    async handleFullSync() {
      try {
        await this.$modal.confirm('全量同步将耗费较长时间，确定要执行吗？')
        this.loading.full = true
        const response = await triggerFullSync()
        if (response.code === 200) {
          this.$modal.msgSuccess('全量同步任务已启动')
          this.loadSyncProgress()
        } else {
          this.$modal.msgError(response.msg || '启动全量同步失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$modal.msgError('启动全量同步失败')
        }
      } finally {
        this.loading.full = false
      }
    },
    // 显示批量同步对话框
    showBatchSyncDialog() {
      this.batchSyncDialog = true
    },
    // 批量同步
    async handleBatchSync() {
      this.loading.batch = true
      try {
        const response = await batchSyncHistoricalData(this.batchSyncForm)
        if (response.code === 200) {
          this.$modal.msgSuccess('批量同步任务已启动')
          this.batchSyncDialog = false
          this.loadSyncProgress()
        } else {
          this.$modal.msgError(response.msg || '启动批量同步失败')
        }
      } catch (error) {
        this.$modal.msgError('启动批量同步失败')
      } finally {
        this.loading.batch = false
      }
    },
    // 同步单个动态
    async handleSyncMoment() {
      if (!this.singleSync.momentId) {
        this.$modal.msgWarning('请输入动态ID')
        return
      }
      try {
        const response = await syncMoment(this.singleSync.momentId)
        if (response.code === 200) {
          this.$modal.msgSuccess('动态同步成功')
        } else {
          this.$modal.msgError(response.msg || '动态同步失败')
        }
      } catch (error) {
        this.$modal.msgError('动态同步失败')
      }
    },
    // 删除单个动态
    async handleDeleteMoment() {
      if (!this.singleSync.momentId) {
        this.$modal.msgWarning('请输入动态ID')
        return
      }
      try {
        await this.$modal.confirm('确定要从 Algolia 中删除该动态吗？')
        const response = await deleteMoment(this.singleSync.momentId)
        if (response.code === 200) {
          this.$modal.msgSuccess('动态删除成功')
        } else {
          this.$modal.msgError(response.msg || '动态删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$modal.msgError('动态删除失败')
        }
      }
    },
    // 同步单个用户
    async handleSyncUser() {
      if (!this.singleSync.userId) {
        this.$modal.msgWarning('请输入用户ID')
        return
      }
      try {
        const response = await syncUser(this.singleSync.userId)
        if (response.code === 200) {
          this.$modal.msgSuccess('用户同步成功')
        } else {
          this.$modal.msgError(response.msg || '用户同步失败')
        }
      } catch (error) {
        this.$modal.msgError('用户同步失败')
      }
    },
    // 删除单个用户
    async handleDeleteUser() {
      if (!this.singleSync.userId) {
        this.$modal.msgWarning('请输入用户ID')
        return
      }
      try {
        await this.$modal.confirm('确定要从 Algolia 中删除该用户吗？')
        const response = await deleteUser(this.singleSync.userId)
        if (response.code === 200) {
          this.$modal.msgSuccess('用户删除成功')
        } else {
          this.$modal.msgError(response.msg || '用户删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$modal.msgError('用户删除失败')
        }
      }
    },
    // 同步单个钓点
    async handleSyncSpot() {
      if (!this.singleSync.spotId) {
        this.$modal.msgWarning('请输入钓点ID')
        return
      }
      try {
        const response = await syncFishingSpot(this.singleSync.spotId)
        if (response.code === 200) {
          this.$modal.msgSuccess('钓点同步成功')
        } else {
          this.$modal.msgError(response.msg || '钓点同步失败')
        }
      } catch (error) {
        this.$modal.msgError('钓点同步失败')
      }
    },
    // 删除单个钓点
    async handleDeleteSpot() {
      if (!this.singleSync.spotId) {
        this.$modal.msgWarning('请输入钓点ID')
        return
      }
      try {
        await this.$modal.confirm('确定要从 Algolia 中删除该钓点吗？')
        const response = await deleteFishingSpot(this.singleSync.spotId)
        if (response.code === 200) {
          this.$modal.msgSuccess('钓点删除成功')
        } else {
          this.$modal.msgError(response.msg || '钓点删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$modal.msgError('钓点删除失败')
        }
      }
    },
    // 获取类型显示名称
    getTypeDisplayName(type) {
      const typeMap = {
        moment: '动态数据',
        user: '用户数据',
        spot: '钓点数据',
        all: '全部数据',
      }
      return typeMap[type] || type
    },
    // 清空所有索引
    async handleClearAllIndexes() {
      try {
        await this.$modal.confirm(
          '此操作将清空 Algolia 中的所有索引数据，数据清空后无法恢复，确定要继续吗？',
          '危险操作确认',
          {
            confirmButtonText: '确定清空',
            cancelButtonText: '取消',
            type: 'warning',
          },
        )

        this.loading.clear = true
        const response = await clearAllIndexes()
        if (response.code === 200) {
          this.$modal.msgSuccess('所有索引清空成功')
          // 刷新状态
          this.loadSyncStatus()
          this.loadSyncProgress()
        } else {
          this.$modal.msgError(response.msg || '清空索引失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$modal.msgError('清空索引失败')
        }
      } finally {
        this.loading.clear = false
      }
    },
  },
}
</script>

<style scoped>
.status-overview {
  margin-bottom: 20px;
}

.sync-progress {
  margin: 20px 0;
}

.sync-actions {
  margin-bottom: 20px;
}

.single-sync {
  margin-bottom: 20px;
}

.el-card {
  margin-bottom: 20px;
}

.el-statistic {
  text-align: center;
}

.dialog-footer {
  text-align: right;
}
</style>
