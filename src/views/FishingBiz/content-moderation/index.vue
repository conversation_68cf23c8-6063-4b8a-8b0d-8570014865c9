<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6" v-for="(item, index) in statisticsData" :key="index">
        <el-card shadow="hover" class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon" :style="{ backgroundColor: item.color }">
              <i :class="item.icon"></i>
            </div>
            <div class="statistics-text">
              <div class="statistics-title">{{ item.title }}</div>
              <div class="statistics-value">{{ item.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能标签页 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 审核队列 -->
      <el-tab-pane label="审核队列" name="queue">
        <el-form :model="queueParams" ref="queueForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="内容类型">
            <el-select v-model="queueParams.contentType" @change="getQueueList">
              <el-option label="全部" value="all" />
              <el-option label="动态" value="moment" />
              <el-option label="评论" value="comment" />
              <el-option label="用户资料" value="user" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="queueParams.status" @change="getQueueList">
              <el-option label="待审核" value="pending" />
              <el-option label="已通过" value="approved" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getQueueList">刷新</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-check"
              size="mini"
              :disabled="queueMultiple"
              @click="handleBatchReview('approve')"
            >批量通过</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-close"
              size="mini"
              :disabled="queueMultiple"
              @click="handleBatchReview('reject')"
            >批量拒绝</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="queueMultiple"
              @click="handleBatchReview('delete')"
            >批量删除</el-button>
          </el-col>
        </el-row>

        <el-table v-loading="queueLoading" :data="queueList" @selection-change="handleQueueSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="内容ID" prop="contentId" width="80" />
          <el-table-column label="类型" prop="contentType" width="80">
            <template slot-scope="scope">
              <el-tag :type="getContentTypeTag(scope.row.contentType)">
                {{ formatContentType(scope.row.contentType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="内容预览" prop="content" :show-overflow-tooltip="true" />
          <el-table-column label="用户" prop="userName" width="100" />
          <el-table-column label="风险等级" prop="riskLevel" width="100">
            <template slot-scope="scope">
              <el-tag :type="getRiskLevelTag(scope.row.riskLevel)">
                {{ formatRiskLevel(scope.row.riskLevel) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="提交时间" prop="createdAt" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <el-button size="mini" type="success" @click="handleReview(scope.row, 'approve')">通过</el-button>
              <el-button size="mini" type="warning" @click="handleReview(scope.row, 'reject')">拒绝</el-button>
              <el-button size="mini" type="danger" @click="handleReview(scope.row, 'delete')">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="queueTotal > 0"
          :total="queueTotal"
          :page.sync="queueParams.pageNum"
          :limit.sync="queueParams.pageSize"
          @pagination="getQueueList"
        />
      </el-tab-pane>

      <!-- 敏感词管理 -->
      <el-tab-pane label="敏感词管理" name="sensitive">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAddSensitiveWord"
            >新增敏感词</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="sensitiveMultiple"
              @click="handleDeleteSensitive"
            >删除</el-button>
          </el-col>
        </el-row>

        <el-table v-loading="sensitiveLoading" :data="sensitiveList" @selection-change="handleSensitiveSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="敏感词" prop="word" />
          <el-table-column label="分类" prop="category" width="100" />
          <el-table-column label="等级" prop="level" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.level === 1 ? 'success' : scope.row.level === 2 ? 'warning' : 'danger'">
                {{ scope.row.level === 1 ? '轻微' : scope.row.level === 2 ? '中等' : '严重' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createdAt" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="handleDeleteSensitive(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="sensitiveTotal > 0"
          :total="sensitiveTotal"
          :page.sync="sensitiveParams.pageNum"
          :limit.sync="sensitiveParams.pageSize"
          @pagination="getSensitiveList"
        />
      </el-tab-pane>

      <!-- 举报处理 -->
      <el-tab-pane label="举报处理" name="reports">
        <el-form :model="reportParams" ref="reportForm" size="small" :inline="true" label-width="68px">
          <el-form-item label="状态">
            <el-select v-model="reportParams.status" @change="getReportsList">
              <el-option label="待处理" value="pending" />
              <el-option label="已处理" value="handled" />
              <el-option label="已忽略" value="ignored" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="getReportsList">刷新</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="reportsLoading" :data="reportsList">
          <el-table-column label="举报ID" prop="id" width="80" />
          <el-table-column label="举报类型" prop="reportType" width="100" />
          <el-table-column label="被举报内容" prop="contentPreview" :show-overflow-tooltip="true" />
          <el-table-column label="举报人" prop="reporterName" width="100" />
          <el-table-column label="举报原因" prop="reason" :show-overflow-tooltip="true" />
          <el-table-column label="状态" prop="status" width="100">
            <template slot-scope="scope">
              <el-tag :type="getReportStatusTag(scope.row.status)">
                {{ formatReportStatus(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="举报时间" prop="createdAt" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <el-button size="mini" type="success" @click="handleReport(scope.row, 'accept')">接受</el-button>
              <el-button size="mini" type="warning" @click="handleReport(scope.row, 'reject')">拒绝</el-button>
              <el-button size="mini" type="info" @click="handleReport(scope.row, 'ignore')">忽略</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="reportsTotal > 0"
          :total="reportsTotal"
          :page.sync="reportParams.pageNum"
          :limit.sync="reportParams.pageSize"
          @pagination="getReportsList"
        />
      </el-tab-pane>

      <!-- 黑名单管理 -->
      <el-tab-pane label="黑名单管理" name="blacklist">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAddBlacklist"
            >添加黑名单</el-button>
          </el-col>
        </el-row>

        <el-table v-loading="blacklistLoading" :data="blacklistList">
          <el-table-column label="用户ID" prop="userId" width="80" />
          <el-table-column label="用户名" prop="userName" width="120" />
          <el-table-column label="封禁原因" prop="reason" :show-overflow-tooltip="true" />
          <el-table-column label="封禁时长" prop="duration" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.duration === -1 ? '永久' : scope.row.duration + '天' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="到期时间" prop="expireAt" width="160">
            <template slot-scope="scope">
              <span>{{ scope.row.duration === -1 ? '永不过期' : parseTime(scope.row.expireAt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createdAt" width="160">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createdAt) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="handleRemoveBlacklist(scope.row)">解封</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="blacklistTotal > 0"
          :total="blacklistTotal"
          :page.sync="blacklistParams.pageNum"
          :limit.sync="blacklistParams.pageSize"
          @pagination="getBlacklistList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 添加敏感词对话框 -->
    <el-dialog title="添加敏感词" :visible.sync="sensitiveOpen" width="400px" append-to-body>
      <el-form ref="sensitiveForm" :model="sensitiveForm" :rules="sensitiveRules" label-width="80px">
        <el-form-item label="敏感词" prop="word">
          <el-input v-model="sensitiveForm.word" placeholder="请输入敏感词" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="sensitiveForm.category" placeholder="请选择分类">
            <el-option label="政治" value="politics" />
            <el-option label="色情" value="porn" />
            <el-option label="暴力" value="violence" />
            <el-option label="赌博" value="gambling" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="等级" prop="level">
          <el-select v-model="sensitiveForm.level" placeholder="请选择等级">
            <el-option label="轻微" :value="1" />
            <el-option label="中等" :value="2" />
            <el-option label="严重" :value="3" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="sensitiveOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitSensitiveForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 添加黑名单对话框 -->
    <el-dialog title="添加黑名单" :visible.sync="blacklistOpen" width="400px" append-to-body>
      <el-form ref="blacklistForm" :model="blacklistForm" :rules="blacklistRules" label-width="80px">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="blacklistForm.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="封禁原因" prop="reason">
          <el-input v-model="blacklistForm.reason" type="textarea" placeholder="请输入封禁原因" />
        </el-form-item>
        <el-form-item label="封禁时长" prop="duration">
          <el-select v-model="blacklistForm.duration" placeholder="请选择时长">
            <el-option label="1天" :value="1" />
            <el-option label="3天" :value="3" />
            <el-option label="7天" :value="7" />
            <el-option label="30天" :value="30" />
            <el-option label="永久" :value="-1" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="blacklistOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitBlacklistForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getModerationQueue, reviewContent, batchReview, getSensitiveWords, addSensitiveWord, deleteSensitiveWords, getReports, handleReport, getModerationStatistics, getBlacklist, addToBlacklist, removeFromBlacklist } from "@/api/fishing-biz/content-moderation";

export default {
  name: "ContentModeration",
  data() {
    return {
      activeTab: 'queue',
      statisticsData: [],
      
      // 审核队列
      queueLoading: true,
      queueList: [],
      queueTotal: 0,
      queueIds: [],
      queueMultiple: true,
      queueParams: {
        pageNum: 1,
        pageSize: 10,
        status: 'pending',
        contentType: 'all'
      },
      
      // 敏感词
      sensitiveLoading: true,
      sensitiveList: [],
      sensitiveTotal: 0,
      sensitiveIds: [],
      sensitiveMultiple: true,
      sensitiveOpen: false,
      sensitiveParams: {
        pageNum: 1,
        pageSize: 10
      },
      sensitiveForm: {
        word: '',
        category: '',
        level: 1
      },
      sensitiveRules: {
        word: [{ required: true, message: "敏感词不能为空", trigger: "blur" }],
        category: [{ required: true, message: "分类不能为空", trigger: "change" }],
        level: [{ required: true, message: "等级不能为空", trigger: "change" }]
      },
      
      // 举报
      reportsLoading: true,
      reportsList: [],
      reportsTotal: 0,
      reportParams: {
        pageNum: 1,
        pageSize: 10,
        status: 'pending'
      },
      
      // 黑名单
      blacklistLoading: true,
      blacklistList: [],
      blacklistTotal: 0,
      blacklistOpen: false,
      blacklistParams: {
        pageNum: 1,
        pageSize: 10
      },
      blacklistForm: {
        userId: '',
        reason: '',
        duration: 1
      },
      blacklistRules: {
        userId: [{ required: true, message: "用户ID不能为空", trigger: "blur" }],
        reason: [{ required: true, message: "封禁原因不能为空", trigger: "blur" }],
        duration: [{ required: true, message: "封禁时长不能为空", trigger: "change" }]
      }
    };
  },
  created() {
    this.getStatisticsData();
    this.getQueueList();
  },
  methods: {
    /** 获取统计数据 */
    async getStatisticsData() {
      try {
        const res = await getModerationStatistics(7);
        const data = res.data;
        
        this.statisticsData = [
          {
            title: '待审核',
            value: data.pendingCount || 0,
            icon: 'el-icon-warning',
            color: '#E6A23C'
          },
          {
            title: '今日审核',
            value: data.todayReviewed || 0,
            icon: 'el-icon-check',
            color: '#67C23A'
          },
          {
            title: '待处理举报',
            value: data.pendingReports || 0,
            icon: 'el-icon-message',
            color: '#F56C6C'
          },
          {
            title: '黑名单用户',
            value: data.blacklistCount || 0,
            icon: 'el-icon-user',
            color: '#909399'
          }
        ];
      } catch (error) {
        console.error('获取统计数据失败:', error);
      }
    },
    
    /** 标签页切换 */
    handleTabClick(tab) {
      switch (tab.name) {
        case 'queue':
          this.getQueueList();
          break;
        case 'sensitive':
          this.getSensitiveList();
          break;
        case 'reports':
          this.getReportsList();
          break;
        case 'blacklist':
          this.getBlacklistList();
          break;
      }
    },
    
    /** 获取审核队列 */
    getQueueList() {
      this.queueLoading = true;
      getModerationQueue(this.queueParams).then(response => {
        this.queueList = response.rows;
        this.queueTotal = response.total;
        this.queueLoading = false;
      });
    },
    
    /** 审核内容 */
    handleReview(row, action) {
      let actionText = action === 'approve' ? '通过' : action === 'reject' ? '拒绝' : '删除';
      
      this.$prompt('请输入' + actionText + '原因', '内容审核', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '原因不能为空'
      }).then(({ value }) => {
        const data = {
          contentType: row.contentType,
          contentId: row.contentId,
          action: action,
          reason: value
        };
        
        reviewContent(data).then(response => {
          this.$modal.msgSuccess(actionText + "成功");
          this.getQueueList();
          this.getStatisticsData();
        });
      });
    },
    
    /** 批量审核 */
    handleBatchReview(action) {
      if (this.queueIds.length === 0) {
        this.$modal.msgWarning("请选择要操作的内容");
        return;
      }
      
      let actionText = action === 'approve' ? '通过' : action === 'reject' ? '拒绝' : '删除';
      
      this.$prompt('请输入批量' + actionText + '原因', '批量审核', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '原因不能为空'
      }).then(({ value }) => {
        const data = {
          contentType: this.queueParams.contentType,
          contentIds: this.queueIds,
          action: action,
          reason: value
        };
        
        batchReview(data).then(response => {
          this.$modal.msgSuccess('批量' + actionText + "成功");
          this.getQueueList();
          this.getStatisticsData();
        });
      });
    },
    
    // 队列多选框选中数据
    handleQueueSelectionChange(selection) {
      this.queueIds = selection.map(item => item.contentId);
      this.queueMultiple = !selection.length;
    },
    
    /** 获取敏感词列表 */
    getSensitiveList() {
      this.sensitiveLoading = true;
      getSensitiveWords(this.sensitiveParams).then(response => {
        this.sensitiveList = response.rows;
        this.sensitiveTotal = response.total;
        this.sensitiveLoading = false;
      });
    },
    
    /** 添加敏感词 */
    handleAddSensitiveWord() {
      this.sensitiveForm = {
        word: '',
        category: '',
        level: 1
      };
      this.sensitiveOpen = true;
    },
    
    /** 提交敏感词表单 */
    submitSensitiveForm() {
      this.$refs["sensitiveForm"].validate(valid => {
        if (valid) {
          addSensitiveWord(this.sensitiveForm).then(response => {
            this.$modal.msgSuccess("添加成功");
            this.sensitiveOpen = false;
            this.getSensitiveList();
          });
        }
      });
    },
    
    /** 删除敏感词 */
    handleDeleteSensitive(row) {
      const ids = row.id || this.sensitiveIds;
      this.$modal.confirm('是否确认删除选中的敏感词？').then(() => {
        return deleteSensitiveWords(ids);
      }).then(() => {
        this.getSensitiveList();
        this.$modal.msgSuccess("删除成功");
      });
    },
    
    // 敏感词多选框选中数据
    handleSensitiveSelectionChange(selection) {
      this.sensitiveIds = selection.map(item => item.id);
      this.sensitiveMultiple = !selection.length;
    },
    
    /** 获取举报列表 */
    getReportsList() {
      this.reportsLoading = true;
      getReports(this.reportParams).then(response => {
        this.reportsList = response.rows;
        this.reportsTotal = response.total;
        this.reportsLoading = false;
      });
    },
    
    /** 处理举报 */
    handleReport(row, action) {
      let actionText = action === 'accept' ? '接受' : action === 'reject' ? '拒绝' : '忽略';
      
      this.$prompt('请输入处理说明', '处理举报', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        const data = {
          reportId: row.id,
          action: action,
          handleNote: value || ''
        };
        
        handleReport(data).then(response => {
          this.$modal.msgSuccess("处理成功");
          this.getReportsList();
          this.getStatisticsData();
        });
      });
    },
    
    /** 获取黑名单列表 */
    getBlacklistList() {
      this.blacklistLoading = true;
      getBlacklist(this.blacklistParams).then(response => {
        this.blacklistList = response.rows;
        this.blacklistTotal = response.total;
        this.blacklistLoading = false;
      });
    },
    
    /** 添加黑名单 */
    handleAddBlacklist() {
      this.blacklistForm = {
        userId: '',
        reason: '',
        duration: 1
      };
      this.blacklistOpen = true;
    },
    
    /** 提交黑名单表单 */
    submitBlacklistForm() {
      this.$refs["blacklistForm"].validate(valid => {
        if (valid) {
          addToBlacklist(this.blacklistForm).then(response => {
            this.$modal.msgSuccess("添加成功");
            this.blacklistOpen = false;
            this.getBlacklistList();
            this.getStatisticsData();
          });
        }
      });
    },
    
    /** 移除黑名单 */
    handleRemoveBlacklist(row) {
      this.$modal.confirm('是否确认解封该用户？').then(() => {
        return removeFromBlacklist(row.userId);
      }).then(() => {
        this.getBlacklistList();
        this.getStatisticsData();
        this.$modal.msgSuccess("解封成功");
      });
    },
    
    // 格式化方法
    formatContentType(type) {
      const map = { 'moment': '动态', 'comment': '评论', 'user': '用户资料' };
      return map[type] || type;
    },
    
    getContentTypeTag(type) {
      const map = { 'moment': 'primary', 'comment': 'success', 'user': 'warning' };
      return map[type] || 'info';
    },
    
    formatRiskLevel(level) {
      const map = { 'low': '低风险', 'medium': '中风险', 'high': '高风险' };
      return map[level] || level;
    },
    
    getRiskLevelTag(level) {
      const map = { 'low': 'success', 'medium': 'warning', 'high': 'danger' };
      return map[level] || 'info';
    },
    
    formatReportStatus(status) {
      const map = { 'pending': '待处理', 'handled': '已处理', 'ignored': '已忽略' };
      return map[status] || status;
    },
    
    getReportStatusTag(status) {
      const map = { 'pending': 'warning', 'handled': 'success', 'ignored': 'info' };
      return map[status] || 'info';
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.statistics-card {
  height: 100px;
}

.statistics-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.statistics-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.statistics-icon i {
  font-size: 20px;
  color: white;
}

.statistics-text {
  flex: 1;
}

.statistics-title {
  font-size: 14px;
  color: #999;
  margin-bottom: 5px;
}

.statistics-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}
</style>