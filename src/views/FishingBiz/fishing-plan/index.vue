<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="计划标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入计划标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="活跃" value="active" />
          <el-option label="已取消" value="cancelled" />
          <el-option label="已完成" value="completed" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['fishing-biz:fishing-plan:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multiple"
          @click="handleBatchStatus"
          v-hasPermi="['fishing-biz:fishing-plan:edit']"
        >批量状态</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="fishingPlanList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="计划ID" align="center" prop="id" width="80" />
      <el-table-column label="计划标题" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="地点" align="center" prop="location" :show-overflow-tooltip="true" />
      <el-table-column label="计划日期" align="center" prop="planDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="时间段" align="center" prop="timeRange" width="120" />
      <el-table-column label="参与人数" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.currentParticipants }}/{{ scope.row.maxParticipants }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.status === 'active' ? 'success' : scope.row.status === 'cancelled' ? 'danger' : 'info'"
          >
            {{ formatStatus(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否公开" align="center" prop="isPublic" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.isPublic ? 'success' : 'warning'">
            {{ scope.row.isPublic ? '公开' : '私密' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['fishing-biz:fishing-plan:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleStatusChange(scope.row)"
            v-hasPermi="['fishing-biz:fishing-plan:edit']"
          >状态</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['fishing-biz:fishing-plan:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 计划详情对话框 -->
    <el-dialog title="计划详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions v-if="planDetail" border :column="2">
        <el-descriptions-item label="计划标题">{{ planDetail.title }}</el-descriptions-item>
        <el-descriptions-item label="地点">{{ planDetail.location }}</el-descriptions-item>
        <el-descriptions-item label="计划日期">{{ parseTime(planDetail.planDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="时间段">{{ planDetail.timeRange }}</el-descriptions-item>
        <el-descriptions-item label="最大参与人数">{{ planDetail.maxParticipants }}</el-descriptions-item>
        <el-descriptions-item label="当前参与人数">{{ planDetail.currentParticipants }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ formatStatus(planDetail.status) }}</el-descriptions-item>
        <el-descriptions-item label="是否公开">{{ planDetail.isPublic ? '公开' : '私密' }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ planDetail.description || '无' }}</el-descriptions-item>
        <el-descriptions-item label="联系信息" :span="2">{{ planDetail.contactInfo || '无' }}</el-descriptions-item>
      </el-descriptions>
      
      <el-divider content-position="left">参与者列表</el-divider>
      <el-table :data="participants" size="small" max-height="300">
        <el-table-column label="用户ID" prop="userId" width="80" />
        <el-table-column label="用户名" prop="userName" />
        <el-table-column label="是否组织者" prop="isOwner" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.isOwner ? 'success' : 'info'">
              {{ scope.row.isOwner ? '组织者' : '参与者' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="参与状态" prop="status" width="100">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 'confirmed' ? 'success' : 'warning'">
              {{ scope.row.status === 'confirmed' ? '已确认' : '待确认' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="加入时间" prop="joinedAt" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.joinedAt) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button
              v-if="!scope.row.isOwner"
              size="mini"
              type="text"
              @click="handleRemoveParticipant(scope.row)"
            >移除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 状态修改对话框 -->
    <el-dialog title="修改状态" :visible.sync="statusOpen" width="400px" append-to-body>
      <el-form :model="statusForm" ref="statusForm" label-width="80px">
        <el-form-item label="状态" prop="status">
          <el-select v-model="statusForm.status" placeholder="请选择状态">
            <el-option label="活跃" value="active" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="statusOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitStatusChange">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listFishingPlan, getFishingPlan, delFishingPlan, updatePlanStatus, batchUpdatePlanStatus, getPlanParticipants, removeParticipant } from "@/api/fishing-biz/fishing-plan";

export default {
  name: "FishingPlan",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 钓鱼计划表格数据
      fishingPlanList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 详情弹出层
      detailOpen: false,
      // 状态修改弹出层
      statusOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        status: null,
      },
      // 计划详情
      planDetail: null,
      // 参与者列表
      participants: [],
      // 状态表单
      statusForm: {
        id: null,
        status: null
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询钓鱼计划列表 */
    getList() {
      this.loading = true;
      listFishingPlan(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.fishingPlanList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        location: null,
        planDate: null,
        timeRange: null,
        description: null,
        maxParticipants: null,
        currentParticipants: null,
        ownerId: null,
        spotId: null,
        contactInfo: null,
        weather: null,
        temperature: null,
        isPublic: null,
        status: null,
        deleted: null,
        createdAt: null,
        updatedAt: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const id = row.id || this.ids
      getFishingPlan(id).then(response => {
        this.planDetail = response.data;
        this.detailOpen = true;
        // 获取参与者列表
        this.getParticipants(id);
      });
    },
    /** 获取参与者列表 */
    getParticipants(planId) {
      getPlanParticipants(planId).then(response => {
        this.participants = response.data;
      });
    },
    /** 状态修改按钮操作 */
    handleStatusChange(row) {
      this.statusForm.id = row.id;
      this.statusForm.status = row.status;
      this.statusOpen = true;
    },
    /** 提交状态修改 */
    submitStatusChange() {
      updatePlanStatus(this.statusForm.id, this.statusForm.status).then(response => {
        this.$modal.msgSuccess("修改成功");
        this.statusOpen = false;
        this.getList();
      });
    },
    /** 批量状态修改 */
    handleBatchStatus() {
      this.$prompt('请选择状态', '批量修改', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'select',
        inputOptions: {
          'active': '活跃',
          'cancelled': '已取消',
          'completed': '已完成'
        }
      }).then(({ value }) => {
        batchUpdatePlanStatus(this.ids, value).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.getList();
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除钓鱼计划编号为"' + ids + '"的数据项？').then(function() {
        return delFishingPlan(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 移除参与者 */
    handleRemoveParticipant(participant) {
      this.$modal.confirm('是否确认移除该参与者？').then(() => {
        return removeParticipant(this.planDetail.id, participant.userId);
      }).then(() => {
        this.$modal.msgSuccess("移除成功");
        this.getParticipants(this.planDetail.id);
        this.getList(); // 刷新列表以更新参与人数
      }).catch(() => {});
    },
    /** 格式化状态 */
    formatStatus(status) {
      const statusMap = {
        'active': '活跃',
        'cancelled': '已取消',
        'completed': '已完成'
      };
      return statusMap[status] || status;
    }
  }
};
</script>