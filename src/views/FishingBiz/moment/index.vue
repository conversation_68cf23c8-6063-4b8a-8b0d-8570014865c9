<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="动态类型" prop="momentType">
        <el-select v-model="queryParams.momentType" placeholder="请选择动态类型" clearable>
          <el-option label="钓获分享" value="fishing_catch" />
          <el-option label="装备展示" value="equipment" />
          <el-option label="技巧分享" value="technique" />
          <el-option label="问答求助" value="question" />
        </el-select>
      </el-form-item>
      <el-form-item label="可见性" prop="visibility">
        <el-select v-model="queryParams.visibility" placeholder="请选择可见性" clearable>
          <el-option label="公开" value="public" />
          <el-option label="仅关注者" value="followers" />
          <el-option label="私密" value="private" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['fishing-biz:moment:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleAudit('approved')"
          v-hasPermi="['fishing-biz:moment:audit']"
        >批量通过</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-close"
          size="mini"
          :disabled="multiple"
          @click="handleAudit('rejected')"
          v-hasPermi="['fishing-biz:moment:audit']"
        >批量拒绝</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="momentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="动态ID" align="center" prop="id" />
      <el-table-column label="用户" align="center" prop="userName" />
      <el-table-column label="动态类型" align="center" prop="momentType">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.momentType === 'fishing_catch'" type="success">钓获分享</el-tag>
          <el-tag v-else-if="scope.row.momentType === 'equipment'" type="primary">装备展示</el-tag>
          <el-tag v-else-if="scope.row.momentType === 'technique'" type="info">技巧分享</el-tag>
          <el-tag v-else-if="scope.row.momentType === 'question'" type="warning">问答求助</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="内容" align="center" prop="content" :show-overflow-tooltip="true" />
      <el-table-column label="可见性" align="center" prop="visibility">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.visibility === 'public'" type="success">公开</el-tag>
          <el-tag v-else-if="scope.row.visibility === 'followers'" type="warning">仅关注者</el-tag>
          <el-tag v-else-if="scope.row.visibility === 'private'" type="danger">私密</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="点赞数" align="center" prop="likeCount" />
      <el-table-column label="评论数" align="center" prop="commentCount" />
      <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['fishing-biz:moment:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-comment"
            @click="viewMomentComments(scope.row)"
            v-if="scope.row.commentCount > 0"
            v-hasPermi="['fishing-biz:comment:list']"
          >评论({{ scope.row.commentCount }})</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['fishing-biz:moment:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 动态详情对话框 -->
    <el-dialog title="动态详情" :visible.sync="open" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="动态ID">{{ form.id }}</el-descriptions-item>
        <el-descriptions-item label="用户">{{ form.userName }}</el-descriptions-item>
        <el-descriptions-item label="动态类型">{{ form.momentType }}</el-descriptions-item>
        <el-descriptions-item label="可见性">{{ form.visibility }}</el-descriptions-item>
        <el-descriptions-item label="内容" :span="2">{{ form.content }}</el-descriptions-item>
        <el-descriptions-item label="点赞数">{{ form.likeCount }}</el-descriptions-item>
        <el-descriptions-item label="评论数">{{ form.commentCount }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ parseTime(form.createdAt) }}</el-descriptions-item>
      </el-descriptions>
      
      <div v-if="form.images && form.images.length > 0" style="margin-top: 20px;">
        <h4>图片</h4>
        <el-row :gutter="10">
          <el-col :span="6" v-for="(image, index) in form.images" :key="index">
            <el-image
              :src="image.imageUrl"
              :preview-src-list="form.images.map(img => img.imageUrl)"
              fit="cover"
              style="width: 100%; height: 120px;"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 评论列表 -->
      <div v-if="form.commentCount > 0" style="margin-top: 20px;">
        <h4>评论列表 ({{ form.commentCount }}条)</h4>
        <el-table 
          v-loading="commentLoading" 
          :data="momentComments" 
          size="small" 
          max-height="300"
          style="margin-top: 10px;"
        >
          <el-table-column label="用户" prop="userName" width="100">
            <template slot-scope="scope">
              <div style="display: flex; align-items: center;">
                <el-avatar v-if="scope.row.userAvatar" :src="scope.row.userAvatar" size="small" style="margin-right: 8px"></el-avatar>
                <span>{{ scope.row.userName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="评论内容" prop="content" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <div class="comment-content">
                <p v-if="scope.row.parentId" class="parent-comment">
                  回复 <el-tag size="mini">{{ scope.row.parentUserName }}</el-tag>: {{ scope.row.parentContent }}
                </p>
                <p class="current-comment">{{ scope.row.content }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="时间" prop="createdAt" width="140">
            <template slot-scope="scope">
              {{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}') }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="deleteComment(scope.row)"
                v-hasPermi="['fishing-biz:comment:remove']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div style="text-align: center; margin-top: 10px;">
          <el-button size="small" type="primary" @click="viewAllComments(form.id)">查看所有评论</el-button>
        </div>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMoment, getMoment, delMoment, auditMoments, setMomentVisibility } from "@/api/fishing-biz/moment";
import { getMomentComments, delComment } from "@/api/fishing-biz/comment";

export default {
  name: "Moment",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 动态表格数据
      momentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        momentType: null,
        visibility: null
      },
      // 表单参数
      form: {},
      // 评论相关
      commentLoading: false,
      momentComments: []
    };
  },
  created() {
    this.initializeFromRoute();
    this.getList();
  },
  methods: {
    /** 查询动态列表 */
    getList() {
      this.loading = true;
      listMoment(this.queryParams).then(response => {
        this.momentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids
      getMoment(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "动态详情";
        
        // 如果有评论，加载评论列表
        if (this.form.commentCount > 0) {
          this.loadMomentComments(id);
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除动态编号为"' + ids + '"的数据项？').then(function() {
        return delMoment(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 批量审核操作 */
    handleAudit(status) {
      const statusText = status === 'approved' ? '通过' : '拒绝';
      this.$modal.confirm('是否确认' + statusText + '选中的动态？').then(() => {
        return auditMoments({ ids: this.ids, status: status });
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("审核成功");
      }).catch(() => {});
    },

    /** 加载动态评论 */
    loadMomentComments(momentId) {
      this.commentLoading = true;
      getMomentComments(momentId, 1, 5).then(response => {
        this.momentComments = response.rows || [];
        this.commentLoading = false;
      }).catch(() => {
        this.commentLoading = false;
      });
    },

    /** 删除评论 */
    deleteComment(comment) {
      this.$modal.confirm('是否确认删除该评论？').then(() => {
        return delComment(comment.id);
      }).then(() => {
        this.loadMomentComments(this.form.id);
        this.$modal.msgSuccess("删除成功");
        // 更新评论数量
        this.form.commentCount = Math.max(0, this.form.commentCount - 1);
      }).catch(() => {});
    },

    /** 查看所有评论 */
    viewAllComments(momentId) {
      this.$router.push({
        path: '/fishing-biz/comment',
        query: { momentId: momentId }
      });
    },

    /** 从动态列表跳转到评论管理 */
    viewMomentComments(moment) {
      this.$router.push({
        path: '/fishing-biz/comment',
        query: { 
          momentId: moment.id,
          momentContent: moment.content
        }
      });
    },
    
    /** 从路由参数初始化页面 */
    initializeFromRoute() {
      const { momentId, action } = this.$route.query;
      if (momentId && action === 'view') {
        // 自动打开动态详情
        this.$nextTick(() => {
          const moment = this.momentList.find(m => m.id.toString() === momentId.toString());
          if (moment) {
            this.handleView(moment);
          } else {
            // 如果当前列表中没有该动态，尝试直接加载
            getMoment(momentId).then(response => {
              this.form = response.data;
              this.open = true;
              this.title = "动态详情";
              
              // 如果有评论，加载评论列表
              if (this.form.commentCount > 0) {
                this.loadMomentComments(momentId);
              }
            }).catch(() => {
              this.$message.error('动态不存在或已被删除');
            });
          }
        });
      }
    }
  }
};
</script>

<style scoped>
.comment-content {
  text-align: left;
}

.parent-comment {
  font-size: 12px;
  color: #999;
  margin: 0 0 4px 0;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
}

.current-comment {
  margin: 0;
  font-size: 14px;
}
</style>
