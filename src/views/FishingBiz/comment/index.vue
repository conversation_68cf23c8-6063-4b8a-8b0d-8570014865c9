<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="88px">
      <el-form-item label="动态ID" prop="momentId">
        <el-input
          v-model="queryParams.momentId"
          placeholder="请输入动态ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评论内容" prop="content">
        <el-input
          v-model="queryParams.content"
          placeholder="请输入评论内容关键词"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评论类型" prop="topLevelOnly">
        <el-select v-model="queryParams.topLevelOnly" placeholder="请选择评论类型" clearable>
          <el-option label="全部评论" :value="null" />
          <el-option label="顶级评论" :value="true" />
          <el-option label="回复评论" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleBatchDelete"
          v-hasPermi="['fishing-biz:comment:remove']"
        >批量删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-pie-chart"
          size="mini"
          @click="handleStats"
          v-hasPermi="['fishing-biz:comment:stats']"
        >统计信息</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="commentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="评论ID" align="center" prop="id" width="80" />
      <el-table-column label="动态ID" align="center" prop="momentId" width="80">
        <template slot-scope="scope">
          <el-link type="primary" @click="viewMoment(scope.row.momentId)">{{ scope.row.momentId }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="用户" align="center" prop="userName" width="120">
        <template slot-scope="scope">
          <div style="display: flex; align-items: center; justify-content: center;">
            <el-avatar v-if="scope.row.userAvatar" :src="scope.row.userAvatar" size="small" style="margin-right: 8px"></el-avatar>
            <span>{{ scope.row.userName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="评论内容" align="center" prop="content" :show-overflow-tooltip="true" min-width="200">
        <template slot-scope="scope">
          <div class="comment-content">
            <p v-if="scope.row.parentId" class="parent-comment">
              回复 <el-tag size="mini">{{ scope.row.parentUserName }}</el-tag>: {{ scope.row.parentContent }}
            </p>
            <p class="current-comment">{{ scope.row.content }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="动态摘要" align="center" prop="momentContent" :show-overflow-tooltip="true" width="150">
        <template slot-scope="scope">
          <el-link type="info" @click="viewMomentDetail(scope.row)" :underline="false">
            {{ scope.row.momentContent }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="评论等级" align="center" prop="level" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.level === 1" type="primary" size="mini">顶级</el-tag>
          <el-tag v-else type="info" size="mini">回复</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="回复数" align="center" prop="replyCount" width="80" />
      <el-table-column label="地理位置" align="center" width="120">
        <template slot-scope="scope">
          {{ scope.row.province }}{{ scope.row.city }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['fishing-biz:comment:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-document"
            @click="viewMomentDetail(scope.row)"
            v-hasPermi="['fishing-biz:moment:query']"
          >动态详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-comment"
            @click="viewReplies(scope.row)"
            v-if="scope.row.level === 1 && scope.row.replyCount > 0"
          >回复({{ scope.row.replyCount }})</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['fishing-biz:comment:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 评论详情对话框 -->
    <el-dialog title="评论详情" :visible.sync="open" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="评论ID">{{ form.id }}</el-descriptions-item>
        <el-descriptions-item label="动态ID">{{ form.momentId }}</el-descriptions-item>
        <el-descriptions-item label="用户">{{ form.userName }}</el-descriptions-item>
        <el-descriptions-item label="评论等级">
          <el-tag v-if="form.level === 1" type="primary" size="mini">顶级评论</el-tag>
          <el-tag v-else type="info" size="mini">回复评论</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="评论内容" :span="2">{{ form.content }}</el-descriptions-item>
        <el-descriptions-item label="动态摘要" :span="2">{{ form.momentContent }}</el-descriptions-item>
        <el-descriptions-item label="父评论" v-if="form.parentId" :span="2">
          <div>
            <p><strong>用户:</strong> {{ form.parentUserName }}</p>
            <p><strong>内容:</strong> {{ form.parentContent }}</p>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="回复数量">{{ form.replyCount }}</el-descriptions-item>
        <el-descriptions-item label="地理位置">{{ form.province }}{{ form.city }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(form.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(form.updatedAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 回复列表对话框 -->
    <el-dialog title="回复列表" :visible.sync="replyOpen" width="1000px" append-to-body>
      <div style="margin-bottom: 16px;">
        <el-tag type="primary">原评论</el-tag>
        <span style="margin-left: 8px;">{{ currentComment.content }}</span>
      </div>
      
      <el-table v-loading="replyLoading" :data="replyList" max-height="400">
        <el-table-column label="回复ID" align="center" prop="id" width="80" />
        <el-table-column label="回复用户" align="center" prop="userName" width="120">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center; justify-content: center;">
              <el-avatar v-if="scope.row.userAvatar" :src="scope.row.userAvatar" size="small" style="margin-right: 8px"></el-avatar>
              <span>{{ scope.row.userName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="回复内容" align="center" prop="content" :show-overflow-tooltip="true" />
        <el-table-column label="回复时间" align="center" prop="createdAt" width="160">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['fishing-biz:comment:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="replyTotal > 0"
        :total="replyTotal"
        :page.sync="replyQueryParams.pageNum"
        :limit.sync="replyQueryParams.pageSize"
        @pagination="getReplies"
      />
    </el-dialog>

    <!-- 批量删除确认对话框 -->
    <el-dialog title="批量删除评论" :visible.sync="batchDeleteOpen" width="500px" append-to-body>
      <div>
        <p>确认删除选中的 <strong>{{ ids.length }}</strong> 条评论吗？</p>
        <el-form-item label="删除原因：">
          <el-input
            v-model="deleteReason"
            placeholder="请输入删除原因（可选）"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDeleteOpen = false">取 消</el-button>
        <el-button type="danger" @click="confirmBatchDelete">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog title="评论统计信息" :visible.sync="statsOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="总评论数">{{ stats.totalCount }}</el-descriptions-item>
        <el-descriptions-item label="今日评论数">{{ stats.todayCount }}</el-descriptions-item>
        <el-descriptions-item label="活跃评论数">{{ stats.activeComments }}</el-descriptions-item>
        <el-descriptions-item label="平均回复数">{{ avgReplyCount }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listComment, getComment, delComment, batchDeleteComments, getCommentStats } from "@/api/fishing-biz/comment"
import { getMomentComments } from "@/api/fishing-biz/comment"

export default {
  name: "Comment",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 评论表格数据
      commentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        momentId: null,
        userId: null,
        content: null,
        parentId: null,
        topLevelOnly: null,
        startTime: null,
        endTime: null
      },
      // 表单参数
      form: {},
      // 日期范围
      dateRange: [],
      // 回复相关
      replyOpen: false,
      replyLoading: false,
      replyList: [],
      replyTotal: 0,
      currentComment: {},
      replyQueryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 批量删除
      batchDeleteOpen: false,
      deleteReason: "",
      // 统计信息
      statsOpen: false,
      stats: {
        totalCount: 0,
        todayCount: 0,
        activeComments: 0
      }
    };
  },
  computed: {
    avgReplyCount() {
      if (this.commentList.length === 0) return 0;
      const totalReplies = this.commentList.reduce((sum, comment) => sum + (comment.replyCount || 0), 0);
      return (totalReplies / this.commentList.length).toFixed(2);
    }
  },
  created() {
    this.initializeFromRoute();
    this.getList();
  },
  methods: {
    /** 查询评论列表 */
    getList() {
      this.loading = true;
      const queryParams = { ...this.queryParams };
      
      // 处理时间范围
      if (this.dateRange != null && this.dateRange !== '') {
        queryParams.startTime = this.dateRange[0];
        queryParams.endTime = this.dateRange[1];
      }
      
      listComment(queryParams).then(response => {
        this.commentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    
    // 表单重置
    reset() {
      this.form = {
        id: null,
        momentId: null,
        userId: null,
        content: null,
        parentId: null,
        createdAt: null
      };
      this.resetForm("form");
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const id = row.id || this.ids
      getComment(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "评论详情";
      });
    },
    
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除评论编号为"' + ids + '"的数据项？').then(function() {
        return delComment(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    
    /** 批量删除按钮操作 */
    handleBatchDelete() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要删除的评论");
        return;
      }
      this.batchDeleteOpen = true;
    },
    
    /** 确认批量删除 */
    confirmBatchDelete() {
      const data = {
        commentIds: this.ids,
        deleteReason: this.deleteReason
      };
      
      batchDeleteComments(data).then(() => {
        this.getList();
        this.batchDeleteOpen = false;
        this.deleteReason = "";
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
        this.batchDeleteOpen = false;
      });
    },
    
    /** 查看回复 */
    viewReplies(row) {
      this.currentComment = row;
      this.replyQueryParams.pageNum = 1;
      this.replyOpen = true;
      this.getReplies();
    },
    
    /** 获取回复列表 */
    getReplies() {
      this.replyLoading = true;
      const queryParams = {
        parentId: this.currentComment.id,
        pageNum: this.replyQueryParams.pageNum,
        pageSize: this.replyQueryParams.pageSize
      };
      
      listComment(queryParams).then(response => {
        this.replyList = response.rows;
        this.replyTotal = response.total;
        this.replyLoading = false;
      });
    },
    
    /** 查看动态 */
    viewMoment(momentId) {
      this.$router.push(`/fishing-biz/moment/detail/${momentId}`);
    },
    
    /** 查看统计信息 */
    handleStats() {
      getCommentStats().then(response => {
        this.stats = response.data;
        this.statsOpen = true;
      });
    },
    
    /** 查看动态详情 */
    viewMomentDetail(comment) {
      this.$router.push({
        path: '/fishing-biz/moment',
        query: { 
          momentId: comment.momentId,
          action: 'view'
        }
      });
    },
    
    /** 从路由参数初始化页面 */
    initializeFromRoute() {
      const { momentId, momentContent } = this.$route.query;
      if (momentId) {
        this.queryParams.momentId = momentId;
        // 如果有动态内容，可以显示提示信息
        if (momentContent) {
          this.$message({
            message: `正在查看动态「${momentContent.substring(0, 20)}${momentContent.length > 20 ? '...' : ''}」的评论`,
            type: 'info',
            duration: 3000
          });
        }
      }
    }
  }
};
</script>

<style scoped>
.comment-content {
  text-align: left;
}

.parent-comment {
  font-size: 12px;
  color: #999;
  margin: 0 0 4px 0;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
}

.current-comment {
  margin: 0;
  font-size: 14px;
}

.el-descriptions {
  margin-top: 20px;
}
</style>