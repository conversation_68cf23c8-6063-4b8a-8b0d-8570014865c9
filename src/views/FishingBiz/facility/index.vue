<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设施名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入设施名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6" v-for="(item, index) in statisticsData" :key="index">
        <el-card shadow="hover" class="statistics-card">
          <div class="statistics-content">
            <div class="statistics-icon" :style="{ backgroundColor: item.color }">
              <i :class="item.icon"></i>
            </div>
            <div class="statistics-text">
              <div class="statistics-title">{{ item.title }}</div>
              <div class="statistics-value">{{ item.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['fishing-biz:facility:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['fishing-biz:facility:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['fishing-biz:facility:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-setting"
          size="mini"
          :disabled="multiple"
          @click="handleBatchStatus"
          v-hasPermi="['fishing-biz:facility:edit']"
        >批量状态</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-pie-chart"
          size="mini"
          @click="showStatistics"
        >统计分析</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="facilityList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设施ID" align="center" prop="id" width="80" />
      <el-table-column label="设施图标" align="center" prop="icon" width="80">
        <template slot-scope="scope">
          <img v-if="scope.row.icon" :src="scope.row.icon" alt="图标" style="width: 30px; height: 30px;" />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="设施名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="使用次数" align="center" prop="usageCount" width="100" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['fishing-biz:facility:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-upload"
            @click="handleUploadIcon(scope.row)"
            v-hasPermi="['fishing-biz:facility:edit']"
          >图标</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-connection"
            @click="handleSync(scope.row)"
            v-hasPermi="['fishing-biz:facility:edit']"
          >同步</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['fishing-biz:facility:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改设施对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="设施名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入设施名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 上传图标对话框 -->
    <el-dialog title="上传设施图标" :visible.sync="iconOpen" width="400px" append-to-body>
      <el-upload
        class="upload-demo"
        drag
        :action="uploadAction"
        :headers="uploadHeaders"
        :on-success="handleUploadSuccess"
        :before-upload="beforeUpload"
        accept="image/*"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过2MB</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="iconOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 同步到钓点对话框 -->
    <el-dialog title="同步设施到钓点" :visible.sync="syncOpen" width="600px" append-to-body>
      <el-form ref="syncForm" :model="syncForm" label-width="80px">
        <el-form-item label="设施名称">
          <el-input v-model="currentFacility.name" disabled />
        </el-form-item>
        <el-form-item label="选择钓点">
          <el-select v-model="syncForm.spotIds" multiple placeholder="请选择钓点" style="width: 100%">
            <el-option
              v-for="spot in spotOptions"
              :key="spot.id"
              :label="spot.name"
              :value="spot.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="syncOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitSync">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 统计分析对话框 -->
    <el-dialog title="设施统计分析" :visible.sync="statisticsOpen" width="800px" append-to-body>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <div slot="header">热门设施排行</div>
            <div class="ranking-list">
              <div v-for="(item, index) in popularityRanking" :key="index" class="ranking-item">
                <div class="ranking-index" :class="'rank-' + (index + 1)">{{ index + 1 }}</div>
                <div class="ranking-info">
                  <div class="ranking-name">{{ item.name }}</div>
                  <div class="ranking-value">{{ item.usageCount }}次使用</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <div slot="header">设施分类统计</div>
            <div ref="categoryChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { listFacility, getFacility, delFacility, addFacility, updateFacility, batchUpdateFacilityStatus, uploadFacilityIcon, getFacilityUsageStatistics, getFacilityPopularityRanking, getFacilityCategoryStatistics, syncFacilityToSpots } from "@/api/fishing-biz/facility";
import { listSpot } from "@/api/fishing-biz/spot";
import { getToken } from "@/utils/auth";
import * as echarts from 'echarts'

export default {
  name: "Facility",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设施表格数据
      facilityList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 图标上传弹出层
      iconOpen: false,
      // 同步弹出层
      syncOpen: false,
      // 统计弹出层
      statisticsOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        status: null
      },
      // 表单参数
      form: {},
      // 同步表单
      syncForm: {
        spotIds: []
      },
      // 当前设施
      currentFacility: {},
      // 钓点选项
      spotOptions: [],
      // 统计数据
      statisticsData: [],
      // 热门排行
      popularityRanking: [],
      // 上传地址
      uploadAction: process.env.VUE_APP_BASE_API + '/fishing-biz/facility/upload-icon/',
      // 上传请求头
      uploadHeaders: {
        Authorization: "Bearer " + getToken()
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "设施名称不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getStatisticsData();
    this.getSpotOptions();
  },
  methods: {
    /** 查询设施列表 */
    getList() {
      this.loading = true;
      listFacility(this.queryParams).then(response => {
        this.facilityList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取统计数据 */
    async getStatisticsData() {
      try {
        const res = await getFacilityUsageStatistics();
        const data = res.data;
        
        this.statisticsData = [
          {
            title: '总设施数',
            value: data.totalFacilities || 0,
            icon: 'el-icon-office-building',
            color: '#409EFF'
          },
          {
            title: '启用设施',
            value: data.activeFacilities || 0,
            icon: 'el-icon-check',
            color: '#67C23A'
          },
          {
            title: '总使用次数',
            value: data.totalUsage || 0,
            icon: 'el-icon-data-line',
            color: '#E6A23C'
          },
          {
            title: '平均使用率',
            value: (data.averageUsage || 0) + '%',
            icon: 'el-icon-pie-chart',
            color: '#F56C6C'
          }
        ];
      } catch (error) {
        console.error('获取统计数据失败:', error);
      }
    },
    /** 获取钓点选项 */
    getSpotOptions() {
      listSpot({ pageNum: 1, pageSize: 1000 }).then(response => {
        this.spotOptions = response.rows.map(spot => ({
          id: spot.id,
          name: spot.name
        }));
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        description: null,
        icon: null,
        status: 1
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设施";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getFacility(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改设施";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateFacility(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addFacility(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === 1 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.name + '"设施吗？').then(() => {
        return updateFacility(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {
        row.status = row.status === 0 ? 1 : 0;
      });
    },
    /** 批量状态修改 */
    handleBatchStatus() {
      this.$prompt('请选择状态', '批量修改', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'select',
        inputOptions: {
          '1': '启用',
          '0': '禁用'
        }
      }).then(({ value }) => {
        batchUpdateFacilityStatus(this.ids, value).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.getList();
        });
      });
    },
    /** 上传图标 */
    handleUploadIcon(row) {
      this.currentFacility = row;
      this.uploadAction = process.env.VUE_APP_BASE_API + '/fishing-biz/facility/upload-icon/' + row.id;
      this.iconOpen = true;
    },
    /** 上传前校验 */
    beforeUpload(file) {
      const isImage = file.type.indexOf('image/') === 0;
      const isLt2M = file.size / 1024 / 1024 < 2;
      
      if (!isImage) {
        this.$modal.msgError('只能上传图片文件!');
        return false;
      }
      if (!isLt2M) {
        this.$modal.msgError('上传图片大小不能超过 2MB!');
        return false;
      }
      return true;
    },
    /** 上传成功 */
    handleUploadSuccess(response) {
      if (response.code === 200) {
        this.$modal.msgSuccess("上传成功");
        this.iconOpen = false;
        this.getList();
      } else {
        this.$modal.msgError(response.msg);
      }
    },
    /** 同步到钓点 */
    handleSync(row) {
      this.currentFacility = row;
      this.syncForm.spotIds = [];
      this.syncOpen = true;
    },
    /** 提交同步 */
    submitSync() {
      if (this.syncForm.spotIds.length === 0) {
        this.$modal.msgWarning("请选择要同步的钓点");
        return;
      }
      
      syncFacilityToSpots(this.currentFacility.id, this.syncForm.spotIds).then(response => {
        this.$modal.msgSuccess("同步成功");
        this.syncOpen = false;
      });
    },
    /** 显示统计 */
    async showStatistics() {
      try {
        // 获取热门排行
        const rankingRes = await getFacilityPopularityRanking(10);
        this.popularityRanking = rankingRes.data;
        
        this.statisticsOpen = true;
        
        // 等待DOM更新后绘制图表
        this.$nextTick(() => {
          this.initCategoryChart();
        });
      } catch (error) {
        console.error('获取统计数据失败:', error);
      }
    },
    /** 初始化分类图表 */
    async initCategoryChart() {
      try {
        const res = await getFacilityCategoryStatistics();
        const data = res.data.categories || [];
        
        const chart = echarts.init(this.$refs.categoryChart);
        const option = {
          tooltip: { trigger: 'item' },
          series: [
            {
              type: 'pie',
              radius: '60%',
              data: data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        };
        
        chart.setOption(option);
      } catch (error) {
        console.error('获取分类统计失败:', error);
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除设施编号为"' + ids + '"的数据项？').then(function() {
        return delFacility(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.statistics-card {
  height: 100px;
}

.statistics-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.statistics-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.statistics-icon i {
  font-size: 20px;
  color: white;
}

.statistics-text {
  flex: 1;
}

.statistics-title {
  font-size: 14px;
  color: #999;
  margin-bottom: 5px;
}

.statistics-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.ranking-list {
  max-height: 300px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.ranking-index {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 10px;
  background-color: #f0f0f0;
  color: #666;
}

.ranking-index.rank-1 {
  background-color: #FFD700;
  color: white;
}

.ranking-index.rank-2 {
  background-color: #C0C0C0;
  color: white;
}

.ranking-index.rank-3 {
  background-color: #CD7F32;
  color: white;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-weight: bold;
  margin-bottom: 2px;
}

.ranking-value {
  font-size: 12px;
  color: #999;
}
</style>