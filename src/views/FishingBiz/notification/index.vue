<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="通知类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择通知类型" clearable>
          <el-option label="系统通知" value="system" />
          <el-option label="活动通知" value="activity" />
          <el-option label="个人通知" value="personal" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="已发送" value="sent" />
          <el-option label="草稿" value="draft" />
          <el-option label="已撤回" value="recalled" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['fishing-biz:notification:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-s-promotion"
          size="mini"
          @click="handleSend"
          v-hasPermi="['fishing-biz:notification:send']"
        >发送通知</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['fishing-biz:notification:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="notificationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="通知ID" align="center" prop="id" width="80" />
      <el-table-column label="标题" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="类型" align="center" prop="type" width="100">
        <template slot-scope="scope">
          <el-tag :type="getTypeTagType(scope.row.type)">
            {{ formatType(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="接收人数" align="center" prop="targetCount" width="100" />
      <el-table-column label="已读人数" align="center" prop="readCount" width="100" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)">
            {{ formatStatus(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createdAt" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['fishing-biz:notification:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['fishing-biz:notification:edit']"
            v-if="scope.row.status === 'draft'"
          >编辑</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh-left"
            @click="handleRecall(scope.row)"
            v-hasPermi="['fishing-biz:notification:edit']"
            v-if="scope.row.status === 'sent'"
          >撤回</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['fishing-biz:notification:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改通知对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="通知标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入通知标题" />
        </el-form-item>
        <el-form-item label="通知类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择通知类型">
            <el-option label="系统通知" value="system" />
            <el-option label="活动通知" value="activity" />
            <el-option label="个人通知" value="personal" />
          </el-select>
        </el-form-item>
        <el-form-item label="通知内容" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="4" placeholder="请输入通知内容" />
        </el-form-item>
        <el-form-item label="链接地址" prop="linkUrl">
          <el-input v-model="form.linkUrl" placeholder="请输入链接地址（可选）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 发送通知对话框 -->
    <el-dialog title="发送通知" :visible.sync="sendOpen" width="600px" append-to-body>
      <el-form ref="sendForm" :model="sendForm" :rules="sendRules" label-width="80px">
        <el-form-item label="发送类型" prop="type">
          <el-radio-group v-model="sendForm.type">
            <el-radio label="broadcast">全体用户</el-radio>
            <el-radio label="targeted">指定用户组</el-radio>
            <el-radio label="single">单个用户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="用户ID" prop="userIds" v-if="sendForm.type !== 'broadcast'">
          <el-input v-model="sendForm.userIdsText" placeholder="请输入用户ID，多个用逗号分隔" />
        </el-form-item>
        <el-form-item label="通知标题" prop="title">
          <el-input v-model="sendForm.title" placeholder="请输入通知标题" />
        </el-form-item>
        <el-form-item label="通知内容" prop="content">
          <el-input v-model="sendForm.content" type="textarea" :rows="4" placeholder="请输入通知内容" />
        </el-form-item>
        <el-form-item label="选择模板">
          <el-select v-model="selectedTemplate" placeholder="请选择模板" @change="applyTemplate">
            <el-option
              v-for="template in templates"
              :key="template.id"
              :label="template.title"
              :value="template.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="sendOpen = false">取 消</el-button>
        <el-button type="primary" @click="submitSend">立即发送</el-button>
      </div>
    </el-dialog>

    <!-- 通知详情对话框 -->
    <el-dialog title="通知详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions v-if="notificationDetail" border :column="2">
        <el-descriptions-item label="通知ID">{{ notificationDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="标题">{{ notificationDetail.title }}</el-descriptions-item>
        <el-descriptions-item label="类型">{{ formatType(notificationDetail.type) }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ formatStatus(notificationDetail.status) }}</el-descriptions-item>
        <el-descriptions-item label="接收人数">{{ notificationDetail.targetCount }}</el-descriptions-item>
        <el-descriptions-item label="已读人数">{{ notificationDetail.readCount }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(notificationDetail.createdAt) }}</el-descriptions-item>
        <el-descriptions-item label="发送时间">{{ parseTime(notificationDetail.sentAt) }}</el-descriptions-item>
        <el-descriptions-item label="内容" :span="2">{{ notificationDetail.content }}</el-descriptions-item>
        <el-descriptions-item label="链接地址" :span="2">{{ notificationDetail.linkUrl || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listNotification, getNotification, delNotification, addNotification, updateNotification, sendNotification, getNotificationTemplates, recallNotification } from "@/api/fishing-biz/notification";

export default {
  name: "Notification",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 通知表格数据
      notificationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 发送通知弹出层
      sendOpen: false,
      // 详情弹出层
      detailOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        type: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 发送表单参数
      sendForm: {
        type: 'broadcast',
        userIdsText: '',
        title: '',
        content: ''
      },
      // 通知详情
      notificationDetail: null,
      // 模板列表
      templates: [],
      // 选中的模板
      selectedTemplate: null,
      // 表单校验
      rules: {
        title: [
          { required: true, message: "通知标题不能为空", trigger: "blur" }
        ],
        type: [
          { required: true, message: "通知类型不能为空", trigger: "change" }
        ],
        content: [
          { required: true, message: "通知内容不能为空", trigger: "blur" }
        ]
      },
      // 发送表单校验
      sendRules: {
        title: [
          { required: true, message: "通知标题不能为空", trigger: "blur" }
        ],
        content: [
          { required: true, message: "通知内容不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getTemplates();
  },
  methods: {
    /** 查询通知列表 */
    getList() {
      this.loading = true;
      listNotification(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.notificationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取模板列表 */
    getTemplates() {
      getNotificationTemplates().then(response => {
        this.templates = response.data || [];
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        type: null,
        content: null,
        linkUrl: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加通知";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getNotification(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改通知";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const id = row.id || this.ids
      getNotification(id).then(response => {
        this.notificationDetail = response.data;
        this.detailOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateNotification(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNotification(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 发送通知 */
    handleSend() {
      this.sendForm = {
        type: 'broadcast',
        userIdsText: '',
        title: '',
        content: ''
      };
      this.selectedTemplate = null;
      this.sendOpen = true;
    },
    /** 提交发送 */
    submitSend() {
      this.$refs["sendForm"].validate(valid => {
        if (valid) {
          const data = {
            type: this.sendForm.type,
            title: this.sendForm.title,
            content: this.sendForm.content,
            userIds: this.sendForm.type !== 'broadcast' ? 
              this.sendForm.userIdsText.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : 
              null
          };
          
          sendNotification(data).then(response => {
            this.$modal.msgSuccess("发送成功");
            this.sendOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 应用模板 */
    applyTemplate() {
      if (this.selectedTemplate) {
        const template = this.templates.find(t => t.id === this.selectedTemplate);
        if (template) {
          this.sendForm.title = template.title;
          this.sendForm.content = template.content;
        }
      }
    },
    /** 撤回通知 */
    handleRecall(row) {
      this.$modal.confirm('是否确认撤回该通知？').then(() => {
        return recallNotification(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("撤回成功");
      }).catch(() => {});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除通知编号为"' + ids + '"的数据项？').then(function() {
        return delNotification(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 格式化类型 */
    formatType(type) {
      const typeMap = {
        'system': '系统通知',
        'activity': '活动通知',
        'personal': '个人通知'
      };
      return typeMap[type] || type;
    },
    /** 格式化状态 */
    formatStatus(status) {
      const statusMap = {
        'sent': '已发送',
        'draft': '草稿',
        'recalled': '已撤回'
      };
      return statusMap[status] || status;
    },
    /** 获取类型标签类型 */
    getTypeTagType(type) {
      const typeMap = {
        'system': 'primary',
        'activity': 'success',
        'personal': 'warning'
      };
      return typeMap[type] || 'info';
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusMap = {
        'sent': 'success',
        'draft': 'info',
        'recalled': 'danger'
      };
      return statusMap[status] || 'info';
    }
  }
};
</script>