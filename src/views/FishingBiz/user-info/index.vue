<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="手机号" prop="telephoneNumber">
        <el-input
          v-model="queryParams.telephoneNumber"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select
          v-model="queryParams.gender"
          placeholder="请选择性别"
          clearable
        >
          <el-option label="未知" :value="0" />
          <el-option label="男" :value="1" />
          <el-option label="女" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="enabled">
        <el-select
          v-model="queryParams.enabled"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="正常" :value="true" />
          <el-option label="停用" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleStatusChange(true)"
          v-hasPermi="['fishing-biz:user-info:status']"
          >启用</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-close"
          size="mini"
          :disabled="multiple"
          @click="handleStatusChange(false)"
          v-hasPermi="['fishing-biz:user-info:status']"
          >停用</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['fishing-biz:user-info:remove']"
          >删除</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="userList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="用户ID" align="center" prop="id" />
      <el-table-column label="头像" align="center" prop="avatarUrl" width="100">
        <template slot-scope="scope">
          <el-avatar
            v-if="scope.row.avatarUrl"
            :src="scope.row.avatarUrl"
            :size="40"
          />
          <el-avatar v-else :size="40">{{
            scope.row.name ? scope.row.name.charAt(0) : 'U'
          }}</el-avatar>
        </template>
      </el-table-column>
      <el-table-column label="用户名" align="center" prop="name" />
      <el-table-column label="手机号" align="center" prop="telephoneNumber" />
      <el-table-column label="性别" align="center" prop="gender">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.gender === 1" type="primary">男</el-tag>
          <el-tag v-else-if="scope.row.gender === 2" type="danger">女</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="地区" align="center">
        <template slot-scope="scope">
          {{
            [scope.row.province, scope.row.city, scope.row.county]
              .filter(Boolean)
              .join(' ')
          }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="enabled">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.enabled"
            @change="handleStatusChangeRow(scope.row)"
            v-hasPermi="['fishing-biz:user-info:status']"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}')
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['fishing-biz:user-info:query']"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['fishing-biz:user-info:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-key"
            @click="handleResetPwd(scope.row)"
            v-hasPermi="['fishing-biz:user-info:resetPwd']"
            >重置密码</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['fishing-biz:user-info:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户名" prop="name">
              <el-input v-model="form.name" placeholder="请输入用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="telephoneNumber">
              <el-input
                v-model="form.telephoneNumber"
                placeholder="请输入手机号"
                :disabled="form.id != null"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="form.gender" placeholder="请选择性别">
                <el-option label="未知" :value="0" />
                <el-option label="男" :value="1" />
                <el-option label="女" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="enabled">
              <el-radio-group v-model="form.enabled">
                <el-radio :label="true">正常</el-radio>
                <el-radio :label="false">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="个人简介" prop="introduce">
          <el-input
            v-model="form.introduce"
            type="textarea"
            placeholder="请输入个人简介"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      title="重置密码"
      :visible.sync="resetPwdOpen"
      width="400px"
      append-to-body
    >
      <el-form
        ref="resetPwdForm"
        :model="resetPwdForm"
        :rules="resetPwdRules"
        label-width="80px"
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="resetPwdForm.newPassword"
            type="password"
            placeholder="请输入新密码"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitResetPwd">确 定</el-button>
        <el-button @click="cancelResetPwd">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listUserInfo,
  getUserInfo,
  updateUserInfo,
  delUserInfo,
  setUserStatus,
  resetUserPassword,
} from '@/api/fishing-biz/user-info'

export default {
  name: 'UserInfo',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 重置密码弹出层
      resetPwdOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        telephoneNumber: null,
        name: null,
        gender: null,
        enabled: null,
      },
      // 表单参数
      form: {},
      // 重置密码表单
      resetPwdForm: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
        telephoneNumber: [
          { required: true, message: '手机号不能为空', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '手机号格式不正确',
            trigger: 'blur',
          },
        ],
      },
      // 重置密码校验
      resetPwdRules: {
        newPassword: [
          { required: true, message: '新密码不能为空', trigger: 'blur' },
          {
            min: 6,
            max: 20,
            message: '密码长度在 6 到 20 个字符',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true
      listUserInfo(this.queryParams).then((response) => {
        this.userList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        telephoneNumber: null,
        gender: 0,
        enabled: true,
        introduce: null,
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset()
      const id = row.id || this.ids
      getUserInfo(id).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '用户详情'
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加用户'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getUserInfo(id).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '修改用户'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          updateUserInfo(this.form).then((response) => {
            this.$modal.msgSuccess('修改成功')
            this.open = false
            this.getList()
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除用户编号为"' + ids + '"的数据项？')
        .then(function () {
          return delUserInfo(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    /** 批量状态修改 */
    handleStatusChange(enabled) {
      const statusText = enabled ? '启用' : '停用'
      this.$modal
        .confirm('是否确认' + statusText + '选中的用户？')
        .then(() => {
          return setUserStatus({ ids: this.ids, enabled: enabled })
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('操作成功')
        })
        .catch(() => {})
    },
    /** 单行状态修改 */
    handleStatusChangeRow(row) {
      // 先恢复开关状态，因为@change已经改变了值
      const newEnabled = row.enabled
      row.enabled = !row.enabled

      let text = newEnabled ? '启用' : '停用'
      this.$modal
        .confirm('确认要"' + text + '""' + row.name + '"用户吗？')
        .then(function () {
          return setUserStatus({ ids: [row.id], enabled: newEnabled })
        })
        .then(() => {
          row.enabled = newEnabled // 确认后设置新状态
          this.$modal.msgSuccess(text + '成功')
        })
        .catch(function () {
          // 取消时保持原状态，不需要额外操作
        })
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.resetPwdForm = {
        ids: [row.id],
        newPassword: '',
      }
      this.resetPwdOpen = true
    },
    /** 提交重置密码 */
    submitResetPwd() {
      this.$refs['resetPwdForm'].validate((valid) => {
        if (valid) {
          resetUserPassword(this.resetPwdForm).then((response) => {
            this.$modal.msgSuccess('重置密码成功')
            this.resetPwdOpen = false
          })
        }
      })
    },
    /** 取消重置密码 */
    cancelResetPwd() {
      this.resetPwdOpen = false
      this.resetPwdForm = {}
    },
  },
}
</script>
