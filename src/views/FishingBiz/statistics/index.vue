<template>
  <div class="app-container">
    <!-- 数据概览卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6" v-for="(item, index) in overviewData" :key="index">
        <el-card shadow="hover" class="overview-card">
          <div class="overview-content">
            <div class="overview-icon" :style="{ backgroundColor: item.color }">
              <i :class="item.icon"></i>
            </div>
            <div class="overview-text">
              <div class="overview-title">{{ item.title }}</div>
              <div class="overview-value">{{ item.value }}</div>
              <div class="overview-change" :class="item.changeType">
                <i :class="item.changeIcon"></i>
                {{ item.change }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="12">
        <el-card shadow="hover">
          <div slot="header" class="card-header">
            <span>用户增长趋势</span>
            <el-select v-model="userDays" @change="getUserData" size="small" style="width: 100px">
              <el-option label="7天" :value="7"></el-option>
              <el-option label="30天" :value="30"></el-option>
              <el-option label="90天" :value="90"></el-option>
            </el-select>
          </div>
          <div ref="userChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <div slot="header" class="card-header">
            <span>钓点签到统计</span>
            <el-select v-model="spotDays" @change="getSpotData" size="small" style="width: 100px">
              <el-option label="7天" :value="7"></el-option>
              <el-option label="30天" :value="30"></el-option>
              <el-option label="90天" :value="90"></el-option>
            </el-select>
          </div>
          <div ref="spotChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mb20">
      <el-col :span="8">
        <el-card shadow="hover">
          <div slot="header">地域分布</div>
          <div ref="regionChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <div slot="header">热门钓点排行</div>
          <div class="ranking-list">
            <div v-for="(spot, index) in topSpots" :key="index" class="ranking-item">
              <div class="ranking-index" :class="'rank-' + (index + 1)">{{ index + 1 }}</div>
              <div class="ranking-info">
                <div class="ranking-name">{{ spot.name }}</div>
                <div class="ranking-value">{{ spot.checkinCount }}次签到</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <div slot="header">用户活跃度分析</div>
          <div ref="behaviorChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时数据 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card shadow="hover">
          <div slot="header" class="card-header">
            <span>实时数据监控</span>
            <el-button @click="refreshRealtime" size="small" icon="el-icon-refresh">刷新</el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="6" v-for="(item, index) in realtimeData" :key="index">
              <div class="realtime-item">
                <div class="realtime-label">{{ item.label }}</div>
                <div class="realtime-value">{{ item.value }}</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getDashboard, getUserStatistics, getSpotStatistics, getMomentStatistics, getRegionStatistics, getUserBehaviorAnalysis, getSpotRanking, getRealtimeStatistics } from "@/api/fishing-biz/statistics"

export default {
  name: "Statistics",
  data() {
    return {
      userDays: 7,
      spotDays: 7,
      overviewData: [],
      topSpots: [],
      realtimeData: [],
      userChart: null,
      spotChart: null,
      regionChart: null,
      behaviorChart: null
    }
  },
  mounted() {
    this.initData()
    this.initCharts()
    // 每30秒刷新一次实时数据
    this.realtimeTimer = setInterval(() => {
      this.refreshRealtime()
    }, 30000)
  },
  beforeDestroy() {
    if (this.realtimeTimer) {
      clearInterval(this.realtimeTimer)
    }
  },
  methods: {
    async initData() {
      try {
        // 获取看板数据
        const dashboardRes = await getDashboard()
        this.overviewData = [
          {
            title: '总用户数',
            value: dashboardRes.data.totalUsers || 0,
            change: '+12.5%',
            changeType: 'increase',
            changeIcon: 'el-icon-arrow-up',
            icon: 'el-icon-user',
            color: '#409EFF'
          },
          {
            title: '总钓点数',
            value: dashboardRes.data.totalSpots || 0,
            change: '+8.2%',
            changeType: 'increase',
            changeIcon: 'el-icon-arrow-up',
            icon: 'el-icon-location',
            color: '#67C23A'
          },
          {
            title: '今日动态',
            value: dashboardRes.data.todayMoments || 0,
            change: '+15.3%',
            changeType: 'increase',
            changeIcon: 'el-icon-arrow-up',
            icon: 'el-icon-chat-dot-round',
            color: '#E6A23C'
          },
          {
            title: '今日签到',
            value: dashboardRes.data.todayCheckins || 0,
            change: '-2.1%',
            changeType: 'decrease',
            changeIcon: 'el-icon-arrow-down',
            icon: 'el-icon-check',
            color: '#F56C6C'
          }
        ]

        // 获取热门钓点
        const rankingRes = await getSpotRanking('checkin', 5)
        this.topSpots = rankingRes.data.ranking || []

        // 获取实时数据
        this.refreshRealtime()
      } catch (error) {
        console.error('初始化数据失败:', error)
      }
    },

    initCharts() {
      this.initUserChart()
      this.initSpotChart()
      this.initRegionChart()
      this.initBehaviorChart()
    },

    initUserChart() {
      this.userChart = echarts.init(this.$refs.userChart)
      this.getUserData()
    },

    initSpotChart() {
      this.spotChart = echarts.init(this.$refs.spotChart)
      this.getSpotData()
    },

    initRegionChart() {
      this.regionChart = echarts.init(this.$refs.regionChart)
      this.getRegionData()
    },

    initBehaviorChart() {
      this.behaviorChart = echarts.init(this.$refs.behaviorChart)
      this.getBehaviorData()
    },

    async getUserData() {
      try {
        const res = await getUserStatistics(this.userDays)
        const data = res.data
        
        const option = {
          title: { text: '用户增长', left: 'center', textStyle: { fontSize: 14 } },
          tooltip: { trigger: 'axis' },
          xAxis: {
            type: 'category',
            data: data.dates || []
          },
          yAxis: { type: 'value' },
          series: [
            {
              name: '新增用户',
              type: 'line',
              data: data.newUsers || [],
              smooth: true,
              itemStyle: { color: '#409EFF' }
            },
            {
              name: '活跃用户',
              type: 'line',
              data: data.activeUsers || [],
              smooth: true,
              itemStyle: { color: '#67C23A' }
            }
          ]
        }
        
        this.userChart.setOption(option)
      } catch (error) {
        console.error('获取用户数据失败:', error)
      }
    },

    async getSpotData() {
      try {
        const res = await getSpotStatistics(this.spotDays)
        const data = res.data
        
        const option = {
          title: { text: '钓点签到', left: 'center', textStyle: { fontSize: 14 } },
          tooltip: { trigger: 'axis' },
          xAxis: {
            type: 'category',
            data: data.dates || []
          },
          yAxis: { type: 'value' },
          series: [
            {
              name: '签到次数',
              type: 'bar',
              data: data.checkins || [],
              itemStyle: { color: '#E6A23C' }
            }
          ]
        }
        
        this.spotChart.setOption(option)
      } catch (error) {
        console.error('获取钓点数据失败:', error)
      }
    },

    async getRegionData() {
      try {
        const res = await getRegionStatistics()
        const data = res.data.regions || []
        
        const option = {
          title: { text: '地域分布', left: 'center', textStyle: { fontSize: 14 } },
          tooltip: { trigger: 'item' },
          series: [
            {
              type: 'pie',
              radius: '60%',
              data: data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
        
        this.regionChart.setOption(option)
      } catch (error) {
        console.error('获取地域数据失败:', error)
      }
    },

    async getBehaviorData() {
      try {
        const res = await getUserBehaviorAnalysis(30)
        const data = res.data
        
        const option = {
          title: { text: '用户行为', left: 'center', textStyle: { fontSize: 14 } },
          tooltip: { trigger: 'item' },
          radar: {
            indicator: [
              { name: '发布动态', max: 100 },
              { name: '签到活跃', max: 100 },
              { name: '点赞互动', max: 100 },
              { name: '评论参与', max: 100 },
              { name: '收藏钓点', max: 100 }
            ]
          },
          series: [
            {
              type: 'radar',
              data: [
                {
                  value: data.behaviorScore || [50, 50, 50, 50, 50],
                  name: '用户活跃度'
                }
              ]
            }
          ]
        }
        
        this.behaviorChart.setOption(option)
      } catch (error) {
        console.error('获取行为数据失败:', error)
      }
    },

    async refreshRealtime() {
      try {
        const res = await getRealtimeStatistics()
        const data = res.data
        
        this.realtimeData = [
          { label: '在线用户', value: data.onlineUsers || 0 },
          { label: '今日新增', value: data.todayNewUsers || 0 },
          { label: '今日活跃', value: data.todayActiveUsers || 0 },
          { label: '今日签到', value: data.todayCheckins || 0 }
        ]
      } catch (error) {
        console.error('获取实时数据失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.overview-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.overview-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.overview-icon i {
  font-size: 24px;
  color: white;
}

.overview-text {
  flex: 1;
}

.overview-title {
  font-size: 14px;
  color: #999;
  margin-bottom: 5px;
}

.overview-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.overview-change {
  font-size: 12px;
}

.overview-change.increase {
  color: #67C23A;
}

.overview-change.decrease {
  color: #F56C6C;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ranking-list {
  max-height: 260px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.ranking-index {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 10px;
  background-color: #f0f0f0;
  color: #666;
}

.ranking-index.rank-1 {
  background-color: #FFD700;
  color: white;
}

.ranking-index.rank-2 {
  background-color: #C0C0C0;
  color: white;
}

.ranking-index.rank-3 {
  background-color: #CD7F32;
  color: white;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-weight: bold;
  margin-bottom: 2px;
}

.ranking-value {
  font-size: 12px;
  color: #999;
}

.realtime-item {
  text-align: center;
  padding: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.realtime-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.realtime-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}
</style>