{"version": "2.0.0", "tasks": [{"label": "Flutter: Get Dependencies", "type": "shell", "command": "flutter", "args": ["pub", "get"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Clean", "type": "shell", "command": "flutter", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Run Web", "type": "shell", "command": "flutter", "args": ["run", "-d", "chrome"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build Web", "type": "shell", "command": "flutter", "args": ["build", "web"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Doctor", "type": "shell", "command": "flutter", "args": ["doctor"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Ana<PERSON>ze", "type": "shell", "command": "flutter", "args": ["analyze"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}