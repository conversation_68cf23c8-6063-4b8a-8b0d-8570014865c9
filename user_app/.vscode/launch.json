{"version": "0.2.0", "configurations": [{"name": "Flutter (Debug)", "type": "dart", "request": "launch", "program": "lib/main.dart", "args": ["--debug"], "console": "debugConsole"}, {"name": "Flutter (Profile)", "type": "dart", "request": "launch", "program": "lib/main.dart", "flutterMode": "profile"}, {"name": "Flutter (Release)", "type": "dart", "request": "launch", "program": "lib/main.dart", "flutterMode": "release"}, {"name": "Flutter Web (Debug)", "type": "dart", "request": "launch", "program": "lib/main.dart", "args": ["-d", "chrome", "--web-renderer", "html", "--web-port", "8080"], "console": "debugConsole", "env": {"FLUTTER_WEB_AUTO_DETECT": "true"}}, {"name": "Flutter Web (Profile)", "type": "dart", "request": "launch", "program": "lib/main.dart", "args": ["-d", "chrome", "--web-renderer", "html"], "flutterMode": "profile"}]}