{"dart.flutterSdkPath": null, "dart.debugExternalPackageLibraries": false, "dart.debugSdkLibraries": false, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.hotReloadOnSave": "always", "dart.openDevTools": "flutter", "dart.warnWhenEditingFilesOutsideWorkspace": true, "dart.analysisServerFolding": true, "dart.closingLabels": true, "dart.enableSnippets": true, "dart.insertArgumentPlaceholders": true, "dart.lineLength": 80, "dart.runPubGetOnPubspecChanges": "prompt", "dart.showTodos": true, "dart.updateImportsOnRename": true, "editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off", "files.associations": {"*.dart": "dart"}, "search.exclude": {"**/build/**": true, "**/.dart_tool/**": true, "**/.pub-cache/**": true}, "files.watcherExclude": {"**/build/**": true, "**/.dart_tool/**": true, "**/.pub-cache/**": true}}