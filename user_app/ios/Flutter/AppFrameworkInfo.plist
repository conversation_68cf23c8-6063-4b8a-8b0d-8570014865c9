<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>App</string>
	<key>CFBundleIdentifier</key>
	<string>io.flutter.flutter.app</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>App</string>
	<key>CFBundlePackageType</key>
	<string>FMWK</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1.0</string>
	<key>MinimumOSVersion</key>
	<string>12.0</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>上传图片功能需要访问您的相册。</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>地图功能需要您的定位服务，否则无法使用，如果您需要使用后台定位功能请选择“始终允许”。</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>地图功能需要您的定位服务，否则无法使用。</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>地图功能需要您的定位服务，否则无法使用。</string>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
</dict>
</plist>
