<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>user_app</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSAppleMusicUsageDescription</key>
		<string>Music!</string>
		<key>NSBluetoothAlwaysUsageDescription</key>
		<string>bluetooth</string>
		<key>NSBluetoothPeripheralUsageDescription</key>
		<string>bluetooth</string>
		<key>NSCalendarsFullAccessUsageDescription</key>
		<string>Calendar full access</string>
		<key>NSCalendarsUsageDescription</key>
		<string>Calendars</string>
		<key>NSCameraUsageDescription</key>
		<string>camera</string>
		<key>NSContactsUsageDescription</key>
		<string>contacts</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Always and when in use!</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>Can I have location always?</string>
		<key>NSLocationUsageDescription</key>
		<string>Older devices need location.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Need location when in use</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>microphone</string>
		<key>NSMotionUsageDescription</key>
		<string>motion</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>photos</string>
		<key>NSRemindersUsageDescription</key>
		<string>reminders</string>
		<key>NSSiriUsageDescription</key>
		<string>The example app would like access to Siri Kit to demonstrate requesting authorization.</string>
		<key>NSSpeechRecognitionUsageDescription</key>
		<string>speech</string>
		<key>NSUserTrackingUsageDescription</key>
		<string>appTrackingTransparency</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>kTCCServiceMediaLibrary</key>
		<string>media</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>需要访问位置信息以获取当前位置</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>需要访问位置信息以获取当前位置</string>
	</dict>
</plist>
