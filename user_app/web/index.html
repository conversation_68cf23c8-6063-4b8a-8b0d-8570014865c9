<!DOCTYPE html>
<html>
<head>
    <!--
      If you are serving your web app in a path other than the root, change the
      href value below to reflect the base path you are serving from.

      The path provided below has to start and end with a slash "/" in order for
      it to work correctly.

      For more details:
      * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

      This is a placeholder for base href that will be replaced by the value of
      the `--base-href` argument provided to `flutter build`.
    -->
    <base href="$FLUTTER_BASE_HREF">

    <meta charset="UTF-8">
    <meta content="IE=Edge" http-equiv="X-UA-Compatible">
    <meta content="A new Flutter project." name="description">

    <!-- iOS meta tags & icons -->
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="钓鱼之旅" name="apple-mobile-web-app-title">
    <link href="icons/Icon-192.png" rel="apple-touch-icon">

    <!-- Favicon -->
    <link href="favicon.png" rel="icon" type="image/png"/>

    <title>钓鱼之旅</title>
    <link href="manifest.json" rel="manifest">
</head>
<body>
<script type="text/javascript">
    window._AMapSecurityConfig = {
      securityJsCode: "313cbc623afe19945a8fe748820ece3e",
    };
</script>
<script src="https://webapi.amap.com/loader.js"></script>
<script src="./node_modules/tim-js-sdk/tim-js.js"></script>
<script src="./node_modules/tim-upload-plugin/index.js"></script>
<!--<script async src="flutter_bootstrap.js"></script>-->
<script>
    {{flutter_bootstrap_js}}
</script>
</body>
</html>
