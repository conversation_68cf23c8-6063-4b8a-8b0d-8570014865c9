!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).TIMUploadPlugin=t()}(this,(function(){function e(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function t(t){for(var o=1;o<arguments.length;o++){var n=null!=arguments[o]?arguments[o]:{};o%2?e(Object(n),!0).forEach((function(e){s(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):e(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function a(e,t,o){return t&&r(e.prototype,t),o&&r(e,o),Object.defineProperty(e,"prototype",{writable:!1}),e}function s(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}function i(e,t){if(null==e)return{};var o,n,r=function(e,t){if(null==e)return{};var o,n,r={},a=Object.keys(e);for(n=0;n<a.length;n++)o=a[n],t.indexOf(o)>=0||(r[o]=e[o]);return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)o=a[n],t.indexOf(o)>=0||Object.prototype.propertyIsEnumerable.call(e,o)&&(r[o]=e[o])}return r}var u="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},l="undefined"!=typeof wx&&"function"==typeof wx.getSystemInfoSync&&Boolean(wx.getSystemInfoSync().fontSizeSetting),f="undefined"!=typeof qq&&"function"==typeof qq.getSystemInfoSync&&Boolean(qq.getSystemInfoSync().fontSizeSetting),c="undefined"!=typeof tt&&"function"==typeof tt.getSystemInfoSync&&Boolean(tt.getSystemInfoSync().fontSizeSetting),d="undefined"!=typeof swan&&"function"==typeof swan.getSystemInfoSync&&Boolean(swan.getSystemInfoSync().fontSizeSetting),p="undefined"!=typeof my&&"function"==typeof my.getSystemInfoSync&&Boolean(my.getSystemInfoSync().fontSizeSetting),y="undefined"!=typeof uni&&"undefined"==typeof window&&"function"==typeof uni.requireNativePlugin,h=y&&"ios"===uni.getDeviceInfo().platform.toLocaleLowerCase(),g=(y&&uni.getDeviceInfo().platform.toLocaleLowerCase(),l||f||c||d||p||y),m=void 0!==u&&(void 0!==u.nativeModuleProxy||void 0!==u.ReactNative),v=f?qq:c?tt:d?swan:p?my:l?wx:y?uni:{},b=function(e){if("object"!==o(e)||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n};function w(e){if(null==e)return!0;if("boolean"==typeof e)return!1;if("number"==typeof e)return 0===e;if("string"==typeof e)return 0===e.length;if("function"==typeof e)return 0===e.length;if(Array.isArray(e))return 0===e.length;if(e instanceof Error)return""===e.message;if(b(e)){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))return!1;return!0}return!1}var O=function(){function e(){n(this,e),this._n="WebRequest"}return a(e,[{key:"request",value:function(e,o){var n=this,r="".concat(this._n,".request"),a=e.downloadUrl||"",s=(e.method||"PUT").toUpperCase(),i=e.url;if(console.log("%c tim-upload-plugin %c","background:#0abf5b; padding:1px; border-radius:3px; color: #fff","background:transparent","".concat(r," URL:").concat(i)),e.qs){var u=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"&",o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"=";return w(e)?"":b(e)?Object.keys(e).map((function(n){var r=encodeURIComponent(n)+o;return Array.isArray(e[n])?e[n].map((function(e){return r+encodeURIComponent(e)})).join(t):r+encodeURIComponent(e[n])})).filter(Boolean).join(t):void 0}(e.qs);u&&(i+="".concat(-1===i.indexOf("?")?"?":"&").concat(u))}var l=new XMLHttpRequest;l.open(s,i,!0),l.responseType=e.dataType||"text";var f=e.headers||{};if(e.uploadByIP&&(f=t(t({},f),{},{host:e.uploadIP})),!w(f))for(var c in f)f.hasOwnProperty(c)&&"content-length"!==c.toLowerCase()&&"user-agent"!==c.toLowerCase()&&"origin"!==c.toLowerCase()&&"host"!==c.toLowerCase()&&l.setRequestHeader(c,f[c]);return l.onload=function(){if(200===l.status)o(null,n._xhrRes(l,n._xhrBody(l,a,e.uploadByIP&&e.uploadIP),f));else{if(e.uploadIP&&-1===e.url.indexOf(e.uploadIP))return e.url=function(e,t){return e.replace(/^http(s)?:\/\/(.*?)\//,"https://".concat(t,"/"))}(e.url,e.uploadIP),e.uploadByIP=!0,n.request(e,o);var t={code:l.status,message:JSON.stringify(l.responseText)};o(t,n._xhrRes(l,n._xhrBody(l,a,e.uploadByIP&&e.uploadIP),f))}},l.onerror=function(t){var r=n._xhrBody(l,a,e.uploadByIP&&e.uploadIP),s={code:l.status,message:JSON.stringify(l.responseText)};r||l.statusText||0!==l.status||(t.message="CORS blocked or network error"),o(s,n._xhrRes(l,r)),s=null},e.onProgress&&l.upload&&(l.upload.onprogress=function(t){var o=t.total,n=t.loaded,r=Math.floor(100*n/o);e.onProgress({total:o,loaded:n,percent:(r>=100?100:r)/100})}),l.send(e.resources),l}},{key:"_xhrRes",value:function(e,t){var o={};return e.getAllResponseHeaders().trim().split("\n").forEach((function(e){if(e){var t=e.indexOf(":"),n=e.substr(0,t).trim().toLowerCase(),r=e.substr(t+1).trim();o[n]=r}})),{statusCode:e.status,statusMessage:e.statusText,headers:o,data:t}}},{key:"_xhrBody",value:function(e,t,o){return 200===e.status&&t?{location:t,uploadIP:o}:{response:e.responseText,uploadIP:o}}}]),e}(),S=["unknown","image","video","audio","log"],P=["name"],I=function(){function e(){n(this,e)}return a(e,[{key:"request",value:function(e,o){var n=this,r=e.resources,a=void 0===r?"":r,s=e.headers,u=void 0===s?{}:s,l=e.url,f=e.downloadUrl,c=void 0===f?"":f,d=l,y=null,g=c.match(/^(https?:\/\/[^/]+\/)([^/]*\/?)(.*)$/),m=decodeURIComponent(g[3]),b=m.indexOf("?")>-1?m.split("?")[0]:m,w={key:e.fileKey?e.fileKey:b,success_action_status:200,"Content-Type":""},O={};if(h){var I=l.split("?sign=");if(I.length>1){var C=I[1];d="".concat(I[0],"?sign=").concat(encodeURIComponent("".concat(C))),O.sign=decodeURIComponent(C),O.signature=decodeURIComponent(C)}}var k={url:d,header:u,name:"file",filePath:a,formData:t(t({},w),O),timeout:e.timeout||3e5};if(p){var x=k;x.name;k=t(t({},i(x,P)),{},{fileName:"file",fileType:S[e.fileType]})}return(y=v.uploadFile(t(t({},k),{},{success:function(e){n._handleResponse({response:e,downloadUrl:c,callback:o})},fail:function(e){n._handleResponse({response:e,downloadUrl:c,callback:o})}}))).onProgressUpdate&&y.onProgressUpdate((function(t){e.onProgress&&e.onProgress({total:t.totalBytesExpectedToSend,loaded:t.totalBytesSent,percent:Math.floor(t.progress)/100})})),y}},{key:"_handleResponse",value:function(e){var o=e.downloadUrl,n=e.response,r=e.callback,a=n.header,s={};if(a)for(var i in a)a.hasOwnProperty(i)&&(s[i.toLowerCase()]=a[i]);var u=+n.statusCode;200===u?r(null,{statusCode:u,headers:s,data:t(t({},n.data),{},{location:o})}):r({code:u,message:JSON.stringify(n.data)},{statusCode:u,headers:s,data:void 0})}}]),e}(),C=function(){function e(){n(this,e)}return a(e,[{key:"request",value:function(e,t){var o=this,n=e.resources,r=void 0===n?"":n,a=e.fileKey,s=void 0===a?"":a,i=e.url,u=e.downloadUrl,l=void 0===u?"":u,f=new FormData;f.append("key",s),f.append("success_action_status",200),f.append("file",{uri:r,type:"application/octet-stream",name:"uploaded_file"}),fetch(i,{method:"POST",headers:{"Content-Type":"multipart/form-data"},body:f}).then((function(e){o._handleResponse({response:e,downloadUrl:l,callback:t})})).catch((function(e){o._handleResponse({response:e,downloadUrl:l,callback:t})}))}},{key:"_handleResponse",value:function(e){var t=e.downloadUrl,o=e.response,n=e.callback,r=o.headers,a=o.status,s=r&&r.map||{};200===a?n(null,{statusCode:200,headers:s,data:{location:t}}):n({code:a,message:JSON.stringify(o)},{statusCode:a,headers:s,data:void 0})}}]),e}();return function(){function e(){n(this,e),this.retry=1,this.tryCount=0,this.systemClockOffset=0,this.httpRequest=g?new I:m?new C:new O,console.log("TIMUploadPlugin.VERSION: ".concat("1.4.2"))}return a(e,[{key:"uploadFile",value:function(e,t){var o=this;return this.httpRequest.request(e,(function(n,r){n&&o.tryCount<o.retry&&o.allowRetry(n)?(o.tryCount++,o.uploadFile(e,t)):(o.tryCount=0,t(n,r))}))}},{key:"allowRetry",value:function(e){var t=!1,o=!1;if(e){var n=e.headers&&(e.headers.date||e.headers.Date)||e.error&&e.error.ServerTime;try{var r=e.error&&e.error.Code,a=e.error&&e.error.Message;("RequestTimeTooSkewed"===r||"AccessDenied"===r&&"Request has expired"===a)&&(o=!0)}catch(u){}if(o&&n){var s=Date.now(),i=Date.parse(n);Math.abs(s+this.systemClockOffset-i)>=3e4&&(this.systemClockOffset=i-s,t=!0)}else 5===Math.floor(e.statusCode/100)&&(t=!0)}return t}}],[{key:"getVersion",value:function(){return"1.4.2"}}]),e}()}));
