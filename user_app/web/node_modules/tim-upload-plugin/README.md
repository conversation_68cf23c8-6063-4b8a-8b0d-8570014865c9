### Introduction

Tencent Cloud Chat provides globally interconnected chat APIs, multi-platform SDKs, and UIKit components to help you quickly bring messaging capabilities such as one-to-one chat, group chat, chat rooms, and system notifications to your applications and websites.

Tencent Cloud Chat upload plugin is used to upload messages of types such as images, voice, video, and files. It supports platforms including Web, ReactNative, WeChat, QQ, Baidu, Toutiao, and Alipay Mini Programs.

### Advantages

Using tim-upload-plugin has the following 2 advantages:

- More secure application data
- Very small size, only 9KB, which is more friendly to Mini Program applications

### How to Use

Use npm to install the corresponding Chat SDK dependencies in your project.
```javascript
npm install @tencentcloud/chat --save
npm install tim-upload-plugin --save
```

```javascript
import TencentCloudChat from '@tencentcloud/chat';
import TIMUploadPlugin from 'tim-upload-plugin';

let options = {
  SDKAppID: 0 // Replace 0 with the SDKAppID of your Chat application when connecting.
};
// Create an SDK instance.
// The `TencentCloudChat.create()` method returns the same instance for the same `SDKAppID`.
// The SDK instance is usually represented by chat.
let chat = TencentCloudChat.create(options);

// Set the SDK log level.
// 0: Common level. You are advised to use this level during access as it covers more logs.
// 1: Release level. You are advised to use this level for key information in a production environment.
chat.setLogLevel(0);
// chat.setLogLevel(1);

// Register the Tencent Cloud Chat upload plugin.
chat.registerPlugin({'tim-upload-plugin': TIMUploadPlugin});
```
