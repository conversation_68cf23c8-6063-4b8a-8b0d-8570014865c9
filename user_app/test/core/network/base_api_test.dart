import 'package:flutter_test/flutter_test.dart';
import 'package:user_app/core/network/api_error.dart';

void main() {
  group('BaseApi Integration Tests', () {
    test('ApiError should be created correctly', () {
      final error = ApiError(code: 404, message: 'Not Found');

      expect(error.code, equals(404));
      expect(error.message, equals('Not Found'));
      expect(error.toString(), contains('404'));
      expect(error.toString(), contains('Not Found'));
    });

    test('ApiError should handle different error codes', () {
      final errors = [
        ApiError(code: 200, message: 'Success'),
        ApiError(code: 400, message: 'Bad Request'),
        ApiError(code: 401, message: 'Unauthorized'),
        ApiError(code: 403, message: 'Forbidden'),
        ApiError(code: 404, message: 'Not Found'),
        ApiError(code: 500, message: 'Internal Server Error'),
      ];

      for (final error in errors) {
        expect(error.code, isA<int>());
        expect(error.message, isA<String>());
        expect(error.message.isNotEmpty, isTrue);
      }
    });

    test('ApiError should be throwable', () {
      expect(
        () => throw ApiError(code: 500, message: 'Test error'),
        throwsA(isA<ApiError>()
            .having((e) => e.code, 'code', 500)
            .having((e) => e.message, 'message', 'Test error')),
      );
    });
  });
}
