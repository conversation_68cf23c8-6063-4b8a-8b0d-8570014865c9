import 'package:flutter_test/flutter_test.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_detail_vo.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_page_vo.dart';

void main() {
  group('FishingSpot VO Tests', () {
    test('SpotSummaryVo should parse correctly from backend data', () {
      // 模拟后端SpotSummaryVO的数据结构
      final Map<String, dynamic> backendData = {
        'id': 1,
        'name': '测试钓点',
        'address': '测试地址',
        'latitude': 39.9042,
        'longitude': 116.4074,
        'province': '北京市',
        'city': '北京市',
        'county': '朝阳区',
        'rating': 4.5,
        'is_official': true,
        'is_paid': false,
        'has_facilities': true,
        'verification_level': 2,
        'visitor_count': 100,
        'checkin_count': 50,
        'recent_moments_count': 10,
        'main_image': 'https://example.com/image.jpg',
        'price_text': '免费',
        'fish_type_names': ['鲤鱼', '草鱼', '鲫鱼'],
        'latest_moment': null,
      };

      // 测试SpotSummaryVo解析
      final spotSummary = SpotSummaryVo.fromMap(backendData);

      expect(spotSummary.id, equals(1));
      expect(spotSummary.name, equals('测试钓点'));
      expect(spotSummary.official, equals(true));
      expect(spotSummary.paid, equals(false));
      expect(spotSummary.mainImage, equals('https://example.com/image.jpg'));
      expect(spotSummary.priceText, equals('免费'));
      expect(spotSummary.fishTypeNames, equals(['鲤鱼', '草鱼', '鲫鱼']));

      // 测试转换为FishingSpotVo
      final fishingSpotVo = spotSummary.toFishingSpotVo();
      expect(fishingSpotVo.id, equals(1));
      expect(fishingSpotVo.name, equals('测试钓点'));
      expect(fishingSpotVo.images, equals(['https://example.com/image.jpg']));
      expect(fishingSpotVo.price, equals('免费'));
    });

    test('SpotDetailVo should parse correctly from backend data', () {
      // 模拟后端SpotDetailVO的数据结构
      final Map<String, dynamic> backendData = {
        'id': 1,
        'name': '测试钓点详情',
        'address': '测试详细地址',
        'latitude': 39.9042,
        'longitude': 116.4074,
        'province': '北京市',
        'city': '北京市',
        'county': '朝阳区',
        'is_official': true,
        'verification_level': 2,
        'description': '这是一个测试钓点的详细描述',
        'visitor_count': 100,
        'rating': 4.5,
        'has_facilities': true,
        'is_paid': false,
        'checkin_count': 50,
        'extra_fish_types': '黑鱼,鳜鱼',
        'extra_facilities': '停车场,餐厅',
        'created_by': 123,
        'status': 1,
        'source': 'user',
        'visibility': 'public',
        'fish_type_list': [],
        'images': [
          'https://example.com/image1.jpg',
          'https://example.com/image2.jpg'
        ],
        'certification_documents': [],
        'prices': [],
        'facilities': [],
        'recent_moments_count': 10,
        'latest_moment': null,
        'creator': null,
      };

      // 测试SpotDetailVo解析
      final spotDetail = SpotDetailVo.fromMap(backendData);

      expect(spotDetail.id, equals(1));
      expect(spotDetail.name, equals('测试钓点详情'));
      expect(spotDetail.description, equals('这是一个测试钓点的详细描述'));
      expect(spotDetail.extraFishTypes, equals('黑鱼,鳜鱼'));
      expect(spotDetail.extraFacilities, equals('停车场,餐厅'));
      expect(
          spotDetail.images,
          equals([
            'https://example.com/image1.jpg',
            'https://example.com/image2.jpg'
          ]));

      // 测试解析方法
      expect(spotDetail.getExtraFishTypesList(), equals(['黑鱼', '鳜鱼']));
      expect(spotDetail.getExtraFacilitiesList(), equals(['停车场', '餐厅']));

      // 测试转换为FishingSpotVo
      final fishingSpotVo = spotDetail.toFishingSpotVo();
      expect(fishingSpotVo.id, equals(1));
      expect(fishingSpotVo.name, equals('测试钓点详情'));
      expect(fishingSpotVo.description, equals('这是一个测试钓点的详细描述'));
      expect(fishingSpotVo.extraFishTypesList, equals(['黑鱼', '鳜鱼']));
      expect(fishingSpotVo.extraFacilitiesList, equals(['停车场', '餐厅']));
    });

    test('SpotSummaryPageVo should convert to FishingSpotPageVo correctly', () {
      // 创建测试数据
      final spotSummary = SpotSummaryVo(
        id: 1,
        name: '测试钓点',
        address: '测试地址',
        latitude: 39.9042,
        longitude: 116.4074,
        province: '北京市',
        city: '北京市',
        county: '朝阳区',
        rating: 4.5,
        official: true,
        paid: false,
        hasFacilities: true,
        verificationLevel: 2,
        visitorCount: 100,
        checkinCount: 50,
        recentMomentsCount: 10,
        mainImage: 'https://example.com/image.jpg',
        priceText: '免费',
        fishTypeNames: ['鲤鱼', '草鱼'],
      );

      final summaryPageVo = SpotSummaryPageVo(
        content: [spotSummary],
        totalPages: 1,
        totalElements: 1,
        size: 10,
        number: 0,
        first: true,
        last: true,
      );

      // 测试转换
      final fishingSpotPageVo = summaryPageVo.toFishingSpotPageVo();

      expect(fishingSpotPageVo.content.length, equals(1));
      expect(fishingSpotPageVo.content.first.id, equals(1));
      expect(fishingSpotPageVo.content.first.name, equals('测试钓点'));
      expect(fishingSpotPageVo.totalPages, equals(1));
      expect(fishingSpotPageVo.totalElements, equals(1));
      expect(fishingSpotPageVo.first, equals(true));
      expect(fishingSpotPageVo.last, equals(true));
    });
  });
}
