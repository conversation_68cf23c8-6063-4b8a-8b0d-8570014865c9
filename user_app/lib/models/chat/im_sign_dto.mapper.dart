// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'im_sign_dto.dart';

class ImSignDtoMapper extends ClassMapperBase<ImSignDto> {
  ImSignDtoMapper._();

  static ImSignDtoMapper? _instance;
  static ImSignDtoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = ImSignDtoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'ImSignDto';

  static int _$appId(ImSignDto v) => v.appId;
  static const Field<ImSignDto, int> _f$appId =
      Field('appId', _$appId, key: r'app_id');
  static String _$userId(ImSignDto v) => v.userId;
  static const Field<ImSignDto, String> _f$userId =
      Field('userId', _$userId, key: r'user_id');
  static String _$sign(ImSignDto v) => v.sign;
  static const Field<ImSignDto, String> _f$sign = Field('sign', _$sign);

  @override
  final MappableFields<ImSignDto> fields = const {
    #appId: _f$appId,
    #userId: _f$userId,
    #sign: _f$sign,
  };

  static ImSignDto _instantiate(DecodingData data) {
    return ImSignDto(
        appId: data.dec(_f$appId),
        userId: data.dec(_f$userId),
        sign: data.dec(_f$sign));
  }

  @override
  final Function instantiate = _instantiate;

  static ImSignDto fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<ImSignDto>(map);
  }

  static ImSignDto fromJson(String json) {
    return ensureInitialized().decodeJson<ImSignDto>(json);
  }
}

mixin ImSignDtoMappable {
  String toJson() {
    return ImSignDtoMapper.ensureInitialized()
        .encodeJson<ImSignDto>(this as ImSignDto);
  }

  Map<String, dynamic> toMap() {
    return ImSignDtoMapper.ensureInitialized()
        .encodeMap<ImSignDto>(this as ImSignDto);
  }

  ImSignDtoCopyWith<ImSignDto, ImSignDto, ImSignDto> get copyWith =>
      _ImSignDtoCopyWithImpl<ImSignDto, ImSignDto>(
          this as ImSignDto, $identity, $identity);
  @override
  String toString() {
    return ImSignDtoMapper.ensureInitialized()
        .stringifyValue(this as ImSignDto);
  }

  @override
  bool operator ==(Object other) {
    return ImSignDtoMapper.ensureInitialized()
        .equalsValue(this as ImSignDto, other);
  }

  @override
  int get hashCode {
    return ImSignDtoMapper.ensureInitialized().hashValue(this as ImSignDto);
  }
}

extension ImSignDtoValueCopy<$R, $Out> on ObjectCopyWith<$R, ImSignDto, $Out> {
  ImSignDtoCopyWith<$R, ImSignDto, $Out> get $asImSignDto =>
      $base.as((v, t, t2) => _ImSignDtoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class ImSignDtoCopyWith<$R, $In extends ImSignDto, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call({int? appId, String? userId, String? sign});
  ImSignDtoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _ImSignDtoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, ImSignDto, $Out>
    implements ImSignDtoCopyWith<$R, ImSignDto, $Out> {
  _ImSignDtoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<ImSignDto> $mapper =
      ImSignDtoMapper.ensureInitialized();
  @override
  $R call({int? appId, String? userId, String? sign}) =>
      $apply(FieldCopyWithData({
        if (appId != null) #appId: appId,
        if (userId != null) #userId: userId,
        if (sign != null) #sign: sign
      }));
  @override
  ImSignDto $make(CopyWithData data) => ImSignDto(
      appId: data.get(#appId, or: $value.appId),
      userId: data.get(#userId, or: $value.userId),
      sign: data.get(#sign, or: $value.sign));

  @override
  ImSignDtoCopyWith<$R2, ImSignDto, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _ImSignDtoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
