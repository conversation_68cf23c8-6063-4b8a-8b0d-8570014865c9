// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'user.dart';

class UserMapper extends ClassMapperBase<User> {
  UserMapper._();

  static UserMapper? _instance;
  static UserMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = UserMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'User';

  static int _$id(User v) => v.id;
  static const Field<User, int> _f$id = Field('id', _$id);
  static String _$telephoneNumber(User v) => v.telephoneNumber;
  static const Field<User, String> _f$telephoneNumber =
      Field('telephoneNumber', _$telephoneNumber, key: r'telephone_number');
  static String? _$password(User v) => v.password;
  static const Field<User, String> _f$password =
      Field('password', _$password, opt: true);
  static String _$title(User v) => v.title;
  static const Field<User, String> _f$title = Field('title', _$title);
  static String _$name(User v) => v.name;
  static const Field<User, String> _f$name = Field('name', _$name);
  static int _$gender(User v) => v.gender;
  static const Field<User, int> _f$gender = Field('gender', _$gender);
  static String? _$avatarUrl(User v) => v.avatarUrl;
  static const Field<User, String> _f$avatarUrl =
      Field('avatarUrl', _$avatarUrl, key: r'avatar_url', opt: true);
  static String? _$username(User v) => v.username;
  static const Field<User, String> _f$username =
      Field('username', _$username, opt: true);
  static String? _$province(User v) => v.province;
  static const Field<User, String> _f$province =
      Field('province', _$province, opt: true);
  static String? _$city(User v) => v.city;
  static const Field<User, String> _f$city = Field('city', _$city, opt: true);
  static String? _$county(User v) => v.county;
  static const Field<User, String> _f$county =
      Field('county', _$county, opt: true);
  static String? _$introduce(User v) => v.introduce;
  static const Field<User, String> _f$introduce =
      Field('introduce', _$introduce, opt: true);
  static int? _$followCount(User v) => v.followCount;
  static const Field<User, int> _f$followCount =
      Field('followCount', _$followCount, key: r'follow_count', opt: true);
  static int? _$attentionCount(User v) => v.attentionCount;
  static const Field<User, int> _f$attentionCount = Field(
      'attentionCount', _$attentionCount,
      key: r'attention_count', opt: true);
  static int? _$momentCount(User v) => v.momentCount;
  static const Field<User, int> _f$momentCount =
      Field('momentCount', _$momentCount, key: r'moment_count', opt: true);
  static int? _$commentCount(User v) => v.commentCount;
  static const Field<User, int> _f$commentCount =
      Field('commentCount', _$commentCount, key: r'comment_count', opt: true);
  static bool? _$isFollowing(User v) => v.isFollowing;
  static const Field<User, bool> _f$isFollowing =
      Field('isFollowing', _$isFollowing, key: r'is_following', opt: true);

  @override
  final MappableFields<User> fields = const {
    #id: _f$id,
    #telephoneNumber: _f$telephoneNumber,
    #password: _f$password,
    #title: _f$title,
    #name: _f$name,
    #gender: _f$gender,
    #avatarUrl: _f$avatarUrl,
    #username: _f$username,
    #province: _f$province,
    #city: _f$city,
    #county: _f$county,
    #introduce: _f$introduce,
    #followCount: _f$followCount,
    #attentionCount: _f$attentionCount,
    #momentCount: _f$momentCount,
    #commentCount: _f$commentCount,
    #isFollowing: _f$isFollowing,
  };

  static User _instantiate(DecodingData data) {
    return User(
        id: data.dec(_f$id),
        telephoneNumber: data.dec(_f$telephoneNumber),
        password: data.dec(_f$password),
        title: data.dec(_f$title),
        name: data.dec(_f$name),
        gender: data.dec(_f$gender),
        avatarUrl: data.dec(_f$avatarUrl),
        username: data.dec(_f$username),
        province: data.dec(_f$province),
        city: data.dec(_f$city),
        county: data.dec(_f$county),
        introduce: data.dec(_f$introduce),
        followCount: data.dec(_f$followCount),
        attentionCount: data.dec(_f$attentionCount),
        momentCount: data.dec(_f$momentCount),
        commentCount: data.dec(_f$commentCount),
        isFollowing: data.dec(_f$isFollowing));
  }

  @override
  final Function instantiate = _instantiate;

  static User fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<User>(map);
  }

  static User fromJson(String json) {
    return ensureInitialized().decodeJson<User>(json);
  }
}

mixin UserMappable {
  String toJson() {
    return UserMapper.ensureInitialized().encodeJson<User>(this as User);
  }

  Map<String, dynamic> toMap() {
    return UserMapper.ensureInitialized().encodeMap<User>(this as User);
  }

  UserCopyWith<User, User, User> get copyWith =>
      _UserCopyWithImpl<User, User>(this as User, $identity, $identity);
  @override
  String toString() {
    return UserMapper.ensureInitialized().stringifyValue(this as User);
  }

  @override
  bool operator ==(Object other) {
    return UserMapper.ensureInitialized().equalsValue(this as User, other);
  }

  @override
  int get hashCode {
    return UserMapper.ensureInitialized().hashValue(this as User);
  }
}

extension UserValueCopy<$R, $Out> on ObjectCopyWith<$R, User, $Out> {
  UserCopyWith<$R, User, $Out> get $asUser =>
      $base.as((v, t, t2) => _UserCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class UserCopyWith<$R, $In extends User, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? id,
      String? telephoneNumber,
      String? password,
      String? title,
      String? name,
      int? gender,
      String? avatarUrl,
      String? username,
      String? province,
      String? city,
      String? county,
      String? introduce,
      int? followCount,
      int? attentionCount,
      int? momentCount,
      int? commentCount,
      bool? isFollowing});
  UserCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _UserCopyWithImpl<$R, $Out> extends ClassCopyWithBase<$R, User, $Out>
    implements UserCopyWith<$R, User, $Out> {
  _UserCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<User> $mapper = UserMapper.ensureInitialized();
  @override
  $R call(
          {int? id,
          String? telephoneNumber,
          Object? password = $none,
          String? title,
          String? name,
          int? gender,
          Object? avatarUrl = $none,
          Object? username = $none,
          Object? province = $none,
          Object? city = $none,
          Object? county = $none,
          Object? introduce = $none,
          Object? followCount = $none,
          Object? attentionCount = $none,
          Object? momentCount = $none,
          Object? commentCount = $none,
          Object? isFollowing = $none}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (telephoneNumber != null) #telephoneNumber: telephoneNumber,
        if (password != $none) #password: password,
        if (title != null) #title: title,
        if (name != null) #name: name,
        if (gender != null) #gender: gender,
        if (avatarUrl != $none) #avatarUrl: avatarUrl,
        if (username != $none) #username: username,
        if (province != $none) #province: province,
        if (city != $none) #city: city,
        if (county != $none) #county: county,
        if (introduce != $none) #introduce: introduce,
        if (followCount != $none) #followCount: followCount,
        if (attentionCount != $none) #attentionCount: attentionCount,
        if (momentCount != $none) #momentCount: momentCount,
        if (commentCount != $none) #commentCount: commentCount,
        if (isFollowing != $none) #isFollowing: isFollowing
      }));
  @override
  User $make(CopyWithData data) => User(
      id: data.get(#id, or: $value.id),
      telephoneNumber: data.get(#telephoneNumber, or: $value.telephoneNumber),
      password: data.get(#password, or: $value.password),
      title: data.get(#title, or: $value.title),
      name: data.get(#name, or: $value.name),
      gender: data.get(#gender, or: $value.gender),
      avatarUrl: data.get(#avatarUrl, or: $value.avatarUrl),
      username: data.get(#username, or: $value.username),
      province: data.get(#province, or: $value.province),
      city: data.get(#city, or: $value.city),
      county: data.get(#county, or: $value.county),
      introduce: data.get(#introduce, or: $value.introduce),
      followCount: data.get(#followCount, or: $value.followCount),
      attentionCount: data.get(#attentionCount, or: $value.attentionCount),
      momentCount: data.get(#momentCount, or: $value.momentCount),
      commentCount: data.get(#commentCount, or: $value.commentCount),
      isFollowing: data.get(#isFollowing, or: $value.isFollowing));

  @override
  UserCopyWith<$R2, User, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
      _UserCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
