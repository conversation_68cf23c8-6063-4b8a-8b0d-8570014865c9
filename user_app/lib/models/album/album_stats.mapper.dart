// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'album_stats.dart';

class AlbumStatsMapper extends ClassMapperBase<AlbumStats> {
  AlbumStatsMapper._();

  static AlbumStatsMapper? _instance;
  static AlbumStatsMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = AlbumStatsMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'AlbumStats';

  static int _$totalCount(AlbumStats v) => v.totalCount;
  static const Field<AlbumStats, int> _f$totalCount =
      Field('totalCount', _$totalCount, key: r'total_count');
  static int _$totalSize(AlbumStats v) => v.totalSize;
  static const Field<AlbumStats, int> _f$totalSize =
      Field('totalSize', _$totalSize, key: r'total_size');
  static int _$publicCount(AlbumStats v) => v.publicCount;
  static const Field<AlbumStats, int> _f$publicCount =
      Field('publicCount', _$publicCount, key: r'public_count');
  static int _$privateCount(AlbumStats v) => v.privateCount;
  static const Field<AlbumStats, int> _f$privateCount =
      Field('privateCount', _$privateCount, key: r'private_count');

  @override
  final MappableFields<AlbumStats> fields = const {
    #totalCount: _f$totalCount,
    #totalSize: _f$totalSize,
    #publicCount: _f$publicCount,
    #privateCount: _f$privateCount,
  };

  static AlbumStats _instantiate(DecodingData data) {
    return AlbumStats(
        totalCount: data.dec(_f$totalCount),
        totalSize: data.dec(_f$totalSize),
        publicCount: data.dec(_f$publicCount),
        privateCount: data.dec(_f$privateCount));
  }

  @override
  final Function instantiate = _instantiate;

  static AlbumStats fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<AlbumStats>(map);
  }

  static AlbumStats fromJson(String json) {
    return ensureInitialized().decodeJson<AlbumStats>(json);
  }
}

mixin AlbumStatsMappable {
  String toJson() {
    return AlbumStatsMapper.ensureInitialized()
        .encodeJson<AlbumStats>(this as AlbumStats);
  }

  Map<String, dynamic> toMap() {
    return AlbumStatsMapper.ensureInitialized()
        .encodeMap<AlbumStats>(this as AlbumStats);
  }

  AlbumStatsCopyWith<AlbumStats, AlbumStats, AlbumStats> get copyWith =>
      _AlbumStatsCopyWithImpl<AlbumStats, AlbumStats>(
          this as AlbumStats, $identity, $identity);
  @override
  String toString() {
    return AlbumStatsMapper.ensureInitialized()
        .stringifyValue(this as AlbumStats);
  }

  @override
  bool operator ==(Object other) {
    return AlbumStatsMapper.ensureInitialized()
        .equalsValue(this as AlbumStats, other);
  }

  @override
  int get hashCode {
    return AlbumStatsMapper.ensureInitialized().hashValue(this as AlbumStats);
  }
}

extension AlbumStatsValueCopy<$R, $Out>
    on ObjectCopyWith<$R, AlbumStats, $Out> {
  AlbumStatsCopyWith<$R, AlbumStats, $Out> get $asAlbumStats =>
      $base.as((v, t, t2) => _AlbumStatsCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class AlbumStatsCopyWith<$R, $In extends AlbumStats, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? totalCount, int? totalSize, int? publicCount, int? privateCount});
  AlbumStatsCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _AlbumStatsCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, AlbumStats, $Out>
    implements AlbumStatsCopyWith<$R, AlbumStats, $Out> {
  _AlbumStatsCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<AlbumStats> $mapper =
      AlbumStatsMapper.ensureInitialized();
  @override
  $R call(
          {int? totalCount,
          int? totalSize,
          int? publicCount,
          int? privateCount}) =>
      $apply(FieldCopyWithData({
        if (totalCount != null) #totalCount: totalCount,
        if (totalSize != null) #totalSize: totalSize,
        if (publicCount != null) #publicCount: publicCount,
        if (privateCount != null) #privateCount: privateCount
      }));
  @override
  AlbumStats $make(CopyWithData data) => AlbumStats(
      totalCount: data.get(#totalCount, or: $value.totalCount),
      totalSize: data.get(#totalSize, or: $value.totalSize),
      publicCount: data.get(#publicCount, or: $value.publicCount),
      privateCount: data.get(#privateCount, or: $value.privateCount));

  @override
  AlbumStatsCopyWith<$R2, AlbumStats, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _AlbumStatsCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
