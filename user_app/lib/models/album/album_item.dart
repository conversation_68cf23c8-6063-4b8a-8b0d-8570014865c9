import 'package:dart_mappable/dart_mappable.dart';

part 'album_item.mapper.dart';

@MappableClass()
class AlbumItem with AlbumItemMappable {
  final int id;
  final String imageUrl;
  final String? description;
  final String? tags;
  final int? fileSize;
  final int? width;
  final int? height;
  final String? imageType;
  final bool isPublic;
  final DateTime createTime;
  final DateTime updateTime;

  const AlbumItem({
    required this.id,
    required this.imageUrl,
    this.description,
    this.tags,
    this.fileSize,
    this.width,
    this.height,
    this.imageType,
    required this.isPublic,
    required this.createTime,
    required this.updateTime,
  });

  static const fromMap = AlbumItemMapper.fromMap;
  static const fromJson = AlbumItemMapper.fromJson;

  /// 获取标签列表
  List<String> getTagList() {
    if (tags == null || tags!.isEmpty) return [];
    return tags!.split(',').map((tag) => tag.trim()).where((tag) => tag.isNotEmpty).toList();
  }

  /// 获取格式化的文件大小
  String getFormattedFileSize() {
    if (fileSize == null) return '';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    double size = fileSize!.toDouble();
    int unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }

  /// 获取图片尺寸字符串
  String getDimensions() {
    if (width == null || height == null) return '';
    return '${width}x$height';
  }
}