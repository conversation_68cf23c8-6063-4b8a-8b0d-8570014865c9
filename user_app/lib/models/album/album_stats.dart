import 'package:dart_mappable/dart_mappable.dart';

part 'album_stats.mapper.dart';

@MappableClass()
class AlbumStats with AlbumStatsMappable {
  final int totalCount;
  final int totalSize;
  final int publicCount;
  final int privateCount;

  const AlbumStats({
    required this.totalCount,
    required this.totalSize,
    required this.publicCount,
    required this.privateCount,
  });

  static const fromMap = AlbumStatsMapper.fromMap;
  static const fromJson = AlbumStatsMapper.fromJson;

  /// 获取格式化的总大小
  String getFormattedTotalSize() {
    const units = ['B', 'KB', 'MB', 'GB'];
    double size = totalSize.toDouble();
    int unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }

  /// 获取公开图片比例
  double getPublicRatio() {
    if (totalCount == 0) return 0.0;
    return publicCount / totalCount;
  }

  /// 获取私密图片比例
  double getPrivateRatio() {
    if (totalCount == 0) return 0.0;
    return privateCount / totalCount;
  }
}