// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'album_item.dart';

class AlbumItemMapper extends ClassMapperBase<AlbumItem> {
  AlbumItemMapper._();

  static AlbumItemMapper? _instance;
  static AlbumItemMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = AlbumItemMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'AlbumItem';

  static int _$id(AlbumItem v) => v.id;
  static const Field<AlbumItem, int> _f$id = Field('id', _$id);
  static String _$imageUrl(AlbumItem v) => v.imageUrl;
  static const Field<AlbumItem, String> _f$imageUrl =
      Field('imageUrl', _$imageUrl, key: r'image_url');
  static String? _$description(AlbumItem v) => v.description;
  static const Field<AlbumItem, String> _f$description =
      Field('description', _$description, opt: true);
  static String? _$tags(AlbumItem v) => v.tags;
  static const Field<AlbumItem, String> _f$tags =
      Field('tags', _$tags, opt: true);
  static int? _$fileSize(AlbumItem v) => v.fileSize;
  static const Field<AlbumItem, int> _f$fileSize =
      Field('fileSize', _$fileSize, key: r'file_size', opt: true);
  static int? _$width(AlbumItem v) => v.width;
  static const Field<AlbumItem, int> _f$width =
      Field('width', _$width, opt: true);
  static int? _$height(AlbumItem v) => v.height;
  static const Field<AlbumItem, int> _f$height =
      Field('height', _$height, opt: true);
  static String? _$imageType(AlbumItem v) => v.imageType;
  static const Field<AlbumItem, String> _f$imageType =
      Field('imageType', _$imageType, key: r'image_type', opt: true);
  static bool _$isPublic(AlbumItem v) => v.isPublic;
  static const Field<AlbumItem, bool> _f$isPublic =
      Field('isPublic', _$isPublic, key: r'is_public');
  static DateTime _$createTime(AlbumItem v) => v.createTime;
  static const Field<AlbumItem, DateTime> _f$createTime =
      Field('createTime', _$createTime, key: r'create_time');
  static DateTime _$updateTime(AlbumItem v) => v.updateTime;
  static const Field<AlbumItem, DateTime> _f$updateTime =
      Field('updateTime', _$updateTime, key: r'update_time');

  @override
  final MappableFields<AlbumItem> fields = const {
    #id: _f$id,
    #imageUrl: _f$imageUrl,
    #description: _f$description,
    #tags: _f$tags,
    #fileSize: _f$fileSize,
    #width: _f$width,
    #height: _f$height,
    #imageType: _f$imageType,
    #isPublic: _f$isPublic,
    #createTime: _f$createTime,
    #updateTime: _f$updateTime,
  };

  static AlbumItem _instantiate(DecodingData data) {
    return AlbumItem(
        id: data.dec(_f$id),
        imageUrl: data.dec(_f$imageUrl),
        description: data.dec(_f$description),
        tags: data.dec(_f$tags),
        fileSize: data.dec(_f$fileSize),
        width: data.dec(_f$width),
        height: data.dec(_f$height),
        imageType: data.dec(_f$imageType),
        isPublic: data.dec(_f$isPublic),
        createTime: data.dec(_f$createTime),
        updateTime: data.dec(_f$updateTime));
  }

  @override
  final Function instantiate = _instantiate;

  static AlbumItem fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<AlbumItem>(map);
  }

  static AlbumItem fromJson(String json) {
    return ensureInitialized().decodeJson<AlbumItem>(json);
  }
}

mixin AlbumItemMappable {
  String toJson() {
    return AlbumItemMapper.ensureInitialized()
        .encodeJson<AlbumItem>(this as AlbumItem);
  }

  Map<String, dynamic> toMap() {
    return AlbumItemMapper.ensureInitialized()
        .encodeMap<AlbumItem>(this as AlbumItem);
  }

  AlbumItemCopyWith<AlbumItem, AlbumItem, AlbumItem> get copyWith =>
      _AlbumItemCopyWithImpl<AlbumItem, AlbumItem>(
          this as AlbumItem, $identity, $identity);
  @override
  String toString() {
    return AlbumItemMapper.ensureInitialized()
        .stringifyValue(this as AlbumItem);
  }

  @override
  bool operator ==(Object other) {
    return AlbumItemMapper.ensureInitialized()
        .equalsValue(this as AlbumItem, other);
  }

  @override
  int get hashCode {
    return AlbumItemMapper.ensureInitialized().hashValue(this as AlbumItem);
  }
}

extension AlbumItemValueCopy<$R, $Out> on ObjectCopyWith<$R, AlbumItem, $Out> {
  AlbumItemCopyWith<$R, AlbumItem, $Out> get $asAlbumItem =>
      $base.as((v, t, t2) => _AlbumItemCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class AlbumItemCopyWith<$R, $In extends AlbumItem, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? id,
      String? imageUrl,
      String? description,
      String? tags,
      int? fileSize,
      int? width,
      int? height,
      String? imageType,
      bool? isPublic,
      DateTime? createTime,
      DateTime? updateTime});
  AlbumItemCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _AlbumItemCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, AlbumItem, $Out>
    implements AlbumItemCopyWith<$R, AlbumItem, $Out> {
  _AlbumItemCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<AlbumItem> $mapper =
      AlbumItemMapper.ensureInitialized();
  @override
  $R call(
          {int? id,
          String? imageUrl,
          Object? description = $none,
          Object? tags = $none,
          Object? fileSize = $none,
          Object? width = $none,
          Object? height = $none,
          Object? imageType = $none,
          bool? isPublic,
          DateTime? createTime,
          DateTime? updateTime}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (imageUrl != null) #imageUrl: imageUrl,
        if (description != $none) #description: description,
        if (tags != $none) #tags: tags,
        if (fileSize != $none) #fileSize: fileSize,
        if (width != $none) #width: width,
        if (height != $none) #height: height,
        if (imageType != $none) #imageType: imageType,
        if (isPublic != null) #isPublic: isPublic,
        if (createTime != null) #createTime: createTime,
        if (updateTime != null) #updateTime: updateTime
      }));
  @override
  AlbumItem $make(CopyWithData data) => AlbumItem(
      id: data.get(#id, or: $value.id),
      imageUrl: data.get(#imageUrl, or: $value.imageUrl),
      description: data.get(#description, or: $value.description),
      tags: data.get(#tags, or: $value.tags),
      fileSize: data.get(#fileSize, or: $value.fileSize),
      width: data.get(#width, or: $value.width),
      height: data.get(#height, or: $value.height),
      imageType: data.get(#imageType, or: $value.imageType),
      isPublic: data.get(#isPublic, or: $value.isPublic),
      createTime: data.get(#createTime, or: $value.createTime),
      updateTime: data.get(#updateTime, or: $value.updateTime));

  @override
  AlbumItemCopyWith<$R2, AlbumItem, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _AlbumItemCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
