// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'photo_page_response.dart';

class PhotoPageResponseMapper extends ClassMapperBase<PhotoPageResponse> {
  PhotoPageResponseMapper._();

  static PhotoPageResponseMapper? _instance;
  static PhotoPageResponseMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = PhotoPageResponseMapper._());
      ResponsePageInfoMapper.ensureInitialized();
      PhotoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'PhotoPageResponse';

  static List<Photo> _$records(PhotoPageResponse v) => v.records;
  static const Field<PhotoPageResponse, List<Photo>> _f$records =
      Field('records', _$records);
  static num _$current(PhotoPageResponse v) => v.current;
  static const Field<PhotoPageResponse, num> _f$current =
      Field('current', _$current);
  static num _$size(PhotoPageResponse v) => v.size;
  static const Field<PhotoPageResponse, num> _f$size = Field('size', _$size);
  static num _$total(PhotoPageResponse v) => v.total;
  static const Field<PhotoPageResponse, num> _f$total = Field('total', _$total);
  static num _$pages(PhotoPageResponse v) => v.pages;
  static const Field<PhotoPageResponse, num> _f$pages = Field('pages', _$pages);

  @override
  final MappableFields<PhotoPageResponse> fields = const {
    #records: _f$records,
    #current: _f$current,
    #size: _f$size,
    #total: _f$total,
    #pages: _f$pages,
  };

  static PhotoPageResponse _instantiate(DecodingData data) {
    return PhotoPageResponse(
        records: data.dec(_f$records),
        current: data.dec(_f$current),
        size: data.dec(_f$size),
        total: data.dec(_f$total),
        pages: data.dec(_f$pages));
  }

  @override
  final Function instantiate = _instantiate;

  static PhotoPageResponse fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<PhotoPageResponse>(map);
  }

  static PhotoPageResponse fromJson(String json) {
    return ensureInitialized().decodeJson<PhotoPageResponse>(json);
  }
}

mixin PhotoPageResponseMappable {
  String toJson() {
    return PhotoPageResponseMapper.ensureInitialized()
        .encodeJson<PhotoPageResponse>(this as PhotoPageResponse);
  }

  Map<String, dynamic> toMap() {
    return PhotoPageResponseMapper.ensureInitialized()
        .encodeMap<PhotoPageResponse>(this as PhotoPageResponse);
  }

  PhotoPageResponseCopyWith<PhotoPageResponse, PhotoPageResponse,
          PhotoPageResponse>
      get copyWith =>
          _PhotoPageResponseCopyWithImpl<PhotoPageResponse, PhotoPageResponse>(
              this as PhotoPageResponse, $identity, $identity);
  @override
  String toString() {
    return PhotoPageResponseMapper.ensureInitialized()
        .stringifyValue(this as PhotoPageResponse);
  }

  @override
  bool operator ==(Object other) {
    return PhotoPageResponseMapper.ensureInitialized()
        .equalsValue(this as PhotoPageResponse, other);
  }

  @override
  int get hashCode {
    return PhotoPageResponseMapper.ensureInitialized()
        .hashValue(this as PhotoPageResponse);
  }
}

extension PhotoPageResponseValueCopy<$R, $Out>
    on ObjectCopyWith<$R, PhotoPageResponse, $Out> {
  PhotoPageResponseCopyWith<$R, PhotoPageResponse, $Out>
      get $asPhotoPageResponse => $base
          .as((v, t, t2) => _PhotoPageResponseCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class PhotoPageResponseCopyWith<$R, $In extends PhotoPageResponse,
    $Out> implements ResponsePageInfoCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, Photo, PhotoCopyWith<$R, Photo, Photo>> get records;
  @override
  $R call(
      {List<Photo>? records, num? current, num? size, num? total, num? pages});
  PhotoPageResponseCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _PhotoPageResponseCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, PhotoPageResponse, $Out>
    implements PhotoPageResponseCopyWith<$R, PhotoPageResponse, $Out> {
  _PhotoPageResponseCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<PhotoPageResponse> $mapper =
      PhotoPageResponseMapper.ensureInitialized();
  @override
  ListCopyWith<$R, Photo, PhotoCopyWith<$R, Photo, Photo>> get records =>
      ListCopyWith($value.records, (v, t) => v.copyWith.$chain(t),
          (v) => call(records: v));
  @override
  $R call(
          {List<Photo>? records,
          num? current,
          num? size,
          num? total,
          num? pages}) =>
      $apply(FieldCopyWithData({
        if (records != null) #records: records,
        if (current != null) #current: current,
        if (size != null) #size: size,
        if (total != null) #total: total,
        if (pages != null) #pages: pages
      }));
  @override
  PhotoPageResponse $make(CopyWithData data) => PhotoPageResponse(
      records: data.get(#records, or: $value.records),
      current: data.get(#current, or: $value.current),
      size: data.get(#size, or: $value.size),
      total: data.get(#total, or: $value.total),
      pages: data.get(#pages, or: $value.pages));

  @override
  PhotoPageResponseCopyWith<$R2, PhotoPageResponse, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _PhotoPageResponseCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
