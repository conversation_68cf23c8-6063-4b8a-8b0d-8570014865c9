// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'photo.dart';

class PhotoMapper extends ClassMapperBase<Photo> {
  PhotoMapper._();

  static PhotoMapper? _instance;
  static PhotoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = PhotoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'Photo';

  static int _$id(Photo v) => v.id;
  static const Field<Photo, int> _f$id = Field('id', _$id);
  static String _$originalUrl(Photo v) => v.originalUrl;
  static const Field<Photo, String> _f$originalUrl =
      Field('originalUrl', _$originalUrl, key: r'original_url');
  static String? _$thumbnailUrl(Photo v) => v.thumbnailUrl;
  static const Field<Photo, String> _f$thumbnailUrl =
      Field('thumbnailUrl', _$thumbnailUrl, key: r'thumbnail_url', opt: true);
  static String? _$description(Photo v) => v.description;
  static const Field<Photo, String> _f$description =
      Field('description', _$description, opt: true);
  static DateTime _$createdAt(Photo v) => v.createdAt;
  static const Field<Photo, DateTime> _f$createdAt =
      Field('createdAt', _$createdAt, key: r'created_at');
  static int _$userId(Photo v) => v.userId;
  static const Field<Photo, int> _f$userId =
      Field('userId', _$userId, key: r'user_id');
  static int? _$momentId(Photo v) => v.momentId;
  static const Field<Photo, int> _f$momentId =
      Field('momentId', _$momentId, key: r'moment_id', opt: true);
  static int _$width(Photo v) => v.width;
  static const Field<Photo, int> _f$width = Field('width', _$width);
  static int _$height(Photo v) => v.height;
  static const Field<Photo, int> _f$height = Field('height', _$height);
  static String? _$fileType(Photo v) => v.fileType;
  static const Field<Photo, String> _f$fileType =
      Field('fileType', _$fileType, key: r'file_type', opt: true);
  static int _$fileSize(Photo v) => v.fileSize;
  static const Field<Photo, int> _f$fileSize =
      Field('fileSize', _$fileSize, key: r'file_size');

  @override
  final MappableFields<Photo> fields = const {
    #id: _f$id,
    #originalUrl: _f$originalUrl,
    #thumbnailUrl: _f$thumbnailUrl,
    #description: _f$description,
    #createdAt: _f$createdAt,
    #userId: _f$userId,
    #momentId: _f$momentId,
    #width: _f$width,
    #height: _f$height,
    #fileType: _f$fileType,
    #fileSize: _f$fileSize,
  };

  static Photo _instantiate(DecodingData data) {
    return Photo(
        id: data.dec(_f$id),
        originalUrl: data.dec(_f$originalUrl),
        thumbnailUrl: data.dec(_f$thumbnailUrl),
        description: data.dec(_f$description),
        createdAt: data.dec(_f$createdAt),
        userId: data.dec(_f$userId),
        momentId: data.dec(_f$momentId),
        width: data.dec(_f$width),
        height: data.dec(_f$height),
        fileType: data.dec(_f$fileType),
        fileSize: data.dec(_f$fileSize));
  }

  @override
  final Function instantiate = _instantiate;

  static Photo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<Photo>(map);
  }

  static Photo fromJson(String json) {
    return ensureInitialized().decodeJson<Photo>(json);
  }
}

mixin PhotoMappable {
  String toJson() {
    return PhotoMapper.ensureInitialized().encodeJson<Photo>(this as Photo);
  }

  Map<String, dynamic> toMap() {
    return PhotoMapper.ensureInitialized().encodeMap<Photo>(this as Photo);
  }

  PhotoCopyWith<Photo, Photo, Photo> get copyWith =>
      _PhotoCopyWithImpl<Photo, Photo>(this as Photo, $identity, $identity);
  @override
  String toString() {
    return PhotoMapper.ensureInitialized().stringifyValue(this as Photo);
  }

  @override
  bool operator ==(Object other) {
    return PhotoMapper.ensureInitialized().equalsValue(this as Photo, other);
  }

  @override
  int get hashCode {
    return PhotoMapper.ensureInitialized().hashValue(this as Photo);
  }
}

extension PhotoValueCopy<$R, $Out> on ObjectCopyWith<$R, Photo, $Out> {
  PhotoCopyWith<$R, Photo, $Out> get $asPhoto =>
      $base.as((v, t, t2) => _PhotoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class PhotoCopyWith<$R, $In extends Photo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? id,
      String? originalUrl,
      String? thumbnailUrl,
      String? description,
      DateTime? createdAt,
      int? userId,
      int? momentId,
      int? width,
      int? height,
      String? fileType,
      int? fileSize});
  PhotoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _PhotoCopyWithImpl<$R, $Out> extends ClassCopyWithBase<$R, Photo, $Out>
    implements PhotoCopyWith<$R, Photo, $Out> {
  _PhotoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<Photo> $mapper = PhotoMapper.ensureInitialized();
  @override
  $R call(
          {int? id,
          String? originalUrl,
          Object? thumbnailUrl = $none,
          Object? description = $none,
          DateTime? createdAt,
          int? userId,
          Object? momentId = $none,
          int? width,
          int? height,
          Object? fileType = $none,
          int? fileSize}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (originalUrl != null) #originalUrl: originalUrl,
        if (thumbnailUrl != $none) #thumbnailUrl: thumbnailUrl,
        if (description != $none) #description: description,
        if (createdAt != null) #createdAt: createdAt,
        if (userId != null) #userId: userId,
        if (momentId != $none) #momentId: momentId,
        if (width != null) #width: width,
        if (height != null) #height: height,
        if (fileType != $none) #fileType: fileType,
        if (fileSize != null) #fileSize: fileSize
      }));
  @override
  Photo $make(CopyWithData data) => Photo(
      id: data.get(#id, or: $value.id),
      originalUrl: data.get(#originalUrl, or: $value.originalUrl),
      thumbnailUrl: data.get(#thumbnailUrl, or: $value.thumbnailUrl),
      description: data.get(#description, or: $value.description),
      createdAt: data.get(#createdAt, or: $value.createdAt),
      userId: data.get(#userId, or: $value.userId),
      momentId: data.get(#momentId, or: $value.momentId),
      width: data.get(#width, or: $value.width),
      height: data.get(#height, or: $value.height),
      fileType: data.get(#fileType, or: $value.fileType),
      fileSize: data.get(#fileSize, or: $value.fileSize));

  @override
  PhotoCopyWith<$R2, Photo, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
      _PhotoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
