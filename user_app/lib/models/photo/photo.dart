import 'package:dart_mappable/dart_mappable.dart';

part 'photo.mapper.dart';

@MappableClass()
class Photo with PhotoMappable {
  final int id;
  final String originalUrl;
  final String? thumbnailUrl;
  final String? description;
  final DateTime createdAt;
  final int userId;
  final int? momentId;
  final int width;
  final int height;
  final String? fileType;
  final int fileSize;

  const Photo({
    required this.id,
    required this.originalUrl,
    this.thumbnailUrl,
    this.description,
    required this.createdAt,
    required this.userId,
    this.momentId,
    required this.width,
    required this.height,
    this.fileType,
    required this.fileSize,
  });

  static final fromMap = PhotoMapper.fromMap;
}