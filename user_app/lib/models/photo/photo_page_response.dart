import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/models/photo/photo.dart';
import 'package:user_app/models/response_page_info.dart';

part 'photo_page_response.mapper.dart';

@MappableClass()
class PhotoPageResponse extends ResponsePageInfo with PhotoPageResponseMappable {
  final List<Photo> records;

  PhotoPageResponse({
    required this.records,
    required super.current,
    required super.size,
    required super.total,
    required super.pages,
  });

  static final fromMap = PhotoPageResponseMapper.fromMap;
}