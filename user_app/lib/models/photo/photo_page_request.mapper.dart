// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'photo_page_request.dart';

class PhotoPageRequestMapper extends ClassMapperBase<PhotoPageRequest> {
  PhotoPageRequestMapper._();

  static PhotoPageRequestMapper? _instance;
  static PhotoPageRequestMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = PhotoPageRequestMapper._());
      RequestPageInfoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'PhotoPageRequest';

  static num? _$pageNum(PhotoPageRequest v) => v.pageNum;
  static const Field<PhotoPageRequest, num> _f$pageNum =
      Field('pageNum', _$pageNum, key: r'page_num');
  static num? _$pageSize(PhotoPageRequest v) => v.pageSize;
  static const Field<PhotoPageRequest, num> _f$pageSize =
      Field('pageSize', _$pageSize, key: r'page_size');
  static bool _$onlyUserPhotos(PhotoPageRequest v) => v.onlyUserPhotos;
  static const Field<PhotoPageRequest, bool> _f$onlyUserPhotos = Field(
      'onlyUserPhotos', _$onlyUserPhotos,
      key: r'only_user_photos', opt: true, def: false);
  static int? _$userId(PhotoPageRequest v) => v.userId;
  static const Field<PhotoPageRequest, int> _f$userId =
      Field('userId', _$userId, key: r'user_id', opt: true);
  static int? _$momentId(PhotoPageRequest v) => v.momentId;
  static const Field<PhotoPageRequest, int> _f$momentId =
      Field('momentId', _$momentId, key: r'moment_id', opt: true);

  @override
  final MappableFields<PhotoPageRequest> fields = const {
    #pageNum: _f$pageNum,
    #pageSize: _f$pageSize,
    #onlyUserPhotos: _f$onlyUserPhotos,
    #userId: _f$userId,
    #momentId: _f$momentId,
  };

  static PhotoPageRequest _instantiate(DecodingData data) {
    return PhotoPageRequest(
        pageNum: data.dec(_f$pageNum),
        pageSize: data.dec(_f$pageSize),
        onlyUserPhotos: data.dec(_f$onlyUserPhotos),
        userId: data.dec(_f$userId),
        momentId: data.dec(_f$momentId));
  }

  @override
  final Function instantiate = _instantiate;

  static PhotoPageRequest fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<PhotoPageRequest>(map);
  }

  static PhotoPageRequest fromJson(String json) {
    return ensureInitialized().decodeJson<PhotoPageRequest>(json);
  }
}

mixin PhotoPageRequestMappable {
  String toJson() {
    return PhotoPageRequestMapper.ensureInitialized()
        .encodeJson<PhotoPageRequest>(this as PhotoPageRequest);
  }

  Map<String, dynamic> toMap() {
    return PhotoPageRequestMapper.ensureInitialized()
        .encodeMap<PhotoPageRequest>(this as PhotoPageRequest);
  }

  PhotoPageRequestCopyWith<PhotoPageRequest, PhotoPageRequest, PhotoPageRequest>
      get copyWith =>
          _PhotoPageRequestCopyWithImpl<PhotoPageRequest, PhotoPageRequest>(
              this as PhotoPageRequest, $identity, $identity);
  @override
  String toString() {
    return PhotoPageRequestMapper.ensureInitialized()
        .stringifyValue(this as PhotoPageRequest);
  }

  @override
  bool operator ==(Object other) {
    return PhotoPageRequestMapper.ensureInitialized()
        .equalsValue(this as PhotoPageRequest, other);
  }

  @override
  int get hashCode {
    return PhotoPageRequestMapper.ensureInitialized()
        .hashValue(this as PhotoPageRequest);
  }
}

extension PhotoPageRequestValueCopy<$R, $Out>
    on ObjectCopyWith<$R, PhotoPageRequest, $Out> {
  PhotoPageRequestCopyWith<$R, PhotoPageRequest, $Out>
      get $asPhotoPageRequest => $base
          .as((v, t, t2) => _PhotoPageRequestCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class PhotoPageRequestCopyWith<$R, $In extends PhotoPageRequest, $Out>
    implements RequestPageInfoCopyWith<$R, $In, $Out> {
  @override
  $R call(
      {num? pageNum,
      num? pageSize,
      bool? onlyUserPhotos,
      int? userId,
      int? momentId});
  PhotoPageRequestCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _PhotoPageRequestCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, PhotoPageRequest, $Out>
    implements PhotoPageRequestCopyWith<$R, PhotoPageRequest, $Out> {
  _PhotoPageRequestCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<PhotoPageRequest> $mapper =
      PhotoPageRequestMapper.ensureInitialized();
  @override
  $R call(
          {Object? pageNum = $none,
          Object? pageSize = $none,
          bool? onlyUserPhotos,
          Object? userId = $none,
          Object? momentId = $none}) =>
      $apply(FieldCopyWithData({
        if (pageNum != $none) #pageNum: pageNum,
        if (pageSize != $none) #pageSize: pageSize,
        if (onlyUserPhotos != null) #onlyUserPhotos: onlyUserPhotos,
        if (userId != $none) #userId: userId,
        if (momentId != $none) #momentId: momentId
      }));
  @override
  PhotoPageRequest $make(CopyWithData data) => PhotoPageRequest(
      pageNum: data.get(#pageNum, or: $value.pageNum),
      pageSize: data.get(#pageSize, or: $value.pageSize),
      onlyUserPhotos: data.get(#onlyUserPhotos, or: $value.onlyUserPhotos),
      userId: data.get(#userId, or: $value.userId),
      momentId: data.get(#momentId, or: $value.momentId));

  @override
  PhotoPageRequestCopyWith<$R2, PhotoPageRequest, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _PhotoPageRequestCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
