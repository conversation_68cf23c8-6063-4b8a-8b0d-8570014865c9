import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/models/request_page_info.dart';

part 'photo_page_request.mapper.dart';

@MappableClass()
class PhotoPageRequest extends RequestPageInfo with PhotoPageRequestMappable {
  final bool onlyUserPhotos;
  final int? userId;
  final int? momentId;

  PhotoPageRequest({
    required super.pageNum,
    required super.pageSize,
    this.onlyUserPhotos = false,
    this.userId,
    this.momentId,
  });

  static final fromMap = PhotoPageRequestMapper.fromMap;
}