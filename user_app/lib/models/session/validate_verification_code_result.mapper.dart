// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'validate_verification_code_result.dart';

class ValidateVerificationCodeResultMapper
    extends ClassMapperBase<ValidateVerificationCodeResult> {
  ValidateVerificationCodeResultMapper._();

  static ValidateVerificationCodeResultMapper? _instance;
  static ValidateVerificationCodeResultMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals
          .use(_instance = ValidateVerificationCodeResultMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'ValidateVerificationCodeResult';

  static String _$phoneNumber(ValidateVerificationCodeResult v) =>
      v.phoneNumber;
  static const Field<ValidateVerificationCodeResult, String> _f$phoneNumber =
      Field('phoneNumber', _$phoneNumber, key: r'phone_number');
  static String _$validateCode(ValidateVerificationCodeResult v) =>
      v.validateCode;
  static const Field<ValidateVerificationCodeResult, String> _f$validateCode =
      Field('validateCode', _$validateCode, key: r'validate_code');

  @override
  final MappableFields<ValidateVerificationCodeResult> fields = const {
    #phoneNumber: _f$phoneNumber,
    #validateCode: _f$validateCode,
  };

  static ValidateVerificationCodeResult _instantiate(DecodingData data) {
    return ValidateVerificationCodeResult(
        phoneNumber: data.dec(_f$phoneNumber),
        validateCode: data.dec(_f$validateCode));
  }

  @override
  final Function instantiate = _instantiate;

  static ValidateVerificationCodeResult fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<ValidateVerificationCodeResult>(map);
  }

  static ValidateVerificationCodeResult fromJson(String json) {
    return ensureInitialized().decodeJson<ValidateVerificationCodeResult>(json);
  }
}

mixin ValidateVerificationCodeResultMappable {
  String toJson() {
    return ValidateVerificationCodeResultMapper.ensureInitialized()
        .encodeJson<ValidateVerificationCodeResult>(
            this as ValidateVerificationCodeResult);
  }

  Map<String, dynamic> toMap() {
    return ValidateVerificationCodeResultMapper.ensureInitialized()
        .encodeMap<ValidateVerificationCodeResult>(
            this as ValidateVerificationCodeResult);
  }

  ValidateVerificationCodeResultCopyWith<ValidateVerificationCodeResult,
          ValidateVerificationCodeResult, ValidateVerificationCodeResult>
      get copyWith => _ValidateVerificationCodeResultCopyWithImpl<
              ValidateVerificationCodeResult, ValidateVerificationCodeResult>(
          this as ValidateVerificationCodeResult, $identity, $identity);
  @override
  String toString() {
    return ValidateVerificationCodeResultMapper.ensureInitialized()
        .stringifyValue(this as ValidateVerificationCodeResult);
  }

  @override
  bool operator ==(Object other) {
    return ValidateVerificationCodeResultMapper.ensureInitialized()
        .equalsValue(this as ValidateVerificationCodeResult, other);
  }

  @override
  int get hashCode {
    return ValidateVerificationCodeResultMapper.ensureInitialized()
        .hashValue(this as ValidateVerificationCodeResult);
  }
}

extension ValidateVerificationCodeResultValueCopy<$R, $Out>
    on ObjectCopyWith<$R, ValidateVerificationCodeResult, $Out> {
  ValidateVerificationCodeResultCopyWith<$R, ValidateVerificationCodeResult,
          $Out>
      get $asValidateVerificationCodeResult => $base.as((v, t, t2) =>
          _ValidateVerificationCodeResultCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class ValidateVerificationCodeResultCopyWith<
    $R,
    $In extends ValidateVerificationCodeResult,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  $R call({String? phoneNumber, String? validateCode});
  ValidateVerificationCodeResultCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _ValidateVerificationCodeResultCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, ValidateVerificationCodeResult, $Out>
    implements
        ValidateVerificationCodeResultCopyWith<$R,
            ValidateVerificationCodeResult, $Out> {
  _ValidateVerificationCodeResultCopyWithImpl(
      super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<ValidateVerificationCodeResult> $mapper =
      ValidateVerificationCodeResultMapper.ensureInitialized();
  @override
  $R call({String? phoneNumber, String? validateCode}) =>
      $apply(FieldCopyWithData({
        if (phoneNumber != null) #phoneNumber: phoneNumber,
        if (validateCode != null) #validateCode: validateCode
      }));
  @override
  ValidateVerificationCodeResult $make(CopyWithData data) =>
      ValidateVerificationCodeResult(
          phoneNumber: data.get(#phoneNumber, or: $value.phoneNumber),
          validateCode: data.get(#validateCode, or: $value.validateCode));

  @override
  ValidateVerificationCodeResultCopyWith<$R2, ValidateVerificationCodeResult,
      $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _ValidateVerificationCodeResultCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
