// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'comment_vo.dart';

class CommentVoMapper extends ClassMapperBase<CommentVo> {
  CommentVoMapper._();

  static CommentVoMapper? _instance;
  static CommentVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = CommentVoMapper._());
      CommentVoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'CommentVo';

  static int _$id(CommentVo v) => v.id;
  static const Field<CommentVo, int> _f$id = Field('id', _$id);
  static int _$momentId(CommentVo v) => v.momentId;
  static const Field<CommentVo, int> _f$momentId =
      Field('momentId', _$momentId, key: r'moment_id');
  static int _$userId(CommentVo v) => v.userId;
  static const Field<CommentVo, int> _f$userId =
      Field('userId', _$userId, key: r'user_id');
  static String _$userName(CommentVo v) => v.userName;
  static const Field<CommentVo, String> _f$userName =
      Field('userName', _$userName, key: r'user_name');
  static String? _$userAvatar(CommentVo v) => v.userAvatar;
  static const Field<CommentVo, String> _f$userAvatar =
      Field('userAvatar', _$userAvatar, key: r'user_avatar', opt: true);
  static String _$content(CommentVo v) => v.content;
  static const Field<CommentVo, String> _f$content =
      Field('content', _$content);
  static int? _$parentId(CommentVo v) => v.parentId;
  static const Field<CommentVo, int> _f$parentId =
      Field('parentId', _$parentId, key: r'parent_id', opt: true);
  static CommentVo? _$parentComment(CommentVo v) => v.parentComment;
  static const Field<CommentVo, CommentVo> _f$parentComment = Field(
      'parentComment', _$parentComment,
      key: r'parent_comment', opt: true);
  static List<CommentVo> _$replies(CommentVo v) => v.replies;
  static const Field<CommentVo, List<CommentVo>> _f$replies =
      Field('replies', _$replies, opt: true, def: const []);
  static DateTime _$createdAt(CommentVo v) => v.createdAt;
  static const Field<CommentVo, DateTime> _f$createdAt =
      Field('createdAt', _$createdAt, key: r'created_at');
  static int _$upVotes(CommentVo v) => v.upVotes;
  static const Field<CommentVo, int> _f$upVotes =
      Field('upVotes', _$upVotes, key: r'up_votes', opt: true, def: 0);
  static int _$downVotes(CommentVo v) => v.downVotes;
  static const Field<CommentVo, int> _f$downVotes =
      Field('downVotes', _$downVotes, key: r'down_votes', opt: true, def: 0);
  static bool _$upVoted(CommentVo v) => v.upVoted;
  static const Field<CommentVo, bool> _f$upVoted =
      Field('upVoted', _$upVoted, key: r'up_voted', opt: true, def: false);
  static bool _$downVoted(CommentVo v) => v.downVoted;
  static const Field<CommentVo, bool> _f$downVoted = Field(
      'downVoted', _$downVoted,
      key: r'down_voted', opt: true, def: false);

  @override
  final MappableFields<CommentVo> fields = const {
    #id: _f$id,
    #momentId: _f$momentId,
    #userId: _f$userId,
    #userName: _f$userName,
    #userAvatar: _f$userAvatar,
    #content: _f$content,
    #parentId: _f$parentId,
    #parentComment: _f$parentComment,
    #replies: _f$replies,
    #createdAt: _f$createdAt,
    #upVotes: _f$upVotes,
    #downVotes: _f$downVotes,
    #upVoted: _f$upVoted,
    #downVoted: _f$downVoted,
  };

  static CommentVo _instantiate(DecodingData data) {
    return CommentVo(
        id: data.dec(_f$id),
        momentId: data.dec(_f$momentId),
        userId: data.dec(_f$userId),
        userName: data.dec(_f$userName),
        userAvatar: data.dec(_f$userAvatar),
        content: data.dec(_f$content),
        parentId: data.dec(_f$parentId),
        parentComment: data.dec(_f$parentComment),
        replies: data.dec(_f$replies),
        createdAt: data.dec(_f$createdAt),
        upVotes: data.dec(_f$upVotes),
        downVotes: data.dec(_f$downVotes),
        upVoted: data.dec(_f$upVoted),
        downVoted: data.dec(_f$downVoted));
  }

  @override
  final Function instantiate = _instantiate;

  static CommentVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<CommentVo>(map);
  }

  static CommentVo fromJson(String json) {
    return ensureInitialized().decodeJson<CommentVo>(json);
  }
}

mixin CommentVoMappable {
  String toJson() {
    return CommentVoMapper.ensureInitialized()
        .encodeJson<CommentVo>(this as CommentVo);
  }

  Map<String, dynamic> toMap() {
    return CommentVoMapper.ensureInitialized()
        .encodeMap<CommentVo>(this as CommentVo);
  }

  CommentVoCopyWith<CommentVo, CommentVo, CommentVo> get copyWith =>
      _CommentVoCopyWithImpl<CommentVo, CommentVo>(
          this as CommentVo, $identity, $identity);
  @override
  String toString() {
    return CommentVoMapper.ensureInitialized()
        .stringifyValue(this as CommentVo);
  }

  @override
  bool operator ==(Object other) {
    return CommentVoMapper.ensureInitialized()
        .equalsValue(this as CommentVo, other);
  }

  @override
  int get hashCode {
    return CommentVoMapper.ensureInitialized().hashValue(this as CommentVo);
  }
}

extension CommentVoValueCopy<$R, $Out> on ObjectCopyWith<$R, CommentVo, $Out> {
  CommentVoCopyWith<$R, CommentVo, $Out> get $asCommentVo =>
      $base.as((v, t, t2) => _CommentVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class CommentVoCopyWith<$R, $In extends CommentVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  CommentVoCopyWith<$R, CommentVo, CommentVo>? get parentComment;
  ListCopyWith<$R, CommentVo, CommentVoCopyWith<$R, CommentVo, CommentVo>>
      get replies;
  $R call(
      {int? id,
      int? momentId,
      int? userId,
      String? userName,
      String? userAvatar,
      String? content,
      int? parentId,
      CommentVo? parentComment,
      List<CommentVo>? replies,
      DateTime? createdAt,
      int? upVotes,
      int? downVotes,
      bool? upVoted,
      bool? downVoted});
  CommentVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _CommentVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, CommentVo, $Out>
    implements CommentVoCopyWith<$R, CommentVo, $Out> {
  _CommentVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<CommentVo> $mapper =
      CommentVoMapper.ensureInitialized();
  @override
  CommentVoCopyWith<$R, CommentVo, CommentVo>? get parentComment =>
      $value.parentComment?.copyWith.$chain((v) => call(parentComment: v));
  @override
  ListCopyWith<$R, CommentVo, CommentVoCopyWith<$R, CommentVo, CommentVo>>
      get replies => ListCopyWith($value.replies,
          (v, t) => v.copyWith.$chain(t), (v) => call(replies: v));
  @override
  $R call(
          {int? id,
          int? momentId,
          int? userId,
          String? userName,
          Object? userAvatar = $none,
          String? content,
          Object? parentId = $none,
          Object? parentComment = $none,
          List<CommentVo>? replies,
          DateTime? createdAt,
          int? upVotes,
          int? downVotes,
          bool? upVoted,
          bool? downVoted}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (momentId != null) #momentId: momentId,
        if (userId != null) #userId: userId,
        if (userName != null) #userName: userName,
        if (userAvatar != $none) #userAvatar: userAvatar,
        if (content != null) #content: content,
        if (parentId != $none) #parentId: parentId,
        if (parentComment != $none) #parentComment: parentComment,
        if (replies != null) #replies: replies,
        if (createdAt != null) #createdAt: createdAt,
        if (upVotes != null) #upVotes: upVotes,
        if (downVotes != null) #downVotes: downVotes,
        if (upVoted != null) #upVoted: upVoted,
        if (downVoted != null) #downVoted: downVoted
      }));
  @override
  CommentVo $make(CopyWithData data) => CommentVo(
      id: data.get(#id, or: $value.id),
      momentId: data.get(#momentId, or: $value.momentId),
      userId: data.get(#userId, or: $value.userId),
      userName: data.get(#userName, or: $value.userName),
      userAvatar: data.get(#userAvatar, or: $value.userAvatar),
      content: data.get(#content, or: $value.content),
      parentId: data.get(#parentId, or: $value.parentId),
      parentComment: data.get(#parentComment, or: $value.parentComment),
      replies: data.get(#replies, or: $value.replies),
      createdAt: data.get(#createdAt, or: $value.createdAt),
      upVotes: data.get(#upVotes, or: $value.upVotes),
      downVotes: data.get(#downVotes, or: $value.downVotes),
      upVoted: data.get(#upVoted, or: $value.upVoted),
      downVoted: data.get(#downVoted, or: $value.downVoted));

  @override
  CommentVoCopyWith<$R2, CommentVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _CommentVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
