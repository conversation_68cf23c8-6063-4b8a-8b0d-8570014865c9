import 'package:dart_mappable/dart_mappable.dart';

part 'comment_vo.mapper.dart';

@MappableClass()
class CommentVo with CommentVoMappable {
  final int id;
  final int momentId;
  final int userId;
  final String userName;
  final String? userAvatar;
  final String content;
  final int? parentId;
  final CommentVo? parentComment;
  final List<CommentVo> replies;
  final DateTime createdAt;
  int upVotes;
  int downVotes;
  bool upVoted;
  bool downVoted;

  CommentVo({
    required this.id,
    required this.momentId,
    required this.userId,
    required this.userName,
    this.userAvatar,
    required this.content,
    this.parentId,
    this.parentComment,
    this.replies = const [],
    required this.createdAt,
    this.upVotes = 0,
    this.downVotes = 0,
    this.upVoted = false,
    this.downVoted = false,
  });

  static final fromMap = CommentVoMapper.fromMap;

  static CommentVo? findById(List<CommentVo> comments, num targetId) {
    for (var comment in comments) {
      if (comment.id == targetId) {
        return comment;
      }
      if (comment.replies.isNotEmpty) {
        final foundInSub = findById(comment.replies, targetId);
        if (foundInSub != null) {
          return foundInSub;
        }
      }
    }
    return null;
  }
}
