import 'package:dart_mappable/dart_mappable.dart';
import 'package:image_picker/image_picker.dart';

part 'uploaded_image.mapper.dart';

@MappableClass()
class UploadedImage with UploadedImageMappable {
  final XFile file;
  final String? url;
  final bool isUploading;
  final bool hasError;
  final double uploadProgress;

  const UploadedImage({
    required this.file,
    this.url,
    this.isUploading = false,
    this.hasError = false,
    this.uploadProgress = 0.0,
  });

  static UploadedImage fromUrl(String url) {
    return UploadedImage(
      file: XFile(url),
      url: url,
      isUploading: false,
      hasError: false,
      uploadProgress: 1.0,
    );
  }
}
