// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'uploaded_image.dart';

class UploadedImageMapper extends ClassMapperBase<UploadedImage> {
  UploadedImageMapper._();

  static UploadedImageMapper? _instance;
  static UploadedImageMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = UploadedImageMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'UploadedImage';

  static XFile _$file(UploadedImage v) => v.file;
  static const Field<UploadedImage, XFile> _f$file = Field('file', _$file);
  static String? _$url(UploadedImage v) => v.url;
  static const Field<UploadedImage, String> _f$url =
      Field('url', _$url, opt: true);
  static bool _$isUploading(UploadedImage v) => v.isUploading;
  static const Field<UploadedImage, bool> _f$isUploading = Field(
      'isUploading', _$isUploading,
      key: r'is_uploading', opt: true, def: false);
  static bool _$hasError(UploadedImage v) => v.hasError;
  static const Field<UploadedImage, bool> _f$hasError =
      Field('hasError', _$hasError, key: r'has_error', opt: true, def: false);
  static double _$uploadProgress(UploadedImage v) => v.uploadProgress;
  static const Field<UploadedImage, double> _f$uploadProgress = Field(
      'uploadProgress', _$uploadProgress,
      key: r'upload_progress', opt: true, def: 0.0);

  @override
  final MappableFields<UploadedImage> fields = const {
    #file: _f$file,
    #url: _f$url,
    #isUploading: _f$isUploading,
    #hasError: _f$hasError,
    #uploadProgress: _f$uploadProgress,
  };

  static UploadedImage _instantiate(DecodingData data) {
    return UploadedImage(
        file: data.dec(_f$file),
        url: data.dec(_f$url),
        isUploading: data.dec(_f$isUploading),
        hasError: data.dec(_f$hasError),
        uploadProgress: data.dec(_f$uploadProgress));
  }

  @override
  final Function instantiate = _instantiate;

  static UploadedImage fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<UploadedImage>(map);
  }

  static UploadedImage fromJson(String json) {
    return ensureInitialized().decodeJson<UploadedImage>(json);
  }
}

mixin UploadedImageMappable {
  String toJson() {
    return UploadedImageMapper.ensureInitialized()
        .encodeJson<UploadedImage>(this as UploadedImage);
  }

  Map<String, dynamic> toMap() {
    return UploadedImageMapper.ensureInitialized()
        .encodeMap<UploadedImage>(this as UploadedImage);
  }

  UploadedImageCopyWith<UploadedImage, UploadedImage, UploadedImage>
      get copyWith => _UploadedImageCopyWithImpl<UploadedImage, UploadedImage>(
          this as UploadedImage, $identity, $identity);
  @override
  String toString() {
    return UploadedImageMapper.ensureInitialized()
        .stringifyValue(this as UploadedImage);
  }

  @override
  bool operator ==(Object other) {
    return UploadedImageMapper.ensureInitialized()
        .equalsValue(this as UploadedImage, other);
  }

  @override
  int get hashCode {
    return UploadedImageMapper.ensureInitialized()
        .hashValue(this as UploadedImage);
  }
}

extension UploadedImageValueCopy<$R, $Out>
    on ObjectCopyWith<$R, UploadedImage, $Out> {
  UploadedImageCopyWith<$R, UploadedImage, $Out> get $asUploadedImage =>
      $base.as((v, t, t2) => _UploadedImageCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class UploadedImageCopyWith<$R, $In extends UploadedImage, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {XFile? file,
      String? url,
      bool? isUploading,
      bool? hasError,
      double? uploadProgress});
  UploadedImageCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _UploadedImageCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, UploadedImage, $Out>
    implements UploadedImageCopyWith<$R, UploadedImage, $Out> {
  _UploadedImageCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<UploadedImage> $mapper =
      UploadedImageMapper.ensureInitialized();
  @override
  $R call(
          {XFile? file,
          Object? url = $none,
          bool? isUploading,
          bool? hasError,
          double? uploadProgress}) =>
      $apply(FieldCopyWithData({
        if (file != null) #file: file,
        if (url != $none) #url: url,
        if (isUploading != null) #isUploading: isUploading,
        if (hasError != null) #hasError: hasError,
        if (uploadProgress != null) #uploadProgress: uploadProgress
      }));
  @override
  UploadedImage $make(CopyWithData data) => UploadedImage(
      file: data.get(#file, or: $value.file),
      url: data.get(#url, or: $value.url),
      isUploading: data.get(#isUploading, or: $value.isUploading),
      hasError: data.get(#hasError, or: $value.hasError),
      uploadProgress: data.get(#uploadProgress, or: $value.uploadProgress));

  @override
  UploadedImageCopyWith<$R2, UploadedImage, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _UploadedImageCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
