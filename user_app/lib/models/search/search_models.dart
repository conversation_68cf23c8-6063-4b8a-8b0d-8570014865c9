class SearchRequest {
  final String keyword;
  final String type;
  final String sortBy;
  final int page;
  final int size;
  final String? province;
  final String? city;
  final int? dayRange;
  final String? momentType;
  final bool includeHighlight;

  SearchRequest({
    required this.keyword,
    this.type = 'all',
    this.sortBy = 'relevance',
    this.page = 1,
    this.size = 10,
    this.province,
    this.city,
    this.dayRange,
    this.momentType,
    this.includeHighlight = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'keyword': keyword,
      'type': type,
      'sortBy': sortBy,
      'page': page,
      'size': size,
      'includeHighlight': includeHighlight,
      if (province != null) 'province': province,
      if (city != null) 'city': city,
      if (dayRange != null) 'dayRange': dayRange,
      if (momentType != null) 'momentType': momentType,
    };
  }
}

class SearchResultVO {
  final int id;
  final String type;
  final String title;
  final String? content;
  final String? authorName;
  final String? authorAvatar;
  final String? coverImage;
  final List<String>? images;
  final String? location;
  final DateTime? createTime;
  final int? likeCount;
  final int? commentCount;
  final int? viewCount;
  final double? relevanceScore;
  final String? highlightContent;
  final String? momentType;
  final Map<String, dynamic>? typeSpecificData;

  SearchResultVO({
    required this.id,
    required this.type,
    required this.title,
    this.content,
    this.authorName,
    this.authorAvatar,
    this.coverImage,
    this.images,
    this.location,
    this.createTime,
    this.likeCount,
    this.commentCount,
    this.viewCount,
    this.relevanceScore,
    this.highlightContent,
    this.momentType,
    this.typeSpecificData,
  });

  factory SearchResultVO.fromJson(Map<String, dynamic> json) {
    return SearchResultVO(
      id: json['id'],
      type: json['type'],
      title: json['title'] ?? '',
      content: json['content'],
      authorName: json['authorName'],
      authorAvatar: json['authorAvatar'],
      coverImage: json['coverImage'],
      images: json['images'] != null ? List<String>.from(json['images']) : null,
      location: json['location'],
      createTime: json['createTime'] != null
          ? DateTime.parse(json['createTime'])
          : null,
      likeCount: json['likeCount'],
      commentCount: json['commentCount'],
      viewCount: json['viewCount'],
      relevanceScore: json['relevanceScore']?.toDouble(),
      highlightContent: json['highlightContent'],
      momentType: json['momentType'],
      typeSpecificData: json['typeSpecificData'] != null
          ? Map<String, dynamic>.from(json['typeSpecificData'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'title': title,
      'content': content,
      'authorName': authorName,
      'authorAvatar': authorAvatar,
      'coverImage': coverImage,
      'images': images,
      'location': location,
      'createTime': createTime?.toIso8601String(),
      'likeCount': likeCount,
      'commentCount': commentCount,
      'viewCount': viewCount,
      'relevanceScore': relevanceScore,
      'highlightContent': highlightContent,
      'momentType': momentType,
      'typeSpecificData': typeSpecificData,
    };
  }
}

class SearchSuggestionVO {
  final String suggestion;
  final String type;
  final int? count;
  final String? extra;

  SearchSuggestionVO({
    required this.suggestion,
    required this.type,
    this.count,
    this.extra,
  });

  factory SearchSuggestionVO.fromJson(Map<String, dynamic> json) {
    return SearchSuggestionVO(
      suggestion: json['suggestion'],
      type: json['type'],
      count: json['count'],
      extra: json['extra'],
    );
  }
}

class SearchPage<T> {
  final List<T> records;
  final int total;
  final int size;
  final int current;

  SearchPage({
    required this.records,
    required this.total,
    required this.size,
    required this.current,
  });

  factory SearchPage.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return SearchPage<T>(
      records: (json['records'] as List)
          .map((item) => fromJsonT(item as Map<String, dynamic>))
          .toList(),
      total: json['total'],
      size: json['size'],
      current: json['current'],
    );
  }
}
