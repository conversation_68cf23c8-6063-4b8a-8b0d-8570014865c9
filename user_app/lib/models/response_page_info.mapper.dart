// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'response_page_info.dart';

class ResponsePageInfoMapper extends ClassMapperBase<ResponsePageInfo> {
  ResponsePageInfoMapper._();

  static ResponsePageInfoMapper? _instance;
  static ResponsePageInfoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = ResponsePageInfoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'ResponsePageInfo';

  static num _$current(ResponsePageInfo v) => v.current;
  static const Field<ResponsePageInfo, num> _f$current =
      Field('current', _$current);
  static num _$size(ResponsePageInfo v) => v.size;
  static const Field<ResponsePageInfo, num> _f$size = Field('size', _$size);
  static num _$total(ResponsePageInfo v) => v.total;
  static const Field<ResponsePageInfo, num> _f$total = Field('total', _$total);
  static num _$pages(ResponsePageInfo v) => v.pages;
  static const Field<ResponsePageInfo, num> _f$pages = Field('pages', _$pages);

  @override
  final MappableFields<ResponsePageInfo> fields = const {
    #current: _f$current,
    #size: _f$size,
    #total: _f$total,
    #pages: _f$pages,
  };

  static ResponsePageInfo _instantiate(DecodingData data) {
    return ResponsePageInfo(
        current: data.dec(_f$current),
        size: data.dec(_f$size),
        total: data.dec(_f$total),
        pages: data.dec(_f$pages));
  }

  @override
  final Function instantiate = _instantiate;

  static ResponsePageInfo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<ResponsePageInfo>(map);
  }

  static ResponsePageInfo fromJson(String json) {
    return ensureInitialized().decodeJson<ResponsePageInfo>(json);
  }
}

mixin ResponsePageInfoMappable {
  String toJson() {
    return ResponsePageInfoMapper.ensureInitialized()
        .encodeJson<ResponsePageInfo>(this as ResponsePageInfo);
  }

  Map<String, dynamic> toMap() {
    return ResponsePageInfoMapper.ensureInitialized()
        .encodeMap<ResponsePageInfo>(this as ResponsePageInfo);
  }

  ResponsePageInfoCopyWith<ResponsePageInfo, ResponsePageInfo, ResponsePageInfo>
      get copyWith =>
          _ResponsePageInfoCopyWithImpl<ResponsePageInfo, ResponsePageInfo>(
              this as ResponsePageInfo, $identity, $identity);
  @override
  String toString() {
    return ResponsePageInfoMapper.ensureInitialized()
        .stringifyValue(this as ResponsePageInfo);
  }

  @override
  bool operator ==(Object other) {
    return ResponsePageInfoMapper.ensureInitialized()
        .equalsValue(this as ResponsePageInfo, other);
  }

  @override
  int get hashCode {
    return ResponsePageInfoMapper.ensureInitialized()
        .hashValue(this as ResponsePageInfo);
  }
}

extension ResponsePageInfoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, ResponsePageInfo, $Out> {
  ResponsePageInfoCopyWith<$R, ResponsePageInfo, $Out>
      get $asResponsePageInfo => $base
          .as((v, t, t2) => _ResponsePageInfoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class ResponsePageInfoCopyWith<$R, $In extends ResponsePageInfo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call({num? current, num? size, num? total, num? pages});
  ResponsePageInfoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _ResponsePageInfoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, ResponsePageInfo, $Out>
    implements ResponsePageInfoCopyWith<$R, ResponsePageInfo, $Out> {
  _ResponsePageInfoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<ResponsePageInfo> $mapper =
      ResponsePageInfoMapper.ensureInitialized();
  @override
  $R call({num? current, num? size, num? total, num? pages}) =>
      $apply(FieldCopyWithData({
        if (current != null) #current: current,
        if (size != null) #size: size,
        if (total != null) #total: total,
        if (pages != null) #pages: pages
      }));
  @override
  ResponsePageInfo $make(CopyWithData data) => ResponsePageInfo(
      current: data.get(#current, or: $value.current),
      size: data.get(#size, or: $value.size),
      total: data.get(#total, or: $value.total),
      pages: data.get(#pages, or: $value.pages));

  @override
  ResponsePageInfoCopyWith<$R2, ResponsePageInfo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _ResponsePageInfoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
