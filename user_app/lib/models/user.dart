import 'package:dart_mappable/dart_mappable.dart';

part 'user.mapper.dart';

@MappableClass()
class User with UserMappable {
  final int id;
  final String telephoneNumber;
  final String? password;
  final String title;
  final String name;
  final int gender;
  final String? avatarUrl;
  final String? username;
  final String? province;
  final String? city;
  final String? county;
  final String? introduce;
  final int? followCount; //粉丝数
  final int? attentionCount; //关注数
  final int? momentCount; //动态数
  final int? commentCount; //评论数
  final bool? isFollowing; //是否已关注该用户;

  const User({
    required this.id,
    required this.telephoneNumber,
    this.password,
    required this.title,
    required this.name,
    required this.gender,
    this.avatarUrl,
    this.username,
    this.province,
    this.city,
    this.county,
    this.introduce,
    this.followCount, //粉丝数
    this.attentionCount, //关注数
    this.momentCount, //动态数
    this.commentCount, //评论数
    this.isFollowing, //是否已关注该用户
  });

  static final fromMap = UserMapper.fromMap;
  static final fromJson = UserMapper.fromJson;
}
