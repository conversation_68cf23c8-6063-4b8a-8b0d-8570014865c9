import 'package:dart_mappable/dart_mappable.dart';

part 'fishing_spot_vo.mapper.dart';

@MappableClass()
class FishingSpotVo with FishingSpotVoMappable {
  final int id;
  final String name;
  final String? description;
  final double latitude;
  final double longitude;
  final String? address;
  final int userId;
  final String? userName;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const FishingSpotVo({
    required this.id,
    required this.name,
    this.description,
    required this.latitude,
    required this.longitude,
    this.address,
    required this.userId,
    this.userName,
    this.createdAt,
    this.updatedAt,
  });

  static final fromMap = FishingSpotVoMapper.fromMap;
  static final fromJson = FishingSpotVoMapper.fromJson;
}