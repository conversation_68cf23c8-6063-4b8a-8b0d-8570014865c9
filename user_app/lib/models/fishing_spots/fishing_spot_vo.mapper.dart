// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'fishing_spot_vo.dart';

class FishingSpotVoMapper extends ClassMapperBase<FishingSpotVo> {
  FishingSpotVoMapper._();

  static FishingSpotVoMapper? _instance;
  static FishingSpotVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = FishingSpotVoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'FishingSpotVo';

  static int _$id(FishingSpotVo v) => v.id;
  static const Field<FishingSpotVo, int> _f$id = Field('id', _$id);
  static String _$name(FishingSpotVo v) => v.name;
  static const Field<FishingSpotVo, String> _f$name = Field('name', _$name);
  static String? _$description(FishingSpotVo v) => v.description;
  static const Field<FishingSpotVo, String> _f$description =
      Field('description', _$description, opt: true);
  static double _$latitude(FishingSpotVo v) => v.latitude;
  static const Field<FishingSpotVo, double> _f$latitude =
      Field('latitude', _$latitude);
  static double _$longitude(FishingSpotVo v) => v.longitude;
  static const Field<FishingSpotVo, double> _f$longitude =
      Field('longitude', _$longitude);
  static String? _$address(FishingSpotVo v) => v.address;
  static const Field<FishingSpotVo, String> _f$address =
      Field('address', _$address, opt: true);
  static int _$userId(FishingSpotVo v) => v.userId;
  static const Field<FishingSpotVo, int> _f$userId =
      Field('userId', _$userId, key: r'user_id');
  static String? _$userName(FishingSpotVo v) => v.userName;
  static const Field<FishingSpotVo, String> _f$userName =
      Field('userName', _$userName, key: r'user_name', opt: true);
  static DateTime? _$createdAt(FishingSpotVo v) => v.createdAt;
  static const Field<FishingSpotVo, DateTime> _f$createdAt =
      Field('createdAt', _$createdAt, key: r'created_at', opt: true);
  static DateTime? _$updatedAt(FishingSpotVo v) => v.updatedAt;
  static const Field<FishingSpotVo, DateTime> _f$updatedAt =
      Field('updatedAt', _$updatedAt, key: r'updated_at', opt: true);

  @override
  final MappableFields<FishingSpotVo> fields = const {
    #id: _f$id,
    #name: _f$name,
    #description: _f$description,
    #latitude: _f$latitude,
    #longitude: _f$longitude,
    #address: _f$address,
    #userId: _f$userId,
    #userName: _f$userName,
    #createdAt: _f$createdAt,
    #updatedAt: _f$updatedAt,
  };

  static FishingSpotVo _instantiate(DecodingData data) {
    return FishingSpotVo(
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        description: data.dec(_f$description),
        latitude: data.dec(_f$latitude),
        longitude: data.dec(_f$longitude),
        address: data.dec(_f$address),
        userId: data.dec(_f$userId),
        userName: data.dec(_f$userName),
        createdAt: data.dec(_f$createdAt),
        updatedAt: data.dec(_f$updatedAt));
  }

  @override
  final Function instantiate = _instantiate;

  static FishingSpotVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<FishingSpotVo>(map);
  }

  static FishingSpotVo fromJson(String json) {
    return ensureInitialized().decodeJson<FishingSpotVo>(json);
  }
}

mixin FishingSpotVoMappable {
  String toJson() {
    return FishingSpotVoMapper.ensureInitialized()
        .encodeJson<FishingSpotVo>(this as FishingSpotVo);
  }

  Map<String, dynamic> toMap() {
    return FishingSpotVoMapper.ensureInitialized()
        .encodeMap<FishingSpotVo>(this as FishingSpotVo);
  }

  FishingSpotVoCopyWith<FishingSpotVo, FishingSpotVo, FishingSpotVo>
      get copyWith => _FishingSpotVoCopyWithImpl<FishingSpotVo, FishingSpotVo>(
          this as FishingSpotVo, $identity, $identity);
  @override
  String toString() {
    return FishingSpotVoMapper.ensureInitialized()
        .stringifyValue(this as FishingSpotVo);
  }

  @override
  bool operator ==(Object other) {
    return FishingSpotVoMapper.ensureInitialized()
        .equalsValue(this as FishingSpotVo, other);
  }

  @override
  int get hashCode {
    return FishingSpotVoMapper.ensureInitialized()
        .hashValue(this as FishingSpotVo);
  }
}

extension FishingSpotVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, FishingSpotVo, $Out> {
  FishingSpotVoCopyWith<$R, FishingSpotVo, $Out> get $asFishingSpotVo =>
      $base.as((v, t, t2) => _FishingSpotVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class FishingSpotVoCopyWith<$R, $In extends FishingSpotVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? id,
      String? name,
      String? description,
      double? latitude,
      double? longitude,
      String? address,
      int? userId,
      String? userName,
      DateTime? createdAt,
      DateTime? updatedAt});
  FishingSpotVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _FishingSpotVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, FishingSpotVo, $Out>
    implements FishingSpotVoCopyWith<$R, FishingSpotVo, $Out> {
  _FishingSpotVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<FishingSpotVo> $mapper =
      FishingSpotVoMapper.ensureInitialized();
  @override
  $R call(
          {int? id,
          String? name,
          Object? description = $none,
          double? latitude,
          double? longitude,
          Object? address = $none,
          int? userId,
          Object? userName = $none,
          Object? createdAt = $none,
          Object? updatedAt = $none}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (description != $none) #description: description,
        if (latitude != null) #latitude: latitude,
        if (longitude != null) #longitude: longitude,
        if (address != $none) #address: address,
        if (userId != null) #userId: userId,
        if (userName != $none) #userName: userName,
        if (createdAt != $none) #createdAt: createdAt,
        if (updatedAt != $none) #updatedAt: updatedAt
      }));
  @override
  FishingSpotVo $make(CopyWithData data) => FishingSpotVo(
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      description: data.get(#description, or: $value.description),
      latitude: data.get(#latitude, or: $value.latitude),
      longitude: data.get(#longitude, or: $value.longitude),
      address: data.get(#address, or: $value.address),
      userId: data.get(#userId, or: $value.userId),
      userName: data.get(#userName, or: $value.userName),
      createdAt: data.get(#createdAt, or: $value.createdAt),
      updatedAt: data.get(#updatedAt, or: $value.updatedAt));

  @override
  FishingSpotVoCopyWith<$R2, FishingSpotVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _FishingSpotVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
