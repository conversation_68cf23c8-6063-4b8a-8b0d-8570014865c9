// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'fishing_statistics.dart';

class FishingStatisticsMapper extends ClassMapperBase<FishingStatistics> {
  FishingStatisticsMapper._();

  static FishingStatisticsMapper? _instance;
  static FishingStatisticsMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = FishingStatisticsMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'FishingStatistics';

  static int _$totalCatch(FishingStatistics v) => v.totalCatch;
  static const Field<FishingStatistics, int> _f$totalCatch =
      Field('totalCatch', _$totalCatch, key: r'total_catch');
  static int _$totalTrips(FishingStatistics v) => v.totalTrips;
  static const Field<FishingStatistics, int> _f$totalTrips =
      Field('totalTrips', _$totalTrips, key: r'total_trips');
  static double _$totalWeight(FishingStatistics v) => v.totalWeight;
  static const Field<FishingStatistics, double> _f$totalWeight =
      Field('totalWeight', _$totalWeight, key: r'total_weight');
  static String _$timeRange(FishingStatistics v) => v.timeRange;
  static const Field<FishingStatistics, String> _f$timeRange =
      Field('timeRange', _$timeRange, key: r'time_range');
  static DateTime _$lastUpdated(FishingStatistics v) => v.lastUpdated;
  static const Field<FishingStatistics, DateTime> _f$lastUpdated =
      Field('lastUpdated', _$lastUpdated, key: r'last_updated');

  @override
  final MappableFields<FishingStatistics> fields = const {
    #totalCatch: _f$totalCatch,
    #totalTrips: _f$totalTrips,
    #totalWeight: _f$totalWeight,
    #timeRange: _f$timeRange,
    #lastUpdated: _f$lastUpdated,
  };

  static FishingStatistics _instantiate(DecodingData data) {
    return FishingStatistics(
        totalCatch: data.dec(_f$totalCatch),
        totalTrips: data.dec(_f$totalTrips),
        totalWeight: data.dec(_f$totalWeight),
        timeRange: data.dec(_f$timeRange),
        lastUpdated: data.dec(_f$lastUpdated));
  }

  @override
  final Function instantiate = _instantiate;

  static FishingStatistics fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<FishingStatistics>(map);
  }

  static FishingStatistics fromJson(String json) {
    return ensureInitialized().decodeJson<FishingStatistics>(json);
  }
}

mixin FishingStatisticsMappable {
  String toJson() {
    return FishingStatisticsMapper.ensureInitialized()
        .encodeJson<FishingStatistics>(this as FishingStatistics);
  }

  Map<String, dynamic> toMap() {
    return FishingStatisticsMapper.ensureInitialized()
        .encodeMap<FishingStatistics>(this as FishingStatistics);
  }

  FishingStatisticsCopyWith<FishingStatistics, FishingStatistics,
          FishingStatistics>
      get copyWith =>
          _FishingStatisticsCopyWithImpl<FishingStatistics, FishingStatistics>(
              this as FishingStatistics, $identity, $identity);
  @override
  String toString() {
    return FishingStatisticsMapper.ensureInitialized()
        .stringifyValue(this as FishingStatistics);
  }

  @override
  bool operator ==(Object other) {
    return FishingStatisticsMapper.ensureInitialized()
        .equalsValue(this as FishingStatistics, other);
  }

  @override
  int get hashCode {
    return FishingStatisticsMapper.ensureInitialized()
        .hashValue(this as FishingStatistics);
  }
}

extension FishingStatisticsValueCopy<$R, $Out>
    on ObjectCopyWith<$R, FishingStatistics, $Out> {
  FishingStatisticsCopyWith<$R, FishingStatistics, $Out>
      get $asFishingStatistics => $base
          .as((v, t, t2) => _FishingStatisticsCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class FishingStatisticsCopyWith<$R, $In extends FishingStatistics,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? totalCatch,
      int? totalTrips,
      double? totalWeight,
      String? timeRange,
      DateTime? lastUpdated});
  FishingStatisticsCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _FishingStatisticsCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, FishingStatistics, $Out>
    implements FishingStatisticsCopyWith<$R, FishingStatistics, $Out> {
  _FishingStatisticsCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<FishingStatistics> $mapper =
      FishingStatisticsMapper.ensureInitialized();
  @override
  $R call(
          {int? totalCatch,
          int? totalTrips,
          double? totalWeight,
          String? timeRange,
          DateTime? lastUpdated}) =>
      $apply(FieldCopyWithData({
        if (totalCatch != null) #totalCatch: totalCatch,
        if (totalTrips != null) #totalTrips: totalTrips,
        if (totalWeight != null) #totalWeight: totalWeight,
        if (timeRange != null) #timeRange: timeRange,
        if (lastUpdated != null) #lastUpdated: lastUpdated
      }));
  @override
  FishingStatistics $make(CopyWithData data) => FishingStatistics(
      totalCatch: data.get(#totalCatch, or: $value.totalCatch),
      totalTrips: data.get(#totalTrips, or: $value.totalTrips),
      totalWeight: data.get(#totalWeight, or: $value.totalWeight),
      timeRange: data.get(#timeRange, or: $value.timeRange),
      lastUpdated: data.get(#lastUpdated, or: $value.lastUpdated));

  @override
  FishingStatisticsCopyWith<$R2, FishingStatistics, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _FishingStatisticsCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class TripRecordMapper extends ClassMapperBase<TripRecord> {
  TripRecordMapper._();

  static TripRecordMapper? _instance;
  static TripRecordMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = TripRecordMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'TripRecord';

  static int _$id(TripRecord v) => v.id;
  static const Field<TripRecord, int> _f$id = Field('id', _$id);
  static String _$date(TripRecord v) => v.date;
  static const Field<TripRecord, String> _f$date = Field('date', _$date);
  static String _$location(TripRecord v) => v.location;
  static const Field<TripRecord, String> _f$location =
      Field('location', _$location);
  static String _$duration(TripRecord v) => v.duration;
  static const Field<TripRecord, String> _f$duration =
      Field('duration', _$duration);
  static int _$catchCount(TripRecord v) => v.catchCount;
  static const Field<TripRecord, int> _f$catchCount =
      Field('catchCount', _$catchCount, key: r'catch_count');
  static String _$result(TripRecord v) => v.result;
  static const Field<TripRecord, String> _f$result = Field('result', _$result);
  static double? _$weight(TripRecord v) => v.weight;
  static const Field<TripRecord, double> _f$weight =
      Field('weight', _$weight, opt: true);
  static List<String>? _$fishTypes(TripRecord v) => v.fishTypes;
  static const Field<TripRecord, List<String>> _f$fishTypes =
      Field('fishTypes', _$fishTypes, key: r'fish_types', opt: true);
  static String? _$notes(TripRecord v) => v.notes;
  static const Field<TripRecord, String> _f$notes =
      Field('notes', _$notes, opt: true);

  @override
  final MappableFields<TripRecord> fields = const {
    #id: _f$id,
    #date: _f$date,
    #location: _f$location,
    #duration: _f$duration,
    #catchCount: _f$catchCount,
    #result: _f$result,
    #weight: _f$weight,
    #fishTypes: _f$fishTypes,
    #notes: _f$notes,
  };

  static TripRecord _instantiate(DecodingData data) {
    return TripRecord(
        id: data.dec(_f$id),
        date: data.dec(_f$date),
        location: data.dec(_f$location),
        duration: data.dec(_f$duration),
        catchCount: data.dec(_f$catchCount),
        result: data.dec(_f$result),
        weight: data.dec(_f$weight),
        fishTypes: data.dec(_f$fishTypes),
        notes: data.dec(_f$notes));
  }

  @override
  final Function instantiate = _instantiate;

  static TripRecord fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<TripRecord>(map);
  }

  static TripRecord fromJson(String json) {
    return ensureInitialized().decodeJson<TripRecord>(json);
  }
}

mixin TripRecordMappable {
  String toJson() {
    return TripRecordMapper.ensureInitialized()
        .encodeJson<TripRecord>(this as TripRecord);
  }

  Map<String, dynamic> toMap() {
    return TripRecordMapper.ensureInitialized()
        .encodeMap<TripRecord>(this as TripRecord);
  }

  TripRecordCopyWith<TripRecord, TripRecord, TripRecord> get copyWith =>
      _TripRecordCopyWithImpl<TripRecord, TripRecord>(
          this as TripRecord, $identity, $identity);
  @override
  String toString() {
    return TripRecordMapper.ensureInitialized()
        .stringifyValue(this as TripRecord);
  }

  @override
  bool operator ==(Object other) {
    return TripRecordMapper.ensureInitialized()
        .equalsValue(this as TripRecord, other);
  }

  @override
  int get hashCode {
    return TripRecordMapper.ensureInitialized().hashValue(this as TripRecord);
  }
}

extension TripRecordValueCopy<$R, $Out>
    on ObjectCopyWith<$R, TripRecord, $Out> {
  TripRecordCopyWith<$R, TripRecord, $Out> get $asTripRecord =>
      $base.as((v, t, t2) => _TripRecordCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class TripRecordCopyWith<$R, $In extends TripRecord, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>? get fishTypes;
  $R call(
      {int? id,
      String? date,
      String? location,
      String? duration,
      int? catchCount,
      String? result,
      double? weight,
      List<String>? fishTypes,
      String? notes});
  TripRecordCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _TripRecordCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, TripRecord, $Out>
    implements TripRecordCopyWith<$R, TripRecord, $Out> {
  _TripRecordCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<TripRecord> $mapper =
      TripRecordMapper.ensureInitialized();
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>? get fishTypes =>
      $value.fishTypes != null
          ? ListCopyWith(
              $value.fishTypes!,
              (v, t) => ObjectCopyWith(v, $identity, t),
              (v) => call(fishTypes: v))
          : null;
  @override
  $R call(
          {int? id,
          String? date,
          String? location,
          String? duration,
          int? catchCount,
          String? result,
          Object? weight = $none,
          Object? fishTypes = $none,
          Object? notes = $none}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (date != null) #date: date,
        if (location != null) #location: location,
        if (duration != null) #duration: duration,
        if (catchCount != null) #catchCount: catchCount,
        if (result != null) #result: result,
        if (weight != $none) #weight: weight,
        if (fishTypes != $none) #fishTypes: fishTypes,
        if (notes != $none) #notes: notes
      }));
  @override
  TripRecord $make(CopyWithData data) => TripRecord(
      id: data.get(#id, or: $value.id),
      date: data.get(#date, or: $value.date),
      location: data.get(#location, or: $value.location),
      duration: data.get(#duration, or: $value.duration),
      catchCount: data.get(#catchCount, or: $value.catchCount),
      result: data.get(#result, or: $value.result),
      weight: data.get(#weight, or: $value.weight),
      fishTypes: data.get(#fishTypes, or: $value.fishTypes),
      notes: data.get(#notes, or: $value.notes));

  @override
  TripRecordCopyWith<$R2, TripRecord, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _TripRecordCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class EquipmentUsageMapper extends ClassMapperBase<EquipmentUsage> {
  EquipmentUsageMapper._();

  static EquipmentUsageMapper? _instance;
  static EquipmentUsageMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = EquipmentUsageMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'EquipmentUsage';

  static int _$id(EquipmentUsage v) => v.id;
  static const Field<EquipmentUsage, int> _f$id = Field('id', _$id);
  static String _$name(EquipmentUsage v) => v.name;
  static const Field<EquipmentUsage, String> _f$name = Field('name', _$name);
  static String _$category(EquipmentUsage v) => v.category;
  static const Field<EquipmentUsage, String> _f$category =
      Field('category', _$category);
  static double _$usagePercentage(EquipmentUsage v) => v.usagePercentage;
  static const Field<EquipmentUsage, double> _f$usagePercentage =
      Field('usagePercentage', _$usagePercentage, key: r'usage_percentage');
  static int _$usageCount(EquipmentUsage v) => v.usageCount;
  static const Field<EquipmentUsage, int> _f$usageCount =
      Field('usageCount', _$usageCount, key: r'usage_count');
  static DateTime _$lastUsed(EquipmentUsage v) => v.lastUsed;
  static const Field<EquipmentUsage, DateTime> _f$lastUsed =
      Field('lastUsed', _$lastUsed, key: r'last_used');

  @override
  final MappableFields<EquipmentUsage> fields = const {
    #id: _f$id,
    #name: _f$name,
    #category: _f$category,
    #usagePercentage: _f$usagePercentage,
    #usageCount: _f$usageCount,
    #lastUsed: _f$lastUsed,
  };

  static EquipmentUsage _instantiate(DecodingData data) {
    return EquipmentUsage(
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        category: data.dec(_f$category),
        usagePercentage: data.dec(_f$usagePercentage),
        usageCount: data.dec(_f$usageCount),
        lastUsed: data.dec(_f$lastUsed));
  }

  @override
  final Function instantiate = _instantiate;

  static EquipmentUsage fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<EquipmentUsage>(map);
  }

  static EquipmentUsage fromJson(String json) {
    return ensureInitialized().decodeJson<EquipmentUsage>(json);
  }
}

mixin EquipmentUsageMappable {
  String toJson() {
    return EquipmentUsageMapper.ensureInitialized()
        .encodeJson<EquipmentUsage>(this as EquipmentUsage);
  }

  Map<String, dynamic> toMap() {
    return EquipmentUsageMapper.ensureInitialized()
        .encodeMap<EquipmentUsage>(this as EquipmentUsage);
  }

  EquipmentUsageCopyWith<EquipmentUsage, EquipmentUsage, EquipmentUsage>
      get copyWith =>
          _EquipmentUsageCopyWithImpl<EquipmentUsage, EquipmentUsage>(
              this as EquipmentUsage, $identity, $identity);
  @override
  String toString() {
    return EquipmentUsageMapper.ensureInitialized()
        .stringifyValue(this as EquipmentUsage);
  }

  @override
  bool operator ==(Object other) {
    return EquipmentUsageMapper.ensureInitialized()
        .equalsValue(this as EquipmentUsage, other);
  }

  @override
  int get hashCode {
    return EquipmentUsageMapper.ensureInitialized()
        .hashValue(this as EquipmentUsage);
  }
}

extension EquipmentUsageValueCopy<$R, $Out>
    on ObjectCopyWith<$R, EquipmentUsage, $Out> {
  EquipmentUsageCopyWith<$R, EquipmentUsage, $Out> get $asEquipmentUsage =>
      $base.as((v, t, t2) => _EquipmentUsageCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class EquipmentUsageCopyWith<$R, $In extends EquipmentUsage, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? id,
      String? name,
      String? category,
      double? usagePercentage,
      int? usageCount,
      DateTime? lastUsed});
  EquipmentUsageCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _EquipmentUsageCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, EquipmentUsage, $Out>
    implements EquipmentUsageCopyWith<$R, EquipmentUsage, $Out> {
  _EquipmentUsageCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<EquipmentUsage> $mapper =
      EquipmentUsageMapper.ensureInitialized();
  @override
  $R call(
          {int? id,
          String? name,
          String? category,
          double? usagePercentage,
          int? usageCount,
          DateTime? lastUsed}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (category != null) #category: category,
        if (usagePercentage != null) #usagePercentage: usagePercentage,
        if (usageCount != null) #usageCount: usageCount,
        if (lastUsed != null) #lastUsed: lastUsed
      }));
  @override
  EquipmentUsage $make(CopyWithData data) => EquipmentUsage(
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      category: data.get(#category, or: $value.category),
      usagePercentage: data.get(#usagePercentage, or: $value.usagePercentage),
      usageCount: data.get(#usageCount, or: $value.usageCount),
      lastUsed: data.get(#lastUsed, or: $value.lastUsed));

  @override
  EquipmentUsageCopyWith<$R2, EquipmentUsage, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _EquipmentUsageCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class CatchDataMapper extends ClassMapperBase<CatchData> {
  CatchDataMapper._();

  static CatchDataMapper? _instance;
  static CatchDataMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = CatchDataMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'CatchData';

  static String _$date(CatchData v) => v.date;
  static const Field<CatchData, String> _f$date = Field('date', _$date);
  static int _$count(CatchData v) => v.count;
  static const Field<CatchData, int> _f$count = Field('count', _$count);
  static double _$weight(CatchData v) => v.weight;
  static const Field<CatchData, double> _f$weight = Field('weight', _$weight);

  @override
  final MappableFields<CatchData> fields = const {
    #date: _f$date,
    #count: _f$count,
    #weight: _f$weight,
  };

  static CatchData _instantiate(DecodingData data) {
    return CatchData(
        date: data.dec(_f$date),
        count: data.dec(_f$count),
        weight: data.dec(_f$weight));
  }

  @override
  final Function instantiate = _instantiate;

  static CatchData fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<CatchData>(map);
  }

  static CatchData fromJson(String json) {
    return ensureInitialized().decodeJson<CatchData>(json);
  }
}

mixin CatchDataMappable {
  String toJson() {
    return CatchDataMapper.ensureInitialized()
        .encodeJson<CatchData>(this as CatchData);
  }

  Map<String, dynamic> toMap() {
    return CatchDataMapper.ensureInitialized()
        .encodeMap<CatchData>(this as CatchData);
  }

  CatchDataCopyWith<CatchData, CatchData, CatchData> get copyWith =>
      _CatchDataCopyWithImpl<CatchData, CatchData>(
          this as CatchData, $identity, $identity);
  @override
  String toString() {
    return CatchDataMapper.ensureInitialized()
        .stringifyValue(this as CatchData);
  }

  @override
  bool operator ==(Object other) {
    return CatchDataMapper.ensureInitialized()
        .equalsValue(this as CatchData, other);
  }

  @override
  int get hashCode {
    return CatchDataMapper.ensureInitialized().hashValue(this as CatchData);
  }
}

extension CatchDataValueCopy<$R, $Out> on ObjectCopyWith<$R, CatchData, $Out> {
  CatchDataCopyWith<$R, CatchData, $Out> get $asCatchData =>
      $base.as((v, t, t2) => _CatchDataCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class CatchDataCopyWith<$R, $In extends CatchData, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call({String? date, int? count, double? weight});
  CatchDataCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _CatchDataCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, CatchData, $Out>
    implements CatchDataCopyWith<$R, CatchData, $Out> {
  _CatchDataCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<CatchData> $mapper =
      CatchDataMapper.ensureInitialized();
  @override
  $R call({String? date, int? count, double? weight}) =>
      $apply(FieldCopyWithData({
        if (date != null) #date: date,
        if (count != null) #count: count,
        if (weight != null) #weight: weight
      }));
  @override
  CatchData $make(CopyWithData data) => CatchData(
      date: data.get(#date, or: $value.date),
      count: data.get(#count, or: $value.count),
      weight: data.get(#weight, or: $value.weight));

  @override
  CatchDataCopyWith<$R2, CatchData, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _CatchDataCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class FishSpeciesDataMapper extends ClassMapperBase<FishSpeciesData> {
  FishSpeciesDataMapper._();

  static FishSpeciesDataMapper? _instance;
  static FishSpeciesDataMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = FishSpeciesDataMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'FishSpeciesData';

  static String _$species(FishSpeciesData v) => v.species;
  static const Field<FishSpeciesData, String> _f$species =
      Field('species', _$species);
  static int _$count(FishSpeciesData v) => v.count;
  static const Field<FishSpeciesData, int> _f$count = Field('count', _$count);
  static double _$percentage(FishSpeciesData v) => v.percentage;
  static const Field<FishSpeciesData, double> _f$percentage =
      Field('percentage', _$percentage);
  static String _$color(FishSpeciesData v) => v.color;
  static const Field<FishSpeciesData, String> _f$color =
      Field('color', _$color);

  @override
  final MappableFields<FishSpeciesData> fields = const {
    #species: _f$species,
    #count: _f$count,
    #percentage: _f$percentage,
    #color: _f$color,
  };

  static FishSpeciesData _instantiate(DecodingData data) {
    return FishSpeciesData(
        species: data.dec(_f$species),
        count: data.dec(_f$count),
        percentage: data.dec(_f$percentage),
        color: data.dec(_f$color));
  }

  @override
  final Function instantiate = _instantiate;

  static FishSpeciesData fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<FishSpeciesData>(map);
  }

  static FishSpeciesData fromJson(String json) {
    return ensureInitialized().decodeJson<FishSpeciesData>(json);
  }
}

mixin FishSpeciesDataMappable {
  String toJson() {
    return FishSpeciesDataMapper.ensureInitialized()
        .encodeJson<FishSpeciesData>(this as FishSpeciesData);
  }

  Map<String, dynamic> toMap() {
    return FishSpeciesDataMapper.ensureInitialized()
        .encodeMap<FishSpeciesData>(this as FishSpeciesData);
  }

  FishSpeciesDataCopyWith<FishSpeciesData, FishSpeciesData, FishSpeciesData>
      get copyWith =>
          _FishSpeciesDataCopyWithImpl<FishSpeciesData, FishSpeciesData>(
              this as FishSpeciesData, $identity, $identity);
  @override
  String toString() {
    return FishSpeciesDataMapper.ensureInitialized()
        .stringifyValue(this as FishSpeciesData);
  }

  @override
  bool operator ==(Object other) {
    return FishSpeciesDataMapper.ensureInitialized()
        .equalsValue(this as FishSpeciesData, other);
  }

  @override
  int get hashCode {
    return FishSpeciesDataMapper.ensureInitialized()
        .hashValue(this as FishSpeciesData);
  }
}

extension FishSpeciesDataValueCopy<$R, $Out>
    on ObjectCopyWith<$R, FishSpeciesData, $Out> {
  FishSpeciesDataCopyWith<$R, FishSpeciesData, $Out> get $asFishSpeciesData =>
      $base.as((v, t, t2) => _FishSpeciesDataCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class FishSpeciesDataCopyWith<$R, $In extends FishSpeciesData, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call({String? species, int? count, double? percentage, String? color});
  FishSpeciesDataCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _FishSpeciesDataCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, FishSpeciesData, $Out>
    implements FishSpeciesDataCopyWith<$R, FishSpeciesData, $Out> {
  _FishSpeciesDataCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<FishSpeciesData> $mapper =
      FishSpeciesDataMapper.ensureInitialized();
  @override
  $R call({String? species, int? count, double? percentage, String? color}) =>
      $apply(FieldCopyWithData({
        if (species != null) #species: species,
        if (count != null) #count: count,
        if (percentage != null) #percentage: percentage,
        if (color != null) #color: color
      }));
  @override
  FishSpeciesData $make(CopyWithData data) => FishSpeciesData(
      species: data.get(#species, or: $value.species),
      count: data.get(#count, or: $value.count),
      percentage: data.get(#percentage, or: $value.percentage),
      color: data.get(#color, or: $value.color));

  @override
  FishSpeciesDataCopyWith<$R2, FishSpeciesData, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _FishSpeciesDataCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class SpotStatisticsMapper extends ClassMapperBase<SpotStatistics> {
  SpotStatisticsMapper._();

  static SpotStatisticsMapper? _instance;
  static SpotStatisticsMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotStatisticsMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'SpotStatistics';

  static int _$id(SpotStatistics v) => v.id;
  static const Field<SpotStatistics, int> _f$id = Field('id', _$id);
  static String _$name(SpotStatistics v) => v.name;
  static const Field<SpotStatistics, String> _f$name = Field('name', _$name);
  static int _$tripCount(SpotStatistics v) => v.tripCount;
  static const Field<SpotStatistics, int> _f$tripCount =
      Field('tripCount', _$tripCount, key: r'trip_count');
  static int _$catchCount(SpotStatistics v) => v.catchCount;
  static const Field<SpotStatistics, int> _f$catchCount =
      Field('catchCount', _$catchCount, key: r'catch_count');
  static double _$successRate(SpotStatistics v) => v.successRate;
  static const Field<SpotStatistics, double> _f$successRate =
      Field('successRate', _$successRate, key: r'success_rate');
  static String? _$lastVisit(SpotStatistics v) => v.lastVisit;
  static const Field<SpotStatistics, String> _f$lastVisit =
      Field('lastVisit', _$lastVisit, key: r'last_visit', opt: true);

  @override
  final MappableFields<SpotStatistics> fields = const {
    #id: _f$id,
    #name: _f$name,
    #tripCount: _f$tripCount,
    #catchCount: _f$catchCount,
    #successRate: _f$successRate,
    #lastVisit: _f$lastVisit,
  };

  static SpotStatistics _instantiate(DecodingData data) {
    return SpotStatistics(
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        tripCount: data.dec(_f$tripCount),
        catchCount: data.dec(_f$catchCount),
        successRate: data.dec(_f$successRate),
        lastVisit: data.dec(_f$lastVisit));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotStatistics fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotStatistics>(map);
  }

  static SpotStatistics fromJson(String json) {
    return ensureInitialized().decodeJson<SpotStatistics>(json);
  }
}

mixin SpotStatisticsMappable {
  String toJson() {
    return SpotStatisticsMapper.ensureInitialized()
        .encodeJson<SpotStatistics>(this as SpotStatistics);
  }

  Map<String, dynamic> toMap() {
    return SpotStatisticsMapper.ensureInitialized()
        .encodeMap<SpotStatistics>(this as SpotStatistics);
  }

  SpotStatisticsCopyWith<SpotStatistics, SpotStatistics, SpotStatistics>
      get copyWith =>
          _SpotStatisticsCopyWithImpl<SpotStatistics, SpotStatistics>(
              this as SpotStatistics, $identity, $identity);
  @override
  String toString() {
    return SpotStatisticsMapper.ensureInitialized()
        .stringifyValue(this as SpotStatistics);
  }

  @override
  bool operator ==(Object other) {
    return SpotStatisticsMapper.ensureInitialized()
        .equalsValue(this as SpotStatistics, other);
  }

  @override
  int get hashCode {
    return SpotStatisticsMapper.ensureInitialized()
        .hashValue(this as SpotStatistics);
  }
}

extension SpotStatisticsValueCopy<$R, $Out>
    on ObjectCopyWith<$R, SpotStatistics, $Out> {
  SpotStatisticsCopyWith<$R, SpotStatistics, $Out> get $asSpotStatistics =>
      $base.as((v, t, t2) => _SpotStatisticsCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotStatisticsCopyWith<$R, $In extends SpotStatistics, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? id,
      String? name,
      int? tripCount,
      int? catchCount,
      double? successRate,
      String? lastVisit});
  SpotStatisticsCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _SpotStatisticsCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotStatistics, $Out>
    implements SpotStatisticsCopyWith<$R, SpotStatistics, $Out> {
  _SpotStatisticsCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotStatistics> $mapper =
      SpotStatisticsMapper.ensureInitialized();
  @override
  $R call(
          {int? id,
          String? name,
          int? tripCount,
          int? catchCount,
          double? successRate,
          Object? lastVisit = $none}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (tripCount != null) #tripCount: tripCount,
        if (catchCount != null) #catchCount: catchCount,
        if (successRate != null) #successRate: successRate,
        if (lastVisit != $none) #lastVisit: lastVisit
      }));
  @override
  SpotStatistics $make(CopyWithData data) => SpotStatistics(
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      tripCount: data.get(#tripCount, or: $value.tripCount),
      catchCount: data.get(#catchCount, or: $value.catchCount),
      successRate: data.get(#successRate, or: $value.successRate),
      lastVisit: data.get(#lastVisit, or: $value.lastVisit));

  @override
  SpotStatisticsCopyWith<$R2, SpotStatistics, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _SpotStatisticsCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
