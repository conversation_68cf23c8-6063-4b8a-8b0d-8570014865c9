import 'package:dart_mappable/dart_mappable.dart';

part 'fishing_statistics.mapper.dart';

@MappableClass()
class FishingStatistics with FishingStatisticsMappable {
  final int totalCatch;
  final int totalTrips;
  final double totalWeight;
  final String timeRange;
  final DateTime lastUpdated;

  const FishingStatistics({
    required this.totalCatch,
    required this.totalTrips,
    required this.totalWeight,
    required this.timeRange,
    required this.lastUpdated,
  });

  static final fromMap = FishingStatisticsMapper.fromMap;
  static final fromJson = FishingStatisticsMapper.fromJson;
}

@MappableClass()
class TripRecord with TripRecordMappable {
  final int id;
  final String date;
  final String location;
  final String duration;
  final int catchCount;
  final String result; // '优秀', '良好', '一般'
  final double? weight;
  final List<String>? fishTypes;
  final String? notes;

  const TripRecord({
    required this.id,
    required this.date,
    required this.location,
    required this.duration,
    required this.catchCount,
    required this.result,
    this.weight,
    this.fishTypes,
    this.notes,
  });

  static final fromMap = TripRecordMapper.fromMap;
  static final fromJson = TripRecordMapper.fromJson;
}

@MappableClass()
class EquipmentUsage with EquipmentUsageMappable {
  final int id;
  final String name;
  final String category;
  final double usagePercentage;
  final int usageCount;
  final DateTime lastUsed;

  const EquipmentUsage({
    required this.id,
    required this.name,
    required this.category,
    required this.usagePercentage,
    required this.usageCount,
    required this.lastUsed,
  });

  static final fromMap = EquipmentUsageMapper.fromMap;
  static final fromJson = EquipmentUsageMapper.fromJson;
}

@MappableClass()
class CatchData with CatchDataMappable {
  final String date;
  final int count;
  final double weight;

  const CatchData({
    required this.date,
    required this.count,
    required this.weight,
  });

  static final fromMap = CatchDataMapper.fromMap;
  static final fromJson = CatchDataMapper.fromJson;
}

@MappableClass()
class FishSpeciesData with FishSpeciesDataMappable {
  final String species;
  final int count;
  final double percentage;
  final String color;

  const FishSpeciesData({
    required this.species,
    required this.count,
    required this.percentage,
    required this.color,
  });

  static final fromMap = FishSpeciesDataMapper.fromMap;
  static final fromJson = FishSpeciesDataMapper.fromJson;
}

@MappableClass()
class SpotStatistics with SpotStatisticsMappable {
  final int id;
  final String name;
  final int tripCount;
  final int catchCount;
  final double successRate;
  final String? lastVisit;

  const SpotStatistics({
    required this.id,
    required this.name,
    required this.tripCount,
    required this.catchCount,
    required this.successRate,
    this.lastVisit,
  });

  static final fromMap = SpotStatisticsMapper.fromMap;
  static final fromJson = SpotStatisticsMapper.fromJson;
}