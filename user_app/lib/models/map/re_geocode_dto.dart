import 'package:dart_mappable/dart_mappable.dart';

part 're_geocode_dto.mapper.dart';

@MappableClass()
class ReGeocodeDto with ReGeocodeDtoMappable {
  String country;
  String province;
  String city;
  String district;
  String township;
  String street;
  String streetNumber;
  String cityCode;
  String adCode;
  String formattedAddress;

  ReGeocodeDto({
    required this.country,
    required this.province,
    required this.city,
    required this.district,
    required this.township,
    required this.street,
    required this.streetNumber,
    required this.cityCode,
    required this.adCode,
    required this.formattedAddress,
  });

  static final fromMap = ReGeocodeDtoMapper.fromMap;
}
