// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 're_geocode_dto.dart';

class ReGeocodeDtoMapper extends ClassMapperBase<ReGeocodeDto> {
  ReGeocodeDtoMapper._();

  static ReGeocodeDtoMapper? _instance;
  static ReGeocodeDtoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = ReGeocodeDtoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'ReGeocodeDto';

  static String _$country(ReGeocodeDto v) => v.country;
  static const Field<ReGeocodeDto, String> _f$country =
      Field('country', _$country);
  static String _$province(ReGeocodeDto v) => v.province;
  static const Field<ReGeocodeDto, String> _f$province =
      Field('province', _$province);
  static String _$city(ReGeocodeDto v) => v.city;
  static const Field<ReGeocodeDto, String> _f$city = Field('city', _$city);
  static String _$district(ReGeocodeDto v) => v.district;
  static const Field<ReGeocodeDto, String> _f$district =
      Field('district', _$district);
  static String _$township(ReGeocodeDto v) => v.township;
  static const Field<ReGeocodeDto, String> _f$township =
      Field('township', _$township);
  static String _$street(ReGeocodeDto v) => v.street;
  static const Field<ReGeocodeDto, String> _f$street =
      Field('street', _$street);
  static String _$streetNumber(ReGeocodeDto v) => v.streetNumber;
  static const Field<ReGeocodeDto, String> _f$streetNumber =
      Field('streetNumber', _$streetNumber, key: r'street_number');
  static String _$cityCode(ReGeocodeDto v) => v.cityCode;
  static const Field<ReGeocodeDto, String> _f$cityCode =
      Field('cityCode', _$cityCode, key: r'city_code');
  static String _$adCode(ReGeocodeDto v) => v.adCode;
  static const Field<ReGeocodeDto, String> _f$adCode =
      Field('adCode', _$adCode, key: r'ad_code');
  static String _$formattedAddress(ReGeocodeDto v) => v.formattedAddress;
  static const Field<ReGeocodeDto, String> _f$formattedAddress =
      Field('formattedAddress', _$formattedAddress, key: r'formatted_address');

  @override
  final MappableFields<ReGeocodeDto> fields = const {
    #country: _f$country,
    #province: _f$province,
    #city: _f$city,
    #district: _f$district,
    #township: _f$township,
    #street: _f$street,
    #streetNumber: _f$streetNumber,
    #cityCode: _f$cityCode,
    #adCode: _f$adCode,
    #formattedAddress: _f$formattedAddress,
  };

  static ReGeocodeDto _instantiate(DecodingData data) {
    return ReGeocodeDto(
        country: data.dec(_f$country),
        province: data.dec(_f$province),
        city: data.dec(_f$city),
        district: data.dec(_f$district),
        township: data.dec(_f$township),
        street: data.dec(_f$street),
        streetNumber: data.dec(_f$streetNumber),
        cityCode: data.dec(_f$cityCode),
        adCode: data.dec(_f$adCode),
        formattedAddress: data.dec(_f$formattedAddress));
  }

  @override
  final Function instantiate = _instantiate;

  static ReGeocodeDto fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<ReGeocodeDto>(map);
  }

  static ReGeocodeDto fromJson(String json) {
    return ensureInitialized().decodeJson<ReGeocodeDto>(json);
  }
}

mixin ReGeocodeDtoMappable {
  String toJson() {
    return ReGeocodeDtoMapper.ensureInitialized()
        .encodeJson<ReGeocodeDto>(this as ReGeocodeDto);
  }

  Map<String, dynamic> toMap() {
    return ReGeocodeDtoMapper.ensureInitialized()
        .encodeMap<ReGeocodeDto>(this as ReGeocodeDto);
  }

  ReGeocodeDtoCopyWith<ReGeocodeDto, ReGeocodeDto, ReGeocodeDto> get copyWith =>
      _ReGeocodeDtoCopyWithImpl<ReGeocodeDto, ReGeocodeDto>(
          this as ReGeocodeDto, $identity, $identity);
  @override
  String toString() {
    return ReGeocodeDtoMapper.ensureInitialized()
        .stringifyValue(this as ReGeocodeDto);
  }

  @override
  bool operator ==(Object other) {
    return ReGeocodeDtoMapper.ensureInitialized()
        .equalsValue(this as ReGeocodeDto, other);
  }

  @override
  int get hashCode {
    return ReGeocodeDtoMapper.ensureInitialized()
        .hashValue(this as ReGeocodeDto);
  }
}

extension ReGeocodeDtoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, ReGeocodeDto, $Out> {
  ReGeocodeDtoCopyWith<$R, ReGeocodeDto, $Out> get $asReGeocodeDto =>
      $base.as((v, t, t2) => _ReGeocodeDtoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class ReGeocodeDtoCopyWith<$R, $In extends ReGeocodeDto, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {String? country,
      String? province,
      String? city,
      String? district,
      String? township,
      String? street,
      String? streetNumber,
      String? cityCode,
      String? adCode,
      String? formattedAddress});
  ReGeocodeDtoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _ReGeocodeDtoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, ReGeocodeDto, $Out>
    implements ReGeocodeDtoCopyWith<$R, ReGeocodeDto, $Out> {
  _ReGeocodeDtoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<ReGeocodeDto> $mapper =
      ReGeocodeDtoMapper.ensureInitialized();
  @override
  $R call(
          {String? country,
          String? province,
          String? city,
          String? district,
          String? township,
          String? street,
          String? streetNumber,
          String? cityCode,
          String? adCode,
          String? formattedAddress}) =>
      $apply(FieldCopyWithData({
        if (country != null) #country: country,
        if (province != null) #province: province,
        if (city != null) #city: city,
        if (district != null) #district: district,
        if (township != null) #township: township,
        if (street != null) #street: street,
        if (streetNumber != null) #streetNumber: streetNumber,
        if (cityCode != null) #cityCode: cityCode,
        if (adCode != null) #adCode: adCode,
        if (formattedAddress != null) #formattedAddress: formattedAddress
      }));
  @override
  ReGeocodeDto $make(CopyWithData data) => ReGeocodeDto(
      country: data.get(#country, or: $value.country),
      province: data.get(#province, or: $value.province),
      city: data.get(#city, or: $value.city),
      district: data.get(#district, or: $value.district),
      township: data.get(#township, or: $value.township),
      street: data.get(#street, or: $value.street),
      streetNumber: data.get(#streetNumber, or: $value.streetNumber),
      cityCode: data.get(#cityCode, or: $value.cityCode),
      adCode: data.get(#adCode, or: $value.adCode),
      formattedAddress:
          data.get(#formattedAddress, or: $value.formattedAddress));

  @override
  ReGeocodeDtoCopyWith<$R2, ReGeocodeDto, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _ReGeocodeDtoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
