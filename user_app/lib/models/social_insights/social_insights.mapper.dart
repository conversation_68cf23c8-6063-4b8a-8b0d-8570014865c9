// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'social_insights.dart';

class SocialInsightsMapper extends ClassMapperBase<SocialInsights> {
  SocialInsightsMapper._();

  static SocialInsightsMapper? _instance;
  static SocialInsightsMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SocialInsightsMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'SocialInsights';

  static double _$interactionRate(SocialInsights v) => v.interactionRate;
  static const Field<SocialInsights, double> _f$interactionRate =
      Field('interactionRate', _$interactionRate, key: r'interaction_rate');
  static int _$newFollowers(SocialInsights v) => v.newFollowers;
  static const Field<SocialInsights, int> _f$newFollowers =
      Field('newFollowers', _$newFollowers, key: r'new_followers');
  static String _$interactionTrend(SocialInsights v) => v.interactionTrend;
  static const Field<SocialInsights, String> _f$interactionTrend =
      Field('interactionTrend', _$interactionTrend, key: r'interaction_trend');
  static String _$followersTrend(SocialInsights v) => v.followersTrend;
  static const Field<SocialInsights, String> _f$followersTrend =
      Field('followersTrend', _$followersTrend, key: r'followers_trend');
  static List<int> _$weeklyActivity(SocialInsights v) => v.weeklyActivity;
  static const Field<SocialInsights, List<int>> _f$weeklyActivity =
      Field('weeklyActivity', _$weeklyActivity, key: r'weekly_activity');
  static DateTime _$lastUpdated(SocialInsights v) => v.lastUpdated;
  static const Field<SocialInsights, DateTime> _f$lastUpdated =
      Field('lastUpdated', _$lastUpdated, key: r'last_updated');

  @override
  final MappableFields<SocialInsights> fields = const {
    #interactionRate: _f$interactionRate,
    #newFollowers: _f$newFollowers,
    #interactionTrend: _f$interactionTrend,
    #followersTrend: _f$followersTrend,
    #weeklyActivity: _f$weeklyActivity,
    #lastUpdated: _f$lastUpdated,
  };

  static SocialInsights _instantiate(DecodingData data) {
    return SocialInsights(
        interactionRate: data.dec(_f$interactionRate),
        newFollowers: data.dec(_f$newFollowers),
        interactionTrend: data.dec(_f$interactionTrend),
        followersTrend: data.dec(_f$followersTrend),
        weeklyActivity: data.dec(_f$weeklyActivity),
        lastUpdated: data.dec(_f$lastUpdated));
  }

  @override
  final Function instantiate = _instantiate;

  static SocialInsights fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SocialInsights>(map);
  }

  static SocialInsights fromJson(String json) {
    return ensureInitialized().decodeJson<SocialInsights>(json);
  }
}

mixin SocialInsightsMappable {
  String toJson() {
    return SocialInsightsMapper.ensureInitialized()
        .encodeJson<SocialInsights>(this as SocialInsights);
  }

  Map<String, dynamic> toMap() {
    return SocialInsightsMapper.ensureInitialized()
        .encodeMap<SocialInsights>(this as SocialInsights);
  }

  SocialInsightsCopyWith<SocialInsights, SocialInsights, SocialInsights>
      get copyWith =>
          _SocialInsightsCopyWithImpl<SocialInsights, SocialInsights>(
              this as SocialInsights, $identity, $identity);
  @override
  String toString() {
    return SocialInsightsMapper.ensureInitialized()
        .stringifyValue(this as SocialInsights);
  }

  @override
  bool operator ==(Object other) {
    return SocialInsightsMapper.ensureInitialized()
        .equalsValue(this as SocialInsights, other);
  }

  @override
  int get hashCode {
    return SocialInsightsMapper.ensureInitialized()
        .hashValue(this as SocialInsights);
  }
}

extension SocialInsightsValueCopy<$R, $Out>
    on ObjectCopyWith<$R, SocialInsights, $Out> {
  SocialInsightsCopyWith<$R, SocialInsights, $Out> get $asSocialInsights =>
      $base.as((v, t, t2) => _SocialInsightsCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SocialInsightsCopyWith<$R, $In extends SocialInsights, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, int, ObjectCopyWith<$R, int, int>> get weeklyActivity;
  $R call(
      {double? interactionRate,
      int? newFollowers,
      String? interactionTrend,
      String? followersTrend,
      List<int>? weeklyActivity,
      DateTime? lastUpdated});
  SocialInsightsCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _SocialInsightsCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SocialInsights, $Out>
    implements SocialInsightsCopyWith<$R, SocialInsights, $Out> {
  _SocialInsightsCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SocialInsights> $mapper =
      SocialInsightsMapper.ensureInitialized();
  @override
  ListCopyWith<$R, int, ObjectCopyWith<$R, int, int>> get weeklyActivity =>
      ListCopyWith(
          $value.weeklyActivity,
          (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(weeklyActivity: v));
  @override
  $R call(
          {double? interactionRate,
          int? newFollowers,
          String? interactionTrend,
          String? followersTrend,
          List<int>? weeklyActivity,
          DateTime? lastUpdated}) =>
      $apply(FieldCopyWithData({
        if (interactionRate != null) #interactionRate: interactionRate,
        if (newFollowers != null) #newFollowers: newFollowers,
        if (interactionTrend != null) #interactionTrend: interactionTrend,
        if (followersTrend != null) #followersTrend: followersTrend,
        if (weeklyActivity != null) #weeklyActivity: weeklyActivity,
        if (lastUpdated != null) #lastUpdated: lastUpdated
      }));
  @override
  SocialInsights $make(CopyWithData data) => SocialInsights(
      interactionRate: data.get(#interactionRate, or: $value.interactionRate),
      newFollowers: data.get(#newFollowers, or: $value.newFollowers),
      interactionTrend:
          data.get(#interactionTrend, or: $value.interactionTrend),
      followersTrend: data.get(#followersTrend, or: $value.followersTrend),
      weeklyActivity: data.get(#weeklyActivity, or: $value.weeklyActivity),
      lastUpdated: data.get(#lastUpdated, or: $value.lastUpdated));

  @override
  SocialInsightsCopyWith<$R2, SocialInsights, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _SocialInsightsCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
