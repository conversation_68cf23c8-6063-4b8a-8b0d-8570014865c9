import 'package:dart_mappable/dart_mappable.dart';

part 'social_insights.mapper.dart';

@MappableClass()
class SocialInsights with SocialInsightsMappable {
  final double interactionRate;
  final int newFollowers;
  final String interactionTrend;
  final String followersTrend;
  final List<int> weeklyActivity;
  final DateTime lastUpdated;

  SocialInsights({
    required this.interactionRate,
    required this.newFollowers,
    required this.interactionTrend,
    required this.followersTrend,
    required this.weeklyActivity,
    required this.lastUpdated,
  });

  static final fromMap = SocialInsightsMapper.fromMap;
}