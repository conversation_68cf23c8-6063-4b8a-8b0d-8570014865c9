// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'fishing_plan.dart';

class FishingPlanMapper extends ClassMapperBase<FishingPlan> {
  FishingPlanMapper._();

  static FishingPlanMapper? _instance;
  static FishingPlanMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = FishingPlanMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'FishingPlan';

  static int _$id(FishingPlan v) => v.id;
  static const Field<FishingPlan, int> _f$id = Field('id', _$id);
  static String _$title(FishingPlan v) => v.title;
  static const Field<FishingPlan, String> _f$title = Field('title', _$title);
  static String _$location(FishingPlan v) => v.location;
  static const Field<FishingPlan, String> _f$location =
      Field('location', _$location);
  static DateTime _$date(FishingPlan v) => v.date;
  static const Field<FishingPlan, DateTime> _f$date = Field('date', _$date);
  static String _$time(FishingPlan v) => v.time;
  static const Field<FishingPlan, String> _f$time = Field('time', _$time);
  static int _$participants(FishingPlan v) => v.participants;
  static const Field<FishingPlan, int> _f$participants =
      Field('participants', _$participants);
  static int _$maxParticipants(FishingPlan v) => v.maxParticipants;
  static const Field<FishingPlan, int> _f$maxParticipants =
      Field('maxParticipants', _$maxParticipants, key: r'max_participants');
  static String _$weather(FishingPlan v) => v.weather;
  static const Field<FishingPlan, String> _f$weather =
      Field('weather', _$weather);
  static String _$temperature(FishingPlan v) => v.temperature;
  static const Field<FishingPlan, String> _f$temperature =
      Field('temperature', _$temperature);
  static String _$description(FishingPlan v) => v.description;
  static const Field<FishingPlan, String> _f$description =
      Field('description', _$description);
  static bool _$isOwner(FishingPlan v) => v.isOwner;
  static const Field<FishingPlan, bool> _f$isOwner =
      Field('isOwner', _$isOwner, key: r'is_owner');
  static String _$status(FishingPlan v) => v.status;
  static const Field<FishingPlan, String> _f$status = Field('status', _$status);
  static int _$ownerId(FishingPlan v) => v.ownerId;
  static const Field<FishingPlan, int> _f$ownerId =
      Field('ownerId', _$ownerId, key: r'owner_id');
  static String _$ownerName(FishingPlan v) => v.ownerName;
  static const Field<FishingPlan, String> _f$ownerName =
      Field('ownerName', _$ownerName, key: r'owner_name');
  static String? _$ownerAvatar(FishingPlan v) => v.ownerAvatar;
  static const Field<FishingPlan, String> _f$ownerAvatar =
      Field('ownerAvatar', _$ownerAvatar, key: r'owner_avatar', opt: true);
  static DateTime _$createdAt(FishingPlan v) => v.createdAt;
  static const Field<FishingPlan, DateTime> _f$createdAt =
      Field('createdAt', _$createdAt, key: r'created_at');
  static DateTime _$updatedAt(FishingPlan v) => v.updatedAt;
  static const Field<FishingPlan, DateTime> _f$updatedAt =
      Field('updatedAt', _$updatedAt, key: r'updated_at');

  @override
  final MappableFields<FishingPlan> fields = const {
    #id: _f$id,
    #title: _f$title,
    #location: _f$location,
    #date: _f$date,
    #time: _f$time,
    #participants: _f$participants,
    #maxParticipants: _f$maxParticipants,
    #weather: _f$weather,
    #temperature: _f$temperature,
    #description: _f$description,
    #isOwner: _f$isOwner,
    #status: _f$status,
    #ownerId: _f$ownerId,
    #ownerName: _f$ownerName,
    #ownerAvatar: _f$ownerAvatar,
    #createdAt: _f$createdAt,
    #updatedAt: _f$updatedAt,
  };

  static FishingPlan _instantiate(DecodingData data) {
    return FishingPlan(
        id: data.dec(_f$id),
        title: data.dec(_f$title),
        location: data.dec(_f$location),
        date: data.dec(_f$date),
        time: data.dec(_f$time),
        participants: data.dec(_f$participants),
        maxParticipants: data.dec(_f$maxParticipants),
        weather: data.dec(_f$weather),
        temperature: data.dec(_f$temperature),
        description: data.dec(_f$description),
        isOwner: data.dec(_f$isOwner),
        status: data.dec(_f$status),
        ownerId: data.dec(_f$ownerId),
        ownerName: data.dec(_f$ownerName),
        ownerAvatar: data.dec(_f$ownerAvatar),
        createdAt: data.dec(_f$createdAt),
        updatedAt: data.dec(_f$updatedAt));
  }

  @override
  final Function instantiate = _instantiate;

  static FishingPlan fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<FishingPlan>(map);
  }

  static FishingPlan fromJson(String json) {
    return ensureInitialized().decodeJson<FishingPlan>(json);
  }
}

mixin FishingPlanMappable {
  String toJson() {
    return FishingPlanMapper.ensureInitialized()
        .encodeJson<FishingPlan>(this as FishingPlan);
  }

  Map<String, dynamic> toMap() {
    return FishingPlanMapper.ensureInitialized()
        .encodeMap<FishingPlan>(this as FishingPlan);
  }

  FishingPlanCopyWith<FishingPlan, FishingPlan, FishingPlan> get copyWith =>
      _FishingPlanCopyWithImpl<FishingPlan, FishingPlan>(
          this as FishingPlan, $identity, $identity);
  @override
  String toString() {
    return FishingPlanMapper.ensureInitialized()
        .stringifyValue(this as FishingPlan);
  }

  @override
  bool operator ==(Object other) {
    return FishingPlanMapper.ensureInitialized()
        .equalsValue(this as FishingPlan, other);
  }

  @override
  int get hashCode {
    return FishingPlanMapper.ensureInitialized().hashValue(this as FishingPlan);
  }
}

extension FishingPlanValueCopy<$R, $Out>
    on ObjectCopyWith<$R, FishingPlan, $Out> {
  FishingPlanCopyWith<$R, FishingPlan, $Out> get $asFishingPlan =>
      $base.as((v, t, t2) => _FishingPlanCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class FishingPlanCopyWith<$R, $In extends FishingPlan, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? id,
      String? title,
      String? location,
      DateTime? date,
      String? time,
      int? participants,
      int? maxParticipants,
      String? weather,
      String? temperature,
      String? description,
      bool? isOwner,
      String? status,
      int? ownerId,
      String? ownerName,
      String? ownerAvatar,
      DateTime? createdAt,
      DateTime? updatedAt});
  FishingPlanCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _FishingPlanCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, FishingPlan, $Out>
    implements FishingPlanCopyWith<$R, FishingPlan, $Out> {
  _FishingPlanCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<FishingPlan> $mapper =
      FishingPlanMapper.ensureInitialized();
  @override
  $R call(
          {int? id,
          String? title,
          String? location,
          DateTime? date,
          String? time,
          int? participants,
          int? maxParticipants,
          String? weather,
          String? temperature,
          String? description,
          bool? isOwner,
          String? status,
          int? ownerId,
          String? ownerName,
          Object? ownerAvatar = $none,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (title != null) #title: title,
        if (location != null) #location: location,
        if (date != null) #date: date,
        if (time != null) #time: time,
        if (participants != null) #participants: participants,
        if (maxParticipants != null) #maxParticipants: maxParticipants,
        if (weather != null) #weather: weather,
        if (temperature != null) #temperature: temperature,
        if (description != null) #description: description,
        if (isOwner != null) #isOwner: isOwner,
        if (status != null) #status: status,
        if (ownerId != null) #ownerId: ownerId,
        if (ownerName != null) #ownerName: ownerName,
        if (ownerAvatar != $none) #ownerAvatar: ownerAvatar,
        if (createdAt != null) #createdAt: createdAt,
        if (updatedAt != null) #updatedAt: updatedAt
      }));
  @override
  FishingPlan $make(CopyWithData data) => FishingPlan(
      id: data.get(#id, or: $value.id),
      title: data.get(#title, or: $value.title),
      location: data.get(#location, or: $value.location),
      date: data.get(#date, or: $value.date),
      time: data.get(#time, or: $value.time),
      participants: data.get(#participants, or: $value.participants),
      maxParticipants: data.get(#maxParticipants, or: $value.maxParticipants),
      weather: data.get(#weather, or: $value.weather),
      temperature: data.get(#temperature, or: $value.temperature),
      description: data.get(#description, or: $value.description),
      isOwner: data.get(#isOwner, or: $value.isOwner),
      status: data.get(#status, or: $value.status),
      ownerId: data.get(#ownerId, or: $value.ownerId),
      ownerName: data.get(#ownerName, or: $value.ownerName),
      ownerAvatar: data.get(#ownerAvatar, or: $value.ownerAvatar),
      createdAt: data.get(#createdAt, or: $value.createdAt),
      updatedAt: data.get(#updatedAt, or: $value.updatedAt));

  @override
  FishingPlanCopyWith<$R2, FishingPlan, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _FishingPlanCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
