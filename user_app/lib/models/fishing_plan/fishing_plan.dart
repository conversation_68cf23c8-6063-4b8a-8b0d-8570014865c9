import 'package:dart_mappable/dart_mappable.dart';

part 'fishing_plan.mapper.dart';

@MappableClass()
class FishingPlan with FishingPlanMappable {
  final int id;
  final String title;
  final String location;
  final DateTime date;
  final String time;
  final int participants;
  final int maxParticipants;
  final String weather;
  final String temperature;
  final String description;
  final bool isOwner;
  final String status; // 'upcoming', 'completed', 'cancelled'
  final int ownerId;
  final String ownerName;
  final String? ownerAvatar;
  final DateTime createdAt;
  final DateTime updatedAt;

  const FishingPlan({
    required this.id,
    required this.title,
    required this.location,
    required this.date,
    required this.time,
    required this.participants,
    required this.maxParticipants,
    required this.weather,
    required this.temperature,
    required this.description,
    required this.isOwner,
    required this.status,
    required this.ownerId,
    required this.ownerName,
    this.ownerAvatar,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isFull => participants >= maxParticipants;
  bool get isUpcoming => status == 'upcoming';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';

  static final fromMap = FishingPlanMapper.fromMap;
  static final fromJson = FishingPlanMapper.fromJson;
}