// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'oss_auth.dart';

class OssAuthMapper extends ClassMapperBase<OssAuth> {
  OssAuthMapper._();

  static OssAuthMapper? _instance;
  static OssAuthMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = OssAuthMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'OssAuth';

  static String _$accessKey(OssAuth v) => v.accessKey;
  static const Field<OssAuth, String> _f$accessKey =
      Field('accessKey', _$accessKey, key: r'access_key_id');
  static String _$accessSecret(OssAuth v) => v.accessSecret;
  static const Field<OssAuth, String> _f$accessSecret =
      Field('accessSecret', _$accessSecret, key: r'access_key_secret');
  static String _$expire(OssAuth v) => v.expire;
  static const Field<OssAuth, String> _f$expire =
      Field('expire', _$expire, key: r'expiration');
  static String _$secureToken(OssAuth v) => v.secureToken;
  static const Field<OssAuth, String> _f$secureToken =
      Field('secureToken', _$secureToken, key: r'security_token');

  @override
  final MappableFields<OssAuth> fields = const {
    #accessKey: _f$accessKey,
    #accessSecret: _f$accessSecret,
    #expire: _f$expire,
    #secureToken: _f$secureToken,
  };

  static OssAuth _instantiate(DecodingData data) {
    return OssAuth(
        accessKey: data.dec(_f$accessKey),
        accessSecret: data.dec(_f$accessSecret),
        expire: data.dec(_f$expire),
        secureToken: data.dec(_f$secureToken));
  }

  @override
  final Function instantiate = _instantiate;

  static OssAuth fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<OssAuth>(map);
  }

  static OssAuth fromJson(String json) {
    return ensureInitialized().decodeJson<OssAuth>(json);
  }
}

mixin OssAuthMappable {
  String toJson() {
    return OssAuthMapper.ensureInitialized()
        .encodeJson<OssAuth>(this as OssAuth);
  }

  Map<String, dynamic> toMap() {
    return OssAuthMapper.ensureInitialized()
        .encodeMap<OssAuth>(this as OssAuth);
  }

  OssAuthCopyWith<OssAuth, OssAuth, OssAuth> get copyWith =>
      _OssAuthCopyWithImpl<OssAuth, OssAuth>(
          this as OssAuth, $identity, $identity);
  @override
  String toString() {
    return OssAuthMapper.ensureInitialized().stringifyValue(this as OssAuth);
  }

  @override
  bool operator ==(Object other) {
    return OssAuthMapper.ensureInitialized()
        .equalsValue(this as OssAuth, other);
  }

  @override
  int get hashCode {
    return OssAuthMapper.ensureInitialized().hashValue(this as OssAuth);
  }
}

extension OssAuthValueCopy<$R, $Out> on ObjectCopyWith<$R, OssAuth, $Out> {
  OssAuthCopyWith<$R, OssAuth, $Out> get $asOssAuth =>
      $base.as((v, t, t2) => _OssAuthCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class OssAuthCopyWith<$R, $In extends OssAuth, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {String? accessKey,
      String? accessSecret,
      String? expire,
      String? secureToken});
  OssAuthCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _OssAuthCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, OssAuth, $Out>
    implements OssAuthCopyWith<$R, OssAuth, $Out> {
  _OssAuthCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<OssAuth> $mapper =
      OssAuthMapper.ensureInitialized();
  @override
  $R call(
          {String? accessKey,
          String? accessSecret,
          String? expire,
          String? secureToken}) =>
      $apply(FieldCopyWithData({
        if (accessKey != null) #accessKey: accessKey,
        if (accessSecret != null) #accessSecret: accessSecret,
        if (expire != null) #expire: expire,
        if (secureToken != null) #secureToken: secureToken
      }));
  @override
  OssAuth $make(CopyWithData data) => OssAuth(
      accessKey: data.get(#accessKey, or: $value.accessKey),
      accessSecret: data.get(#accessSecret, or: $value.accessSecret),
      expire: data.get(#expire, or: $value.expire),
      secureToken: data.get(#secureToken, or: $value.secureToken));

  @override
  OssAuthCopyWith<$R2, OssAuth, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
      _OssAuthCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
