import 'package:dart_mappable/dart_mappable.dart';

part 'oss_auth.mapper.dart';

@MappableClass()
class OssAuth with OssAuthMappable {
  @MappableField(key: 'access_key_id')
  final String accessKey;

  @MappableField(key: 'access_key_secret')
  final String accessSecret;

  @MappableField(key: 'expiration')
  final String expire;

  @MappableField(key: 'security_token')
  final String secureToken;

  OssAuth({
    required this.accessKey,
    required this.accessSecret,
    required this.expire,
    required this.secureToken,
  });

  static final fromMap = OssAuthMapper.fromMap;
}
