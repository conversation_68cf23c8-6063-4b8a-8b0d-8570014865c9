// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'batch_user_info_response.dart';

class BatchUserInfoResponseMapper
    extends ClassMapperBase<BatchUserInfoResponse> {
  BatchUserInfoResponseMapper._();

  static BatchUserInfoResponseMapper? _instance;
  static BatchUserInfoResponseMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = BatchUserInfoResponseMapper._());
      UserBasicInfoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'BatchUserInfoResponse';

  static List<UserBasicInfo> _$userInfos(BatchUserInfoResponse v) =>
      v.userInfos;
  static const Field<BatchUserInfoResponse, List<UserBasicInfo>> _f$userInfos =
      Field('userInfos', _$userInfos, key: r'user_infos');

  @override
  final MappableFields<BatchUserInfoResponse> fields = const {
    #userInfos: _f$userInfos,
  };

  static BatchUserInfoResponse _instantiate(DecodingData data) {
    return BatchUserInfoResponse(userInfos: data.dec(_f$userInfos));
  }

  @override
  final Function instantiate = _instantiate;

  static BatchUserInfoResponse fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<BatchUserInfoResponse>(map);
  }

  static BatchUserInfoResponse fromJson(String json) {
    return ensureInitialized().decodeJson<BatchUserInfoResponse>(json);
  }
}

mixin BatchUserInfoResponseMappable {
  String toJson() {
    return BatchUserInfoResponseMapper.ensureInitialized()
        .encodeJson<BatchUserInfoResponse>(this as BatchUserInfoResponse);
  }

  Map<String, dynamic> toMap() {
    return BatchUserInfoResponseMapper.ensureInitialized()
        .encodeMap<BatchUserInfoResponse>(this as BatchUserInfoResponse);
  }

  BatchUserInfoResponseCopyWith<BatchUserInfoResponse, BatchUserInfoResponse,
      BatchUserInfoResponse> get copyWith => _BatchUserInfoResponseCopyWithImpl<
          BatchUserInfoResponse, BatchUserInfoResponse>(
      this as BatchUserInfoResponse, $identity, $identity);
  @override
  String toString() {
    return BatchUserInfoResponseMapper.ensureInitialized()
        .stringifyValue(this as BatchUserInfoResponse);
  }

  @override
  bool operator ==(Object other) {
    return BatchUserInfoResponseMapper.ensureInitialized()
        .equalsValue(this as BatchUserInfoResponse, other);
  }

  @override
  int get hashCode {
    return BatchUserInfoResponseMapper.ensureInitialized()
        .hashValue(this as BatchUserInfoResponse);
  }
}

extension BatchUserInfoResponseValueCopy<$R, $Out>
    on ObjectCopyWith<$R, BatchUserInfoResponse, $Out> {
  BatchUserInfoResponseCopyWith<$R, BatchUserInfoResponse, $Out>
      get $asBatchUserInfoResponse => $base.as(
          (v, t, t2) => _BatchUserInfoResponseCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class BatchUserInfoResponseCopyWith<
    $R,
    $In extends BatchUserInfoResponse,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, UserBasicInfo,
      UserBasicInfoCopyWith<$R, UserBasicInfo, UserBasicInfo>> get userInfos;
  $R call({List<UserBasicInfo>? userInfos});
  BatchUserInfoResponseCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _BatchUserInfoResponseCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, BatchUserInfoResponse, $Out>
    implements BatchUserInfoResponseCopyWith<$R, BatchUserInfoResponse, $Out> {
  _BatchUserInfoResponseCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<BatchUserInfoResponse> $mapper =
      BatchUserInfoResponseMapper.ensureInitialized();
  @override
  ListCopyWith<$R, UserBasicInfo,
          UserBasicInfoCopyWith<$R, UserBasicInfo, UserBasicInfo>>
      get userInfos => ListCopyWith($value.userInfos,
          (v, t) => v.copyWith.$chain(t), (v) => call(userInfos: v));
  @override
  $R call({List<UserBasicInfo>? userInfos}) =>
      $apply(FieldCopyWithData({if (userInfos != null) #userInfos: userInfos}));
  @override
  BatchUserInfoResponse $make(CopyWithData data) => BatchUserInfoResponse(
      userInfos: data.get(#userInfos, or: $value.userInfos));

  @override
  BatchUserInfoResponseCopyWith<$R2, BatchUserInfoResponse, $Out2>
      $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
          _BatchUserInfoResponseCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
