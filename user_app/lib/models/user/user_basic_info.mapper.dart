// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'user_basic_info.dart';

class UserBasicInfoMapper extends ClassMapperBase<UserBasicInfo> {
  UserBasicInfoMapper._();

  static UserBasicInfoMapper? _instance;
  static UserBasicInfoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = UserBasicInfoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'UserBasicInfo';

  static int _$userId(UserBasicInfo v) => v.userId;
  static const Field<UserBasicInfo, int> _f$userId =
      Field('userId', _$userId, key: r'user_id');
  static String? _$nickName(UserBasicInfo v) => v.nickName;
  static const Field<UserBasicInfo, String> _f$nickName =
      Field('nickName', _$nickName, key: r'nick_name', opt: true);
  static String? _$avatar(UserBasicInfo v) => v.avatar;
  static const Field<UserBasicInfo, String> _f$avatar =
      Field('avatar', _$avatar, opt: true);
  static String? _$username(UserBasicInfo v) => v.username;
  static const Field<UserBasicInfo, String> _f$username =
      Field('username', _$username, opt: true);

  @override
  final MappableFields<UserBasicInfo> fields = const {
    #userId: _f$userId,
    #nickName: _f$nickName,
    #avatar: _f$avatar,
    #username: _f$username,
  };

  static UserBasicInfo _instantiate(DecodingData data) {
    return UserBasicInfo(
        userId: data.dec(_f$userId),
        nickName: data.dec(_f$nickName),
        avatar: data.dec(_f$avatar),
        username: data.dec(_f$username));
  }

  @override
  final Function instantiate = _instantiate;

  static UserBasicInfo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<UserBasicInfo>(map);
  }

  static UserBasicInfo fromJson(String json) {
    return ensureInitialized().decodeJson<UserBasicInfo>(json);
  }
}

mixin UserBasicInfoMappable {
  String toJson() {
    return UserBasicInfoMapper.ensureInitialized()
        .encodeJson<UserBasicInfo>(this as UserBasicInfo);
  }

  Map<String, dynamic> toMap() {
    return UserBasicInfoMapper.ensureInitialized()
        .encodeMap<UserBasicInfo>(this as UserBasicInfo);
  }

  UserBasicInfoCopyWith<UserBasicInfo, UserBasicInfo, UserBasicInfo>
      get copyWith => _UserBasicInfoCopyWithImpl<UserBasicInfo, UserBasicInfo>(
          this as UserBasicInfo, $identity, $identity);
  @override
  String toString() {
    return UserBasicInfoMapper.ensureInitialized()
        .stringifyValue(this as UserBasicInfo);
  }

  @override
  bool operator ==(Object other) {
    return UserBasicInfoMapper.ensureInitialized()
        .equalsValue(this as UserBasicInfo, other);
  }

  @override
  int get hashCode {
    return UserBasicInfoMapper.ensureInitialized()
        .hashValue(this as UserBasicInfo);
  }
}

extension UserBasicInfoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, UserBasicInfo, $Out> {
  UserBasicInfoCopyWith<$R, UserBasicInfo, $Out> get $asUserBasicInfo =>
      $base.as((v, t, t2) => _UserBasicInfoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class UserBasicInfoCopyWith<$R, $In extends UserBasicInfo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call({int? userId, String? nickName, String? avatar, String? username});
  UserBasicInfoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _UserBasicInfoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, UserBasicInfo, $Out>
    implements UserBasicInfoCopyWith<$R, UserBasicInfo, $Out> {
  _UserBasicInfoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<UserBasicInfo> $mapper =
      UserBasicInfoMapper.ensureInitialized();
  @override
  $R call(
          {int? userId,
          Object? nickName = $none,
          Object? avatar = $none,
          Object? username = $none}) =>
      $apply(FieldCopyWithData({
        if (userId != null) #userId: userId,
        if (nickName != $none) #nickName: nickName,
        if (avatar != $none) #avatar: avatar,
        if (username != $none) #username: username
      }));
  @override
  UserBasicInfo $make(CopyWithData data) => UserBasicInfo(
      userId: data.get(#userId, or: $value.userId),
      nickName: data.get(#nickName, or: $value.nickName),
      avatar: data.get(#avatar, or: $value.avatar),
      username: data.get(#username, or: $value.username));

  @override
  UserBasicInfoCopyWith<$R2, UserBasicInfo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _UserBasicInfoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
