// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'batch_user_info_request.dart';

class BatchUserInfoRequestMapper extends ClassMapperBase<BatchUserInfoRequest> {
  BatchUserInfoRequestMapper._();

  static BatchUserInfoRequestMapper? _instance;
  static BatchUserInfoRequestMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = BatchUserInfoRequestMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'BatchUserInfoRequest';

  static List<int> _$userIds(BatchUserInfoRequest v) => v.userIds;
  static const Field<BatchUserInfoRequest, List<int>> _f$userIds =
      Field('userIds', _$userIds, key: r'user_ids');

  @override
  final MappableFields<BatchUserInfoRequest> fields = const {
    #userIds: _f$userIds,
  };

  static BatchUserInfoRequest _instantiate(DecodingData data) {
    return BatchUserInfoRequest(userIds: data.dec(_f$userIds));
  }

  @override
  final Function instantiate = _instantiate;

  static BatchUserInfoRequest fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<BatchUserInfoRequest>(map);
  }

  static BatchUserInfoRequest fromJson(String json) {
    return ensureInitialized().decodeJson<BatchUserInfoRequest>(json);
  }
}

mixin BatchUserInfoRequestMappable {
  String toJson() {
    return BatchUserInfoRequestMapper.ensureInitialized()
        .encodeJson<BatchUserInfoRequest>(this as BatchUserInfoRequest);
  }

  Map<String, dynamic> toMap() {
    return BatchUserInfoRequestMapper.ensureInitialized()
        .encodeMap<BatchUserInfoRequest>(this as BatchUserInfoRequest);
  }

  BatchUserInfoRequestCopyWith<BatchUserInfoRequest, BatchUserInfoRequest,
      BatchUserInfoRequest> get copyWith => _BatchUserInfoRequestCopyWithImpl<
          BatchUserInfoRequest, BatchUserInfoRequest>(
      this as BatchUserInfoRequest, $identity, $identity);
  @override
  String toString() {
    return BatchUserInfoRequestMapper.ensureInitialized()
        .stringifyValue(this as BatchUserInfoRequest);
  }

  @override
  bool operator ==(Object other) {
    return BatchUserInfoRequestMapper.ensureInitialized()
        .equalsValue(this as BatchUserInfoRequest, other);
  }

  @override
  int get hashCode {
    return BatchUserInfoRequestMapper.ensureInitialized()
        .hashValue(this as BatchUserInfoRequest);
  }
}

extension BatchUserInfoRequestValueCopy<$R, $Out>
    on ObjectCopyWith<$R, BatchUserInfoRequest, $Out> {
  BatchUserInfoRequestCopyWith<$R, BatchUserInfoRequest, $Out>
      get $asBatchUserInfoRequest => $base.as(
          (v, t, t2) => _BatchUserInfoRequestCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class BatchUserInfoRequestCopyWith<
    $R,
    $In extends BatchUserInfoRequest,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, int, ObjectCopyWith<$R, int, int>> get userIds;
  $R call({List<int>? userIds});
  BatchUserInfoRequestCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _BatchUserInfoRequestCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, BatchUserInfoRequest, $Out>
    implements BatchUserInfoRequestCopyWith<$R, BatchUserInfoRequest, $Out> {
  _BatchUserInfoRequestCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<BatchUserInfoRequest> $mapper =
      BatchUserInfoRequestMapper.ensureInitialized();
  @override
  ListCopyWith<$R, int, ObjectCopyWith<$R, int, int>> get userIds =>
      ListCopyWith($value.userIds, (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(userIds: v));
  @override
  $R call({List<int>? userIds}) =>
      $apply(FieldCopyWithData({if (userIds != null) #userIds: userIds}));
  @override
  BatchUserInfoRequest $make(CopyWithData data) =>
      BatchUserInfoRequest(userIds: data.get(#userIds, or: $value.userIds));

  @override
  BatchUserInfoRequestCopyWith<$R2, BatchUserInfoRequest, $Out2>
      $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
          _BatchUserInfoRequestCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
