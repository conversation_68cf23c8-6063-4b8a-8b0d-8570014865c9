import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/models/user/user_basic_info.dart';

part 'batch_user_info_response.mapper.dart';

@MappableClass()
class BatchUserInfoResponse with BatchUserInfoResponseMappable {
  final List<UserBasicInfo> userInfos;

  const BatchUserInfoResponse({
    required this.userInfos,
  });

  static final fromMap = BatchUserInfoResponseMapper.fromMap;
}