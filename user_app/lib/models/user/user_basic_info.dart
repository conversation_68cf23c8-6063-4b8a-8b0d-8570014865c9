import 'package:dart_mappable/dart_mappable.dart';

part 'user_basic_info.mapper.dart';

@MappableClass()
class UserBasicInfo with UserBasicInfoMappable {
  final int userId;
  final String? nickName;
  final String? avatar;
  final String? username;

  const UserBasicInfo({
    required this.userId,
    this.nickName,
    this.avatar,
    this.username,
  });

  static final fromMap = UserBasicInfoMapper.fromMap;
}