// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'user_fans_attentions_request.dart';

class UserFansAttentionsRequestMapper
    extends ClassMapperBase<UserFansAttentionsRequest> {
  UserFansAttentionsRequestMapper._();

  static UserFansAttentionsRequestMapper? _instance;
  static UserFansAttentionsRequestMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals
          .use(_instance = UserFansAttentionsRequestMapper._());
      RequestPageInfoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'UserFansAttentionsRequest';

  static num _$userId(UserFansAttentionsRequest v) => v.userId;
  static const Field<UserFansAttentionsRequest, num> _f$userId =
      Field('userId', _$userId, key: r'user_id');
  static num? _$pageNum(UserFansAttentionsRequest v) => v.pageNum;
  static const Field<UserFansAttentionsRequest, num> _f$pageNum =
      Field('pageNum', _$pageNum, key: r'page_num');
  static num? _$pageSize(UserFansAttentionsRequest v) => v.pageSize;
  static const Field<UserFansAttentionsRequest, num> _f$pageSize =
      Field('pageSize', _$pageSize, key: r'page_size');

  @override
  final MappableFields<UserFansAttentionsRequest> fields = const {
    #userId: _f$userId,
    #pageNum: _f$pageNum,
    #pageSize: _f$pageSize,
  };

  static UserFansAttentionsRequest _instantiate(DecodingData data) {
    return UserFansAttentionsRequest(
        userId: data.dec(_f$userId),
        pageNum: data.dec(_f$pageNum),
        pageSize: data.dec(_f$pageSize));
  }

  @override
  final Function instantiate = _instantiate;

  static UserFansAttentionsRequest fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<UserFansAttentionsRequest>(map);
  }

  static UserFansAttentionsRequest fromJson(String json) {
    return ensureInitialized().decodeJson<UserFansAttentionsRequest>(json);
  }
}

mixin UserFansAttentionsRequestMappable {
  String toJson() {
    return UserFansAttentionsRequestMapper.ensureInitialized()
        .encodeJson<UserFansAttentionsRequest>(
            this as UserFansAttentionsRequest);
  }

  Map<String, dynamic> toMap() {
    return UserFansAttentionsRequestMapper.ensureInitialized()
        .encodeMap<UserFansAttentionsRequest>(
            this as UserFansAttentionsRequest);
  }

  UserFansAttentionsRequestCopyWith<UserFansAttentionsRequest,
          UserFansAttentionsRequest, UserFansAttentionsRequest>
      get copyWith => _UserFansAttentionsRequestCopyWithImpl<
              UserFansAttentionsRequest, UserFansAttentionsRequest>(
          this as UserFansAttentionsRequest, $identity, $identity);
  @override
  String toString() {
    return UserFansAttentionsRequestMapper.ensureInitialized()
        .stringifyValue(this as UserFansAttentionsRequest);
  }

  @override
  bool operator ==(Object other) {
    return UserFansAttentionsRequestMapper.ensureInitialized()
        .equalsValue(this as UserFansAttentionsRequest, other);
  }

  @override
  int get hashCode {
    return UserFansAttentionsRequestMapper.ensureInitialized()
        .hashValue(this as UserFansAttentionsRequest);
  }
}

extension UserFansAttentionsRequestValueCopy<$R, $Out>
    on ObjectCopyWith<$R, UserFansAttentionsRequest, $Out> {
  UserFansAttentionsRequestCopyWith<$R, UserFansAttentionsRequest, $Out>
      get $asUserFansAttentionsRequest => $base.as((v, t, t2) =>
          _UserFansAttentionsRequestCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class UserFansAttentionsRequestCopyWith<
    $R,
    $In extends UserFansAttentionsRequest,
    $Out> implements RequestPageInfoCopyWith<$R, $In, $Out> {
  @override
  $R call({num? userId, num? pageNum, num? pageSize});
  UserFansAttentionsRequestCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _UserFansAttentionsRequestCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, UserFansAttentionsRequest, $Out>
    implements
        UserFansAttentionsRequestCopyWith<$R, UserFansAttentionsRequest, $Out> {
  _UserFansAttentionsRequestCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<UserFansAttentionsRequest> $mapper =
      UserFansAttentionsRequestMapper.ensureInitialized();
  @override
  $R call({num? userId, Object? pageNum = $none, Object? pageSize = $none}) =>
      $apply(FieldCopyWithData({
        if (userId != null) #userId: userId,
        if (pageNum != $none) #pageNum: pageNum,
        if (pageSize != $none) #pageSize: pageSize
      }));
  @override
  UserFansAttentionsRequest $make(CopyWithData data) =>
      UserFansAttentionsRequest(
          userId: data.get(#userId, or: $value.userId),
          pageNum: data.get(#pageNum, or: $value.pageNum),
          pageSize: data.get(#pageSize, or: $value.pageSize));

  @override
  UserFansAttentionsRequestCopyWith<$R2, UserFansAttentionsRequest, $Out2>
      $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
          _UserFansAttentionsRequestCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
