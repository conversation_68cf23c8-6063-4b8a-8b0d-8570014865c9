// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'user_comment_status_dto.dart';

class UserCommentStatusDtoMapper extends ClassMapperBase<UserCommentStatusDto> {
  UserCommentStatusDtoMapper._();

  static UserCommentStatusDtoMapper? _instance;
  static UserCommentStatusDtoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = UserCommentStatusDtoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'UserCommentStatusDto';

  static bool _$upVoted(UserCommentStatusDto v) => v.upVoted;
  static const Field<UserCommentStatusDto, bool> _f$upVoted =
      Field('upVoted', _$upVoted, key: r'up_voted');
  static bool _$downVoted(UserCommentStatusDto v) => v.downVoted;
  static const Field<UserCommentStatusDto, bool> _f$downVoted =
      Field('downVoted', _$downVoted, key: r'down_voted');
  static int _$commentId(UserCommentStatusDto v) => v.commentId;
  static const Field<UserCommentStatusDto, int> _f$commentId =
      Field('commentId', _$commentId, key: r'comment_id');
  static int _$upVoteCount(UserCommentStatusDto v) => v.upVoteCount;
  static const Field<UserCommentStatusDto, int> _f$upVoteCount =
      Field('upVoteCount', _$upVoteCount, key: r'up_vote_count');
  static int _$downVoteCount(UserCommentStatusDto v) => v.downVoteCount;
  static const Field<UserCommentStatusDto, int> _f$downVoteCount =
      Field('downVoteCount', _$downVoteCount, key: r'down_vote_count');

  @override
  final MappableFields<UserCommentStatusDto> fields = const {
    #upVoted: _f$upVoted,
    #downVoted: _f$downVoted,
    #commentId: _f$commentId,
    #upVoteCount: _f$upVoteCount,
    #downVoteCount: _f$downVoteCount,
  };

  static UserCommentStatusDto _instantiate(DecodingData data) {
    return UserCommentStatusDto(
        upVoted: data.dec(_f$upVoted),
        downVoted: data.dec(_f$downVoted),
        commentId: data.dec(_f$commentId),
        upVoteCount: data.dec(_f$upVoteCount),
        downVoteCount: data.dec(_f$downVoteCount));
  }

  @override
  final Function instantiate = _instantiate;

  static UserCommentStatusDto fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<UserCommentStatusDto>(map);
  }

  static UserCommentStatusDto fromJson(String json) {
    return ensureInitialized().decodeJson<UserCommentStatusDto>(json);
  }
}

mixin UserCommentStatusDtoMappable {
  String toJson() {
    return UserCommentStatusDtoMapper.ensureInitialized()
        .encodeJson<UserCommentStatusDto>(this as UserCommentStatusDto);
  }

  Map<String, dynamic> toMap() {
    return UserCommentStatusDtoMapper.ensureInitialized()
        .encodeMap<UserCommentStatusDto>(this as UserCommentStatusDto);
  }

  UserCommentStatusDtoCopyWith<UserCommentStatusDto, UserCommentStatusDto,
      UserCommentStatusDto> get copyWith => _UserCommentStatusDtoCopyWithImpl<
          UserCommentStatusDto, UserCommentStatusDto>(
      this as UserCommentStatusDto, $identity, $identity);
  @override
  String toString() {
    return UserCommentStatusDtoMapper.ensureInitialized()
        .stringifyValue(this as UserCommentStatusDto);
  }

  @override
  bool operator ==(Object other) {
    return UserCommentStatusDtoMapper.ensureInitialized()
        .equalsValue(this as UserCommentStatusDto, other);
  }

  @override
  int get hashCode {
    return UserCommentStatusDtoMapper.ensureInitialized()
        .hashValue(this as UserCommentStatusDto);
  }
}

extension UserCommentStatusDtoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, UserCommentStatusDto, $Out> {
  UserCommentStatusDtoCopyWith<$R, UserCommentStatusDto, $Out>
      get $asUserCommentStatusDto => $base.as(
          (v, t, t2) => _UserCommentStatusDtoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class UserCommentStatusDtoCopyWith<
    $R,
    $In extends UserCommentStatusDto,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {bool? upVoted,
      bool? downVoted,
      int? commentId,
      int? upVoteCount,
      int? downVoteCount});
  UserCommentStatusDtoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _UserCommentStatusDtoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, UserCommentStatusDto, $Out>
    implements UserCommentStatusDtoCopyWith<$R, UserCommentStatusDto, $Out> {
  _UserCommentStatusDtoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<UserCommentStatusDto> $mapper =
      UserCommentStatusDtoMapper.ensureInitialized();
  @override
  $R call(
          {bool? upVoted,
          bool? downVoted,
          int? commentId,
          int? upVoteCount,
          int? downVoteCount}) =>
      $apply(FieldCopyWithData({
        if (upVoted != null) #upVoted: upVoted,
        if (downVoted != null) #downVoted: downVoted,
        if (commentId != null) #commentId: commentId,
        if (upVoteCount != null) #upVoteCount: upVoteCount,
        if (downVoteCount != null) #downVoteCount: downVoteCount
      }));
  @override
  UserCommentStatusDto $make(CopyWithData data) => UserCommentStatusDto(
      upVoted: data.get(#upVoted, or: $value.upVoted),
      downVoted: data.get(#downVoted, or: $value.downVoted),
      commentId: data.get(#commentId, or: $value.commentId),
      upVoteCount: data.get(#upVoteCount, or: $value.upVoteCount),
      downVoteCount: data.get(#downVoteCount, or: $value.downVoteCount));

  @override
  UserCommentStatusDtoCopyWith<$R2, UserCommentStatusDto, $Out2>
      $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
          _UserCommentStatusDtoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
