// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'create_moment_model.dart';

class CreateMomentModelMapper extends ClassMapperBase<CreateMomentModel> {
  CreateMomentModelMapper._();

  static CreateMomentModelMapper? _instance;
  static CreateMomentModelMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = CreateMomentModelMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'CreateMomentModel';

  static String _$content(CreateMomentModel v) => v.content;
  static const Field<CreateMomentModel, String> _f$content =
      Field('content', _$content);
  static List<String> _$pictures(CreateMomentModel v) => v.pictures;
  static const Field<CreateMomentModel, List<String>> _f$pictures =
      Field('pictures', _$pictures, key: r'image_urls');
  static String _$momentType(CreateMomentModel v) => v.momentType;
  static const Field<CreateMomentModel, String> _f$momentType =
      Field('momentType', _$momentType, key: r'moment_type');
  static String _$visibility(CreateMomentModel v) => v.visibility;
  static const Field<CreateMomentModel, String> _f$visibility =
      Field('visibility', _$visibility);
  static int? _$fishingSpotId(CreateMomentModel v) => v.fishingSpotId;
  static const Field<CreateMomentModel, int> _f$fishingSpotId = Field(
      'fishingSpotId', _$fishingSpotId,
      key: r'fishing_spot_id', opt: true);
  static String? _$typeSpecificData(CreateMomentModel v) => v.typeSpecificData;
  static const Field<CreateMomentModel, String> _f$typeSpecificData = Field(
      'typeSpecificData', _$typeSpecificData,
      key: r'type_specific_data', opt: true);

  @override
  final MappableFields<CreateMomentModel> fields = const {
    #content: _f$content,
    #pictures: _f$pictures,
    #momentType: _f$momentType,
    #visibility: _f$visibility,
    #fishingSpotId: _f$fishingSpotId,
    #typeSpecificData: _f$typeSpecificData,
  };

  static CreateMomentModel _instantiate(DecodingData data) {
    return CreateMomentModel(
        content: data.dec(_f$content),
        pictures: data.dec(_f$pictures),
        momentType: data.dec(_f$momentType),
        visibility: data.dec(_f$visibility),
        fishingSpotId: data.dec(_f$fishingSpotId),
        typeSpecificData: data.dec(_f$typeSpecificData));
  }

  @override
  final Function instantiate = _instantiate;

  static CreateMomentModel fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<CreateMomentModel>(map);
  }

  static CreateMomentModel fromJson(String json) {
    return ensureInitialized().decodeJson<CreateMomentModel>(json);
  }
}

mixin CreateMomentModelMappable {
  String toJson() {
    return CreateMomentModelMapper.ensureInitialized()
        .encodeJson<CreateMomentModel>(this as CreateMomentModel);
  }

  Map<String, dynamic> toMap() {
    return CreateMomentModelMapper.ensureInitialized()
        .encodeMap<CreateMomentModel>(this as CreateMomentModel);
  }

  CreateMomentModelCopyWith<CreateMomentModel, CreateMomentModel,
          CreateMomentModel>
      get copyWith =>
          _CreateMomentModelCopyWithImpl<CreateMomentModel, CreateMomentModel>(
              this as CreateMomentModel, $identity, $identity);
  @override
  String toString() {
    return CreateMomentModelMapper.ensureInitialized()
        .stringifyValue(this as CreateMomentModel);
  }

  @override
  bool operator ==(Object other) {
    return CreateMomentModelMapper.ensureInitialized()
        .equalsValue(this as CreateMomentModel, other);
  }

  @override
  int get hashCode {
    return CreateMomentModelMapper.ensureInitialized()
        .hashValue(this as CreateMomentModel);
  }
}

extension CreateMomentModelValueCopy<$R, $Out>
    on ObjectCopyWith<$R, CreateMomentModel, $Out> {
  CreateMomentModelCopyWith<$R, CreateMomentModel, $Out>
      get $asCreateMomentModel => $base
          .as((v, t, t2) => _CreateMomentModelCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class CreateMomentModelCopyWith<$R, $In extends CreateMomentModel,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get pictures;
  $R call(
      {String? content,
      List<String>? pictures,
      String? momentType,
      String? visibility,
      int? fishingSpotId,
      String? typeSpecificData});
  CreateMomentModelCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _CreateMomentModelCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, CreateMomentModel, $Out>
    implements CreateMomentModelCopyWith<$R, CreateMomentModel, $Out> {
  _CreateMomentModelCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<CreateMomentModel> $mapper =
      CreateMomentModelMapper.ensureInitialized();
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get pictures =>
      ListCopyWith($value.pictures, (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(pictures: v));
  @override
  $R call(
          {String? content,
          List<String>? pictures,
          String? momentType,
          String? visibility,
          Object? fishingSpotId = $none,
          Object? typeSpecificData = $none}) =>
      $apply(FieldCopyWithData({
        if (content != null) #content: content,
        if (pictures != null) #pictures: pictures,
        if (momentType != null) #momentType: momentType,
        if (visibility != null) #visibility: visibility,
        if (fishingSpotId != $none) #fishingSpotId: fishingSpotId,
        if (typeSpecificData != $none) #typeSpecificData: typeSpecificData
      }));
  @override
  CreateMomentModel $make(CopyWithData data) => CreateMomentModel(
      content: data.get(#content, or: $value.content),
      pictures: data.get(#pictures, or: $value.pictures),
      momentType: data.get(#momentType, or: $value.momentType),
      visibility: data.get(#visibility, or: $value.visibility),
      fishingSpotId: data.get(#fishingSpotId, or: $value.fishingSpotId),
      typeSpecificData:
          data.get(#typeSpecificData, or: $value.typeSpecificData));

  @override
  CreateMomentModelCopyWith<$R2, CreateMomentModel, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _CreateMomentModelCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
