import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/models/moment/user_comment_status_dto.dart';

part 'user_moment_status_dto.mapper.dart';

@MappableClass()
class UserMomentStatusDto with UserMomentStatusDtoMappable {
  bool upVoted;
  bool downVoted;
  bool followed;
  int upVoteCount;
  int downVoteCount;

  List<UserCommentStatusDto> commentsStatus;

  UserMomentStatusDto({
    required this.upVoted,
    required this.downVoted,
    required this.followed,
    required this.upVoteCount,
    required this.downVoteCount,
    required this.commentsStatus,
  });

  static final fromMap = UserMomentStatusDtoMapper.fromMap;
}
