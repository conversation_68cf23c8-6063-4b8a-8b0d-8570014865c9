// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'moment_page_request.dart';

class MomentPageRequestMapper extends ClassMapperBase<MomentPageRequest> {
  MomentPageRequestMapper._();

  static MomentPageRequestMapper? _instance;
  static MomentPageRequestMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = MomentPageRequestMapper._());
      RequestPageInfoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'MomentPageRequest';

  static num? _$pageNum(MomentPageRequest v) => v.pageNum;
  static const Field<MomentPageRequest, num> _f$pageNum =
      Field('pageNum', _$pageNum, key: r'page_num');
  static num? _$pageSize(MomentPageRequest v) => v.pageSize;
  static const Field<MomentPageRequest, num> _f$pageSize =
      Field('pageSize', _$pageSize, key: r'page_size');
  static bool _$onlyMine(MomentPageRequest v) => v.onlyMine;
  static const Field<MomentPageRequest, bool> _f$onlyMine =
      Field('onlyMine', _$onlyMine, key: r'only_mine', opt: true, def: false);
  static String? _$momentType(MomentPageRequest v) => v.momentType;
  static const Field<MomentPageRequest, String> _f$momentType =
      Field('momentType', _$momentType, key: r'moment_type', opt: true);
  static String? _$sortBy(MomentPageRequest v) => v.sortBy;
  static const Field<MomentPageRequest, String> _f$sortBy =
      Field('sortBy', _$sortBy, key: r'sort_by', opt: true);

  @override
  final MappableFields<MomentPageRequest> fields = const {
    #pageNum: _f$pageNum,
    #pageSize: _f$pageSize,
    #onlyMine: _f$onlyMine,
    #momentType: _f$momentType,
    #sortBy: _f$sortBy,
  };

  static MomentPageRequest _instantiate(DecodingData data) {
    return MomentPageRequest(
        pageNum: data.dec(_f$pageNum),
        pageSize: data.dec(_f$pageSize),
        onlyMine: data.dec(_f$onlyMine),
        momentType: data.dec(_f$momentType),
        sortBy: data.dec(_f$sortBy));
  }

  @override
  final Function instantiate = _instantiate;

  static MomentPageRequest fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<MomentPageRequest>(map);
  }

  static MomentPageRequest fromJson(String json) {
    return ensureInitialized().decodeJson<MomentPageRequest>(json);
  }
}

mixin MomentPageRequestMappable {
  String toJson() {
    return MomentPageRequestMapper.ensureInitialized()
        .encodeJson<MomentPageRequest>(this as MomentPageRequest);
  }

  Map<String, dynamic> toMap() {
    return MomentPageRequestMapper.ensureInitialized()
        .encodeMap<MomentPageRequest>(this as MomentPageRequest);
  }

  MomentPageRequestCopyWith<MomentPageRequest, MomentPageRequest,
          MomentPageRequest>
      get copyWith =>
          _MomentPageRequestCopyWithImpl<MomentPageRequest, MomentPageRequest>(
              this as MomentPageRequest, $identity, $identity);
  @override
  String toString() {
    return MomentPageRequestMapper.ensureInitialized()
        .stringifyValue(this as MomentPageRequest);
  }

  @override
  bool operator ==(Object other) {
    return MomentPageRequestMapper.ensureInitialized()
        .equalsValue(this as MomentPageRequest, other);
  }

  @override
  int get hashCode {
    return MomentPageRequestMapper.ensureInitialized()
        .hashValue(this as MomentPageRequest);
  }
}

extension MomentPageRequestValueCopy<$R, $Out>
    on ObjectCopyWith<$R, MomentPageRequest, $Out> {
  MomentPageRequestCopyWith<$R, MomentPageRequest, $Out>
      get $asMomentPageRequest => $base
          .as((v, t, t2) => _MomentPageRequestCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class MomentPageRequestCopyWith<$R, $In extends MomentPageRequest,
    $Out> implements RequestPageInfoCopyWith<$R, $In, $Out> {
  @override
  $R call(
      {num? pageNum,
      num? pageSize,
      bool? onlyMine,
      String? momentType,
      String? sortBy});
  MomentPageRequestCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _MomentPageRequestCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, MomentPageRequest, $Out>
    implements MomentPageRequestCopyWith<$R, MomentPageRequest, $Out> {
  _MomentPageRequestCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<MomentPageRequest> $mapper =
      MomentPageRequestMapper.ensureInitialized();
  @override
  $R call(
          {Object? pageNum = $none,
          Object? pageSize = $none,
          bool? onlyMine,
          Object? momentType = $none,
          Object? sortBy = $none}) =>
      $apply(FieldCopyWithData({
        if (pageNum != $none) #pageNum: pageNum,
        if (pageSize != $none) #pageSize: pageSize,
        if (onlyMine != null) #onlyMine: onlyMine,
        if (momentType != $none) #momentType: momentType,
        if (sortBy != $none) #sortBy: sortBy
      }));
  @override
  MomentPageRequest $make(CopyWithData data) => MomentPageRequest(
      pageNum: data.get(#pageNum, or: $value.pageNum),
      pageSize: data.get(#pageSize, or: $value.pageSize),
      onlyMine: data.get(#onlyMine, or: $value.onlyMine),
      momentType: data.get(#momentType, or: $value.momentType),
      sortBy: data.get(#sortBy, or: $value.sortBy));

  @override
  MomentPageRequestCopyWith<$R2, MomentPageRequest, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _MomentPageRequestCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
