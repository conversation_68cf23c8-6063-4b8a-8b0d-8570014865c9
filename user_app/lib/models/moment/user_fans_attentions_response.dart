import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/models/response_page_info.dart';
import 'package:user_app/models/user.dart';

part 'user_fans_attentions_response.mapper.dart';

@MappableClass()
class UserFansAttentionsResponse extends ResponsePageInfo
    with UserFansAttentionsResponseMappable {
  final List<User> records;

  UserFansAttentionsResponse({
    required this.records,
    required super.current,
    required super.size,
    required super.total,
    required super.pages,
  });

  static final fromMap = UserFansAttentionsResponseMapper.fromMap;
}
