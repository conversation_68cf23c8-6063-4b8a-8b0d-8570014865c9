import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/response_page_info.dart';

part 'moment_response.mapper.dart';

@MappableClass()
class MomentResponse extends ResponsePageInfo with MomentResponseMappable {
  final List<MomentVo> records;

  const MomentResponse({
    required this.records,
    required super.current,
    required super.size,
    required super.total,
    required super.pages,
  });

  static final fromMap = MomentResponseMapper.fromMap;
}
