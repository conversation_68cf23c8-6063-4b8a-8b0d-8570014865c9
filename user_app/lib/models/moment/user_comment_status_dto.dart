import 'package:dart_mappable/dart_mappable.dart';

part 'user_comment_status_dto.mapper.dart';

@MappableClass()
class UserCommentStatusDto with UserCommentStatusDtoMappable {
  bool upVoted;
  bool downVoted;
  int commentId;
  int upVoteCount;
  int downVoteCount;

  UserCommentStatusDto({
    required this.upVoted,
    required this.downVoted,
    required this.commentId,
    required this.upVoteCount,
    required this.downVoteCount,
  });

  static final fromMap = UserCommentStatusDtoMapper.fromMap;
}
