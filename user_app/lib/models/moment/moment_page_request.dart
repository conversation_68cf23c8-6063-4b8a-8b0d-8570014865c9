import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/models/request_page_info.dart';

part 'moment_page_request.mapper.dart';

@MappableClass()
class MomentPageRequest extends RequestPageInfo with MomentPageRequestMappable {
  final bool onlyMine;
  final String? momentType;
  final String? sortBy;

  MomentPageRequest({
    required super.pageNum,
    required super.pageSize,
    this.onlyMine = false,
    this.momentType,
    this.sortBy,
  });

  static final fromMap = MomentPageRequestMapper.fromMap;
}