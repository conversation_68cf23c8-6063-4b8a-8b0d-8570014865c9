// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'user_fans_attentions_response.dart';

class UserFansAttentionsResponseMapper
    extends ClassMapperBase<UserFansAttentionsResponse> {
  UserFansAttentionsResponseMapper._();

  static UserFansAttentionsResponseMapper? _instance;
  static UserFansAttentionsResponseMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals
          .use(_instance = UserFansAttentionsResponseMapper._());
      ResponsePageInfoMapper.ensureInitialized();
      UserMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'UserFansAttentionsResponse';

  static List<User> _$records(UserFansAttentionsResponse v) => v.records;
  static const Field<UserFansAttentionsResponse, List<User>> _f$records =
      Field('records', _$records);
  static num _$current(UserFansAttentionsResponse v) => v.current;
  static const Field<UserFansAttentionsResponse, num> _f$current =
      Field('current', _$current);
  static num _$size(UserFansAttentionsResponse v) => v.size;
  static const Field<UserFansAttentionsResponse, num> _f$size =
      Field('size', _$size);
  static num _$total(UserFansAttentionsResponse v) => v.total;
  static const Field<UserFansAttentionsResponse, num> _f$total =
      Field('total', _$total);
  static num _$pages(UserFansAttentionsResponse v) => v.pages;
  static const Field<UserFansAttentionsResponse, num> _f$pages =
      Field('pages', _$pages);

  @override
  final MappableFields<UserFansAttentionsResponse> fields = const {
    #records: _f$records,
    #current: _f$current,
    #size: _f$size,
    #total: _f$total,
    #pages: _f$pages,
  };

  static UserFansAttentionsResponse _instantiate(DecodingData data) {
    return UserFansAttentionsResponse(
        records: data.dec(_f$records),
        current: data.dec(_f$current),
        size: data.dec(_f$size),
        total: data.dec(_f$total),
        pages: data.dec(_f$pages));
  }

  @override
  final Function instantiate = _instantiate;

  static UserFansAttentionsResponse fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<UserFansAttentionsResponse>(map);
  }

  static UserFansAttentionsResponse fromJson(String json) {
    return ensureInitialized().decodeJson<UserFansAttentionsResponse>(json);
  }
}

mixin UserFansAttentionsResponseMappable {
  String toJson() {
    return UserFansAttentionsResponseMapper.ensureInitialized()
        .encodeJson<UserFansAttentionsResponse>(
            this as UserFansAttentionsResponse);
  }

  Map<String, dynamic> toMap() {
    return UserFansAttentionsResponseMapper.ensureInitialized()
        .encodeMap<UserFansAttentionsResponse>(
            this as UserFansAttentionsResponse);
  }

  UserFansAttentionsResponseCopyWith<UserFansAttentionsResponse,
          UserFansAttentionsResponse, UserFansAttentionsResponse>
      get copyWith => _UserFansAttentionsResponseCopyWithImpl<
              UserFansAttentionsResponse, UserFansAttentionsResponse>(
          this as UserFansAttentionsResponse, $identity, $identity);
  @override
  String toString() {
    return UserFansAttentionsResponseMapper.ensureInitialized()
        .stringifyValue(this as UserFansAttentionsResponse);
  }

  @override
  bool operator ==(Object other) {
    return UserFansAttentionsResponseMapper.ensureInitialized()
        .equalsValue(this as UserFansAttentionsResponse, other);
  }

  @override
  int get hashCode {
    return UserFansAttentionsResponseMapper.ensureInitialized()
        .hashValue(this as UserFansAttentionsResponse);
  }
}

extension UserFansAttentionsResponseValueCopy<$R, $Out>
    on ObjectCopyWith<$R, UserFansAttentionsResponse, $Out> {
  UserFansAttentionsResponseCopyWith<$R, UserFansAttentionsResponse, $Out>
      get $asUserFansAttentionsResponse => $base.as((v, t, t2) =>
          _UserFansAttentionsResponseCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class UserFansAttentionsResponseCopyWith<
    $R,
    $In extends UserFansAttentionsResponse,
    $Out> implements ResponsePageInfoCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, User, UserCopyWith<$R, User, User>> get records;
  @override
  $R call(
      {List<User>? records, num? current, num? size, num? total, num? pages});
  UserFansAttentionsResponseCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _UserFansAttentionsResponseCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, UserFansAttentionsResponse, $Out>
    implements
        UserFansAttentionsResponseCopyWith<$R, UserFansAttentionsResponse,
            $Out> {
  _UserFansAttentionsResponseCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<UserFansAttentionsResponse> $mapper =
      UserFansAttentionsResponseMapper.ensureInitialized();
  @override
  ListCopyWith<$R, User, UserCopyWith<$R, User, User>> get records =>
      ListCopyWith($value.records, (v, t) => v.copyWith.$chain(t),
          (v) => call(records: v));
  @override
  $R call(
          {List<User>? records,
          num? current,
          num? size,
          num? total,
          num? pages}) =>
      $apply(FieldCopyWithData({
        if (records != null) #records: records,
        if (current != null) #current: current,
        if (size != null) #size: size,
        if (total != null) #total: total,
        if (pages != null) #pages: pages
      }));
  @override
  UserFansAttentionsResponse $make(CopyWithData data) =>
      UserFansAttentionsResponse(
          records: data.get(#records, or: $value.records),
          current: data.get(#current, or: $value.current),
          size: data.get(#size, or: $value.size),
          total: data.get(#total, or: $value.total),
          pages: data.get(#pages, or: $value.pages));

  @override
  UserFansAttentionsResponseCopyWith<$R2, UserFansAttentionsResponse, $Out2>
      $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
          _UserFansAttentionsResponseCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
