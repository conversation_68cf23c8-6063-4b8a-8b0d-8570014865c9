// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'user_moment_status_dto.dart';

class UserMomentStatusDtoMapper extends ClassMapperBase<UserMomentStatusDto> {
  UserMomentStatusDtoMapper._();

  static UserMomentStatusDtoMapper? _instance;
  static UserMomentStatusDtoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = UserMomentStatusDtoMapper._());
      UserCommentStatusDtoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'UserMomentStatusDto';

  static bool _$upVoted(UserMomentStatusDto v) => v.upVoted;
  static const Field<UserMomentStatusDto, bool> _f$upVoted =
      Field('upVoted', _$upVoted, key: r'up_voted');
  static bool _$downVoted(UserMomentStatusDto v) => v.downVoted;
  static const Field<UserMomentStatusDto, bool> _f$downVoted =
      Field('downVoted', _$downVoted, key: r'down_voted');
  static bool _$followed(UserMomentStatusDto v) => v.followed;
  static const Field<UserMomentStatusDto, bool> _f$followed =
      Field('followed', _$followed);
  static int _$upVoteCount(UserMomentStatusDto v) => v.upVoteCount;
  static const Field<UserMomentStatusDto, int> _f$upVoteCount =
      Field('upVoteCount', _$upVoteCount, key: r'up_vote_count');
  static int _$downVoteCount(UserMomentStatusDto v) => v.downVoteCount;
  static const Field<UserMomentStatusDto, int> _f$downVoteCount =
      Field('downVoteCount', _$downVoteCount, key: r'down_vote_count');
  static List<UserCommentStatusDto> _$commentsStatus(UserMomentStatusDto v) =>
      v.commentsStatus;
  static const Field<UserMomentStatusDto, List<UserCommentStatusDto>>
      _f$commentsStatus =
      Field('commentsStatus', _$commentsStatus, key: r'comments_status');

  @override
  final MappableFields<UserMomentStatusDto> fields = const {
    #upVoted: _f$upVoted,
    #downVoted: _f$downVoted,
    #followed: _f$followed,
    #upVoteCount: _f$upVoteCount,
    #downVoteCount: _f$downVoteCount,
    #commentsStatus: _f$commentsStatus,
  };

  static UserMomentStatusDto _instantiate(DecodingData data) {
    return UserMomentStatusDto(
        upVoted: data.dec(_f$upVoted),
        downVoted: data.dec(_f$downVoted),
        followed: data.dec(_f$followed),
        upVoteCount: data.dec(_f$upVoteCount),
        downVoteCount: data.dec(_f$downVoteCount),
        commentsStatus: data.dec(_f$commentsStatus));
  }

  @override
  final Function instantiate = _instantiate;

  static UserMomentStatusDto fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<UserMomentStatusDto>(map);
  }

  static UserMomentStatusDto fromJson(String json) {
    return ensureInitialized().decodeJson<UserMomentStatusDto>(json);
  }
}

mixin UserMomentStatusDtoMappable {
  String toJson() {
    return UserMomentStatusDtoMapper.ensureInitialized()
        .encodeJson<UserMomentStatusDto>(this as UserMomentStatusDto);
  }

  Map<String, dynamic> toMap() {
    return UserMomentStatusDtoMapper.ensureInitialized()
        .encodeMap<UserMomentStatusDto>(this as UserMomentStatusDto);
  }

  UserMomentStatusDtoCopyWith<UserMomentStatusDto, UserMomentStatusDto,
      UserMomentStatusDto> get copyWith => _UserMomentStatusDtoCopyWithImpl<
          UserMomentStatusDto, UserMomentStatusDto>(
      this as UserMomentStatusDto, $identity, $identity);
  @override
  String toString() {
    return UserMomentStatusDtoMapper.ensureInitialized()
        .stringifyValue(this as UserMomentStatusDto);
  }

  @override
  bool operator ==(Object other) {
    return UserMomentStatusDtoMapper.ensureInitialized()
        .equalsValue(this as UserMomentStatusDto, other);
  }

  @override
  int get hashCode {
    return UserMomentStatusDtoMapper.ensureInitialized()
        .hashValue(this as UserMomentStatusDto);
  }
}

extension UserMomentStatusDtoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, UserMomentStatusDto, $Out> {
  UserMomentStatusDtoCopyWith<$R, UserMomentStatusDto, $Out>
      get $asUserMomentStatusDto => $base.as(
          (v, t, t2) => _UserMomentStatusDtoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class UserMomentStatusDtoCopyWith<$R, $In extends UserMomentStatusDto,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<
      $R,
      UserCommentStatusDto,
      UserCommentStatusDtoCopyWith<$R, UserCommentStatusDto,
          UserCommentStatusDto>> get commentsStatus;
  $R call(
      {bool? upVoted,
      bool? downVoted,
      bool? followed,
      int? upVoteCount,
      int? downVoteCount,
      List<UserCommentStatusDto>? commentsStatus});
  UserMomentStatusDtoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _UserMomentStatusDtoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, UserMomentStatusDto, $Out>
    implements UserMomentStatusDtoCopyWith<$R, UserMomentStatusDto, $Out> {
  _UserMomentStatusDtoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<UserMomentStatusDto> $mapper =
      UserMomentStatusDtoMapper.ensureInitialized();
  @override
  ListCopyWith<
      $R,
      UserCommentStatusDto,
      UserCommentStatusDtoCopyWith<$R, UserCommentStatusDto,
          UserCommentStatusDto>> get commentsStatus => ListCopyWith(
      $value.commentsStatus,
      (v, t) => v.copyWith.$chain(t),
      (v) => call(commentsStatus: v));
  @override
  $R call(
          {bool? upVoted,
          bool? downVoted,
          bool? followed,
          int? upVoteCount,
          int? downVoteCount,
          List<UserCommentStatusDto>? commentsStatus}) =>
      $apply(FieldCopyWithData({
        if (upVoted != null) #upVoted: upVoted,
        if (downVoted != null) #downVoted: downVoted,
        if (followed != null) #followed: followed,
        if (upVoteCount != null) #upVoteCount: upVoteCount,
        if (downVoteCount != null) #downVoteCount: downVoteCount,
        if (commentsStatus != null) #commentsStatus: commentsStatus
      }));
  @override
  UserMomentStatusDto $make(CopyWithData data) => UserMomentStatusDto(
      upVoted: data.get(#upVoted, or: $value.upVoted),
      downVoted: data.get(#downVoted, or: $value.downVoted),
      followed: data.get(#followed, or: $value.followed),
      upVoteCount: data.get(#upVoteCount, or: $value.upVoteCount),
      downVoteCount: data.get(#downVoteCount, or: $value.downVoteCount),
      commentsStatus: data.get(#commentsStatus, or: $value.commentsStatus));

  @override
  UserMomentStatusDtoCopyWith<$R2, UserMomentStatusDto, $Out2>
      $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
          _UserMomentStatusDtoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
