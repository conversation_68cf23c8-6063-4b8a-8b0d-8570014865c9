import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/models/user.dart';

part 'moment_vo.mapper.dart';

@MappableClass()
class MomentImageVo with MomentImageVoMappable {
  final int? id;
  @MappableField(key: 'moment_id')
  final int? momentId;
  @MappableField(key: 'image_url')
  final String imageUrl;
  @MappableField(key: 'display_order')
  final int? displayOrder;

  const MomentImageVo({
    this.id,
    this.momentId,
    required this.imageUrl,
    this.displayOrder,
  });

  static final fromMap = MomentImageVoMapper.fromMap;
}

@MappableClass()
class MomentVo with MomentVoMappable {
  final int? id;
  final User? publisher;
  final int? userId;
  final String? userName;
  final String? userAvatar;
  final String? momentType;
  final String? content;
  final String? visibility;
  final int? fishingSpotId;
  final String? fishingSpotName;
  final String? typeSpecificData;
  final List<MomentImageVo>? images;
  final int? likeCount;
  final int? commentCount;
  final int? viewCount;
  final bool? isLiked;
  final bool? isBookmarked;
  final String? createdAt;
  final String? updatedAt;

  MomentVo({
    this.id,
    this.publisher,
    this.userId,
    this.userName,
    this.userAvatar,
    this.momentType,
    this.content,
    this.visibility,
    this.fishingSpotId,
    this.fishingSpotName,
    this.typeSpecificData,
    this.images,
    this.likeCount,
    this.commentCount,
    this.viewCount,
    this.isLiked,
    this.isBookmarked,
    this.createdAt,
    this.updatedAt,
  });

  // Convenience getters for backward compatibility
  List<String>? get pictures => images?.map((img) => img.imageUrl).toList();

  int get numberOfLikes => likeCount ?? 0;

  num get numberOfComments => commentCount ?? 0;

  bool? get bookmarked => isBookmarked;

  bool? get followed => false; // This field is no longer in the model

  String? get tag => null; // This field is no longer in the model

  String? get addressDetail => null; // This field is no longer in the model

  String? get province => null; // This field is no longer in the model

  String? get city => null; // This field is no longer in the model

  String? get county => null; // This field is no longer in the model

  double? get longitude => null; // This field is no longer in the model

  double? get latitude => null; // This field is no longer in the model

  num? get fishCatch => null; // This field is no longer in the model

  num get followCount => 0; // This field is no longer in the model

  num get attentionCount => 0; // This field is no longer in the model

  num get momentCount => 0; // This field is no longer in the model

  String? get updateTime => updatedAt;

  DateTime? get createTime =>
      createdAt != null ? DateTime.tryParse(createdAt!) : null;

  // Location getter - for backward compatibility, returns fishing spot name if available
  String? get location {
    return fishingSpotName ?? '未知地点';
  }

  // Tags getter - for backward compatibility, returns empty list
  List<String>? get tags {
    return null;
  }

  static final fromMap = MomentVoMapper.fromMap;
}
