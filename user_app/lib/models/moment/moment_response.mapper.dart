// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'moment_response.dart';

class MomentResponseMapper extends ClassMapperBase<MomentResponse> {
  MomentResponseMapper._();

  static MomentResponseMapper? _instance;
  static MomentResponseMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = MomentResponseMapper._());
      ResponsePageInfoMapper.ensureInitialized();
      MomentVoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'MomentResponse';

  static List<MomentVo> _$records(MomentResponse v) => v.records;
  static const Field<MomentResponse, List<MomentVo>> _f$records =
      Field('records', _$records);
  static num _$current(MomentResponse v) => v.current;
  static const Field<MomentResponse, num> _f$current =
      Field('current', _$current);
  static num _$size(MomentResponse v) => v.size;
  static const Field<MomentResponse, num> _f$size = Field('size', _$size);
  static num _$total(MomentResponse v) => v.total;
  static const Field<MomentResponse, num> _f$total = Field('total', _$total);
  static num _$pages(MomentResponse v) => v.pages;
  static const Field<MomentResponse, num> _f$pages = Field('pages', _$pages);

  @override
  final MappableFields<MomentResponse> fields = const {
    #records: _f$records,
    #current: _f$current,
    #size: _f$size,
    #total: _f$total,
    #pages: _f$pages,
  };

  static MomentResponse _instantiate(DecodingData data) {
    return MomentResponse(
        records: data.dec(_f$records),
        current: data.dec(_f$current),
        size: data.dec(_f$size),
        total: data.dec(_f$total),
        pages: data.dec(_f$pages));
  }

  @override
  final Function instantiate = _instantiate;

  static MomentResponse fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<MomentResponse>(map);
  }

  static MomentResponse fromJson(String json) {
    return ensureInitialized().decodeJson<MomentResponse>(json);
  }
}

mixin MomentResponseMappable {
  String toJson() {
    return MomentResponseMapper.ensureInitialized()
        .encodeJson<MomentResponse>(this as MomentResponse);
  }

  Map<String, dynamic> toMap() {
    return MomentResponseMapper.ensureInitialized()
        .encodeMap<MomentResponse>(this as MomentResponse);
  }

  MomentResponseCopyWith<MomentResponse, MomentResponse, MomentResponse>
      get copyWith =>
          _MomentResponseCopyWithImpl<MomentResponse, MomentResponse>(
              this as MomentResponse, $identity, $identity);
  @override
  String toString() {
    return MomentResponseMapper.ensureInitialized()
        .stringifyValue(this as MomentResponse);
  }

  @override
  bool operator ==(Object other) {
    return MomentResponseMapper.ensureInitialized()
        .equalsValue(this as MomentResponse, other);
  }

  @override
  int get hashCode {
    return MomentResponseMapper.ensureInitialized()
        .hashValue(this as MomentResponse);
  }
}

extension MomentResponseValueCopy<$R, $Out>
    on ObjectCopyWith<$R, MomentResponse, $Out> {
  MomentResponseCopyWith<$R, MomentResponse, $Out> get $asMomentResponse =>
      $base.as((v, t, t2) => _MomentResponseCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class MomentResponseCopyWith<$R, $In extends MomentResponse, $Out>
    implements ResponsePageInfoCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, MomentVo, MomentVoCopyWith<$R, MomentVo, MomentVo>>
      get records;
  @override
  $R call(
      {List<MomentVo>? records,
      num? current,
      num? size,
      num? total,
      num? pages});
  MomentResponseCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _MomentResponseCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, MomentResponse, $Out>
    implements MomentResponseCopyWith<$R, MomentResponse, $Out> {
  _MomentResponseCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<MomentResponse> $mapper =
      MomentResponseMapper.ensureInitialized();
  @override
  ListCopyWith<$R, MomentVo, MomentVoCopyWith<$R, MomentVo, MomentVo>>
      get records => ListCopyWith($value.records,
          (v, t) => v.copyWith.$chain(t), (v) => call(records: v));
  @override
  $R call(
          {List<MomentVo>? records,
          num? current,
          num? size,
          num? total,
          num? pages}) =>
      $apply(FieldCopyWithData({
        if (records != null) #records: records,
        if (current != null) #current: current,
        if (size != null) #size: size,
        if (total != null) #total: total,
        if (pages != null) #pages: pages
      }));
  @override
  MomentResponse $make(CopyWithData data) => MomentResponse(
      records: data.get(#records, or: $value.records),
      current: data.get(#current, or: $value.current),
      size: data.get(#size, or: $value.size),
      total: data.get(#total, or: $value.total),
      pages: data.get(#pages, or: $value.pages));

  @override
  MomentResponseCopyWith<$R2, MomentResponse, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _MomentResponseCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
