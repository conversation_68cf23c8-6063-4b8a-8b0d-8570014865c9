// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'coordinate.dart';

class CoordinateMapper extends ClassMapperBase<Coordinate> {
  CoordinateMapper._();

  static CoordinateMapper? _instance;
  static CoordinateMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = CoordinateMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'Coordinate';

  static double? _$longitude(Coordinate v) => v.longitude;
  static const Field<Coordinate, double> _f$longitude =
      Field('longitude', _$longitude, opt: true);
  static double? _$latitude(Coordinate v) => v.latitude;
  static const Field<Coordinate, double> _f$latitude =
      Field('latitude', _$latitude, opt: true);
  static String? _$province(Coordinate v) => v.province;
  static const Field<Coordinate, String> _f$province =
      Field('province', _$province, mode: FieldMode.member);
  static String? _$city(Coordinate v) => v.city;
  static const Field<Coordinate, String> _f$city =
      Field('city', _$city, mode: FieldMode.member);
  static String? _$county(Coordinate v) => v.county;
  static const Field<Coordinate, String> _f$county =
      Field('county', _$county, mode: FieldMode.member);
  static String? _$addressDetail(Coordinate v) => v.addressDetail;
  static const Field<Coordinate, String> _f$addressDetail = Field(
      'addressDetail', _$addressDetail,
      key: r'address_detail', mode: FieldMode.member);

  @override
  final MappableFields<Coordinate> fields = const {
    #longitude: _f$longitude,
    #latitude: _f$latitude,
    #province: _f$province,
    #city: _f$city,
    #county: _f$county,
    #addressDetail: _f$addressDetail,
  };

  static Coordinate _instantiate(DecodingData data) {
    return Coordinate(
        longitude: data.dec(_f$longitude), latitude: data.dec(_f$latitude));
  }

  @override
  final Function instantiate = _instantiate;

  static Coordinate fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<Coordinate>(map);
  }

  static Coordinate fromJson(String json) {
    return ensureInitialized().decodeJson<Coordinate>(json);
  }
}

mixin CoordinateMappable {
  String toJson() {
    return CoordinateMapper.ensureInitialized()
        .encodeJson<Coordinate>(this as Coordinate);
  }

  Map<String, dynamic> toMap() {
    return CoordinateMapper.ensureInitialized()
        .encodeMap<Coordinate>(this as Coordinate);
  }

  CoordinateCopyWith<Coordinate, Coordinate, Coordinate> get copyWith =>
      _CoordinateCopyWithImpl<Coordinate, Coordinate>(
          this as Coordinate, $identity, $identity);
  @override
  String toString() {
    return CoordinateMapper.ensureInitialized()
        .stringifyValue(this as Coordinate);
  }

  @override
  bool operator ==(Object other) {
    return CoordinateMapper.ensureInitialized()
        .equalsValue(this as Coordinate, other);
  }

  @override
  int get hashCode {
    return CoordinateMapper.ensureInitialized().hashValue(this as Coordinate);
  }
}

extension CoordinateValueCopy<$R, $Out>
    on ObjectCopyWith<$R, Coordinate, $Out> {
  CoordinateCopyWith<$R, Coordinate, $Out> get $asCoordinate =>
      $base.as((v, t, t2) => _CoordinateCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class CoordinateCopyWith<$R, $In extends Coordinate, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call({double? longitude, double? latitude});
  CoordinateCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _CoordinateCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, Coordinate, $Out>
    implements CoordinateCopyWith<$R, Coordinate, $Out> {
  _CoordinateCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<Coordinate> $mapper =
      CoordinateMapper.ensureInitialized();
  @override
  $R call({Object? longitude = $none, Object? latitude = $none}) =>
      $apply(FieldCopyWithData({
        if (longitude != $none) #longitude: longitude,
        if (latitude != $none) #latitude: latitude
      }));
  @override
  Coordinate $make(CopyWithData data) => Coordinate(
      longitude: data.get(#longitude, or: $value.longitude),
      latitude: data.get(#latitude, or: $value.latitude));

  @override
  CoordinateCopyWith<$R2, Coordinate, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _CoordinateCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
