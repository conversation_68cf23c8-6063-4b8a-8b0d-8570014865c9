import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/models/request_page_info.dart';

part 'user_fans_attentions_request.mapper.dart';

@MappableClass()
class UserFansAttentionsRequest extends RequestPageInfo
    with UserFansAttentionsRequestMappable {
  num userId;

  UserFansAttentionsRequest({
    required this.userId,
    required super.pageNum,
    required super.pageSize,
  });

  static final fromMap = UserFansAttentionsRequestMapper.fromMap;
}
