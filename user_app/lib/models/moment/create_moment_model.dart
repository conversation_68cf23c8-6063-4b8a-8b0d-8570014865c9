import 'package:dart_mappable/dart_mappable.dart';

part 'create_moment_model.mapper.dart';

@MappableClass()
class CreateMomentModel with CreateMomentModelMappable {
  /// 动态内容
  final String content;

  /// 图片列表
  @MappableField(key: 'image_urls')
  final List<String> pictures;

  /// 动态类型：fishing_catch/equipment/technique/question
  final String momentType;

  /// 可见性：public/followers/private
  final String visibility;

  /// 钓点ID，可为空
  final int? fishingSpotId;

  /// 类型特定数据，JSON格式字符串
  final String? typeSpecificData;

  CreateMomentModel({
    required this.content,
    required this.pictures,
    required this.momentType,
    required this.visibility,
    this.fishingSpotId,
    this.typeSpecificData,
  });
}
