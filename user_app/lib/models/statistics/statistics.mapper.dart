// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'statistics.dart';

class StatisticsMapper extends ClassMapperBase<Statistics> {
  StatisticsMapper._();

  static StatisticsMapper? _instance;
  static StatisticsMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = StatisticsMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'Statistics';

  static int _$momentCount(Statistics v) => v.momentCount;
  static const Field<Statistics, int> _f$momentCount =
      Field('momentCount', _$momentCount, key: r'moment_count');
  static int _$fansCount(Statistics v) => v.fansCount;
  static const Field<Statistics, int> _f$fansCount =
      Field('fansCount', _$fansCount, key: r'fans_count');
  static int _$followCount(Statistics v) => v.followCount;
  static const Field<Statistics, int> _f$followCount =
      Field('followCount', _$followCount, key: r'follow_count');

  @override
  final MappableFields<Statistics> fields = const {
    #momentCount: _f$momentCount,
    #fansCount: _f$fansCount,
    #followCount: _f$followCount,
  };

  static Statistics _instantiate(DecodingData data) {
    return Statistics(
        momentCount: data.dec(_f$momentCount),
        fansCount: data.dec(_f$fansCount),
        followCount: data.dec(_f$followCount));
  }

  @override
  final Function instantiate = _instantiate;

  static Statistics fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<Statistics>(map);
  }

  static Statistics fromJson(String json) {
    return ensureInitialized().decodeJson<Statistics>(json);
  }
}

mixin StatisticsMappable {
  String toJson() {
    return StatisticsMapper.ensureInitialized()
        .encodeJson<Statistics>(this as Statistics);
  }

  Map<String, dynamic> toMap() {
    return StatisticsMapper.ensureInitialized()
        .encodeMap<Statistics>(this as Statistics);
  }

  StatisticsCopyWith<Statistics, Statistics, Statistics> get copyWith =>
      _StatisticsCopyWithImpl<Statistics, Statistics>(
          this as Statistics, $identity, $identity);
  @override
  String toString() {
    return StatisticsMapper.ensureInitialized()
        .stringifyValue(this as Statistics);
  }

  @override
  bool operator ==(Object other) {
    return StatisticsMapper.ensureInitialized()
        .equalsValue(this as Statistics, other);
  }

  @override
  int get hashCode {
    return StatisticsMapper.ensureInitialized().hashValue(this as Statistics);
  }
}

extension StatisticsValueCopy<$R, $Out>
    on ObjectCopyWith<$R, Statistics, $Out> {
  StatisticsCopyWith<$R, Statistics, $Out> get $asStatistics =>
      $base.as((v, t, t2) => _StatisticsCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class StatisticsCopyWith<$R, $In extends Statistics, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call({int? momentCount, int? fansCount, int? followCount});
  StatisticsCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _StatisticsCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, Statistics, $Out>
    implements StatisticsCopyWith<$R, Statistics, $Out> {
  _StatisticsCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<Statistics> $mapper =
      StatisticsMapper.ensureInitialized();
  @override
  $R call({int? momentCount, int? fansCount, int? followCount}) =>
      $apply(FieldCopyWithData({
        if (momentCount != null) #momentCount: momentCount,
        if (fansCount != null) #fansCount: fansCount,
        if (followCount != null) #followCount: followCount
      }));
  @override
  Statistics $make(CopyWithData data) => Statistics(
      momentCount: data.get(#momentCount, or: $value.momentCount),
      fansCount: data.get(#fansCount, or: $value.fansCount),
      followCount: data.get(#followCount, or: $value.followCount));

  @override
  StatisticsCopyWith<$R2, Statistics, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _StatisticsCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
