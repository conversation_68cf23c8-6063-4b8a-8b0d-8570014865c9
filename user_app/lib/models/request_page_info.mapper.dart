// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'request_page_info.dart';

class RequestPageInfoMapper extends ClassMapperBase<RequestPageInfo> {
  RequestPageInfoMapper._();

  static RequestPageInfoMapper? _instance;
  static RequestPageInfoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = RequestPageInfoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'RequestPageInfo';

  static num? _$pageNum(RequestPageInfo v) => v.pageNum;
  static const Field<RequestPageInfo, num> _f$pageNum =
      Field('pageNum', _$pageNum, key: r'page_num', opt: true, def: 1);
  static num? _$pageSize(RequestPageInfo v) => v.pageSize;
  static const Field<RequestPageInfo, num> _f$pageSize =
      Field('pageSize', _$pageSize, key: r'page_size', opt: true, def: 10);

  @override
  final MappableFields<RequestPageInfo> fields = const {
    #pageNum: _f$pageNum,
    #pageSize: _f$pageSize,
  };

  static RequestPageInfo _instantiate(DecodingData data) {
    return RequestPageInfo(
        pageNum: data.dec(_f$pageNum), pageSize: data.dec(_f$pageSize));
  }

  @override
  final Function instantiate = _instantiate;

  static RequestPageInfo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<RequestPageInfo>(map);
  }

  static RequestPageInfo fromJson(String json) {
    return ensureInitialized().decodeJson<RequestPageInfo>(json);
  }
}

mixin RequestPageInfoMappable {
  String toJson() {
    return RequestPageInfoMapper.ensureInitialized()
        .encodeJson<RequestPageInfo>(this as RequestPageInfo);
  }

  Map<String, dynamic> toMap() {
    return RequestPageInfoMapper.ensureInitialized()
        .encodeMap<RequestPageInfo>(this as RequestPageInfo);
  }

  RequestPageInfoCopyWith<RequestPageInfo, RequestPageInfo, RequestPageInfo>
      get copyWith =>
          _RequestPageInfoCopyWithImpl<RequestPageInfo, RequestPageInfo>(
              this as RequestPageInfo, $identity, $identity);
  @override
  String toString() {
    return RequestPageInfoMapper.ensureInitialized()
        .stringifyValue(this as RequestPageInfo);
  }

  @override
  bool operator ==(Object other) {
    return RequestPageInfoMapper.ensureInitialized()
        .equalsValue(this as RequestPageInfo, other);
  }

  @override
  int get hashCode {
    return RequestPageInfoMapper.ensureInitialized()
        .hashValue(this as RequestPageInfo);
  }
}

extension RequestPageInfoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, RequestPageInfo, $Out> {
  RequestPageInfoCopyWith<$R, RequestPageInfo, $Out> get $asRequestPageInfo =>
      $base.as((v, t, t2) => _RequestPageInfoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class RequestPageInfoCopyWith<$R, $In extends RequestPageInfo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call({num? pageNum, num? pageSize});
  RequestPageInfoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _RequestPageInfoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, RequestPageInfo, $Out>
    implements RequestPageInfoCopyWith<$R, RequestPageInfo, $Out> {
  _RequestPageInfoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<RequestPageInfo> $mapper =
      RequestPageInfoMapper.ensureInitialized();
  @override
  $R call({Object? pageNum = $none, Object? pageSize = $none}) =>
      $apply(FieldCopyWithData({
        if (pageNum != $none) #pageNum: pageNum,
        if (pageSize != $none) #pageSize: pageSize
      }));
  @override
  RequestPageInfo $make(CopyWithData data) => RequestPageInfo(
      pageNum: data.get(#pageNum, or: $value.pageNum),
      pageSize: data.get(#pageSize, or: $value.pageSize));

  @override
  RequestPageInfoCopyWith<$R2, RequestPageInfo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _RequestPageInfoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
