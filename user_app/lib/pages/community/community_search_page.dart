import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:shimmer/shimmer.dart';

class CommunitySearchPage extends StatefulWidget {
  const CommunitySearchPage({super.key});

  @override
  State<CommunitySearchPage> createState() => _CommunitySearchPageState();
}

class _CommunitySearchPageState extends State<CommunitySearchPage>
    with TickerProviderStateMixin {
  // 搜索控制器
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _bounceController;
  late AnimationController _rippleController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _bounceAnimation;
  late Animation<double> _rippleAnimation;

  // 列表项动画
  final List<AnimationController> _itemControllers = [];

  // 搜索状态
  bool _isSearching = false;
  bool _isLoading = false;
  bool _hasSearched = false;
  String _searchQuery = '';
  String _selectedFilter = 'all'; // all, moment, user, spot

  // 搜索数据
  List<SearchResult> _searchResults = [];
  List<String> _searchHistory = [];
  List<String> _hotSearches = [];
  List<String> _suggestions = [];

  @override
  void initState() {
    super.initState();

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _rippleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    _bounceAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.bounceOut,
    ));
    _rippleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeInOut,
    ));

    // 启动动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();
    _bounceController.forward();

    // 监听搜索输入
    _searchController.addListener(_onSearchTextChanged);
    _searchFocusNode.addListener(_onFocusChanged);

    // 加载初始数据
    _loadInitialData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _scrollController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _bounceController.dispose();
    _rippleController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadInitialData() {
    // 模拟加载数据
    setState(() {
      _searchHistory = ['夜钓技巧', '鲤鱼饵料', '东湖钓场', '路亚装备'];
      _hotSearches = ['冬季钓鱼', '新手入门', '钓鱼比赛', '钓具推荐', '钓点分享', '饵料配方'];
    });
  }

  void _onSearchTextChanged() {
    final query = _searchController.text;
    if (query.isEmpty) {
      setState(() {
        _suggestions = [];
      });
      return;
    }

    // 模拟搜索建议
    setState(() {
      _suggestions = [
        '$query钓法',
        '$query饵料',
        '$query技巧',
        '$query装备',
      ];
    });
  }

  void _onFocusChanged() {
    setState(() {
      _isSearching = _searchFocusNode.hasFocus;
    });
  }

  AnimationController _createItemController(int index) {
    while (index >= _itemControllers.length) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 400 + (_itemControllers.length * 100)),
        vsync: this,
      );
      _itemControllers.add(controller);
      controller.forward();
    }
    return _itemControllers[index];
  }

  Future<void> _performSearch() async {
    if (_searchController.text.isEmpty) return;

    HapticFeedback.lightImpact();
    FocusScope.of(context).unfocus();

    setState(() {
      _isLoading = true;
      _hasSearched = true;
      _searchQuery = _searchController.text;
    });

    // 添加到搜索历史
    if (!_searchHistory.contains(_searchQuery)) {
      _searchHistory.insert(0, _searchQuery);
      if (_searchHistory.length > 10) {
        _searchHistory.removeLast();
      }
    }

    // 模拟搜索延迟
    await Future.delayed(const Duration(seconds: 1));

    // 模拟搜索结果
    setState(() {
      _searchResults = List.generate(10, (index) {
        final types = ['moment', 'user', 'spot'];
        final type = types[index % 3];

        return SearchResult(
          id: index + 1,
          type: type,
          title: _getTitleByType(type, index),
          subtitle: _getSubtitleByType(type, index),
          imageUrl: index % 2 == 0 ? 'https://example.com/image.jpg' : null,
          tags: _getTagsByType(type),
          stats: _getStatsByType(type),
          createdAt: DateTime.now().subtract(Duration(hours: index * 2)),
        );
      });
      _isLoading = false;
    });
  }

  String _getTitleByType(String type, int index) {
    switch (type) {
      case 'moment':
        return '${_searchQuery}相关动态 ${index + 1}';
      case 'user':
        return '钓友_${_searchQuery}_${index + 1}';
      case 'spot':
        return '${_searchQuery}钓场 ${index + 1}';
      default:
        return '搜索结果 ${index + 1}';
    }
  }

  String _getSubtitleByType(String type, int index) {
    switch (type) {
      case 'moment':
        return '分享了关于${_searchQuery}的钓鱼经验，获得了很多钓友的点赞...';
      case 'user':
        return '资深钓友，擅长${_searchQuery}，已发布${index + 10}篇相关内容';
      case 'spot':
        return '位置优越，适合${_searchQuery}，已有${(index + 1) * 23}位钓友打卡';
      default:
        return '相关内容描述';
    }
  }

  List<String> _getTagsByType(String type) {
    switch (type) {
      case 'moment':
        return ['技巧分享', _searchQuery];
      case 'user':
        return ['资深钓友', _searchQuery];
      case 'spot':
        return ['热门钓点', _searchQuery];
      default:
        return [_searchQuery];
    }
  }

  Map<String, dynamic> _getStatsByType(String type) {
    switch (type) {
      case 'moment':
        return {'likes': 156, 'comments': 23, 'views': 1234};
      case 'user':
        return {'followers': 856, 'moments': 42};
      case 'spot':
        return {'checkins': 234, 'rating': 4.5};
      default:
        return {};
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: Stack(
        children: [
          // 渐变背景
          Container(
            height: 300,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF4E8BF7),
                  Color(0xFF667EEA),
                ],
              ),
            ),
          ),
          // 装饰元素
          Positioned(
            top: 100,
            right: -50,
            child: AnimatedBuilder(
              animation: _rippleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _rippleAnimation.value,
                  child: Container(
                    width: 150,
                    height: 150,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withOpacity(0.1),
                    ),
                  ),
                );
              },
            ),
          ),
          // 主内容
          SafeArea(
            child: Column(
              children: [
                // 搜索头部
                _buildSearchHeader(),
                // 内容区域
                Expanded(
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: _buildContent(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        children: [
          // 搜索栏
          Row(
            children: [
              // 返回按钮
              IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.arrow_back_ios,
                      color: Colors.white, size: 20),
                ),
                onPressed: () {
                  HapticFeedback.lightImpact();
                  Navigator.pop(context);
                },
              ),
              // 搜索输入框
              Expanded(
                child: Container(
                  height: 44,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(22),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    focusNode: _searchFocusNode,
                    textInputAction: TextInputAction.search,
                    onSubmitted: (_) => _performSearch(),
                    decoration: InputDecoration(
                      hintText: '搜索动态、钓友、钓点...',
                      hintStyle: TextStyle(color: Colors.grey[400]),
                      border: InputBorder.none,
                      contentPadding:
                          const EdgeInsets.symmetric(horizontal: 20),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: Icon(Icons.clear, color: Colors.grey[400]),
                              onPressed: () {
                                _searchController.clear();
                                setState(() {
                                  _hasSearched = false;
                                  _searchResults = [];
                                });
                              },
                            )
                          : Icon(Icons.search, color: Colors.grey[400]),
                    ),
                  ),
                ),
              ),
              // 搜索按钮
              const SizedBox(width: 8),
              ScaleTransition(
                scale: _bounceAnimation,
                child: TextButton(
                  onPressed: _performSearch,
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                  ),
                  child: const Text(
                    '搜索',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
          // 筛选标签
          if (_hasSearched) ...[
            const SizedBox(height: 12),
            _buildFilterTags(),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterTags() {
    final filters = [
      {'key': 'all', 'label': '全部', 'icon': Icons.apps},
      {'key': 'moment', 'label': '动态', 'icon': Icons.article},
      {'key': 'user', 'label': '钓友', 'icon': Icons.person},
      {'key': 'spot', 'label': '钓点', 'icon': Icons.location_on},
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: filters.map((filter) {
          final isSelected = _selectedFilter == filter['key'];
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    filter['icon'] as IconData,
                    size: 16,
                    color: isSelected ? Colors.white : Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(filter['label'] as String),
                ],
              ),
              selected: isSelected,
              onSelected: (selected) {
                HapticFeedback.lightImpact();
                setState(() {
                  _selectedFilter = filter['key'] as String;
                });
                _performSearch();
              },
              backgroundColor: Colors.white,
              selectedColor: const Color(0xFF4E8BF7),
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              elevation: isSelected ? 4 : 0,
              pressElevation: 8,
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_hasSearched) {
      if (_searchResults.isEmpty) {
        return _buildEmptyResultState();
      }
      return _buildSearchResults();
    }

    return _buildInitialState();
  }

  Widget _buildInitialState() {
    return SingleChildScrollView(
      padding: const EdgeInsets.only(top: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索建议
          if (_suggestions.isNotEmpty) ...[
            _buildSuggestionsSection(),
            const SizedBox(height: 24),
          ],
          // 搜索历史
          if (_searchHistory.isNotEmpty) ...[
            _buildHistorySection(),
            const SizedBox(height: 24),
          ],
          // 热门搜索
          _buildHotSearchSection(),
          const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildSuggestionsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '搜索建议',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1A1E25),
            ),
          ),
          const SizedBox(height: 12),
          ...List.generate(
            math.min(_suggestions.length, 4),
            (index) => InkWell(
              onTap: () {
                HapticFeedback.lightImpact();
                _searchController.text = _suggestions[index];
                _performSearch();
              },
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Icon(Icons.search, size: 18, color: Colors.grey[400]),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _suggestions[index],
                        style: TextStyle(
                          fontSize: 15,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    Icon(Icons.north_west, size: 16, color: Colors.grey[400]),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistorySection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '搜索历史',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1A1E25),
                ),
              ),
              TextButton(
                onPressed: () {
                  HapticFeedback.lightImpact();
                  _showClearHistoryDialog();
                },
                child: const Text(
                  '清空',
                  style: TextStyle(color: Color(0xFF4E8BF7)),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: _searchHistory.map((keyword) {
              return GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  _searchController.text = keyword;
                  _performSearch();
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.grey[300]!,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.history, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 6),
                      Text(
                        keyword,
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHotSearchSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFFFF6B6B), Color(0xFFFF8E53)],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.local_fire_department,
                    color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              const Text(
                '热门搜索',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1A1E25),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: List.generate(_hotSearches.length, (index) {
              final keyword = _hotSearches[index];
              final isTop3 = index < 3;

              return GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  _searchController.text = keyword;
                  _performSearch();
                },
                child: Container(
                  padding: EdgeInsets.only(
                    left: isTop3 ? 8 : 14,
                    right: 14,
                    top: 8,
                    bottom: 8,
                  ),
                  decoration: BoxDecoration(
                    gradient: isTop3
                        ? LinearGradient(
                            colors: [
                              const Color(0xFFFF6B6B).withOpacity(0.1),
                              const Color(0xFFFF8E53).withOpacity(0.1),
                            ],
                          )
                        : null,
                    color: isTop3 ? null : Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isTop3
                          ? const Color(0xFFFF6B6B).withOpacity(0.3)
                          : Colors.grey[300]!,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isTop3) ...[
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFFFF6B6B), Color(0xFFFF8E53)],
                            ),
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Text(
                              '${index + 1}',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 11,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 6),
                      ],
                      Text(
                        keyword,
                        style: TextStyle(
                          color: isTop3
                              ? const Color(0xFFFF6B6B)
                              : Colors.grey[700],
                          fontSize: 14,
                          fontWeight:
                              isTop3 ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.only(bottom: 80),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        return _buildSearchResultItem(_searchResults[index], index);
      },
    );
  }

  Widget _buildSearchResultItem(SearchResult result, int index) {
    final controller = _createItemController(index);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (controller.value * 0.2),
          child: Opacity(
            opacity: controller.value,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colors.white, Color(0xFFFBFCFE)],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: InkWell(
                onTap: () => _navigateToDetail(result),
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 类型标签和标题
                      Row(
                        children: [
                          _buildTypeLabel(result.type),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              result.title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF1A1E25),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      // 副标题
                      Text(
                        result.subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          height: 1.4,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      // 标签
                      if (result.tags.isNotEmpty) ...[
                        const SizedBox(height: 12),
                        Wrap(
                          spacing: 8,
                          children: result.tags.map((tag) {
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 4),
                              decoration: BoxDecoration(
                                color: const Color(0xFF4E8BF7).withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                tag,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF4E8BF7),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ],
                      // 统计信息
                      const SizedBox(height: 12),
                      _buildStats(result),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTypeLabel(String type) {
    IconData icon;
    Color color;
    String label;

    switch (type) {
      case 'moment':
        icon = Icons.article;
        color = const Color(0xFF667EEA);
        label = '动态';
        break;
      case 'user':
        icon = Icons.person;
        color = const Color(0xFF00C9A7);
        label = '钓友';
        break;
      case 'spot':
        icon = Icons.location_on;
        color = const Color(0xFFFF6B6B);
        label = '钓点';
        break;
      default:
        icon = Icons.search;
        color = Colors.grey;
        label = '其他';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color.withOpacity(0.8), color],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.white),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStats(SearchResult result) {
    final stats = result.stats;

    return Row(
      children: [
        if (stats['likes'] != null)
          _buildStatItem(Icons.favorite, stats['likes'].toString()),
        if (stats['comments'] != null)
          _buildStatItem(Icons.comment, stats['comments'].toString()),
        if (stats['views'] != null)
          _buildStatItem(Icons.visibility, _formatCount(stats['views'])),
        if (stats['followers'] != null)
          _buildStatItem(Icons.people, _formatCount(stats['followers'])),
        if (stats['moments'] != null)
          _buildStatItem(Icons.article, stats['moments'].toString()),
        if (stats['checkins'] != null)
          _buildStatItem(Icons.check_circle, stats['checkins'].toString()),
        if (stats['rating'] != null)
          _buildStatItem(Icons.star, stats['rating'].toString()),
        const Spacer(),
        Text(
          _formatTime(result.createdAt),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(IconData icon, String value) {
    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[500]),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyResultState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF4E8BF7).withOpacity(0.1),
                    const Color(0xFF667EEA).withOpacity(0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.search_off,
                size: 60,
                color: Color(0xFF4E8BF7),
              ),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            '未找到"$_searchQuery"相关内容',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1A1E25),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '换个关键词试试吧',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _searchController.clear();
              setState(() {
                _hasSearched = false;
                _searchResults = [];
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4E8BF7),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
            ),
            child: const Text(
              '重新搜索',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showClearHistoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '清空搜索历史',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: const Text(
          '确定要清空所有搜索历史吗？',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              '取消',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _searchHistory.clear();
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('搜索历史已清空')),
              );
            },
            child: const Text(
              '清空',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToDetail(SearchResult result) {
    HapticFeedback.lightImpact();

    switch (result.type) {
      case 'moment':
        context.push('/momentDetail', extra: {'momentId': result.id});
        break;
      case 'user':
        context.push('/other_profile_page/${result.id}');
        break;
      case 'spot':
        context.push('/fishingSpotDetail', extra: {'spotId': result.id});
        break;
    }
  }

  String _formatCount(int count) {
    if (count >= 10000) {
      return '${(count / 10000).toStringAsFixed(1)}w';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    }
    return count.toString();
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${time.month}月${time.day}日';
    }
  }
}

// 搜索结果数据模型
class SearchResult {
  final int id;
  final String type; // moment, user, spot
  final String title;
  final String subtitle;
  final String? imageUrl;
  final List<String> tags;
  final Map<String, dynamic> stats;
  final DateTime createdAt;

  SearchResult({
    required this.id,
    required this.type,
    required this.title,
    required this.subtitle,
    this.imageUrl,
    required this.tags,
    required this.stats,
    required this.createdAt,
  });
}
