import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/search/search_models.dart';

class Search<PERSON>pi extends BaseApi {
  SearchApi(super.dio);

  /// 综合搜索
  Future<SearchPage<SearchResultVO>> search(
    SearchRequest request,
  ) async {
    return await safeApiCall<SearchPage<SearchResultVO>>(
      () => dio.post('/api/search/comprehensive', data: request.toJson()),
      (data) => SearchPage.fromJson(
        data as Map<String, dynamic>,
        (json) => SearchResultVO.fromJson(json),
      ),
    );
  }

  /// 简单搜索（URL参数方式）
  Future<SearchPage<SearchResultVO>> simpleSearch({
    required String keyword,
    String type = 'all',
    String sortBy = 'relevance',
    int page = 1,
    int size = 20,
    String? province,
    String? city,
    int? dayRange,
    String? momentType,
  }) async {
    final queryParams = <String, dynamic>{
      'keyword': keyword,
      'type': type,
      'sortBy': sortBy,
      'page': page,
      'size': size,
    };

    if (province != null) queryParams['province'] = province;
    if (city != null) queryParams['city'] = city;
    if (dayRange != null) queryParams['dayRange'] = dayRange;
    if (momentType != null) queryParams['momentType'] = momentType;

    return await safeApiCall<SearchPage<SearchResultVO>>(
      () => dio.get('/api/search/simple', queryParameters: queryParams),
      (data) => SearchPage.fromJson(
        data as Map<String, dynamic>,
        (json) => SearchResultVO.fromJson(json),
      ),
    );
  }

  /// 获取搜索建议
  Future<List<SearchSuggestionVO>> getSearchSuggestions(
    String keyword, {
    int limit = 10,
  }) async {
    return await safeApiCall<List<SearchSuggestionVO>>(
      () => dio.get('/api/search/suggestions', queryParameters: {
        'keyword': keyword,
        'limit': limit,
      }),
      (data) => (data as List)
          .map((item) =>
              SearchSuggestionVO.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 获取搜索历史
  Future<List<String>> getSearchHistory() async {
    return await safeApiCall<List<String>>(
      () => dio.get('/search/history'),
      (data) => (data as List).cast<String>(),
    );
  }

  /// 清除搜索历史
  Future<void> clearSearchHistory() async {
    await safeApiCall<void>(
      () => dio.delete('/search/history'),
    );
  }

  /// 获取热门搜索
  Future<List<String>> getHotSearchKeywords({
    int limit = 10,
  }) async {
    return await safeApiCall<List<String>>(
      () => dio.get('/api/search/hot-keywords', queryParameters: {
        'limit': limit,
      }),
      (data) => (data as List).cast<String>(),
    );
  }

  /// 记录搜索行为
  Future<void> recordSearch(String keyword) async {
    await safeApiCall<void>(
      () => dio.post('/search/record', queryParameters: {
        'keyword': keyword,
      }),
    );
  }
}
