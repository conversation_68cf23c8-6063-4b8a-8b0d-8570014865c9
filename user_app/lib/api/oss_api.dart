import 'package:flutter_oss_aliyun/flutter_oss_aliyun.dart';
import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/oss/oss_auth.dart';

class OssApi extends BaseApi {
  OssApi(super.dio);

  Future<OssAuth> getStsPolicy() async {
    return await safeApiCall<OssAuth>(
      () => dio.get('/oss/sts-policy'),
      (data) => OssAuth.fromMap(data),
    );
  }

  Future<String> saveFile(List<int> data, String suffix) async {
    final uploadResult = await Client().putObject(
      data,
      "moments/${DateTime.now().millisecondsSinceEpoch}.$suffix",
    );
    if (uploadResult.statusCode != 200) {
      throw Exception("Failed to upload file");
    }
    return uploadResult.requestOptions.path;
  }

  Future<void> init() async {
    try {
      Client.init(
        ossEndpoint: "oss-cn-chengdu.aliyuncs.com",
        bucketName: "fishing-server",
        authGetter: () async {
          try {
            final OssAuth auth = await getStsPolicy();
            return Auth(
              accessKey: auth.accessKey,
              accessSecret: auth.accessSecret,
              expire: auth.expire,
              secureToken: auth.secureToken,
            );
          } catch (e) {
            rethrow;
          }
        },
      );
    } catch (e) {
      rethrow;
    }
  }
}
