import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/social_insights/social_insights.dart';

class SocialInsightsApi extends BaseApi {
  SocialInsightsApi(super.dio);

  Future<SocialInsights> fetchSocialInsights({String? timeRange}) async {
    return await safeApiCall<SocialInsights>(
      () => dio.get('/api/user/social/insights', queryParameters: {
        if (timeRange != null) 'timeRange': timeRange,
      }),
      (data) => SocialInsights.fromMap(data),
    );
  }
}