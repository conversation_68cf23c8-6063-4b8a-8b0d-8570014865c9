import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/session/validate_verification_code_result.dart';

class SmsApi extends BaseApi {
  SmsApi(super.dio);

  Future<ValidateVerificationCodeResult> getVerificationCodeForRegister(
      String phoneNumber) async {
    return await safeApiCall<ValidateVerificationCodeResult>(
      () => dio.get('sms/register/$phoneNumber'),
      (data) => ValidateVerificationCodeResult.fromMap(data),
    );
  }
}
