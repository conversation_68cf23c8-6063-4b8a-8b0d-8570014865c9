import 'package:user_app/core/network/base_api.dart';

class Report<PERSON>pi extends BaseApi {
  static const String reportPath = '/reports';

  ReportApi(super.dio);

  /// Report a moment
  Future<void> reportMoment(int momentId, String reason,
      {String? description}) async {
    await safeApiCall<void>(
      () => dio.post('$reportPath/moment', data: {
        'momentId': momentId,
        'reason': reason,
        'description': description,
      }),
    );
  }

  /// Report a user
  Future<void> reportUser(int userId, String reason,
      {String? description}) async {
    await safeApiCall<void>(
      () => dio.post('$reportPath/user', data: {
        'userId': userId,
        'reason': reason,
        'description': description,
      }),
    );
  }

  /// Get report reasons
  Future<List<String>> getReportReasons() async {
    try {
      return await safeApiCall<List<String>>(
        () => dio.get('$reportPath/reasons'),
        (data) => List<String>.from(data['reasons'] ?? []),
      );
    } catch (e) {
      // Return default reasons if API fails
      return ['垃圾信息', '违法违规', '色情内容', '暴力内容', '虚假信息', '侵犯版权', '其他'];
    }
  }
}
