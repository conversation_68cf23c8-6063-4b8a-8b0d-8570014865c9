import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/session/login_response.dart';

class Session<PERSON><PERSON> extends BaseApi {
  static const String sessionPath = '/sessions/';

  SessionApi(super.dio);

  Future<LoginResponse> login(String phoneNumber, String password) async {
    return await safeApiCall<LoginResponse>(
      () => dio.post(
        '${sessionPath}password_login',
        data: {
          'telephone_number': phoneNumber,
          'password': password,
        },
      ),
      (data) => LoginResponse.fromMap(data),
    );
  }

  Future<LoginResponse> register(
    String phoneNumber,
    String verificationCode,
    String password,
    String confirmPassword,
  ) async {
    return await safeApiCall<LoginResponse>(
      () => dio.post(
        '${sessionPath}register',
        data: {
          'telephone_number': phoneNumber,
          'verification_code': verificationCode,
          'password': password,
          'validate_password': confirmPassword,
        },
      ),
      (data) => LoginResponse.fromMap(data),
    );
  }

  Future<LoginResponse> resetPassword(
    String phoneNumber,
    String verificationCode,
    String password,
    String confirmPassword,
  ) async {
    return await safeApiCall<LoginResponse>(
      () => dio.post(
        '${sessionPath}reset_password_by_verification_code',
        data: {
          'telephone_number': phoneNumber,
          'verification_code': verificationCode,
          'password': password,
          'confirm_password': confirmPassword,
        },
      ),
      (data) => LoginResponse.fromMap(data),
    );
  }
}
