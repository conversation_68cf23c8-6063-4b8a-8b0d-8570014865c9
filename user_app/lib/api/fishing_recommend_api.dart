import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/features/fishing_spots/models/fishing_recommendation.dart';

class FishingRecommendApi extends BaseApi {
  static const String basePath = '/api/v1/fishing';

  FishingRecommendApi(super.dio);

  /// Fetches fishing recommendations based on weather and temperature
  ///
  /// [weather] The weather code
  /// [temperature] The current temperature in celsius
  Future<FishingRecommendation> getFishingRecommendation({
    required String weather,
    required double temperature,
  }) async {
    return await safeApiCall<FishingRecommendation>(
      () => dio.get(
        "$basePath/recommend",
        queryParameters: {
          'weather': weather,
          'temperature': temperature,
        },
      ),
      (data) => FishingRecommendation.fromMap(data),
    );
  }
}
