import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/fishing_plan/fishing_plan.dart';

class FishingPlanApi extends BaseApi {
  FishingPlanApi(super.dio);

  Future<List<FishingPlan>> fetchFishingPlans({int? status}) async {
    return await safeApiCall<List<FishingPlan>>(
      () => dio.get('/api/user/fishing-plans', queryParameters: {
        if (status != null) 'status': status,
      }),
      (data) => (data as List<dynamic>)
          .map((item) => FishingPlan.fromMap(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Future<FishingPlan> createFishingPlan(FishingPlan plan) async {
    return await safeApiCall<FishingPlan>(
      () => dio.post('/api/user/fishing-plans', data: plan.toMap()),
      (data) => FishingPlan.fromMap(data as Map<String, dynamic>),
    );
  }

  Future<FishingPlan> updateFishingPlan(int id, FishingPlan plan) async {
    return await safeApiCall<FishingPlan>(
      () => dio.put('/api/user/fishing-plans/$id', data: plan.toMap()),
      (data) => FishingPlan.fromMap(data as Map<String, dynamic>),
    );
  }

  Future<void> deleteFishingPlan(int id) async {
    return await safeApiCall(
      () => dio.delete('/api/user/fishing-plans/$id'),
      (data) => data,
    );
  }

  Future<void> joinFishingPlan(int planId) async {
    return await safeApiCall(
      () => dio.post('/api/user/fishing-plans/$planId/join'),
      (data) => data,
    );
  }

  Future<void> leaveFishingPlan(int planId) async {
    return await safeApiCall(
      () => dio.post('/api/user/fishing-plans/$planId/leave'),
      (data) => data,
    );
  }
}
