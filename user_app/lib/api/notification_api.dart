import 'package:dio/dio.dart';
import 'package:user_app/core/network/api_adapter.dart';
import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/core/network/api_response.dart';
import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/notification/notification_vo.dart';

class NotificationApi extends BaseApi {
  static const String notificationPath = '/notifications';

  NotificationApi(super.dio);

  /// Get user notifications
  Future<ApiResponse<List<NotificationVo>>> getNotifications({
    int page = 1,
    int pageSize = 20,
    String? type,
  }) async {
    try {
      final response = await dio.get(notificationPath, queryParameters: {
        'page': page,
        'page_size': pageSize,
        if (type != null) 'type': type,
      });

      if (response.data is List) {
        return ApiResponse(code: 200, message: 'Success', data: []);
      } else if (response.data is Map<String, dynamic>) {
        return ApiResponse.fromJson(response.data, (data) {
          if (data is Map<String, dynamic> && data.containsKey('records')) {
            final records = data['records'] as List;
            return records
                .map((item) =>
                    NotificationVo.fromMap(item as Map<String, dynamic>))
                .toList();
          } else {
            return <NotificationVo>[];
          }
        });
      } else {
        throw ApiError(
          code: 500,
          message: 'Unexpected response format: ${response.data.runtimeType}',
        );
      }
    } on DioException catch (e) {
      throw ApiError(
          code: e.response?.statusCode ?? 500,
          message: e.message ?? 'Unknown DioError');
    } catch (e) {
      throw ApiError(code: 500, message: 'Unexpected error: $e');
    }
  }

  /// Mark notification as read
  Future<ApiResponse<void>> markAsRead(int notificationId) async {
    await safeApiCall<void>(
      () => dio.put('$notificationPath/$notificationId/read'),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
    );
  }

  /// Mark all notifications as read
  Future<ApiResponse<void>> markAllAsRead() async {
    await safeApiCall<void>(
      () => dio.put('$notificationPath/read-all'),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
    );
  }

  /// Get unread notification count
  Future<ApiResponse<int>> getUnreadCount() async {
    try {
      final response = await dio.get('$notificationPath/unread-count');
      return ApiAdapter.handleCountResponse(response.data);
    } on DioException catch (e) {
      throw ApiError(
          code: e.response?.statusCode ?? 500,
          message: e.message ?? 'Unknown DioError');
    } catch (e) {
      throw ApiError(code: 500, message: 'Unexpected error: $e');
    }
  }

  /// Delete notification
  Future<ApiResponse<void>> deleteNotification(int notificationId) async {
    await safeApiCall<void>(
      () => dio.delete('$notificationPath/$notificationId'),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
    );
  }

  /// Clear all notifications
  Future<ApiResponse<void>> clearAllNotifications() async {
    await safeApiCall<void>(
      () => dio.delete('$notificationPath/clear-all'),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
    );
  }
}
