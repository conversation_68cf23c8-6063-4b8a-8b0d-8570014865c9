import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/core/network/api_response.dart';

class BookmarkApi extends BaseApi {
  static const String bookmarkPath = '/bookmarks';

  BookmarkApi(super.dio);

  /// Bookmark a moment
  Future<void> bookmarkMoment(int momentId) async {
    await safeApiCall<void>(
      () => dio.post('$bookmarkPath/moment/$momentId'),
    );
  }

  /// Remove bookmark from a moment
  Future<void> unbookmarkMoment(int momentId) async {
    await safeApiCall<void>(
      () => dio.delete('$bookmarkPath/moment/$momentId'),
    );
  }

  /// Check if a moment is bookmarked
  Future<bool> isBookmarked(int momentId) async {
    return await safeApiCall<bool>(
      () => dio.get('$bookmarkPath/moment/$momentId/status'),
      (data) => data['bookmarked'] as bool? ?? false,
    );
  }

  /// Get bookmarked moments with pagination
  Future<ApiResponse<List<MomentVo>>> getBookmarkedMoments({
    int page = 1,
    int pageSize = 20,
  }) async {
    final response = await safeApiCall<Map<String, dynamic>>(
      () => dio.get('$bookmarkPath/moments', queryParameters: {
        'page': page,
        'pageSize': pageSize,
      }),
      (data) => data as Map<String, dynamic>,
    );

    final moments = (response['records'] as List?)
            ?.map((item) => MomentVo.fromMap(item))
            .toList() ??
        [];

    return ApiResponse(
      code: 200,
      message: 'Success',
      data: moments,
    );
  }

  /// Get bookmarked spots with pagination
  Future<ApiResponse<List<FishingSpotVo>>> getBookmarkedSpots({
    int page = 1,
    int pageSize = 20,
  }) async {
    final response = await safeApiCall<Map<String, dynamic>>(
      () => dio.get('$bookmarkPath/spots', queryParameters: {
        'page': page,
        'pageSize': pageSize,
      }),
      (data) => data as Map<String, dynamic>,
    );

    final spots = (response['records'] as List?)
            ?.map((item) => FishingSpotVo.fromMap(item))
            .toList() ??
        [];

    return ApiResponse(
      code: 200,
      message: 'Success',
      data: spots,
    );
  }

  /// Bookmark a spot
  Future<ApiResponse<void>> bookmarkSpot(int spotId) async {
    await safeApiCall<void>(
      () => dio.post('$bookmarkPath/spot/$spotId'),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
    );
  }

  /// Remove bookmark from a spot
  Future<ApiResponse<void>> unbookmarkSpot(int spotId) async {
    await safeApiCall<void>(
      () => dio.delete('$bookmarkPath/spot/$spotId'),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
    );
  }
}
