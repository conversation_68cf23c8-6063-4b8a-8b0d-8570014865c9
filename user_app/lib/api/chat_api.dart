import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/chat/im_sign_dto.dart';

class ChatApi extends BaseApi {
  static const String imPath = '/im/';

  ChatApi(super.dio);

  Future<void> accountImport() async {
    await safeApiCall<void>(
      () => dio.get('${imPath}account_import'),
    );
  }

  Future<ImSignDto> getImSign() async {
    return await safeApiCall<ImSignDto>(
      () => dio.get('${imPath}sign'),
      (data) => ImSignDto.fromMap(data),
    );
  }
}
