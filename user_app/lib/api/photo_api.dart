import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/photo/photo_page_request.dart';
import 'package:user_app/models/photo/photo_page_response.dart';

class PhotoApi extends BaseApi {
  PhotoApi(super.dio);

  Future<PhotoPageResponse> getUserPhotos(PhotoPageRequest request) async {
    return await safeApiCall<PhotoPageResponse>(
      () => dio.post('/photos/user', data: request.toMap()),
      (data) => PhotoPageResponse.fromMap(data),
    );
  }

  Future<void> deletePhoto(int photoId) async {
    await safeApiCall<void>(
      () => dio.delete('/photos/$photoId'),
    );
  }

  Future<void> deletePhotos(List<int> photoIds) async {
    await safeApiCall<void>(
      () => dio.delete('/photos/batch', data: {'photoIds': photoIds}),
    );
  }
}