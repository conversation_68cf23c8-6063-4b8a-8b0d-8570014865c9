import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/core/network/api_response.dart';
import 'package:user_app/models/album/album_item.dart';
import 'package:user_app/models/album/album_stats.dart';

class AlbumApi extends BaseApi {
  static const String albumPath = '/album';

  AlbumApi(super.dio);

  /// 获取用户相册列表
  Future<ApiResponse<Map<String, dynamic>>> getAlbumImages({
    int page = 1,
    int pageSize = 20,
  }) async {
    final response = await safeApiCall<Map<String, dynamic>>(
      () => dio.get(albumPath, queryParameters: {
        'page': page,
        'pageSize': pageSize,
      }),
      (data) => data as Map<String, dynamic>,
    );

    // 转换图片数据
    final List<dynamic>? imagesData = response['images'];
    final List<AlbumItem> images = imagesData
            ?.map((item) => AlbumItem.fromMap(item))
            .toList()
            .cast<AlbumItem>() ??
        [];

    final result = {
      'images': images,
      'total': response['total'] ?? 0,
      'page': response['page'] ?? page,
      'pageSize': response['pageSize'] ?? pageSize,
      'pages': response['pages'] ?? 0,
    };

    return ApiResponse(
      code: 200,
      message: 'Success',
      data: result,
    );
  }

  /// 添加图片到相册
  Future<ApiResponse<String>> addToAlbum({
    required String imageUrl,
    String? description,
    String? tags,
  }) async {
    await safeApiCall<void>(
      () => dio.post(albumPath, data: {
        'imageUrl': imageUrl,
        'description': description,
        'tags': tags,
      }),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
      data: '上传成功',
    );
  }

  /// 删除相册图片
  Future<ApiResponse<String>> deleteFromAlbum(int imageId) async {
    await safeApiCall<void>(
      () => dio.delete('$albumPath/$imageId'),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
      data: '删除成功',
    );
  }

  /// 获取相册统计信息
  Future<ApiResponse<AlbumStats>> getAlbumStats() async {
    final stats = await safeApiCall<AlbumStats>(
      () => dio.get('$albumPath/stats'),
      (data) => AlbumStats.fromMap(data),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
      data: stats,
    );
  }

  /// 获取最近上传的图片
  Future<ApiResponse<List<AlbumItem>>> getRecentImages({
    int limit = 10,
  }) async {
    final images = await safeApiCall<List<AlbumItem>>(
      () => dio.get('$albumPath/recent', queryParameters: {
        'limit': limit,
      }),
      (data) => (data as List)
          .map((item) => AlbumItem.fromMap(item))
          .toList()
          .cast<AlbumItem>(),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
      data: images,
    );
  }

  /// 根据标签搜索图片
  Future<ApiResponse<List<AlbumItem>>> searchByTags(String tags) async {
    final images = await safeApiCall<List<AlbumItem>>(
      () => dio.get('$albumPath/search', queryParameters: {
        'tags': tags,
      }),
      (data) => (data as List)
          .map((item) => AlbumItem.fromMap(item))
          .toList()
          .cast<AlbumItem>(),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
      data: images,
    );
  }

  /// 批量删除图片
  Future<ApiResponse<String>> batchDeleteImages(List<int> imageIds) async {
    await safeApiCall<void>(
      () => dio.delete('$albumPath/batch', data: {
        'imageIds': imageIds,
      }),
    );

    return ApiResponse(
      code: 200,
      message: 'Success',
      data: '批量删除成功',
    );
  }
}