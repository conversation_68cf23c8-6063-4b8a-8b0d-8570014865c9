import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';

class FishTypeApi extends BaseApi {
  static const String fishTypePath = '/api/v1/fishing-types';

  FishTypeApi(super.dio);

  Future<List<FishType>> getFishTypes() async {
    return await safeApiCall<List<FishType>>(
      () => dio.get(fishTypePath),
      (data) {
        if (data is List) {
          return data.map((e) => FishType.fromMap(e)).toList();
        }
        return <FishType>[];
      },
    );
  }
}
