import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/fishing_statistics/fishing_statistics.dart';

class FishingStatisticsApi extends BaseApi {
  FishingStatisticsApi(super.dio);

  Future<FishingStatistics> fetchFishingStatistics({
    String? timeRange, // 'week', 'month', 'year'
  }) async {
    return await safeApiCall<FishingStatistics>(
      () => dio.get('/api/user/fishing/statistics', queryParameters: {
        if (timeRange != null) 'timeRange': timeRange,
      }),
      (data) => FishingStatistics.fromMap(data as Map<String, dynamic>),
    );
  }

  Future<List<TripRecord>> fetchTripRecords({
    String? timeRange,
    int? limit,
    int? offset,
  }) async {
    return await safeApiCall<List<TripRecord>>(
      () => dio.get('/api/user/fishing/trip-records', queryParameters: {
        if (timeRange != null) 'timeRange': timeRange,
        if (limit != null) 'limit': limit,
        if (offset != null) 'offset': offset,
      }),
      (data) => (data as List<dynamic>).map((item) => TripRecord.fromMap(item as Map<String, dynamic>)).toList(),
    );
  }

  Future<List<EquipmentUsage>> fetchEquipmentStatistics({
    String? category,
  }) async {
    return await safeApiCall<List<EquipmentUsage>>(
      () => dio.get('/api/user/fishing/equipment-stats', queryParameters: {
        if (category != null) 'category': category,
      }),
      (data) => (data as List<dynamic>).map((item) => EquipmentUsage.fromMap(item as Map<String, dynamic>)).toList(),
    );
  }

  Future<List<CatchData>> fetchCatchTrend({
    String? timeRange,
  }) async {
    return await safeApiCall<List<CatchData>>(
      () => dio.get('/api/user/fishing/catch-trend', queryParameters: {
        if (timeRange != null) 'timeRange': timeRange,
      }),
      (data) => (data as List<dynamic>).map((item) => CatchData.fromMap(item as Map<String, dynamic>)).toList(),
    );
  }

  Future<List<FishSpeciesData>> fetchFishSpeciesDistribution({
    String? timeRange,
  }) async {
    return await safeApiCall<List<FishSpeciesData>>(
      () => dio.get('/api/user/fishing/fish-species', queryParameters: {
        if (timeRange != null) 'timeRange': timeRange,
      }),
      (data) => (data as List<dynamic>).map((item) => FishSpeciesData.fromMap(item as Map<String, dynamic>)).toList(),
    );
  }

  Future<List<SpotStatistics>> fetchSpotStatistics({
    String? timeRange,
    int? limit,
  }) async {
    return await safeApiCall<List<SpotStatistics>>(
      () => dio.get('/api/user/fishing/spot-stats', queryParameters: {
        if (timeRange != null) 'timeRange': timeRange,
        if (limit != null) 'limit': limit,
      }),
      (data) => (data as List<dynamic>).map((item) => SpotStatistics.fromMap(item as Map<String, dynamic>)).toList(),
    );
  }
}