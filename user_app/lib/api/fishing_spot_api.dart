import 'package:flutter/foundation.dart';
import 'package:user_app/core/models/page_result.dart';
import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_page_vo.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_detail_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_map_vo.dart';

/// API class for interacting with fishing spot data on the server
///
/// Provides methods to fetch, filter, and manage fishing spots
class FishingSpotApi extends BaseApi {
  /// Base path for fishing spot API endpoints
  static const String fishingSpotPath = '/api/v1/fishing-spots';

  /// Constructor that takes a Dio instance
  FishingSpotApi(super.dio);

  /// Retrieves a paginated list of fishing spots with optional filtering
  /// (Returns SpotSummaryPageVo for list scenarios)
  ///
  /// Parameters:
  /// - [page] Page number (0-based indexing)
  /// - [size] Number of items per page
  /// - [filterType] Filter by spot type (e.g., "官方认证", "用户推荐", "免费钓场", "付费钓场")
  /// - [fishTypes] List of fish types to filter by
  /// - [hasFacilities] Filter for spots with facilities
  ///
  /// Returns:
  /// A [SpotSummaryPageVo] containing the list of summary spots and pagination info
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<SpotSummaryPageVo> getFishingSpotsAsSummary({
    int page = 0,
    int size = 10,
    String? filterType,
    List<String>? fishTypes,
    bool? hasFacilities,
  }) async {
    try {
      debugPrint('🔧 [API] Initializing SpotSummaryVo mapper...');

      debugPrint(
          "请求参数: page=$page, size=$size, filterType=$filterType, fishTypes=$fishTypes, hasFacilities=$hasFacilities");

      // 使用新的统一API调用方法，直接获取data部分
      final pageData = await safeApiCall<Map<String, dynamic>>(
        () => dio.post(
          fishingSpotPath,
          data: {
            'page_num': page + 1, // 后端使用1-based分页
            'page_size': size,
            if (filterType != null) 'filter_type': filterType,
            if (fishTypes != null && fishTypes.isNotEmpty)
              'fish_types': fishTypes,
            if (hasFacilities != null) 'has_facilities': hasFacilities,
          },
        ),
        (data) => data as Map<String, dynamic>,
      );

      debugPrint("响应成功! 数据类型: ${pageData.runtimeType}");

      // 提取记录和分页信息
      final records = pageData['records'];
      if (records == null || records is! List) {
        debugPrint("无法从响应中获取records列表，可用字段: ${pageData.keys.toList()}");

        // 返回空列表而不是抛出异常
        return SpotSummaryPageVo(
          content: [],
          totalPages: 1,
          totalElements: 0,
          size: 10,
          number: page,
          first: true,
          last: true,
        );
      }

      debugPrint("成功获取${records.length}条记录");

      // 直接使用SpotSummaryVo解析列表数据
      List<SpotSummaryVo> summarySpots = [];
      for (var spotData in records) {
        try {
          debugPrint("🔍 正在解析单个钓点数据: $spotData");
          final spotSummary =
              SpotSummaryVo.fromMap(Map<String, dynamic>.from(spotData));
          debugPrint(
              "✅ 成功解析钓点: ${spotSummary.name}, paid: ${spotSummary.paid}");
          summarySpots.add(spotSummary);
        } catch (e, stackTrace) {
          debugPrint("❌ 解析单个钓点摘要数据失败: $e");
          debugPrint("错误堆栈: $stackTrace");
          debugPrint("问题数据: $spotData");
          // 继续处理其他钓点
        }
      }

      // 创建SpotSummaryPageVo
      final currentPage = (pageData['current'] as int? ?? 1) - 1; // 转换为0-based
      final totalPages = pageData['pages'] as int? ?? 1;
      final totalElements = pageData['total'] as int? ?? 0;
      final pageSize = pageData['size'] as int? ?? 10;

      // 修复后端分页信息错误的问题
      final actualTotalPages =
          totalPages > 0 ? totalPages : (summarySpots.isNotEmpty ? 1 : 0);
      final actualTotalElements =
          totalElements > 0 ? totalElements : summarySpots.length;

      return SpotSummaryPageVo(
        content: summarySpots,
        totalPages: actualTotalPages,
        totalElements: actualTotalElements,
        size: pageSize,
        number: currentPage,
        first: currentPage == 0,
        last: currentPage >= (actualTotalPages - 1) ||
            summarySpots.length < pageSize,
      );
    } catch (e, stackTrace) {
      debugPrint("获取钓点列表异常: $e");
      debugPrint("错误堆栈: $stackTrace");

      // 返回空列表而不是抛出异常，以避免界面崩溃
      return SpotSummaryPageVo(
        content: [],
        totalPages: 1,
        totalElements: 0,
        size: 10,
        number: page,
        first: true,
        last: true,
      );
    }
  }

  /// Retrieves fishing spots optimized for map display (lightweight data)
  /// 使用 /nearby/map 接口，支持基于位置的查询和筛选
  ///
  /// Parameters:
  /// - [latitude] 中心点纬度 (必需)
  /// - [longitude] 中心点经度 (必需)
  /// - [radius] 搜索半径，单位公里 (默认50km)
  /// - [page] Page number (0-based indexing)
  /// - [size] Number of items per page (default 50 for map)
  /// - [filterType] Filter by spot type (暂不支持，保留接口兼容性)
  /// - [fishTypes] List of fish types to filter by (暂不支持，保留接口兼容性)
  /// - [hasFacilities] Filter for spots with facilities (暂不支持，保留接口兼容性)
  /// - [hasParking] Filter for spots with parking (暂不支持，保留接口兼容性)
  /// - [bounds] Optional map bounds (暂不支持，保留接口兼容性)
  ///
  /// Returns:
  /// A [SpotMapPageVo] containing lightweight spot data for map display
  Future<SpotMapPageVo> getFishingSpotsForMap({
    required double latitude,
    required double longitude,
    double radius = 50.0,
    int page = 0,
    int size = 50,
    String? filterType,
    List<String>? fishTypes,
    bool? hasFacilities,
    bool? hasParking,
    Map<String, double>? bounds,
  }) async {
    try {
      if (kDebugMode) {
        print('🌐 [API] Calling getFishingSpotsForMap via /nearby/map');
        print('   - latitude: $latitude, longitude: $longitude');
        print('   - radius: $radius, page: $page, size: $size');
        print('   - filterType: $filterType (暂不支持)');
        print('   - fishTypes: $fishTypes (暂不支持)');
        print('   - hasFacilities: $hasFacilities (暂不支持)');
        print('   - hasParking: $hasParking (暂不支持)');
      }

      // 调用现有的 /nearby/map 接口
      return await getNearbyMapSpots(
        latitude: latitude,
        longitude: longitude,
        page: page,
        pageSize: size,
        radiusKm: radius,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ [API] Error in getFishingSpotsForMap: $e');
      }
      rethrow;
    }
  }

  /// Retrieves a paginated list of fishing spots with optional filtering
  /// (Legacy method for backward compatibility)
  ///
  /// Parameters:
  /// - [page] Page number (0-based indexing)
  /// - [size] Number of items per page
  /// - [filterType] Filter by spot type (e.g., "官方认证", "用户推荐", "免费钓场", "付费钓场")
  /// - [fishTypes] List of fish types to filter by
  /// - [hasFacilities] Filter for spots with facilities
  ///
  /// Returns:
  /// A [FishingSpotPageVo] containing the list of spots and pagination info
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<FishingSpotPageVo> getFishingSpots({
    int page = 0,
    int size = 10,
    String? filterType,
    List<String>? fishTypes,
    bool? hasFacilities,
  }) async {
    try {
      // 确保所有必要的mapper都已初始化
      debugPrint('🔧 [API] Initializing all required mappers...');

      debugPrint(
          "请求参数: page=$page, size=$size, filterType=$filterType, fishTypes=$fishTypes, hasFacilities=$hasFacilities");

      // 使用新的统一API调用方法，直接获取data部分
      final pageData = await safeApiCall<Map<String, dynamic>>(
        () => dio.post(
          fishingSpotPath,
          data: {
            'page_num': page + 1, // 后端使用1-based分页，保持一致
            'page_size': size,
            if (filterType != null) 'filter_type': filterType,
            if (fishTypes != null && fishTypes.isNotEmpty)
              'fish_types': fishTypes,
            if (hasFacilities != null) 'has_facilities': hasFacilities,
          },
        ),
        (data) => data as Map<String, dynamic>,
      );

      debugPrint("响应成功! 数据类型: ${pageData.runtimeType}");

      // 提取记录和分页信息
      final records = pageData['records'];
      if (records == null || records is! List) {
        debugPrint("无法从响应中获取records列表，可用字段: ${pageData.keys.toList()}");

        // 返回空列表而不是抛出异常
        return FishingSpotPageVo(
          content: [],
          totalPages: 1,
          totalElements: 0,
          size: 10,
          number: 0,
          first: true,
          last: true,
        );
      }

      debugPrint("成功获取${records.length}条记录");

      // 使用SpotSummaryVo解析列表数据
      List<SpotSummaryVo> summarySpots = [];
      for (var spotData in records) {
        try {
          final spotSummary =
              SpotSummaryVo.fromMap(Map<String, dynamic>.from(spotData));
          summarySpots.add(spotSummary);
        } catch (e, stackTrace) {
          debugPrint("解析单个钓点摘要数据失败: $e");
          debugPrint("错误堆栈: $stackTrace");
          debugPrint("问题数据: $spotData");
          // 继续处理其他钓点
        }
      }

      // 创建SpotSummaryPageVo并转换为FishingSpotPageVo
      // 注意：后端返回的current是1-based，需要转换为0-based
      final currentPage = (pageData['current'] as int? ?? 1) - 1;
      final totalPages = pageData['pages'] as int? ?? 1;
      final totalElements = pageData['total'] as int? ?? 0;
      final pageSize = pageData['size'] as int? ?? 10;

      // 修复后端分页信息错误的问题
      final actualTotalPages =
          totalPages > 0 ? totalPages : (summarySpots.isNotEmpty ? 1 : 0);
      final actualTotalElements =
          totalElements > 0 ? totalElements : summarySpots.length;

      final summaryPageVo = SpotSummaryPageVo(
        content: summarySpots,
        totalPages: actualTotalPages,
        totalElements: actualTotalElements,
        size: pageSize,
        number: currentPage,
        first: currentPage == 0,
        last: currentPage >= (actualTotalPages - 1) ||
            summarySpots.length < pageSize,
      );

      // 转换为FishingSpotPageVo以兼容现有代码
      return summaryPageVo.toFishingSpotPageVo();
    } catch (e, stackTrace) {
      debugPrint("获取钓点列表异常: $e");
      debugPrint("错误堆栈: $stackTrace");

      // 返回空列表而不是抛出异常，以避免界面崩溃
      return FishingSpotPageVo(
        content: [],
        totalPages: 1,
        totalElements: 0,
        size: 10,
        number: 0,
        first: true,
        last: true,
      );
    }
  }

  /// Fetches detailed information about a specific fishing spot
  ///
  /// Parameter:
  /// - [id] The unique identifier of the fishing spot
  ///
  /// Returns:
  /// A [FishingSpotVo] containing detailed information about the spot
  ///
  /// Throws:
  /// - [ApiError] if the request fails or data parsing fails
  Future<FishingSpotVo> getFishingSpotDetail(int id) async {
    debugPrint('🔍 请求钓点详情: $fishingSpotPath/$id');

    try {
      // 使用新的统一API调用方法，直接获取data部分
      final spotData = await safeApiCall<Map<String, dynamic>>(
        () => dio.get('$fishingSpotPath/$id'),
        (data) => data as Map<String, dynamic>,
      );

      debugPrint('=== 钓点详情API响应数据 ===');
      debugPrint('响应数据: $spotData');
      debugPrint('数据类型: ${spotData.runtimeType}');

      // 使用SpotDetailVo解析钓点详情数据
      final spotDetailVo = SpotDetailVo.fromMap(spotData);

      debugPrint('✅ 成功解析为SpotDetailVo');
      debugPrint('钓点ID: ${spotDetailVo.id}');
      debugPrint('钓点名称: ${spotDetailVo.name}');

      // 转换为FishingSpotVo以兼容现有代码
      final fishingSpotVo = spotDetailVo.toFishingSpotVo();

      debugPrint('✅ 成功转换为FishingSpotVo');
      return fishingSpotVo;
    } catch (e, stackTrace) {
      debugPrint('❌ 解析钓点详情失败: $e');
      debugPrint('错误堆栈: $stackTrace');

      // 如果是ApiError，直接重新抛出
      if (e is ApiError) {
        rethrow;
      }

      // 其他异常包装为ApiError
      throw ApiError(code: 500, message: '解析钓点详情失败: $e');
    }
  }

  /// Retrieves detailed information for a specific fishing spot (optimized SpotDetailVO)
  ///
  /// Parameters:
  /// - [id] Unique identifier of the fishing spot
  ///
  /// Returns:
  /// A [SpotDetailVo] containing detailed information about the spot
  ///
  /// Throws:
  /// - [ApiError] if the request fails or data parsing fails
  Future<SpotDetailVo> getFishingSpotDetailVO(int id) async {
    debugPrint('🔍 请求钓点详情VO: $fishingSpotPath/$id');
    try {
      // 使用新的统一API调用方法，直接获取data部分
      final spotData = await safeApiCall<Map<String, dynamic>>(
        () => dio.get('$fishingSpotPath/$id'),
        (data) => data as Map<String, dynamic>,
      );
      debugPrint('=== 钓点详情VO API响应数据 ===');
      debugPrint('响应数据: $spotData');
      debugPrint('数据类型: ${spotData.runtimeType}');
      
      // 直接解析为SpotDetailVo
      final spotDetailVo = SpotDetailVo.fromMap(spotData);
      debugPrint('✅ 成功解析为SpotDetailVo');
      debugPrint('钓点ID: ${spotDetailVo.id}');
      debugPrint('钓点名称: ${spotDetailVo.name}');
      
      return spotDetailVo;
    } catch (e, stackTrace) {
      debugPrint('❌ 解析钓点详情VO失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      // 如果是ApiError，直接重新抛出
      if (e is ApiError) {
        rethrow;
      }
      // 其他异常包装为ApiError
      throw ApiError(code: 500, message: '解析钓点详情VO失败: $e');
    }
  }

  /// Performs a check-in at a specific fishing spot
  ///
  /// Parameter:
  /// - [spotId] The unique identifier of the fishing spot
  ///
  /// Returns:
  /// A boolean indicating if the check-in was successful
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<bool> checkinFishingSpot(int spotId) async {
    debugPrint('🔄 [FishingSpotApi] 开始执行签到请求');
    debugPrint('🔄 [FishingSpotApi] 钓点ID: $spotId');
    debugPrint('🔄 [FishingSpotApi] 请求URL: $fishingSpotPath/$spotId/checkin');
    
    try {
      await safeApiCall<void>(
        () {
          debugPrint('🔄 [FishingSpotApi] 发送POST请求到: $fishingSpotPath/$spotId/checkin');
          return dio.post('$fishingSpotPath/$spotId/checkin');
        },
      );
      debugPrint('✅ [FishingSpotApi] 签到请求成功');
      return true;
    } catch (e) {
      debugPrint('❌ [FishingSpotApi] 签到请求失败: $e');
      debugPrint('❌ [FishingSpotApi] 异常类型: ${e.runtimeType}');
      rethrow;
    }
  }

  /// Fetches fishing spots near a specific location
  ///
  /// Parameters:
  /// - [latitude] Latitude coordinate
  /// - [longitude] Longitude coordinate
  /// - [radiusKm] Search radius in kilometers (default: 5)
  /// - [page] Page number (0-based) for pagination
  /// - [pageSize] Number of items per page
  ///
  /// Returns:
  /// A list of [FishingSpotVo] objects representing nearby spots
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<List<FishingSpotVo>> getNearbySpots({
    required double latitude,
    required double longitude,
    double radiusKm = 5.0,
    int page = 0,
    int pageSize = 10,
  }) async {
    if (kDebugMode) {
      print(
          "Fetching nearby spots: lat=$latitude, lng=$longitude, radius=$radiusKm, page=$page, pageSize=$pageSize");
    }

    final response = await safeApiCall(
      () => dio.get(
        '$fishingSpotPath/nearby',
        queryParameters: {
          'latitude': latitude,
          'longitude': longitude,
          'radius': radiusKm,
          'page': page,
          'size': pageSize,
        },
      ),
    );

    try {
      final data = response.data;
      if (data == null) {
        throw ApiError(code: 500, message: "获取附近钓点失败: 返回数据为空");
      }

      if (kDebugMode) {
        debugPrint("Nearby spots response data type: ${data.runtimeType}");
      }

      // 初始化为空列表，避免空指针问题
      List<dynamic> spots = [];

      // Handle different response formats
      if (data is List) {
        spots = data;
        if (kDebugMode) {
          debugPrint(
              "Nearby spots response is a List with ${spots.length} items");
        }
      } else if (data is Map<String, dynamic>) {
        if (data.containsKey('records')) {
          // Handle paginated response format
          spots = data['records'] as List<dynamic>? ?? [];
          if (kDebugMode) {
            debugPrint(
                "Nearby spots response is a Map with 'records' containing ${spots.length} items");
          }
        } else if (data.containsKey('content')) {
          // 另一种可能的分页格式
          spots = data['content'] as List<dynamic>? ?? [];
          if (kDebugMode) {
            debugPrint(
                "Nearby spots response is a Map with 'content' containing ${spots.length} items");
          }
        } else if (data.containsKey('items')) {
          // 另一种可能的分页格式
          spots = data['items'] as List<dynamic>? ?? [];
          if (kDebugMode) {
            debugPrint(
                "Nearby spots response is a Map with 'items' containing ${spots.length} items");
          }
        } else {
          // 尝试查找任何可能的列表字段
          bool foundList = false;
          for (var key in data.keys) {
            if (data[key] is List) {
              spots = data[key] as List<dynamic>;
              foundList = true;
              if (kDebugMode) {
                debugPrint(
                    "Found list in field '$key' with ${spots.length} items");
              }
              break;
            }
          }

          // 如果没有找到列表，记录可用的键
          if (!foundList) {
            if (kDebugMode) {
              debugPrint(
                  "No list field found in response, available keys: ${data.keys.join(', ')}");
            }
          }
        }
      } else {
        // Fallback for unexpected data type
        if (kDebugMode) {
          debugPrint(
              "Unexpected response format for nearby spots: $data (type: ${data.runtimeType})");
        }
      }

      if (kDebugMode) {
        debugPrint("Found ${spots.length} nearby spots");
      }

      return spots
          .map<FishingSpotVo>((spot) {
            try {
              // 创建FishingSpotVo对象
              // 注意：我们可以在这里处理距离信息，但目前FishingSpotVo模型中没有distance字段
              // 如果需要，可以考虑扩展模型添加distance字段

              // 直接使用SpotSummaryVo解析数据
              final spotSummary =
                  SpotSummaryVo.fromMap(Map<String, dynamic>.from(spot));

              // 转换为FishingSpotVo以兼容现有代码
              return spotSummary.toFishingSpotVo();
            } catch (e) {
              if (kDebugMode) {
                debugPrint("Error parsing fishing spot: $e");
                debugPrint("Problematic data: $spot");
              }
              // Return a placeholder spot in case of parsing error
              return const FishingSpotVo(
                id: 0,
                name: "解析错误",
                address: "数据格式错误",
                latitude: 0,
                longitude: 0,
                province: "",
                city: "",
                county: "",
                isOfficial: false,
                verificationLevel: 0,
                visitorCount: 0,
                rating: 0,
                hasFacilities: false,
                isPaid: false,
                checkinCount: 0,
              );
            }
          })
          .where((spot) => spot.id != 0)
          .toList(); // Filter out error placeholders
    } catch (e) {
      if (kDebugMode) {
        print("Error parsing nearby spots: $e");
      }
      throw ApiError(code: 500, message: '解析附近钓点数据失败: $e');
    }
  }

  /// 获取附近钓点的地图数据（返回SpotMapPageVo格式，专为地图显示优化）
  ///
  /// Parameters:
  /// - [latitude] 纬度坐标
  /// - [longitude] 经度坐标
  /// - [radiusKm] 搜索半径（公里）
  /// - [page] 页码 (0-based)
  /// - [pageSize] 每页数量
  ///
  /// Returns:
  /// A [SpotMapPageVo] containing paginated nearby spots optimized for map display
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<SpotMapPageVo> getNearbyMapSpots({
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
    int page = 0,
    int pageSize = 50,
  }) async {
    if (kDebugMode) {
      print(
          "🗺️ [API] Fetching nearby map spots: lat=$latitude, lng=$longitude, radius=$radiusKm, page=$page, pageSize=$pageSize");
    }

    // 使用专门的地图数据端点
    final pageData = await safeApiCall<Map<String, dynamic>>(
      () => dio.get(
        '$fishingSpotPath/nearby/map',
        queryParameters: {
          'latitude': latitude,
          'longitude': longitude,
          'radius': radiusKm,
          'page': page,
          'size': pageSize,
        },
      ),
      (data) => data as Map<String, dynamic>,
    );

    if (kDebugMode) {
      print("🗺️ [API] Received nearby map spots response: $pageData");
    }

    try {
      // 后端返回的数据结构：{records: [...], total: 0, size: 50, current: 1, pages: 0}
      // 需要转换为SpotMapPageVo期望的结构：{content: [...], totalPages: 0, totalElements: 0, size: 50, number: 0, first: true, last: true}

      final records = pageData['records'] as List? ?? [];
      final total = pageData['total'] as int? ?? 0;
      final size = pageData['size'] as int? ?? pageSize;
      final current = pageData['current'] as int? ?? 1;
      final pages = pageData['pages'] as int? ?? 0;

      // 转换records为SpotMapVo对象
      final mapSpots = <SpotMapVo>[];
      for (final record in records) {
        try {
          final mapSpot = SpotMapVo.fromMap(record as Map<String, dynamic>);
          mapSpots.add(mapSpot);
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ [API] 跳过无效的地图钓点数据: $e');
            print('⚠️ [API] 原始数据: $record');
          }
          // 继续处理其他钓点
        }
      }

      // 构造SpotMapPageVo
      final mapPage = SpotMapPageVo(
        content: mapSpots,
        totalPages: pages > 0 ? pages : (mapSpots.isNotEmpty ? 1 : 0),
        totalElements: total > 0 ? total : mapSpots.length,
        size: size,
        number: (current - 1).clamp(0, pages > 0 ? pages - 1 : 0), // 转换为0-based
        first: current <= 1,
        last: current >= pages || mapSpots.length < size,
      );

      if (kDebugMode) {
        print('✅ [API] 成功解析附近地图钓点分页数据:');
        print('   - 当前页: ${mapPage.number}');
        print('   - 总页数: ${mapPage.totalPages}');
        print('   - 总数量: ${mapPage.totalElements}');
        print('   - 当前页钓点数: ${mapPage.content.length}');
      }

      return mapPage;
    } catch (e) {
      if (kDebugMode) {
        print('❌ [API] 解析附近地图钓点数据失败: $e');
        print('🔍 [API] 原始响应数据: $pageData');
      }
      throw ApiError(code: 500, message: '解析附近地图钓点数据失败: $e');
    }
  }

  /// Rates a fishing spot
  ///
  /// Parameters:
  /// - [spotId] The unique identifier of the fishing spot
  /// - [rating] The rating value (typically 1-5)
  /// - [comment] Optional comment with the rating
  ///
  /// Returns:
  /// A boolean indicating if the rating was successfully submitted
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<bool> rateFishingSpot(
    int spotId,
    int rating, {
    String? comment,
  }) async {
    await safeApiCall<void>(
      () => dio.post(
        '$fishingSpotPath/$spotId/rate',
        data: {
          'rating': rating,
          if (comment != null && comment.isNotEmpty) 'comment': comment,
        },
      ),
    );

    return true;
  }

  /// Submits a problem report for a fishing spot
  ///
  /// Parameters:
  /// - [spotId] The unique identifier of the fishing spot
  /// - [problemType] The type of problem (e.g., "信息错误", "已关闭", "其他")
  /// - [description] A detailed description of the problem
  ///
  /// Returns:
  /// A boolean indicating if the report was successfully submitted
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<bool> reportProblem(
    int spotId,
    String problemType,
    String description,
  ) async {
    await safeApiCall<void>(
      () => dio.post(
        '$fishingSpotPath/$spotId/report-problem',
        data: {
          'problem_type': problemType,
          'description': description,
        },
      ),
    );

    return true;
  }

  /// Creates a new fishing spot
  ///
  /// Parameter:
  /// - [spotData] Map containing all fishing spot details
  ///
  /// Returns:
  /// The ID of the newly created fishing spot
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<int> createFishingSpot(Map<String, dynamic> spotData) async {
    final response = await safeApiCall(
      () => dio.post('$fishingSpotPath/create', data: spotData),
    );

    try {
      final data = response;
      if (data == null) {
        throw ApiError(code: 500, message: "创建钓点失败: 返回数据为空");
      }

      return data ?? 0;
    } catch (e) {
      throw ApiError(code: 500, message: '创建钓点失败: $e');
    }
  }

  /// Fetches fish types available at a specific fishing spot
  ///
  /// Parameter:
  /// - [spotId] The unique identifier of the fishing spot
  ///
  /// Returns:
  /// A list of [FishType] objects
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<List<FishType>> getFishTypesForSpot(int spotId) async {
    return await safeApiCall<List<FishType>>(
      () => dio.get('$fishingSpotPath/$spotId/fish-types'),
      (data) {
        if (data is List) {
          return data.map((e) => FishType.fromMap(e)).toList();
        }
        return <FishType>[];
      },
    );
  }

  /// Fetches user's recently checked-in fishing spots
  ///
  /// Parameters:
  /// - [page] Page number (0-based indexing for pagination)
  /// - [pageSize] Number of items per page
  ///
  /// Returns:
  /// A PageResult containing recently checked-in spots and pagination metadata
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<PageResult<FishingSpotVo>> getRecentCheckins({
    int page = 0,
    int pageSize = 10,
  }) async {
    final response = await safeApiCall(
      () => dio.get(
        '$fishingSpotPath/user/recent-checkins',
        queryParameters: {
          'page': page,
          'size': pageSize,
        },
      ),
    );

    try {
      if (response == null) {
        throw ApiError(code: 500, message: "获取最近签到钓点失败: 返回数据为空");
      }

      return PageResult.fromMap(Map<String, dynamic>.from(response), (value) {
        final spotSummary = SpotSummaryVo.fromMap(Map<String, dynamic>.from(value));
        return spotSummary.toFishingSpotVo();
      });
    } catch (e) {
      throw ApiError(code: 500, message: '解析最近签到数据失败: $e');
    }
  }

  /// Fetches user's favorite fishing spots
  ///
  /// Parameters:
  /// - [page] Page number (0-based indexing for pagination)
  /// - [pageSize] Number of items per page
  ///
  /// Returns:
  /// A list of [FishingSpotVo] representing favorite spots
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<List<FishingSpotVo>> getFavorites({
    int page = 0,
    int pageSize = 10,
  }) async {
    return await safeApiCall<List<FishingSpotVo>>(
      () => dio.get(
        '$fishingSpotPath/user/favorites',
        queryParameters: {
          'page': page,
          'size': pageSize,
        },
      ),
      (data) {
        final List<dynamic> spots = data as List<dynamic>? ?? [];
        return spots.map<FishingSpotVo>((spot) {
          final spotSummary =
              SpotSummaryVo.fromMap(Map<String, dynamic>.from(spot));
          return spotSummary.toFishingSpotVo();
        }).toList();
      },
    );
  }

  /// Fetches fishing spots created by the current user with pagination
  ///
  /// Parameters:
  /// - [page] Page number (0-based indexing for pagination)
  /// - [pageSize] Number of items per page
  ///
  /// Returns:
  /// A PageResult containing spots and pagination metadata
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<PageResult<FishingSpotVo>> getMyCreatedSpots({
    int page = 0,
    int pageSize = 10,
  }) async {
    final response = await safeApiCall(
      () => dio.get(
        '$fishingSpotPath/user/created',
        queryParameters: {
          'page': page,
          'size': pageSize,
        },
      ),
    );

    try {
      if (response == null) {
        throw ApiError(code: 500, message: "获取我创建的钓点失败: 返回数据为空");
      }

      return PageResult.fromMap(Map<String, dynamic>.from(response), (value) {
        final spotSummary = SpotSummaryVo.fromMap(Map<String, dynamic>.from(value));
        return spotSummary.toFishingSpotVo();
      });
    } catch (e) {
      throw ApiError(code: 500, message: '解析我创建的钓点数据失败: $e');
    }
  }

  /// Searches for fishing spots by name, address, or description
  ///
  /// Parameters:
  /// - [query] Search term
  /// - [page] Page number (0-based indexing for pagination)
  /// - [pageSize] Number of items per page
  /// - [latitude] Optional latitude for geo-search
  /// - [longitude] Optional longitude for geo-search
  /// - [radiusKm] Optional radius in kilometers for geo-search
  ///
  /// Returns:
  /// A list of [FishingSpotVo] matching the search query
  ///
  /// Throws:
  /// - [ApiError] if the request fails
  Future<List<FishingSpotVo>> searchFishingSpots({
    required String query,
    int page = 0,
    int pageSize = 10,
    double? latitude,
    double? longitude,
    double? radiusKm,
  }) async {
    final Map<String, dynamic> queryParams = {
      'query': query,
      'page': page,
      'size': pageSize,
    };

    // Add geo parameters if provided
    if (latitude != null && longitude != null) {
      queryParams['latitude'] = latitude;
      queryParams['longitude'] = longitude;
      if (radiusKm != null) {
        queryParams['radius'] = radiusKm;
      }
    }

    final response = await safeApiCall(
      () => dio.get(
        '$fishingSpotPath/search',
        queryParameters: queryParams,
      ),
    );

    try {
      final data = response;
      if (data == null) {
        throw ApiError(code: 500, message: "搜索钓点失败: 返回数据为空");
      }

      final List<dynamic> spots = data as List<dynamic>? ?? [];
      return spots.map<FishingSpotVo>((spot) {
        final spotSummary =
            SpotSummaryVo.fromMap(Map<String, dynamic>.from(spot));
        return spotSummary.toFishingSpotVo();
      }).toList();
    } catch (e) {
      throw ApiError(code: 500, message: '解析搜索结果失败: $e');
    }
  }

  /// 收藏钓点
  ///
  /// [spotId] - 钓点ID
  ///
  /// 返回是否成功收藏
  Future<bool> favoriteFishingSpot(int spotId) async {
    await safeApiCall<void>(
      () => dio.post('$fishingSpotPath/$spotId/favorite'),
    );
    return true;
  }

  /// 取消收藏钓点
  ///
  /// [spotId] - 钓点ID
  ///
  /// 返回是否成功取消收藏
  Future<bool> unfavoriteFishingSpot(int spotId) async {
    await safeApiCall<void>(
      () => dio.delete('$fishingSpotPath/$spotId/favorite'),
    );
    return true;
  }

  /// 检查钓点是否已收藏
  ///
  /// [spotId] - 钓点ID
  ///
  /// 返回钓点是否已被当前用户收藏
  Future<bool> isFavorite(int spotId) async {
    return await safeApiCall<bool>(
      () => dio.get('$fishingSpotPath/$spotId/is-favorite'),
      (data) => data as bool? ?? false,
    );
  }

  /// 获取用户创建的钓点列表
  ///
  /// [userId] - 用户ID
  /// [page] - 页码 (0-based)
  /// [size] - 每页大小
  ///
  /// 返回用户创建的钓点列表
  Future<List<FishingSpotVo>> getUserCreatedSpots(int userId, int page, int size) async {
    return await safeApiCall<List<FishingSpotVo>>(
      () => dio.get('$fishingSpotPath/user/$userId/created', queryParameters: {
        'page': page,
        'size': size,
      }),
      (data) {
        if (data is List) {
          return data.map((item) => FishingSpotVo.fromMap(item as Map<String, dynamic>)).toList();
        }
        return <FishingSpotVo>[];
      },
    );
  }

  /// 删除钓点
  ///
  /// [spotId] - 钓点ID
  ///
  /// 返回是否成功删除
  Future<bool> deleteFishingSpot(int spotId) async {
    await safeApiCall<void>(
      () => dio.delete('$fishingSpotPath/$spotId'),
    );
    return true;
  }
}
