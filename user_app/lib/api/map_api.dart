import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/features/fishing_spots/models/weather_data_dto.dart';
import 'package:user_app/models/map/re_geocode_dto.dart';

class MapApi extends BaseApi {
  MapApi(super.dio);

  Future<ReGeocodeDto> resolveCoordinate({
    required double latitude,
    required double longitude,
  }) async {
    return await safeApiCall<ReGeocodeDto>(
      () => dio.get(
        '/map/resolve_coordinate',
        queryParameters: {
          'latitude': latitude,
          'longitude': longitude,
        },
      ),
      (data) => ReGeocodeDto.fromMap(data),
    );
  }

  Future<WeatherDataDto> getWeatherByIp() async {
    return await safeApiCall<WeatherDataDto>(
      () => dio.get('/map/weather/by-ip'),
      (data) => WeatherDataDto.fromMap(data),
    );
  }

  Future<WeatherDataDto> getWeatherForecast() async {
    return await safeApiCall<WeatherDataDto>(
      () => dio.get('/map/weather_forecast'),
      (data) => WeatherDataDto.fromMap(data),
    );
  }
}
