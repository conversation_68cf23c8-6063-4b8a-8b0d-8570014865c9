import 'package:user_app/core/network/base_api.dart';
import 'package:user_app/models/moment/user_fans_attentions_request.dart';
import 'package:user_app/models/moment/user_fans_attentions_response.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/models/user/batch_user_info_request.dart';
import 'package:user_app/models/user/batch_user_info_response.dart';

class UserApi extends BaseApi {
  UserApi(super.dio);

  Future<User> getCurrentUser() async {
    return await safeApiCall<User>(
      () => dio.get('/users'),
      (data) => User.fromMap(data),
    );
  }

  Future<User> getUserProfile(num userId) async {
    return await safeApiCall<User>(
      () => dio.get('/user-profiles/$userId'),
      (data) => User.fromMap(data),
    );
  }

  Future<BatchUserInfoResponse> getBatchUserInfo(List<int> userIds) async {
    final request = BatchUserInfoRequest(userIds: userIds);
    return await safeApiCall<BatchUserInfoResponse>(
      () => dio.post('/user-profiles/batch', data: request.toMap()),
      (data) => BatchUserInfoResponse.fromMap(data),
    );
  }

  Future<UserFansAttentionsResponse> getFans(
      UserFansAttentionsRequest request) async {
    return await safeApiCall<UserFansAttentionsResponse>(
      () => dio.post('/user-follows/fans', data: request.toJson()),
      (data) => UserFansAttentionsResponse.fromMap(data),
    );
  }

  Future<UserFansAttentionsResponse> getAttentions(
      UserFansAttentionsRequest request) async {
    return await safeApiCall<UserFansAttentionsResponse>(
      () => dio.post('/user-follows/following', data: request.toJson()),
      (data) => UserFansAttentionsResponse.fromMap(data),
    );
  }

  Future<List<User>> searchUsers(String query, int page, int pageSize) async {
    return await safeApiCall<List<User>>(
      () => dio.get('/users/search', queryParameters: {
        'query': query,
        'page': page,
        'size': pageSize,
      }),
      (data) {
        if (data is List) {
          return data.map((item) => User.fromMap(item)).toList();
        }
        return <User>[];
      },
    );
  }
}
