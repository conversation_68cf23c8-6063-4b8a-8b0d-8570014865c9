import 'package:user_app/core/network/base_api.dart';

class UserFollowApi extends BaseApi {
  static const String requestPath = '/user-follows';

  UserFollowApi(super.dio);

  // Follow a user
  Future<void> followUser(num userId) async {
    await safeApiCall<void>(
      () => dio.get('$requestPath/follow/$userId'),
    );
  }

  // Unfollow a user
  Future<void> unfollowUser(num userId) async {
    await safeApiCall<void>(
      () => dio.get('$requestPath/unfollow/$userId'),
    );
  }

  // Check if following user
  Future<bool> isFollowingUser(num userId) async {
    return await safeApiCall<bool>(
      () => dio.get('$requestPath/is_follow/$userId'),
      (data) => data as bool,
    );
  }
}
