import 'package:flutter/material.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:user_app/config/app_router_config.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/providers.dart';
import 'package:user_app/services/oss_service.dart';
import 'package:user_app/utils/shared_preferences_util.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/view_models/auth_view_model.dart';

Future<void> main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();
    await setupInjection();
    
    // Initialize dart_mappable mappers
    debugPrint('🔧 [MAIN] Initializing dart_mappable mappers...');
    FishingSpotVoMapper.ensureInitialized();
    MomentVoMapper.ensureInitialized();
    debugPrint('✅ [MAIN] Mappers initialized successfully');
    
    Flutter2dAMap.updatePrivacy(true);
    timeago.setLocaleMessages('zh_CN', timeago.ZhCnMessages());

    // Initialize services
    await Future.wait([
      SharedPreferencesUtil.init(),
      Flutter2dAMap.setApiKey(
        iOSKey: '780498fc4a6dadc659230dcc386f1dd8',
        webKey: 'e66be3d5db9b91cd0b7bd1eb1cbdb681',
      ),
      getIt<OssService>().init(), // Initialize OssService
    ]);

    // 初始化应用状态（包括IM初始化）
    try {
      final authViewModel = getIt<AuthViewModel>();
      await authViewModel.initializeAppState();
    } catch (e) {
      debugPrint('❌ [MAIN] 应用状态初始化失败: $e');
    }

    runApp(
      MultiProvider(
        providers: providers(),
        child: MaterialApp.router(
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          ),
          routerConfig: AppRouterConfig.router,
        ),
      ),
    );
  } catch (e, stackTrace) {
    debugPrint('Error during application startup: $e');
    debugPrint('Stack trace: $stackTrace');
  }
}
