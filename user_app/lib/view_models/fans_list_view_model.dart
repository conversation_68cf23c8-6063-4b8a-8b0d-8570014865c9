import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/models/moment/user_fans_attentions_request.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/services/app_service.dart';
import 'package:user_app/services/user_service.dart';

class FansListViewModel extends BaseViewModel {
  final num userId;
  final UserService _userService;
  List<User> users = [];
  num _currentPage = 1;
  final num _pageSize = 10;
  bool _hasMore = true;

  FansListViewModel({required this.userId, required AppServices appServices})
      : _userService = appServices.userService;

  Future<void> fetchFans({bool reset = false}) async {
    if (reset) {
      _currentPage = 1;
      _hasMore = true;
      users.clear();
    } else {
      _currentPage++;
    }

    if (!_hasMore) return;

    UserFansAttentionsRequest request = UserFansAttentionsRequest(
      pageNum: _currentPage,
      pageSize: _pageSize,
      userId: userId,
    );
    var fans = await _userService.getFans(request);
    if (fans.pages == _currentPage) {
      _hasMore = false;
    }

    users = List.from(users)..addAll(fans.records);
    setBusy(false);
  }
}
