import 'package:flutter/foundation.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/api/bookmark_api.dart';

class BookmarkViewModel extends BaseViewModel {
  final BookmarkApi _bookmarkApi;

  BookmarkViewModel({required BookmarkApi bookmarkApi})
      : _bookmarkApi = bookmarkApi;

  // 收藏的动态列表
  List<MomentVo> _bookmarkedMoments = [];
  List<MomentVo> get bookmarkedMoments => _bookmarkedMoments;

  // 收藏的钓点列表
  List<FishingSpotVo> _bookmarkedSpots = [];
  List<FishingSpotVo> get bookmarkedSpots => _bookmarkedSpots;

  // 分页信息
  int _currentMomentPage = 1;
  int _currentSpotPage = 1;
  bool _hasMoreMoments = true;
  bool _hasMoreSpots = true;

  bool get hasMoreMoments => _hasMoreMoments;
  bool get hasMoreSpots => _hasMoreSpots;

  // 加载状态
  bool _isLoadingMoments = false;
  bool _isLoadingSpots = false;
  bool _isLoadingMoreMoments = false;
  bool _isLoadingMoreSpots = false;

  bool get isLoadingMoments => _isLoadingMoments;
  bool get isLoadingSpots => _isLoadingSpots;
  bool get isLoadingMoreMoments => _isLoadingMoreMoments;
  bool get isLoadingMoreSpots => _isLoadingMoreSpots;

  // 加载收藏的动态
  Future<void> loadBookmarkedMoments({bool refresh = false}) async {
    if (refresh) {
      _currentMomentPage = 1;
      _hasMoreMoments = true;
      _bookmarkedMoments.clear();
    }

    if (!_hasMoreMoments) return;

    try {
      _isLoadingMoments = refresh;
      _isLoadingMoreMoments = !refresh;
      notifyListeners();

      final response = await _bookmarkApi.getBookmarkedMoments(
        page: _currentMomentPage,
        pageSize: 20,
      );

      if (response.isSuccess && response.data != null) {
        final newMoments = response.data!;

        if (refresh) {
          _bookmarkedMoments = newMoments;
        } else {
          _bookmarkedMoments.addAll(newMoments);
        }

        _hasMoreMoments = newMoments.length >= 20;
        _currentMomentPage++;
      } else {
        debugPrint('加载收藏动态失败: ${response.message}');
      }
    } catch (e) {
      debugPrint('加载收藏动态失败: $e');
    } finally {
      _isLoadingMoments = false;
      _isLoadingMoreMoments = false;
      notifyListeners();
    }
  }

  // 加载收藏的钓点
  Future<void> loadBookmarkedSpots({bool refresh = false}) async {
    if (refresh) {
      _currentSpotPage = 1;
      _hasMoreSpots = true;
      _bookmarkedSpots.clear();
    }

    if (!_hasMoreSpots) return;

    try {
      _isLoadingSpots = refresh;
      _isLoadingMoreSpots = !refresh;
      notifyListeners();

      final response = await _bookmarkApi.getBookmarkedSpots(
        page: _currentSpotPage,
        pageSize: 20,
      );

      if (response.isSuccess && response.data != null) {
        final newSpots = response.data!;

        if (refresh) {
          _bookmarkedSpots = newSpots;
        } else {
          _bookmarkedSpots.addAll(newSpots);
        }

        _hasMoreSpots = newSpots.length >= 20;
        _currentSpotPage++;
      } else {
        debugPrint('加载收藏钓点失败: ${response.message}');
      }
    } catch (e) {
      debugPrint('加载收藏钓点失败: $e');
    } finally {
      _isLoadingSpots = false;
      _isLoadingMoreSpots = false;
      notifyListeners();
    }
  }

  // 取消收藏动态
  Future<void> unbookmarkMoment(int momentId) async {
    try {
      await _bookmarkApi.unbookmarkMoment(momentId);
      _bookmarkedMoments.removeWhere((moment) => moment.id == momentId);
      notifyListeners();
    } catch (e) {
      debugPrint('取消收藏失败: $e');
    }
  }

  // 取消收藏钓点
  Future<void> unbookmarkSpot(int spotId) async {
    try {
      await _bookmarkApi.unbookmarkSpot(spotId);
      _bookmarkedSpots.removeWhere((spot) => spot.id == spotId);
      notifyListeners();
    } catch (e) {
      debugPrint('取消收藏失败: $e');
    }
  }

  // 刷新所有收藏
  Future<void> refreshAll() async {
    await Future.wait([
      loadBookmarkedMoments(refresh: true),
      loadBookmarkedSpots(refresh: true),
    ]);
  }
}
