import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:user_app/models/social_insights/social_insights.dart';
import 'package:user_app/services/social_insights_service.dart';

class SocialInsightsViewModel extends ChangeNotifier {
  final SocialInsightsService _socialInsightsService;

  SocialInsightsViewModel(this._socialInsightsService);

  SocialInsights? _socialInsights;
  bool _isLoading = false;
  String? _error;
  
  // Request deduplication
  Future<SocialInsights>? _currentRequest;

  SocialInsights? get socialInsights => _socialInsights;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasData => _socialInsights != null;

  Future<void> fetchSocialInsights({String? timeRange}) async {
    // If there's already a request in progress, wait for it
    if (_currentRequest != null) {
      try {
        await _currentRequest;
      } catch (e) {
        // Ignore error from previous request
      }
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    _currentRequest = _socialInsightsService.fetchSocialInsights(timeRange: timeRange);

    try {
      _socialInsights = await _currentRequest!;
      _error = null;
    } catch (e) {
      _error = e.toString();
      debugPrint('Failed to fetch social insights: $e');
      
      // 提供默认数据作为后备
      _socialInsights = SocialInsights(
        interactionRate: 78.0,
        newFollowers: 234,
        interactionTrend: '+12%',
        followersTrend: '+45',
        weeklyActivity: [65, 80, 45, 90, 70, 85, 60],
        lastUpdated: DateTime.now(),
      );
    } finally {
      _isLoading = false;
      _currentRequest = null;
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void loadFallbackData() {
    _socialInsights = SocialInsights(
      interactionRate: 65.5,
      newFollowers: 128,
      interactionTrend: '+8%',
      followersTrend: '+25',
      weeklyActivity: [45, 60, 35, 70, 55, 80, 50],
      lastUpdated: DateTime.now(),
    );
    _error = null;
    notifyListeners();
  }
}