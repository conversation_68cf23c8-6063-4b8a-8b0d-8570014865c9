import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/models/moment/moment_list_request.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/services/app_service.dart';
import 'package:user_app/services/chat_service.dart';
import 'package:user_app/services/moment_service.dart';
import 'package:user_app/services/user_service.dart';
import 'package:user_app/view_models/auth_view_model.dart';

enum ProfileTab { moments, spots }

class OtherProfilePageViewModel extends BaseViewModel {
  final num userId;
  final UserService _userService;
  final MomentService _momentService;
  final FishingSpotService _fishingSpotService;
  User? user;
  
  // Tab相关
  ProfileTab _currentTab = ProfileTab.moments;
  
  // 动态相关
  List<MomentVo> _moments = [];
  int _currentMomentsPage = 0;
  final int _pageSize = 10;
  bool _hasMoments = true;
  
  // 钓点相关
  List<FishingSpotVo> _spots = [];
  int _currentSpotsPage = 0;
  bool _hasSpots = true;
  
  final ScrollController scrollController = ScrollController();
  late ChatService _chatService;

  OtherProfilePageViewModel(
      {required this.userId, required AppServices appServices})
      : _userService = appServices.userService,
        _momentService = appServices.momentService,
        _fishingSpotService = appServices.fishingSpotService {
    scrollController.addListener(_onScroll);
    _chatService = appServices.chatService;
  }

  void _onScroll() {
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200) {
      if (!super.busy && hasMore) {
        if (_currentTab == ProfileTab.moments) {
          fetchMoments();
        } else {
          fetchSpots();
        }
      }
    }
  }

  // Getters
  ProfileTab get currentTab => _currentTab;
  List<MomentVo> get moments => _moments;
  List<FishingSpotVo> get spots => _spots;
  bool get hasMore => _currentTab == ProfileTab.moments ? _hasMoments : _hasSpots;

  Future<void> init() async {
    setBusy(true);
    var user = await _userService.getUserProfile(userId);
    this.user = user;
    await fetchMoments(reset: true);
    setBusy(false);
  }
  
  // Tab切换
  void switchTab(ProfileTab tab) {
    if (_currentTab == tab) return;
    
    _currentTab = tab;
    notifyListeners();
    
    if (tab == ProfileTab.spots && _spots.isEmpty) {
      fetchSpots(reset: true);
    }
  }

  Future<void> fetchMoments({bool reset = false}) async {
    if (reset) {
      _hasMoments = true;
      _moments = [];
      _currentMomentsPage = 0;
    }

    if (_hasMoments == false) return;

    setBusy(true);
    _currentMomentsPage++;
    MomentListRequest request = MomentListRequest(
      userId: userId,
      pageSize: _pageSize,
      pageNum: _currentMomentsPage,
    );
    var momentResponse = await _momentService.getMoments(request);
    _moments.addAll(momentResponse.records);

    if (momentResponse.total <= _moments.length) {
      _hasMoments = false;
    }
    setBusy(false);
  }
  
  Future<void> fetchSpots({bool reset = false}) async {
    if (reset) {
      _hasSpots = true;
      _spots = [];
      _currentSpotsPage = 0;
    }

    if (_hasSpots == false) return;

    setBusy(true);
    try {
      // 调用钓点服务获取用户创建的钓点
      var spots = await _fishingSpotService.getUserCreatedSpots(
        userId: userId.toInt(),
        page: _currentSpotsPage,
        size: _pageSize,
      );
      
      if (reset) {
        _spots = spots;
      } else {
        _spots.addAll(spots);
      }
      
      if (spots.length < _pageSize) {
        _hasSpots = false;
      }
      
      _currentSpotsPage++;
      debugPrint('✅ [ProfileVM] 获取到 ${spots.length} 个用户钓点');
    } catch (e) {
      debugPrint('❌ [ProfileVM] 获取用户钓点失败: $e');
      _hasSpots = false;
    }
    setBusy(false);
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  Future<void> navigateToChat(BuildContext context) async {
    if (!context.read<AuthViewModel>().checkAndPromptLogin(context)) {
      return;
    }

    // Navigator.push(
    //   context,
    //   MaterialPageRoute(
    //     builder: (context) => TIMUIKitChat(
    //       conversation: V2TimConversation(
    //         conversationID: 'c2c_$userId',
    //         type: 1,
    //         userID: '$userId',
    //         showName: user?.name ?? '',
    //       ),
    //     ),
    //   ),
    // );
  }
}
