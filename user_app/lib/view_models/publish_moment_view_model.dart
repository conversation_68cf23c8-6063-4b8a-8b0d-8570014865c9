// import 'package:flutter/material.dart';
// import 'package:flutter_2d_amap/flutter_2d_amap.dart';
// import 'package:go_router/go_router.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:user_app/core/view_models/base_view_model.dart';
// import 'package:user_app/models/moment/create_moment_model.dart';
// import 'package:user_app/services/app_service.dart';
// import 'package:user_app/services/map_service.dart';
// import 'package:user_app/services/moment_service.dart';
// import 'package:user_app/services/oss_service.dart';
// import 'package:user_app/utils/dialog_util.dart';
//
// class PublishMomentViewModel extends BaseViewModel {
//   final MomentService _momentService;
//   final OssService _ossService;
//   final MapService _mapService;
//
//   final TextEditingController contentController = TextEditingController();
//   final TextEditingController fishNameController = TextEditingController();
//   final TextEditingController locationController = TextEditingController();
//   final ValueNotifier<List<String>> imageUrls = ValueNotifier<List<String>>([]);
//   final int maxImageCount = 9;
//   final ValueNotifier<List<String>> fishNameTagsNotifier =
//       ValueNotifier<List<String>>([]);
//   late PoiSearch poiSearch;
//
//   final TextEditingController fishWeightController =
//       TextEditingController(text: '0.0');
//
//   num latitude = 0;
//   num longitude = 0;
//
//   PublishMomentViewModel({required AppServices appServices})
//       : _ossService = appServices.ossService,
//         _momentService = appServices.momentService,
//         _mapService = appServices.mapService;
//
//   Future<void> initOssService() async {
//     await _ossService.init();
//   }
//
//   Future<void> pickImages(BuildContext context) async {
//     setBusy(true);
//     final List<XFile> images = await ImagePicker().pickMultiImage();
//     final currentImageCount = imageUrls.value.length;
//     if (images.length + currentImageCount > maxImageCount) {
//       if (context.mounted) {
//         showAlertDialog(context, '提示', '最多只能选择$maxImageCount张图片', [
//           TextButton(
//             onPressed: () => Navigator.pop(context),
//             child: const Text('好的'),
//           ),
//         ]);
//         return;
//       }
//     }
//
//     for (var image in images) {
//       var fileName = await _ossService.saveFile(image);
//       imageUrls.value = List.from(imageUrls.value)..add(fileName);
//     }
//     setBusy(false);
//   }
//
//   void removeImage(int index) {
//     imageUrls.value = List.from(imageUrls.value)..removeAt(index);
//   }
//
//   Future<void> publishMoment(BuildContext context) async {
//     setBusy(true);
//     if (imageUrls.value.isEmpty) {
//       showAlertDialog(
//         context,
//         '提示',
//         '上传图片能让你的回忆更丰富',
//         [
//           TextButton(
//             onPressed: () => {context.pop()},
//             child: const Text('好的'),
//           ),
//         ],
//       );
//       setBusy(false);
//       return;
//     }
//
//     final moment = CreateMomentModel(
//       content: contentController.text,
//       pictures: imageUrls.value,
//       tag: fishNameTagsNotifier.value,
//       fishCatch: double.tryParse(fishWeightController.text) ?? 0,
//       longitude: poiSearch.longitude,
//       latitude: poiSearch.latitude,
//       province: poiSearch.provinceName,
//       city: poiSearch.cityName,
//       county: poiSearch.adName,
//       addressDetail: locationController.text,
//     );
//
//     await _momentService.createMoment(moment);
//     setBusy(false);
//
//     if (!context.mounted) return;
//     showAlertDialog(context, '提示', '动态发布成功', [
//       TextButton(
//         onPressed: () {
//           context.pop();
//           resetData();
//         },
//         child: const Text('好的'),
//       ),
//     ]);
//   }
//
//   void resetData() {
//     contentController.clear();
//     fishNameController.clear();
//     locationController.clear();
//     imageUrls.value = [];
//     fishNameTagsNotifier.value = [];
//     fishWeightController.clear();
//   }
//
//   void addFishNameTag(BuildContext context) {
//     String fishName = fishNameController.text.trim();
//     if (fishName.isEmpty) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: const Text('请输入鱼类标签'),
//           duration: const Duration(seconds: 2),
//           behavior: SnackBarBehavior.floating,
//           action: SnackBarAction(
//             label: '好的',
//             onPressed: () => {
//               fishNameController.clear(),
//               ScaffoldMessenger.of(context).hideCurrentSnackBar(),
//             },
//           ),
//         ),
//       );
//       return;
//     }
//     if (fishNameTagsNotifier.value.contains(fishName)) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: const Text('已添加过该鱼类标签'),
//           duration: const Duration(seconds: 2),
//           behavior: SnackBarBehavior.floating,
//           action: SnackBarAction(
//             label: '好的',
//             onPressed: () => {
//               fishNameController.clear(),
//               ScaffoldMessenger.of(context).hideCurrentSnackBar(),
//             },
//           ),
//         ),
//       );
//       return;
//     }
//     fishNameTagsNotifier.value = List.from(fishNameTagsNotifier.value)
//       ..add(fishName);
//     fishNameController.clear();
//   }
//
//   void decrementWeight() {
//     double currentValue = double.tryParse(fishWeightController.text) ?? 0.0;
//     if (currentValue > 0.1) {
//       updateDisplay(currentValue - 0.1);
//     } else {
//       updateDisplay(0.0);
//     }
//   }
//
//   void incrementWeight() {
//     double currentValue = double.tryParse(fishWeightController.text) ?? 0.0;
//     updateDisplay(currentValue + 0.1);
//   }
//
//   void updateDisplay(double value) {
//     fishWeightController.text = value.toStringAsFixed(1);
//   }
// }
