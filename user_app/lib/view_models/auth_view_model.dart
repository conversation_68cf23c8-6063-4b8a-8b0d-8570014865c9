import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/models/session/login_response.dart';
import 'package:user_app/models/session/validate_verification_code_result.dart';
import 'package:user_app/models/statistics/statistics.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/services/app_service.dart';
import 'package:user_app/services/session_service.dart';
import 'package:user_app/services/sms_service.dart';
import 'package:user_app/services/statistics_service.dart';
import 'package:user_app/services/user_service.dart';
import 'package:user_app/services/notification_service.dart';
import 'package:user_app/services/user_profile_service.dart';
import 'package:user_app/utils/dialog_util.dart';
import 'package:user_app/utils/api_test_helper.dart';
import 'package:user_app/view_models/chat_view_model.dart';
import 'package:user_app/widgets/register_success.dart';

class AuthViewModel extends BaseViewModel {
  final UserService userService;
  final SessionService sessionService;
  final SmsService smsService;
  final StatisticsService statisticsService;
  final NotificationService notificationService;
  final SharedPreferences sharedPreferences;

  final StreamController<void> _loginSuccessController =
      StreamController<void>.broadcast();

  Stream<void> get loginSuccessStream => _loginSuccessController.stream;

  AuthViewModel(
      {required AppServices appServices, required this.sharedPreferences})
      : userService = appServices.userService,
        sessionService = appServices.sessionService,
        smsService = appServices.smsService,
        statisticsService = appServices.statisticsService,
        notificationService = appServices.notificationService {
    _registerPhoneNumberController = TextEditingController();
  }

  User? _currentUser;
  final TextEditingController _loginPhoneNumberController =
      TextEditingController();
  final TextEditingController _loginPasswordController =
      TextEditingController();
  late TextEditingController _registerVerificationCodeController;

  late TextEditingController _registerPasswordController;
  late TextEditingController _registerConfirmPasswordController;

  final _resetPasswordPhoneNumberController = TextEditingController();
  final _resetPasswordVerificationCodeController = TextEditingController();
  final _resetPasswordPasswordController = TextEditingController();
  final _resetPasswordConfirmPasswordController = TextEditingController();

  User? get currentUser => _currentUser;
  Timer? _countdownTimer;
  int _countdown = 0;
  late ValidateVerificationCodeResult? validateVerificationCodeResult;

  int get countdown => _countdown;

  int getCurrentUserId() {
    _currentUser = userService.getCachedUser();
    return _currentUser?.id ?? 0;
  }

  TextEditingController get loginPhoneNumberController =>
      _loginPhoneNumberController;

  TextEditingController get loginPasswordController => _loginPasswordController;

  TextEditingController get registerVerificationCodeController =>
      _registerVerificationCodeController;

  TextEditingController get registerPasswordController =>
      _registerPasswordController;

  TextEditingController get registerConfirmPasswordController =>
      _registerConfirmPasswordController;

  TextEditingController get resetPasswordPhoneNumberController =>
      _resetPasswordPhoneNumberController;

  TextEditingController get resetPasswordVerificationCodeController =>
      _resetPasswordVerificationCodeController;

  TextEditingController get resetPasswordPasswordController =>
      _resetPasswordPasswordController;

  TextEditingController get resetPasswordConfirmPasswordController =>
      _resetPasswordConfirmPasswordController;

  late TextEditingController _registerPhoneNumberController;

  TextEditingController get registerPhoneNumberController =>
      _registerPhoneNumberController;

  void setRegisterPhoneNumberController(TextEditingController controller) {
    _registerPhoneNumberController = controller;
  }

  final PageController _registerPageController = PageController();
  final PageController _resetPasswordPageController = PageController();

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  int unreadNotifications = 0;

  PageController get registerPageController => _registerPageController;

  PageController get resetPasswordPageController =>
      _resetPasswordPageController;

  Future<void> loadUserProfile(BuildContext context) async {
    _isLoading = true;
    notifyListeners();

    try {
      // 优先使用缓存数据
      _currentUser = userService.getCachedUser();
      
      if (_currentUser != null) {
        // 如果有缓存用户数据，并行获取统计和通知数据
        if (context.mounted) {
          await Future.wait([
            fetchStatistics(context),
            fetchUnreadNotifications(),
          ]);
        }
      } else {
        // 如果没有缓存，获取完整的用户数据
        await getCurrentUser();
        if (context.mounted) {
          await Future.wait([
            fetchStatistics(context),
            fetchUnreadNotifications(),
          ]);
        }
      }
    } catch (e) {
      debugPrint('加载用户资料失败: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> getCurrentUser() async {
    setBusy(true);
    try {
      // 优先尝试使用缓存的用户数据
      _currentUser = userService.getCachedUser();
      
      // 如果没有缓存或需要刷新，从服务器获取
      if (_currentUser == null) {
        _currentUser = await userService.getCurrentUser();
        await userService.cacheUser(_currentUser!);
      }
    } on DioException catch (error) {
      if (error.response?.statusCode == 401) {
        await userService.logout();
        _currentUser = null;
      }
    } catch (e) {
      debugPrint('获取用户信息失败: $e');
      // 如果网络请求失败但有缓存，继续使用缓存
      _currentUser ??= userService.getCachedUser();
    }
    setBusy(false);
  }

  Future<bool> login(BuildContext context) async {
    setBusy(true);
    final phoneNumber = loginPhoneNumberController.text;
    final password = loginPasswordController.text;
    if (phoneNumber.isEmpty) {
      showAlertDialog(context, '提示', '请输入手机号');
      setBusy(false);
      return false;
    }

    if (!RegExp(r'^1[3456789]\d{9}$').hasMatch(phoneNumber)) {
      showAlertDialog(context, '提示', '请输入正确的手机号');
      setBusy(false);
      return false;
    }

    if (password.isEmpty) {
      showAlertDialog(context, '提示', '请输入密码');
      setBusy(false);
      return false;
    }

    LoginResponse loginResponse =
        await sessionService.login(phoneNumber, password);
    await userService.cacheUser(loginResponse.user!);
    await userService.cacheToken(loginResponse.token!);

    // 验证token是否已经存储
    final storedToken = userService.getAccessToken();
    debugPrint('🔐 [AuthViewModel] Token存储验证: ${storedToken != null}');
    if (storedToken != null) {
      debugPrint('🔐 [AuthViewModel] 存储的token长度: ${storedToken.length}');
      
      // 立即测试token的有效性
      try {
        debugPrint('🔐 [AuthViewModel] 测试token有效性...');
        await userService.getCurrentUser(); // 这会使用新token发送请求
        debugPrint('✅ [AuthViewModel] Token有效性测试通过');
      } catch (e) {
        debugPrint('❌ [AuthViewModel] Token有效性测试失败: $e');
      }
    }

    _currentUser = await userService.getUserProfile(loginResponse.user!.id);
    _loginSuccessController.add(null);
    
    // 确保token完全存储后再初始化IM
    await Future.delayed(Duration(milliseconds: 500));
    
    // 再次验证token存储
    final finalToken = userService.getAccessToken();
    debugPrint('🔐 [AuthViewModel] 最终token验证: ${finalToken != null}');
    if (finalToken != null) {
      debugPrint('🔐 [AuthViewModel] 最终token长度: ${finalToken.length}');
    }
    
    try {
      await getIt<ChatViewModel>().initialize();
    } catch (e) {
      debugPrint('ChatViewModel初始化失败: $e');
      // 即使IM初始化失败，也不阻止登录成功
    }
    
    setBusy(false);
    return true;
  }

  String? getCachedToken() {
    return userService.getAccessToken();
  }

  bool isUserLoggedIn() {
    try {
      return userService.getAccessToken() != null;
    } catch (e) {
      debugPrint('Error checking login status: $e');
      return false;
    }
  }

  Future<void> logout() async {
    setBusy(true);
    
    // 登出时清理IM状态
    try {
      final chatViewModel = getIt<ChatViewModel>();
      await chatViewModel.logout();
      debugPrint('✅ [AuthViewModel] IM状态清理完成');
    } catch (e) {
      debugPrint('❌ [AuthViewModel] 清理IM状态异常: $e');
    }
    
    await userService.logout();
    _currentUser = null;
    setBusy(false);
  }

  Future<void> sendVerificationCode(
      BuildContext context, String phoneNumber) async {
    if (!RegExp(r'^1[3456789]\d{9}$')
        .hasMatch(registerPhoneNumberController.text)) {
      showAlertDialog(context, '提示', '请输入正确的手机号');
      return;
    }

    setBusy(true);
    validateVerificationCodeResult =
        await smsService.getVerificationCodeForRegister(phoneNumber);
    if (!context.mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
          content: Text('验证码已发送'), behavior: SnackBarBehavior.floating),
    );
    startCountdown(60);
    setBusy(false);

    // registerPageController.nextPage(
    //   duration: const Duration(milliseconds: 300),
    //   curve: Curves.easeIn,
    // );
    if (phoneNumber == registerPhoneNumberController.text) {
      registerPageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
    } else if (phoneNumber == resetPasswordPhoneNumberController.text) {
      resetPasswordPageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeIn,
      );
    }
  }

  void loadCountdownFromSharedPreferences() {
    int? savedCountdown = sharedPreferences.getInt('countdown');
    if (savedCountdown != null) {
      _countdown = savedCountdown;
      if (_countdown > 0 && _countdownTimer == null) {
        startCountdown(_countdown);
      }
    }
  }

  void startCountdown(int duration) {
    _countdown = duration;
    notifyListeners();
    if (_countdownTimer != null && _countdownTimer!.isActive) {
      return;
    }

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        _countdown--;
        notifyListeners();
        _saveCountdownToSharedPreferences();
      } else {
        _countdownTimer?.cancel();
        _clearCountdownFromSharedPreferences();
      }
    });
  }

  Future<void> _saveCountdownToSharedPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('countdown', _countdown);
  }

  Future<void> _clearCountdownFromSharedPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('countdown');
  }

  Future<void> register(BuildContext context) async {
    final phoneNumber = registerPhoneNumberController.text;
    final verificationCode = registerVerificationCodeController.text;
    final password = registerPasswordController.text;
    final confirmPassword = registerConfirmPasswordController.text;

    Navigator.of(context).push(MaterialPageRoute(builder: (context) {
      return const RegisterSuccess();
    }));

    if (phoneNumber.isEmpty) {
      showAlertDialog(context, '提示', '请输入手机号', [
        TextButton(
          onPressed: () {
            registerPageController.jumpToPage(0);
            Navigator.of(context).pop();
          },
          child: const Text('确定'),
        ),
      ]);
      return;
    }

    if (!RegExp(r'^1[3456789]\d{9}$').hasMatch(phoneNumber)) {
      showAlertDialog(context, '提示', '请输入正确的手机号', [
        TextButton(
          onPressed: () {
            registerPageController.jumpToPage(0);
            Navigator.of(context).pop();
          },
          child: const Text('确定'),
        ),
      ]);
      return;
    }

    if (verificationCode.isEmpty) {
      showAlertDialog(context, '提示', '请输入验证码', [
        TextButton(
          onPressed: () {
            registerPageController.jumpToPage(1);
            Navigator.of(context).pop();
          },
          child: const Text('确定'),
        ),
      ]);
      return;
    }

    if (password.isEmpty) {
      showAlertDialog(context, '提示', '请输入密码');
      return;
    }

    if (confirmPassword.isEmpty) {
      showAlertDialog(context, '提示', '请输入确认密码');
      return;
    }

    if (password != confirmPassword) {
      showAlertDialog(context, '提示', '两次输入的密码不一致');
      return;
    }

    try {
      setBusy(true);
      LoginResponse response = await sessionService.register(
        phoneNumber,
        verificationCode,
        password,
        confirmPassword,
      );
      await userService.cacheUser(response.user!);
      await userService.cacheToken(response.token!);
      setBusy(false);

      if (!context.mounted) return;
      showAlertDialog(context, '提示', '注册成功，点击确认按钮去登录', [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            Navigator.of(context).pop();
          },
          child: const Text('确定'),
        )
      ]);
    } on DioException catch (error) {
      if (error.type == DioExceptionType.badResponse &&
          error.response?.statusCode == 400) {
        var data = error.response?.data['message'];
        if (data == '验证码错误') {
          showAlertDialog(context, '错误', '验证码错误，请重新输入', [
            TextButton(
              onPressed: () {
                registerVerificationCodeController.clear();
                registerPageController.jumpToPage(1);
                Navigator.of(context).pop();
              },
              child: const Text('确定'),
            ),
          ]);
          return;
        }

        if (data == '该手机号已注册') {
          showAlertDialog(context, '错误', '手机号已注册', [
            TextButton(
              onPressed: () {
                registerPhoneNumberController.clear();
                registerPageController.jumpToPage(0);
                Navigator.of(context).pop();
              },
              child: const Text('确定'),
            ),
          ]);
          return;
        }

        showAlertDialog(context, '错误', '注册失败: $data');
      } else {
        showAlertDialog(context, '错误', '注册失败: $error');
      }
    }
  }

  Future<void> handleResetPassword(BuildContext context) async {
    final phoneNumber = _resetPasswordPhoneNumberController.text;
    final verificationCode = _resetPasswordVerificationCodeController.text;
    final password = _resetPasswordPasswordController.text;
    final confirmPassword = _resetPasswordConfirmPasswordController.text;

    if (phoneNumber.isEmpty) {
      showAlertDialog(context, '提示', '请输入手机号');
      return;
    }

    if (verificationCode.isEmpty) {
      showAlertDialog(context, '提示', '请输入验证码');
      return;
    }

    if (password.isEmpty) {
      showAlertDialog(context, '提示', '请输入密码');
      return;
    }

    if (confirmPassword.isEmpty) {
      showAlertDialog(context, '提示', '请输入确认密码');
      return;
    }

    if (password != confirmPassword) {
      showAlertDialog(context, '提示', '两次输入的密码不一致');
      return;
    }

    try {
      setBusy(true);
      LoginResponse response = await sessionService.resetPassword(
        phoneNumber,
        verificationCode,
        password,
        confirmPassword,
      );
      await userService.cacheUser(response.user!);
      await userService.cacheToken(response.token!);
      setBusy(false);

      if (!context.mounted) return;
      showAlertDialog(
        context,
        '提示',
        '密码已重置，点击确认按钮去登录',
        [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('确定'),
          ),
        ],
      );
    } catch (error) {
      showAlertDialog(context, '错误', '重置密码失败: $error');
    }
  }

  Statistics statisticsInfo = Statistics(
    momentCount: 0,
    fansCount: 0,
    followCount: 0,
  );

  List<ProfileInfoItem> profileInfoItems = [
    const ProfileInfoItem('动态', 0),
    const ProfileInfoItem('粉丝', 0),
    const ProfileInfoItem('关注', 0),
  ];

  Future<void> fetchStatistics(BuildContext context) async {
    setBusy(true);
    statisticsInfo = await statisticsService.fetchStatistics();

    final userId = getCurrentUserId();
    final fansPageRoute =
        AppRoutes.fansPage.replaceFirst(':userId', userId.toString());
    final attentionsPageRoute =
        AppRoutes.attentionsPage.replaceFirst(':userId', userId.toString());

    profileInfoItems = [
      ProfileInfoItem('动态', statisticsInfo.momentCount, onTap: () {
        context.push(AppRoutes.personalMomentList);
      }),
      ProfileInfoItem(
        '粉丝',
        statisticsInfo.fansCount,
        onTap: () {
          context.push(fansPageRoute);
        },
      ),
      ProfileInfoItem('关注', statisticsInfo.followCount, onTap: () {
        context.push(attentionsPageRoute);
      }),
    ];
    setBusy(false);
  }

  @override
  void dispose() {
    _loginSuccessController.close();
    _loginPhoneNumberController.dispose();
    _loginPasswordController.dispose();
    _registerVerificationCodeController.dispose();
    _registerPasswordController.dispose();
    _registerConfirmPasswordController.dispose();

    if (_countdownTimer != null) {
      _countdownTimer!.cancel();
    }
    super.dispose();
  }

  bool validateRegisterPhoneNumber() {
    return RegExp(r'^1[3456789]\d{9}$')
        .hasMatch(_registerPhoneNumberController.text);
  }

  void clearRegisterPhoneNumber() {
    setBusy(true);
    _registerPhoneNumberController.clear();
    setBusy(false);
  }

  String? validatePin(BuildContext context) {
    if (!context.mounted) return null;

    if (validateVerificationCodeResult!.validateCode ==
        registerVerificationCodeController.text) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('验证码验证成功'),
          behavior: SnackBarBehavior.floating,
          duration: Duration(seconds: 1), // Make notification shorter
        ),
      );
      return null;
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('验证码验证失败'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return '验证码输入错误';
    }
  }

  void resetValidateVerificationCodeResult() {
    validateVerificationCodeResult = null;
  }

  void setRegisterVerificationCodeController(
      TextEditingController textEditingController) {
    _registerVerificationCodeController = textEditingController;
  }

  void setRegisterPasswordController(
      TextEditingController textEditingController) {
    _registerPasswordController = textEditingController;
  }

  void setRegisterConfirmPasswordController(
      TextEditingController textEditingController) {
    _registerConfirmPasswordController = textEditingController;
  }

  Future<void> fetchUnreadNotifications() async {
    if (!isUserLoggedIn()) return;
    
    try {
      final count = await notificationService.getUnreadCount();
      if (unreadNotifications != count) {
        unreadNotifications = count;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('获取未读通知数量失败: $e');
    }
  }

  void refreshNotificationCount() {
    fetchUnreadNotifications();
  }

  /// 初始化应用状态（在应用启动时调用）
  Future<void> initializeAppState() async {
    debugPrint('🚀 [AuthViewModel] 初始化应用状态...');
    
    // 检查用户是否已经登录
    if (isUserLoggedIn()) {
      debugPrint('👤 [AuthViewModel] 检测到用户已登录，初始化IM...');
      
      try {
        // 加载用户信息
        _currentUser = userService.getCachedUser();
        
        // 初始化IM
        final chatViewModel = getIt<ChatViewModel>();
        if (!chatViewModel.isInitialized) {
          await chatViewModel.initialize();
          if (chatViewModel.isInitialized) {
            debugPrint('✅ [AuthViewModel] 应用启动IM初始化成功');
          } else {
            debugPrint('❌ [AuthViewModel] 应用启动IM初始化失败: ${chatViewModel.error}');
          }
        } else {
          debugPrint('📝 [AuthViewModel] IM已经初始化');
        }
      } catch (e) {
        debugPrint('❌ [AuthViewModel] 应用启动IM初始化异常: $e');
      }
    } else {
      debugPrint('🚫 [AuthViewModel] 用户未登录，跳过IM初始化');
    }
  }

  bool checkAndPromptLogin(BuildContext context) {
    if (!isUserLoggedIn()) {
      showAlertDialog(context, '提示', '请先登录后再进行此操作', [
        TextButton(
          onPressed: () {
            context.pop();
          },
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: () {
            context.pop();
            context.push(AppRoutes.login);
          },
          child: const Text('登录'),
        ),
      ]);
      return false;
    }
    return true;
  }

  /// 测试API接口连通性和数据对接
  Future<void> testApiConnectivity() async {
    debugPrint('🚀 开始测试API接口连通性...');
    
    try {
      final results = await ApiTestHelper.runAllTests();
      ApiTestHelper.printTestReport(results);
      
      // 更新UI状态
      notifyListeners();
    } catch (e) {
      debugPrint('❌ API测试过程中发生错误: $e');
    }
  }

  /// 验证数据模型映射
  Future<bool> validateDataMapping() async {
    try {
      debugPrint('🧪 验证数据模型映射...');
      
      // 验证用户数据映射
      final user = userService.getCachedUser();
      if (user != null) {
        debugPrint('✅ 用户数据映射正常: ${user.name} (ID: ${user.id})');
      }
      
      // 验证统计数据映射
      if (statisticsInfo.momentCount >= 0) {
        debugPrint('✅ 统计数据映射正常: 动态${statisticsInfo.momentCount}条, 粉丝${statisticsInfo.fansCount}个');
      }
      
      // 验证通知数据映射
      if (unreadNotifications >= 0) {
        debugPrint('✅ 通知数据映射正常: ${unreadNotifications}条未读');
      }
      
      return true;
    } catch (e) {
      debugPrint('❌ 数据模型映射验证失败: $e');
      return false;
    }
  }
}

class ProfileInfoItem {
  final String title;
  final int value;
  final Function()? onTap;

  const ProfileInfoItem(this.title, this.value, {this.onTap});
}
