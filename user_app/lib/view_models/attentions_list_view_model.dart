import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/models/moment/user_fans_attentions_request.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/services/app_service.dart';
import 'package:user_app/services/user_service.dart';

class AttentionsListViewModel extends BaseViewModel {
  final num userId;
  final UserService _userService;
  List<User> users = [];
  num _currentPage = 1;
  final num _pageSize = 10;
  bool hasMore = true;

  AttentionsListViewModel({
    required this.userId,
    required AppServices appServices,
  }) : _userService = appServices.userService;

  Future<void> fetchAttentions({bool reset = false}) async {
    if (reset) {
      _currentPage = 1;
      hasMore = true;
      users = [];
      notifyListeners();
    } else {
      if (!hasMore) return;
      _currentPage++;
    }

    setBusy(true);

    try {
      final request = UserFansAttentionsRequest(
        pageNum: _currentPage,
        pageSize: _pageSize,
        userId: userId,
      );

      final attentions = await _userService.getAttentions(request);

      if (attentions.pages == _currentPage) {
        hasMore = false;
      }

      if (reset) {
        users = attentions.records;
      } else {
        users = List.from(users)..addAll(attentions.records);
      }
    } catch (e) {
      hasMore = false;
    } finally {
      setBusy(false);
    }
  }
}
