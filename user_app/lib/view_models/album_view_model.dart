import 'package:flutter/foundation.dart';
import 'package:user_app/models/album/album_item.dart';
import 'package:user_app/models/album/album_stats.dart';
import 'package:user_app/services/album_service.dart';

class AlbumViewModel extends ChangeNotifier {
  final AlbumService albumService;

  AlbumViewModel({required this.albumService});

  // 状态管理
  bool _isLoading = false;
  bool _isLoadingMore = false;
  bool _hasError = false;
  String _errorMessage = '';
  
  // 数据
  List<AlbumItem> _images = [];
  AlbumStats? _stats;
  int _currentPage = 1;
  int _totalPages = 1;
  
  // Getters
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  List<AlbumItem> get images => _images;
  AlbumStats? get stats => _stats;
  bool get hasMorePages => _currentPage < _totalPages;

  /// 加载相册图片
  Future<void> loadAlbumImages({bool refresh = false}) async {
    try {
      if (refresh) {
        _currentPage = 1;
        _images.clear();
      }
      
      _setLoading(true);
      _clearError();

      final result = await albumService.getAlbumImages(
        page: _currentPage,
        pageSize: 20,
      );

      final List<AlbumItem> newImages = (result['images'] as List<AlbumItem>?) ?? [];
      
      if (refresh) {
        _images = newImages;
      } else {
        _images.addAll(newImages);
      }
      
      _totalPages = result['pages'] ?? 1;
      
      notifyListeners();
    } catch (e) {
      _setError('加载相册失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 刷新相册
  Future<void> refreshAlbumImages() async {
    await loadAlbumImages(refresh: true);
  }

  /// 加载更多图片
  Future<void> loadMoreImages() async {
    if (_isLoadingMore || !hasMorePages) return;

    try {
      _isLoadingMore = true;
      notifyListeners();

      _currentPage++;
      
      final result = await albumService.getAlbumImages(
        page: _currentPage,
        pageSize: 20,
      );

      final List<AlbumItem> newImages = (result['images'] as List<AlbumItem>?) ?? [];
      _images.addAll(newImages);
      
      notifyListeners();
    } catch (e) {
      // 加载失败时回退页码
      _currentPage--;
      _setError('加载更多失败: $e');
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  /// 加载相册统计信息
  Future<void> loadAlbumStats() async {
    try {
      _stats = await albumService.getAlbumStats();
      notifyListeners();
    } catch (e) {
      debugPrint('加载统计信息失败: $e');
    }
  }

  /// 添加图片到相册
  Future<bool> addToAlbum({
    required String imageUrl,
    String? description,
    String? tags,
  }) async {
    try {
      await albumService.addToAlbum(
        imageUrl: imageUrl,
        description: description,
        tags: tags,
      );
      
      // 重新加载数据
      await Future.wait([
        loadAlbumImages(refresh: true),
        loadAlbumStats(),
      ]);
      
      return true;
    } catch (e) {
      _setError('添加图片失败: $e');
      return false;
    }
  }

  /// 删除图片
  Future<bool> deleteImage(int imageId) async {
    try {
      await albumService.deleteFromAlbum(imageId);
      
      // 从本地列表中移除
      _images.removeWhere((image) => image.id == imageId);
      
      // 重新加载统计信息
      await loadAlbumStats();
      
      notifyListeners();
      return true;
    } catch (e) {
      _setError('删除图片失败: $e');
      return false;
    }
  }

  /// 批量删除图片
  Future<bool> batchDeleteImages(List<int> imageIds) async {
    try {
      await albumService.batchDeleteImages(imageIds);
      
      // 从本地列表中移除
      _images.removeWhere((image) => imageIds.contains(image.id));
      
      // 重新加载统计信息
      await loadAlbumStats();
      
      notifyListeners();
      return true;
    } catch (e) {
      _setError('批量删除失败: $e');
      return false;
    }
  }

  /// 搜索图片
  Future<List<AlbumItem>> searchImages(String tags) async {
    try {
      return await albumService.searchByTags(tags);
    } catch (e) {
      _setError('搜索失败: $e');
      return [];
    }
  }

  /// 获取最近上传的图片
  Future<List<AlbumItem>> getRecentImages({int limit = 10}) async {
    try {
      return await albumService.getRecentImages(limit: limit);
    } catch (e) {
      debugPrint('获取最近图片失败: $e');
      return [];
    }
  }

  // 私有方法
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String message) {
    _hasError = true;
    _errorMessage = message;
    notifyListeners();
  }

  void _clearError() {
    _hasError = false;
    _errorMessage = '';
  }

  @override
  void dispose() {
    super.dispose();
  }
}