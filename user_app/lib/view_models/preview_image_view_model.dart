import 'package:flutter/material.dart';
import 'package:user_app/core/view_models/base_view_model.dart';

class PreviewImageViewModel extends BaseViewModel {
  List<String> _currentPictureUrls = [];
  int _currentIndex = 0;
  late PageController _pageController;

  List<String> get currentPictureUrls => _currentPictureUrls;

  int get currentIndex => _currentIndex;

  PageController get pageController => _pageController;

  PreviewImageViewModel() {
    _pageController = PageController(initialPage: _currentIndex);
  }

  void openPictureViewer(
      BuildContext context, List<String> imageUrls, int initialIndex) {
    _currentPictureUrls = imageUrls;
    _currentIndex = initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  void onPageChanged(int index) {
    setBusy(true);
    _currentIndex = index;
    setBusy(false);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
}
