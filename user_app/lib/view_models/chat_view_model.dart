import 'package:tencent_cloud_chat_sdk/manager/v2_tim_manager.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/manager/v2_tim_manager.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/enum/V2TimSDKListener.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_user_full_info.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/models/chat/im_sign_dto.dart';
import 'package:user_app/services/app_service.dart';
import 'package:user_app/services/chat_service.dart';
import 'package:user_app/services/im_platform_adapter.dart';
import 'package:user_app/services/web_im_initializer.dart';
import 'package:image_picker/image_picker.dart';

// Mock result class for testing
class MockResult {
  final int code;
  final String desc;
  MockResult({required this.code, required this.desc});
}

class ChatViewModel extends BaseViewModel {
  final ChatService chatService;
  final AppServices _appServices;
  V2TIMManager? _timManager;
  IMPlatformAdapter? _imAdapter;

  bool _isInitialized = false;
  String _userId = '';
  String _sign = '';
  String? _error;

  // 消息和会话监听器
  final List<Function(V2TimMessage)> _messageListeners = [];
  final List<Function(List<V2TimConversation>)> _conversationListeners = [];

  // 用于调试的getter
  List<Function(V2TimMessage)> get messageListeners => _messageListeners;

  ChatViewModel({required AppServices appServices})
      : chatService = appServices.chatService,
        _appServices = appServices;

  bool get isInitialized => _isInitialized;
  String get userId => _userId;
  V2TIMManager? get timManager => _timManager;
  bool get hasError => _error != null;
  String? get error => _error;

  /// 检查认证状态和token
  Future<String?> _checkAuthenticationStatus() async {
    try {
      // 使用依赖注入中的SharedPreferences实例
      final SharedPreferences prefs = getIt<SharedPreferences>();
      final token = prefs.getString('token');

      debugPrint('🔐 [ChatViewModel] 检查认证状态...');
      debugPrint('🔐 [ChatViewModel] Token存在: ${token != null}');
      if (token != null) {
        debugPrint('🔐 [ChatViewModel] Token长度: ${token.length}');
        debugPrint(
            '🔐 [ChatViewModel] Token前缀: ${token.length > 10 ? token.substring(0, 10) : token}...');
      }

      return token;
    } catch (e) {
      debugPrint('❌ [ChatViewModel] 检查认证状态失败: $e');
      return null;
    }
  }

  Future<void> initialize() async {
    if (_isInitialized) return;

    _error = null; // 清除之前的错误
    setBusy(true);
    try {
      // 检查认证状态
      final token = await _checkAuthenticationStatus();
      if (token == null) {
        throw Exception('用户未登录或token为空');
      }

      // 强制等待确保token可用
      await Future.delayed(Duration(milliseconds: 100));

      // 1. 导入用户到腾讯云IM
      await chatService.accountImport();

      // 2. 获取用户签名
      await _getImSign();

      // 3. 初始化IM SDK
      await _initTencentIM();

      // 4. 登录IM
      await _login(_userId, _sign);

      // 5. 创建平台适配器 - Web平台特殊处理
      if (kIsWeb) {
        // Web平台使用Web TIM实例，并传递UserService
        _imAdapter = IMPlatformAdapter(
            WebImInitializer.getTIMInstance(), _appServices.userService);
      } else {
        // 移动端使用V2TIMManager，并传递UserService
        _imAdapter = IMPlatformAdapter(_timManager!, _appServices.userService);
      }

      // 6. 设置消息监听器
      await _setupMessageListeners();

      // 7. 设置会话监听器
      await _setupConversationListeners();

      _isInitialized = true;
    } catch (e) {
      _error = 'IM初始化失败: $e';
      debugPrint('IM初始化失败: $e'); // 添加日志输出便于调试
    } finally {
      setBusy(false);
    }
  }

  Future<void> _initTencentIM() async {
    try {
      debugPrint('开始初始化IM SDK...');

      if (kIsWeb) {
        // Web平台使用纯JS SDK
        debugPrint('🌐 [Web] 使用Web平台IM初始化...');

        // 检查Web SDK是否可用
        if (!WebImInitializer.isSDKAvailable()) {
          throw Exception('Web平台TIM SDK未正确加载，请检查index.html中的脚本引用');
        }

        // 使用Web专用初始化，不依赖V2TIMManager
        await WebImInitializer.initWebTIM(sdkAppID: 1600045823);

        debugPrint('✅ [Web] Web平台IM初始化成功');

        // Web平台不使用V2TIMManager，跳过常规初始化
        return;
      } else {
        // 移动端正常构造
        debugPrint('📱 [ChatViewModel] 移动端初始化IM SDK...');
        _timManager = V2TIMManager();
      }

      if (_timManager == null) {
        throw Exception('无法创建V2TIMManager实例');
      }

      // 简化的SDK监听器，避免可能的类型错误
      V2TimSDKListener? listener;

      // 只在移动端设置完整的监听器
      if (!kIsWeb) {
        listener = V2TimSDKListener(
          onConnectFailed: (code, error) {
            debugPrint('IM连接失败: $code - $error');
          },
          onConnectSuccess: () {
            debugPrint('IM连接成功');
          },
          onConnecting: () {
            debugPrint('IM连接中...');
          },
          onKickedOffline: () {
            debugPrint('IM被踢下线');
            _isInitialized = false;
          },
          onSelfInfoUpdated: (V2TimUserFullInfo info) {
            debugPrint('用户信息更新: ${info.nickName}');
          },
          onUserSigExpired: () {
            debugPrint('UserSig过期，需要重新登录');
            _isInitialized = false;
          },
        );
      }

      final result = await _timManager!.initSDK(
        sdkAppID: 1600045823,
        listener: listener,
      );

      debugPrint('initSDK 调用完成，结果: ${result.code}');

      if (result.code != 0) {
        throw Exception('IM SDK初始化失败: ${result.desc}');
      }

      // 在Web平台上，SDK初始化可能需要额外时间
      if (kIsWeb) {
        debugPrint('🌐 [ChatViewModel] Web平台SDK初始化完成，等待事件准备...');
        // Web平台可能需要等待内部事件系统准备
        await Future.delayed(const Duration(milliseconds: 500));
      }

      debugPrint('IM SDK初始化成功');
    } catch (e) {
      debugPrint('IM SDK初始化异常: $e');
      rethrow;
    }
  }

  Future<void> _getImSign() async {
    ImSignDto imSignDto = await chatService.getImSign();
    _userId = imSignDto.userId;
    _sign = imSignDto.sign;
  }

  Future<void> _login(String userId, String userSig) async {
    if (kIsWeb) {
      // Web平台使用Web初始化器登录
      final success = await WebImInitializer.loginWeb(
        userID: userId,
        userSig: userSig,
      );

      if (!success) {
        throw Exception('Web平台IM登录失败');
      }

      debugPrint('✅ [Web] IM登录成功: $userId');
    } else {
      // 移动端使用V2TIMManager登录
      final result = await _timManager!.login(
        userID: userId,
        userSig: userSig,
      );

      if (result.code != 0) {
        throw Exception('IM登录失败: ${result.desc}');
      }

      debugPrint('✅ [Mobile] IM登录成功: $userId');
    }
  }

  Future<void> logout() async {
    if (!_isInitialized) return;

    if (_timManager != null) {
      await _timManager!.logout();
    }

    _isInitialized = false;
    _userId = '';
    _sign = '';
    notifyListeners();
  }

  Future<dynamic> sendMessage(String targetUserId, String text) async {
    if (!_isInitialized || _imAdapter == null) {
      throw Exception('IM未初始化');
    }

    try {
      final result = await _imAdapter!.sendTextMessage(
        text: text,
        userID: targetUserId,
        currentUserId: _userId, // 传入当前用户ID
      );

      if (result.code != 0) {
        throw Exception('消息发送失败: ${result.desc}');
      }

      debugPrint(
          '✅ [ChatViewModel] Message sent successfully to $targetUserId');

      // 发送消息成功后，稍等一下再通知会话列表刷新（Web平台需要时间同步）
      Future.delayed(const Duration(milliseconds: 500)).then((_) {
        _notifyConversationListUpdate();
      });

      return result;
    } catch (e) {
      debugPrint('❌ [ChatViewModel] Send message error: $e');
      rethrow;
    }
  }

  // 获取会话列表
  Future<List<V2TimConversation>> getConversationList() async {
    if (!_isInitialized || _imAdapter == null) {
      debugPrint(
          '❌ [ChatViewModel] Cannot get conversations - IM not initialized');
      return [];
    }

    try {
      final conversations = await _imAdapter!.getConversationList();
      debugPrint('✅ [ChatViewModel] Got ${conversations.length} conversations');
      return conversations;
    } catch (e) {
      debugPrint('❌ [ChatViewModel] Get conversations error: $e');
      return [];
    }
  }

  /// 设置消息监听器
  Future<void> _setupMessageListeners() async {
    if (_imAdapter == null) {
      debugPrint(
          '❌ [ChatViewModel] Cannot setup listeners - adapter not initialized');
      return;
    }

    try {
      final success = await _imAdapter!.setupMessageListeners(
        onMessageReceived: (V2TimMessage message) {
          debugPrint('📨 [ChatViewModel] ▶️ RECEIVED MESSAGE IN VIEWMODEL ◀️');
          debugPrint('📨 [ChatViewModel] Message from: ${message.sender}');
          debugPrint(
              '📨 [ChatViewModel] Message content: ${message.textElem?.text ?? '[non-text]'}');
          debugPrint('📨 [ChatViewModel] Message ID: ${message.msgID}');
          debugPrint(
              '📨 [ChatViewModel] Message timestamp: ${message.timestamp}');
          debugPrint(
              '📨 [ChatViewModel] Available listeners: ${_messageListeners.length}');

          // 通知所有消息监听器
          int notifiedCount = 0;
          for (final listener in _messageListeners) {
            try {
              listener(message);
              notifiedCount++;
              debugPrint('✅ [ChatViewModel] Notified listener $notifiedCount');
            } catch (e) {
              debugPrint(
                  '❌ [ChatViewModel] Error notifying listener $notifiedCount: $e');
            }
          }

          debugPrint(
              '📨 [ChatViewModel] Total listeners notified: $notifiedCount');

          // 通知UI更新
          notifyListeners();
        },
        onMessageRevoked: (String messageID) {
          debugPrint('🚫 [ChatViewModel] Message revoked: $messageID');
          notifyListeners();
        },
      );

      if (success) {
        debugPrint('✅ [ChatViewModel] Message listeners setup successfully');

        // 测试监听器是否正常工作
        Future.delayed(const Duration(seconds: 2)).then((_) {
          _testMessageListener();
        });
      } else {
        debugPrint(
            '⚠️ [ChatViewModel] Message listeners setup with limited functionality');
      }
    } catch (e) {
      debugPrint('❌ [ChatViewModel] Failed to setup message listeners: $e');
    }
  }

  /// 设置会话监听器
  Future<void> _setupConversationListeners() async {
    if (_imAdapter == null) {
      debugPrint(
          '❌ [ChatViewModel] Cannot setup conversation listeners - adapter not initialized');
      return;
    }

    try {
      final success = await _imAdapter!.setupConversationListeners(
        onConversationChanged: (List<V2TimConversation> conversations) {
          debugPrint(
              '📝 [ChatViewModel] Conversation list changed: ${conversations.length} conversations');

          // 计算并输出未读数统计
          int totalUnread = 0;
          for (final conv in conversations) {
            final unread = conv.unreadCount ?? 0;
            totalUnread += unread;
            if (unread > 0) {
              debugPrint(
                  '🔴 [ChatViewModel] ${conv.showName ?? conv.conversationID}: $unread unread');
            }
          }
          debugPrint('🔢 [ChatViewModel] Total unread messages: $totalUnread');

          // 通知所有会话监听器
          for (final listener in _conversationListeners) {
            listener(conversations);
          }

          // 立即通知UI更新
          notifyListeners();
        },
        onTotalUnreadCountChanged: (int totalUnreadCount) {
          debugPrint(
              '🔢 [ChatViewModel] Total unread count changed: $totalUnreadCount');
          // 立即通知UI更新
          notifyListeners();
        },
      );

      if (success) {
        debugPrint(
            '✅ [ChatViewModel] Conversation listeners setup successfully');
      } else {
        debugPrint(
            '⚠️ [ChatViewModel] Conversation listeners setup with limited functionality');
      }
    } catch (e) {
      debugPrint(
          '❌ [ChatViewModel] Failed to setup conversation listeners: $e');
    }
  }

  /// 添加消息监听器
  void addMessageListener(Function(V2TimMessage) listener) {
    _messageListeners.add(listener);
    debugPrint(
        '📝 [ChatViewModel] Added message listener. Total listeners: ${_messageListeners.length}');
  }

  /// 移除消息监听器
  void removeMessageListener(Function(V2TimMessage) listener) {
    final removed = _messageListeners.remove(listener);
    debugPrint(
        '📝 [ChatViewModel] Removed message listener. Success: $removed, Remaining: ${_messageListeners.length}');
  }

  /// 添加会话监听器
  void addConversationListener(Function(List<V2TimConversation>) listener) {
    _conversationListeners.add(listener);
  }

  /// 移除会话监听器
  void removeConversationListener(Function(List<V2TimConversation>) listener) {
    _conversationListeners.remove(listener);
  }

  /// 通知会话列表更新
  Future<void> _notifyConversationListUpdate() async {
    try {
      final conversations = await getConversationList();
      for (final listener in _conversationListeners) {
        listener(conversations);
      }
    } catch (e) {
      debugPrint(
          '❌ [ChatViewModel] Failed to notify conversation list update: $e');
    }
  }

  /// 获取历史消息
  Future<List<V2TimMessage>> getHistoryMessages({
    required String userID,
    String? lastMsgID,
    int count = 20,
  }) async {
    if (!_isInitialized || _imAdapter == null) {
      debugPrint('❌ [ChatViewModel] Cannot get history - IM not initialized');
      return [];
    }

    try {
      final messages = await _imAdapter!.getHistoryMessages(
        userID: userID,
        count: count,
        lastMsgID: lastMsgID,
      );

      debugPrint('✅ [ChatViewModel] Got ${messages.length} history messages');
      return messages;
    } catch (e) {
      debugPrint('❌ [ChatViewModel] Get history messages error: $e');
      return [];
    }
  }

  /// 标记会话已读
  Future<void> markConversationAsRead(String conversationID) async {
    if (!_isInitialized || _imAdapter == null) {
      return;
    }

    try {
      debugPrint(
          '📝 [ChatViewModel] Marking conversation as read: $conversationID');
      final success = await _imAdapter!.markConversationAsRead(conversationID);

      if (success) {
        debugPrint(
            '✅ [ChatViewModel] Marked conversation as read: $conversationID');

        // 立即通知会话列表更新，以更新未读数
        // 使用多个时间点确保更新生效
        Future.delayed(const Duration(milliseconds: 50)).then((_) async {
          await _notifyConversationListUpdate();
        });

        Future.delayed(const Duration(milliseconds: 200)).then((_) async {
          await _notifyConversationListUpdate();
        });

        Future.delayed(const Duration(milliseconds: 500)).then((_) async {
          await _notifyConversationListUpdate();
        });

        // 立即触发UI更新
        notifyListeners();
      } else {
        debugPrint(
            '⚠️ [ChatViewModel] Mark as read not fully supported on this platform');
      }
    } catch (e) {
      debugPrint('❌ [ChatViewModel] Mark as read error: $e');
    }
  }

  /// 搜索用户
  Future<List<V2TimUserFullInfo>> searchUsers(String keyword) async {
    if (!_isInitialized || _imAdapter == null) {
      debugPrint('❌ [ChatViewModel] Cannot search users - IM not initialized');
      return [];
    }

    try {
      final users = await _imAdapter!.searchUsers(keyword);
      debugPrint('✅ [ChatViewModel] Found ${users.length} users');
      return users;
    } catch (e) {
      debugPrint('❌ [ChatViewModel] Search users error: $e');
      return [];
    }
  }

  /// 发送图片消息
  Future<void> sendImageMessage(String targetUserId, XFile imageFile) async {
    if (!_isInitialized || _imAdapter == null) {
      throw Exception('IM未初始化');
    }

    try {
      debugPrint(
          '📷 [ChatViewModel] Starting to send image message to $targetUserId');

      // 1. 上传图片到OSS
      final imageUrl = await _uploadImageToOSS(imageFile);
      debugPrint('☁️ [ChatViewModel] Image uploaded to OSS: $imageUrl');

      // 2. 使用适配器发送图片消息
      final result = await _imAdapter!.sendImageMessage(
        imageUrl: imageUrl,
        userID: targetUserId,
      );

      if (result.code != 0) {
        throw Exception('图片消息发送失败: ${result.desc}');
      }

      debugPrint(
          '✅ [ChatViewModel] Image message sent successfully to $targetUserId');
    } catch (e) {
      debugPrint('❌ [ChatViewModel] Send image message error: $e');
      rethrow;
    }
  }

  /// 上传图片到阿里云OSS
  Future<String> _uploadImageToOSS(XFile imageFile) async {
    try {
      // 生成唯一的文件名
      final fileName =
          'chat_images/${DateTime.now().millisecondsSinceEpoch}_${imageFile.name}';

      // 读取图片数据 (for future OSS upload)
      // final imageBytes = await imageFile.readAsBytes();

      // 这里需要配置OSS参数，具体配置需要从项目设置中获取
      // 暂时返回模拟URL，实际项目中需要实现OSS上传
      // final client = OSSClient(endpoint: '', accessKeyId: '', accessKeySecret: '');
      // final result = await client.putObject(bucket: '', key: fileName, data: imageBytes);

      // 模拟返回OSS URL
      final mockUrl =
          'https://example-bucket.oss-cn-hangzhou.aliyuncs.com/$fileName';
      debugPrint('🔧 [ChatViewModel] Mock upload - would upload to: $mockUrl');

      // 模拟延迟
      await Future.delayed(const Duration(seconds: 1));

      return mockUrl;
    } catch (e) {
      debugPrint('❌ [ChatViewModel] Upload image to OSS error: $e');
      throw Exception('图片上传失败: $e');
    }
  }

  /// 选择并发送图片
  Future<void> pickAndSendImage(String targetUserId) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.gallery);

      if (image != null) {
        await sendImageMessage(targetUserId, image);
      }
    } catch (e) {
      debugPrint('❌ [ChatViewModel] Pick and send image error: $e');
      rethrow;
    }
  }

  /// 拍照并发送图片
  Future<void> captureAndSendImage(String targetUserId) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.camera);

      if (image != null) {
        await sendImageMessage(targetUserId, image);
      }
    } catch (e) {
      debugPrint('❌ [ChatViewModel] Capture and send image error: $e');
      rethrow;
    }
  }

  /// 测试消息监听器是否正常工作
  void _testMessageListener() {
    debugPrint(
        '🧪 [ChatViewModel] Testing message listener with ${_messageListeners.length} listeners');

    if (_messageListeners.isNotEmpty) {
      // 创建一个测试消息
      final testMessage = V2TimMessage.fromJson({
        'msgID': 'test_${DateTime.now().millisecondsSinceEpoch}',
        'sender': 'test_sender',
        'nickName': 'Test User',
        'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'elemType': 1,
        'textElem': {'text': '这是一条测试消息'},
        'status': 1,
        'isPeerRead': false,
      });

      debugPrint(
          '🧪 [ChatViewModel] Sending test message to ${_messageListeners.length} listeners');

      for (int i = 0; i < _messageListeners.length; i++) {
        try {
          _messageListeners[i](testMessage);
          debugPrint('✅ [ChatViewModel] Test message sent to listener $i');
        } catch (e) {
          debugPrint(
              '❌ [ChatViewModel] Error sending test message to listener $i: $e');
        }
      }
    } else {
      debugPrint(
          '⚠️ [ChatViewModel] No message listeners available for testing');
    }
  }

  @override
  void dispose() {
    _messageListeners.clear();
    _conversationListeners.clear();
    super.dispose();
  }
}
