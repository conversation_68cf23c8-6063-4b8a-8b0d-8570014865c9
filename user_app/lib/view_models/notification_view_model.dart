import 'package:flutter/foundation.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/models/notification/notification_vo.dart';
import 'package:user_app/api/notification_api.dart';

class NotificationViewModel extends BaseViewModel {
  final NotificationApi _notificationApi;

  NotificationViewModel({required NotificationApi notificationApi})
      : _notificationApi = notificationApi;

  // 通知列表
  List<NotificationVo> _notifications = [];
  List<NotificationVo> get notifications => _notifications;

  // 分页信息
  int _currentPage = 1;
  bool _hasMore = true;
  bool get hasMore => _hasMore;

  // 加载状态
  bool _isLoadingMore = false;
  bool get isLoadingMore => _isLoadingMore;

  // 未读通知数量
  int _unreadCount = 0;
  int get unreadCount => _unreadCount;

  // 错误状态
  String? _errorMessage;
  String? get errorMessage => _errorMessage;
  bool get hasError => _errorMessage != null;

  // 筛选类型
  String _filterType = 'all'; // all, like, comment, follow, system
  String get filterType => _filterType;

  // 清除错误状态
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // 加载通知列表
  Future<void> loadNotifications({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _notifications.clear();
    }

    if (!_hasMore) return;

    try {
      setBusy(refresh);
      _isLoadingMore = !refresh;
      _errorMessage = null; // 清除之前的错误
      notifyListeners();

      final response = await _notificationApi.getNotifications(
        page: _currentPage,
        pageSize: 20,
        type: _filterType == 'all' ? null : _filterType,
      );

      if (response.isSuccess && response.data != null) {
        final newNotifications = response.data!;

        if (refresh) {
          _notifications = newNotifications;
        } else {
          _notifications.addAll(newNotifications);
        }

        _hasMore = newNotifications.length >= 20;
        _currentPage++;
        _errorMessage = null; // 成功后清除错误
      } else {
        _errorMessage = response.message.isNotEmpty ? response.message : '加载通知失败';
        debugPrint('加载通知失败: ${response.message}');
      }
    } catch (e) {
      _errorMessage = '网络连接失败，请检查网络后重试';
      debugPrint('加载通知失败: $e');
    } finally {
      setBusy(false);
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  // 加载未读通知数量
  Future<void> loadUnreadCount() async {
    try {
      final response = await _notificationApi.getUnreadCount();

      if (response.isSuccess && response.data != null) {
        _unreadCount = response.data!;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('加载未读通知数量失败: $e');
    }
  }

  // 标记通知为已读
  Future<void> markAsRead(int notificationId) async {
    try {
      await _notificationApi.markAsRead(notificationId);

      // 更新本地状态
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1 && !_notifications[index].isRead) {
        _notifications[index] = _notifications[index].copyWith(isRead: true);
        _unreadCount = (_unreadCount - 1).clamp(0, double.infinity).toInt();
        notifyListeners();
      }
    } catch (e) {
      debugPrint('标记已读失败: $e');
    }
  }

  // 标记所有通知为已读
  Future<void> markAllAsRead() async {
    try {
      await _notificationApi.markAllAsRead();

      // 更新本地状态
      for (int i = 0; i < _notifications.length; i++) {
        if (!_notifications[i].isRead) {
          _notifications[i] = _notifications[i].copyWith(isRead: true);
        }
      }
      _unreadCount = 0;
      notifyListeners();
    } catch (e) {
      debugPrint('标记全部已读失败: $e');
    }
  }

  // 删除通知
  Future<void> deleteNotification(int notificationId) async {
    try {
      await _notificationApi.deleteNotification(notificationId);

      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        final notification = _notifications[index];
        _notifications.removeAt(index);

        // 如果删除的是未读通知，减少未读数量
        if (!notification.isRead) {
          _unreadCount = (_unreadCount - 1).clamp(0, double.infinity).toInt();
        }

        notifyListeners();
      }
    } catch (e) {
      debugPrint('删除通知失败: $e');
    }
  }

  // 设置筛选类型
  void setFilterType(String type) {
    if (_filterType != type) {
      _filterType = type;
      loadNotifications(refresh: true);
    }
  }

  // 清空所有通知
  Future<void> clearAllNotifications() async {
    try {
      await _notificationApi.clearAllNotifications();
      _notifications.clear();
      _unreadCount = 0;
      notifyListeners();
    } catch (e) {
      debugPrint('清空通知失败: $e');
    }
  }

  // 刷新通知
  Future<void> refresh() async {
    await Future.wait([
      loadNotifications(refresh: true),
      loadUnreadCount(),
    ]);
  }
}
