// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:go_router/go_router.dart';
// import 'package:provider/provider.dart';
// import 'package:user_app/config/app_routes.dart';
// import 'package:user_app/core/di/injection.dart';
// import 'package:user_app/view_models/preview_image_view_model.dart';
// import 'package:user_app/view_models/publish_moment_view_model.dart';
//
// class CreateMomentPage extends StatefulWidget {
//   const CreateMomentPage({super.key});
//
//   @override
//   State<CreateMomentPage> createState() => _CreateMomentPageState();
// }
//
// class _CreateMomentPageState extends State<CreateMomentPage> {
//   @override
//   void initState() {
//     super.initState();
//     final viewModel = getIt<PublishMomentViewModel>();
//     viewModel.initOssService();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: ListView(
//           children: [
//             _BuildInspirationCard(),
//             const SizedBox(height: 16),
//             _BuildFishingHarvestCard(),
//             const SizedBox(height: 16),
//             _BuildFishWeightCard(),
//             const SizedBox(height: 16),
//             _BuildLocationCard(),
//             const SizedBox(height: 32),
//             const _BuildPublishButton(),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
// class _BuildInspirationCard extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const ListTile(
//               leading: Icon(Icons.tips_and_updates),
//               title: Text('灵感'),
//               subtitle: Text('分享你的钓鱼故事和心得'),
//             ),
//             TextField(
//               controller:
//                   context.read<PublishMomentViewModel>().contentController,
//               maxLines: 3,
//               decoration: const InputDecoration(
//                 border: OutlineInputBorder(),
//                 hintText: '你此刻的想法',
//                 prefixIcon: Icon(Icons.edit),
//               ),
//             ),
//             const SizedBox(height: 16),
//             _BuildImageGrid(),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
// class _BuildFishingHarvestCard extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const ListTile(
//               leading: Icon(Icons.phishing),
//               title: Text('渔获'),
//               subtitle: Text('成功地钓到一条鱼可能需要技巧、耐心和专注'),
//             ),
//             _BuildFishNameInput(),
//             const SizedBox(height: 16),
//             _BuildFishNameTags(),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
// class _BuildFishWeightCard extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const ListTile(
//               leading: Icon(Icons.line_weight),
//               title: Text('重量'),
//               subtitle: Text('记录鱼的重量，让你的钓鱼之旅更加有趣'),
//             ),
//             _BuildFishWeightInput(),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
// class _BuildFishNameInput extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       children: [
//         Expanded(
//           child: TextField(
//             controller:
//                 context.read<PublishMomentViewModel>().fishNameController,
//             decoration: const InputDecoration(
//               border: OutlineInputBorder(),
//               labelText: '鱼类',
//               prefixIcon: Icon(Icons.category),
//             ),
//           ),
//         ),
//         const SizedBox(width: 16.0),
//         ElevatedButton(
//           onPressed: () =>
//               context.read<PublishMomentViewModel>().addFishNameTag(context),
//           child: const Text('添加'),
//         ),
//       ],
//     );
//   }
// }
//
// class _BuildFishNameTags extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return ValueListenableBuilder(
//       valueListenable:
//           context.read<PublishMomentViewModel>().fishNameTagsNotifier,
//       builder: (context, value, child) {
//         return Wrap(
//           alignment: WrapAlignment.start,
//           runSpacing: 8,
//           spacing: 8,
//           children: context
//               .read<PublishMomentViewModel>()
//               .fishNameTagsNotifier
//               .value
//               .map((tag) {
//             return Chip(
//               label: Text(tag),
//               onDeleted: () {
//                 context
//                     .read<PublishMomentViewModel>()
//                     .fishNameTagsNotifier
//                     .value
//                     .remove(tag);
//                 context
//                         .read<PublishMomentViewModel>()
//                         .fishNameTagsNotifier
//                         .value =
//                     List.from(context
//                         .read<PublishMomentViewModel>()
//                         .fishNameTagsNotifier
//                         .value);
//               },
//             );
//           }).toList(),
//         );
//       },
//     );
//   }
// }
//
// class _BuildImageGrid extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return ValueListenableBuilder<List<String>>(
//       valueListenable: context.read<PublishMomentViewModel>().imageUrls,
//       builder: (context, imageUrls, child) {
//         final itemCount = imageUrls.length + 1 <
//                 context.read<PublishMomentViewModel>().maxImageCount
//             ? imageUrls.length + 1
//             : context.read<PublishMomentViewModel>().maxImageCount;
//
//         return GridView.builder(
//           shrinkWrap: true,
//           physics: const NeverScrollableScrollPhysics(),
//           gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
//             crossAxisCount: 3,
//             crossAxisSpacing: 8,
//             mainAxisSpacing: 8,
//             childAspectRatio: 1,
//           ),
//           itemCount: itemCount,
//           itemBuilder: (context, index) {
//             if (index == imageUrls.length) {
//               // This is the last item, used for adding a new image
//               return _BuildAddImageButton();
//             } else {
//               return _BuildImageItem(imageUrl: imageUrls[index], index: index);
//             }
//           },
//         );
//       },
//     );
//   }
// }
//
// class _BuildImageItem extends StatelessWidget {
//   final String imageUrl;
//   final int index;
//
//   const _BuildImageItem({
//     required this.imageUrl,
//     required this.index,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     List<String> imageUrls =
//         context.read<PublishMomentViewModel>().imageUrls.value;
//     return AspectRatio(
//       aspectRatio: 1,
//       child: Stack(
//         fit: StackFit.expand,
//         children: [
//           InkWell(
//             onTap: () => {
//               context.read<PreviewImageViewModel>().openPictureViewer(
//                     context,
//                     imageUrls,
//                     index,
//                   ),
//               context.push(AppRoutes.previewPictures),
//             },
//             child: ClipRRect(
//               borderRadius: BorderRadius.circular(8),
//               child: Image.network(
//                 imageUrl,
//                 fit: BoxFit.cover,
//               ),
//             ),
//           ),
//           Positioned(
//             top: 4,
//             right: 4,
//             child: InkWell(
//               onTap: () =>
//                   context.read<PublishMomentViewModel>().removeImage(index),
//               child: Container(
//                 padding: const EdgeInsets.all(2),
//                 decoration: const BoxDecoration(
//                   color: Colors.white,
//                   shape: BoxShape.circle,
//                 ),
//                 child: const Icon(Icons.close, size: 18),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
//
// class _BuildAddImageButton extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: () async {
//         await context.read<PublishMomentViewModel>().pickImages(context);
//       },
//       child: FloatingActionButton(
//         onPressed: () async =>
//             await context.read<PublishMomentViewModel>().pickImages(context),
//         child: const Icon(Icons.add_a_photo),
//       ),
//     );
//   }
// }
//
// class _BuildFishWeightInput extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Consumer<PublishMomentViewModel>(
//       builder: (context, viewModel, child) {
//         return Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             _BuildCircleButton(
//               icon: Icons.remove,
//               onPressed: () => viewModel.decrementWeight(),
//             ),
//             const SizedBox(width: 10),
//             Column(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 SizedBox(
//                   width: 100,
//                   child: TextField(
//                     controller: viewModel.fishWeightController,
//                     textAlign: TextAlign.center,
//                     keyboardType:
//                         const TextInputType.numberWithOptions(decimal: true),
//                     inputFormatters: [
//                       FilteringTextInputFormatter.allow(
//                           RegExp(r'^\d{0,3}\.?\d{0,1}')),
//                     ],
//                     style: const TextStyle(
//                         fontSize: 48, fontWeight: FontWeight.bold),
//                     decoration: const InputDecoration(
//                       border: InputBorder.none,
//                       contentPadding: EdgeInsets.zero,
//                     ),
//                   ),
//                 ),
//                 const Text('公斤', style: TextStyle(fontSize: 24)),
//               ],
//             ),
//             const SizedBox(width: 10),
//             _BuildCircleButton(
//               icon: Icons.add,
//               onPressed: () =>
//                   context.read<PublishMomentViewModel>().incrementWeight(),
//             ),
//           ],
//         );
//       },
//     );
//   }
// }
//
// class _BuildCircleButton extends StatelessWidget {
//   final IconData icon;
//   final VoidCallback onPressed;
//
//   const _BuildCircleButton({
//     required this.icon,
//     required this.onPressed,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return ElevatedButton(
//       onPressed: onPressed,
//       style: ElevatedButton.styleFrom(
//         shape: const CircleBorder(),
//         padding: const EdgeInsets.all(25),
//       ),
//       child: Icon(icon),
//     );
//   }
// }
//
// class _BuildLocationCard extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     return Card(
//       child: Padding(
//         padding: const EdgeInsets.all(16.0),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             ListTile(
//               leading: const Icon(Icons.location_on),
//               title: const Text('位置'),
//               subtitle: const Text('记录本次钓鱼的位置不仅可与朋友分享，还有助于提升下次钓鱼的效果'),
//             ),
//             Row(
//               children: [
//                 Expanded(
//                   child: TextField(
//                     controller: context
//                         .read<PublishMomentViewModel>()
//                         .locationController,
//                     maxLines: 3,
//                     decoration: const InputDecoration(
//                       border: OutlineInputBorder(),
//                       hintText: '输入位置信息',
//                     ),
//                   ),
//                 ),
//                 const SizedBox(width: 8),
//                 Column(
//                   children: [
//                     IconButton(
//                       onPressed: () {
//                         context.push(AppRoutes.takeAddress);
//                       },
//                       icon: const Icon(Icons.map),
//                       tooltip: '在地图上选择位置',
//                     ),
//                     const Text('地图选点'),
//                   ],
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
// class _BuildPublishButton extends StatelessWidget {
//   const _BuildPublishButton();
//
//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       children: [
//         Expanded(
//           child: FractionallySizedBox(
//             widthFactor: 2 / 3,
//             child: FilledButton.icon(
//               onPressed: () =>
//                   context.read<PublishMomentViewModel>().publishMoment(context),
//               icon: const Icon(Icons.publish),
//               label: const Text('发布'),
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
