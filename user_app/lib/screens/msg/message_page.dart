import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/view_models/chat_view_model.dart';
import 'package:user_app/features/chat/screens/chat_list_page.dart';

class MessagePage extends StatefulWidget {
  const MessagePage({super.key});

  @override
  State<StatefulWidget> createState() => _MessagePageState();
}

class _MessagePageState extends State<MessagePage> {
  @override
  Widget build(BuildContext context) {
    // 直接显示聊天列表页面，保持在 Shell 路由中
    return const ChatListPage();
  }
}

class ConversionList extends StatelessWidget {
  const ConversionList({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatViewModel>(
      builder: (context, chatViewModel, child) {
        return const Center(
          child: Text('暂无消息'),
        );
        // return TIMUIKitConversation(
        //   onTapItem: (V2TimConversation conversation) {
        //     Navigator.push(
        //       context,
        //       MaterialPageRoute(
        //         builder: (context) => Chat(
        //           selectedConversation: conversation,
        //         ),
        //       ),
        //     );
        //   },
        //   emptyBuilder: () {
        //     return const Center(
        //       child: Text('暂无消息'),
        //     );
        //   },
        // );
      },
    );
  }
}
