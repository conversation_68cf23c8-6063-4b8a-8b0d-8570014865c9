// import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
// import 'package:user_app/core/di/injection.dart';
// import 'package:user_app/screens/publish_moment_page.dart';
// import 'package:user_app/view_models/auth_view_model.dart';
// import 'package:user_app/view_models/publish_moment_view_model.dart';
// import 'package:user_app/widgets/logged_out_user_widget.dart';
//
// class PublishPage extends StatelessWidget {
//   const PublishPage({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     return Selector<AuthViewModel, bool>(
//       selector: (context, auth) => auth.isUserLoggedIn(),
//       builder: (context, isLoggedIn, child) {
//         if (isLoggedIn) {
//           return ChangeNotifierProvider(
//             create: (context) => getIt<PublishMomentViewModel>(),
//             child: const CreateMomentPage(),
//           );
//         } else {
//           return const LoggedOutUserWidget();
//         }
//       },
//     );
//   }
// }
