import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/view_models/bookmark_view_model.dart';
import 'package:user_app/widgets/auth_required_widget.dart';

class MyBookmarksPage extends StatefulWidget {
  const MyBookmarksPage({super.key});

  @override
  State<MyBookmarksPage> createState() => _MyBookmarksPageState();
}

class _MyBookmarksPageState extends State<MyBookmarksPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late BookmarkViewModel _viewModel;
  final ScrollController _momentsScrollController = ScrollController();
  final ScrollController _spotsScrollController = ScrollController();

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  // 列表项动画控制器
  final List<AnimationController> _itemAnimationControllers = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _viewModel = context.read<BookmarkViewModel>();

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );

    // 开始动画
    _fadeController.forward();
    _scaleController.forward();

    _loadBookmarks();
    _momentsScrollController.addListener(() => _onScroll('moment'));
    _spotsScrollController.addListener(() => _onScroll('spot'));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _momentsScrollController.dispose();
    _spotsScrollController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    for (var controller in _itemAnimationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadBookmarks() {
    _viewModel.loadBookmarkedMoments(refresh: true);
    _viewModel.loadBookmarkedSpots(refresh: true);
  }

  void _onScroll(String type) {
    final controller =
        type == 'moment' ? _momentsScrollController : _spotsScrollController;
    if (controller.position.pixels >=
        controller.position.maxScrollExtent - 200) {
      if (type == 'moment') {
        _viewModel.loadBookmarkedMoments();
      } else {
        _viewModel.loadBookmarkedSpots();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AuthRequiredWidget(
      title: '我的收藏',
      child: Scaffold(
        backgroundColor: const Color(0xFFF8FAFC),
        body: CustomScrollView(
          slivers: [
            // 现代化的 AppBar
            SliverAppBar(
              expandedHeight: 120,
              floating: false,
              pinned: true,
              backgroundColor: Colors.transparent,
              elevation: 0,
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFF667EEA),
                        Color(0xFF764BA2),
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 50),
                      child: Center(
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: const Text(
                            '我的收藏',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(50),
                child: Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        offset: Offset(0, -5),
                      ),
                    ],
                  ),
                  child: TabBar(
                    controller: _tabController,
                    labelColor: const Color(0xFF667EEA),
                    unselectedLabelColor: Colors.grey[600],
                    indicatorColor: const Color(0xFF667EEA),
                    indicatorWeight: 3,
                    indicatorPadding:
                        const EdgeInsets.symmetric(horizontal: 20),
                    tabs: [
                      _buildModernTab('动态', 'moments'),
                      _buildModernTab('钓点', 'spots'),
                    ],
                  ),
                ),
              ),
            ),

            // 内容区域
            SliverFillRemaining(
              child: Container(
                color: Colors.white,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildMomentsTab(),
                    _buildSpotsTab(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernTab(String label, String type) {
    return Tab(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(label,
              style:
                  const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          const SizedBox(width: 8),
          Consumer<BookmarkViewModel>(
            builder: (context, viewModel, child) {
              final count = type == 'moments'
                  ? viewModel.bookmarkedMoments.length
                  : viewModel.bookmarkedSpots.length;

              if (count > 0) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    count > 99 ? '99+' : count.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMomentsTab() {
    return Consumer<BookmarkViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.isLoadingMoments && viewModel.bookmarkedMoments.isEmpty) {
          return _buildLoadingState();
        }

        if (viewModel.bookmarkedMoments.isEmpty) {
          return _buildEmptyState('动态');
        }

        return RefreshIndicator(
          onRefresh: () => viewModel.loadBookmarkedMoments(refresh: true),
          color: const Color(0xFF667EEA),
          child: ListView.builder(
            controller: _momentsScrollController,
            padding: const EdgeInsets.all(20),
            itemCount: viewModel.bookmarkedMoments.length +
                (viewModel.isLoadingMoreMoments ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == viewModel.bookmarkedMoments.length) {
                return _buildLoadingMoreIndicator();
              }
              return _buildModernMomentCard(
                  viewModel.bookmarkedMoments[index], index);
            },
          ),
        );
      },
    );
  }

  Widget _buildSpotsTab() {
    return Consumer<BookmarkViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.isLoadingSpots && viewModel.bookmarkedSpots.isEmpty) {
          return _buildLoadingState();
        }

        if (viewModel.bookmarkedSpots.isEmpty) {
          return _buildEmptyState('钓点');
        }

        return RefreshIndicator(
          onRefresh: () => viewModel.loadBookmarkedSpots(refresh: true),
          color: const Color(0xFF667EEA),
          child: ListView.builder(
            controller: _spotsScrollController,
            padding: const EdgeInsets.all(20),
            itemCount: viewModel.bookmarkedSpots.length +
                (viewModel.isLoadingMoreSpots ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == viewModel.bookmarkedSpots.length) {
                return _buildLoadingMoreIndicator();
              }
              return _buildModernSpotCard(
                  viewModel.bookmarkedSpots[index], index);
            },
          ),
        );
      },
    );
  }

  Widget _buildModernMomentCard(MomentVo moment, int index) {
    // 创建动画控制器
    if (_itemAnimationControllers.length <= index) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 600 + (index * 50)),
        vsync: this,
      );
      _itemAnimationControllers.add(controller);
      controller.forward();
    }

    return AnimatedBuilder(
      animation: _itemAnimationControllers[index],
      builder: (context, child) {
        final slideAnimation = Tween<Offset>(
          begin: const Offset(0.3, 0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.easeOutBack,
        ));

        final scaleAnimation = Tween<double>(
          begin: 0.8,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.elasticOut,
        ));

        return SlideTransition(
          position: slideAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          if (moment.id != null) {
            context
                .push(AppRoutes.momentDetail, extra: {'momentId': moment.id});
          }
        },
        onLongPress: () {
          HapticFeedback.mediumImpact();
          _showMomentOptions(moment);
        },
        child: Container(
          margin: const EdgeInsets.only(bottom: 20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Color(0xFFFBFCFE),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 用户信息行
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: const LinearGradient(
                          colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.purple.withOpacity(0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.all(2),
                      child: Container(
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white,
                        ),
                        padding: const EdgeInsets.all(2),
                        child: ClipOval(
                          child: CachedNetworkImage(
                            imageUrl: moment.userAvatar ?? '',
                            width: 45,
                            height: 45,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: Colors.grey[200],
                              child:
                                  const Icon(Icons.person, color: Colors.grey),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: Colors.grey[200],
                              child:
                                  const Icon(Icons.person, color: Colors.grey),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            moment.userName ?? '未知用户',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1A1E25),
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            moment.createdAt ?? '',
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            const Color(0xFF667EEA).withOpacity(0.1),
                            const Color(0xFF764BA2).withOpacity(0.1),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Row(
                        children: [
                          Icon(Icons.bookmark,
                              size: 16, color: Color(0xFF667EEA)),
                          SizedBox(width: 4),
                          Text(
                            '已收藏',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF667EEA),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // 内容
                if (moment.content != null && moment.content!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    moment.content!,
                    style: const TextStyle(
                      fontSize: 15,
                      height: 1.5,
                      color: Color(0xFF1A1E25),
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                // 图片网格
                if (moment.images != null && moment.images!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  _buildModernImageGrid(moment.images!.map((img) => img.imageUrl).toList()),
                ],

                const SizedBox(height: 16),

                // 互动栏
                Row(
                  children: [
                    _buildModernActionItem(
                      icon: Icons.thumb_up,
                      count: moment.numberOfLikes,
                      isActive: moment.isLiked ?? false,
                    ),
                    const SizedBox(width: 24),
                    _buildModernActionItem(
                      icon: Icons.chat_bubble_outline,
                      count: moment.commentCount?.toInt() ?? 0,
                      isActive: false,
                    ),
                    const Spacer(),
                    Icon(
                      Icons.more_horiz,
                      size: 22,
                      color: Colors.grey[500],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernSpotCard(FishingSpotVo spot, int index) {
    // 创建动画控制器
    if (_itemAnimationControllers.length <= index) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 600 + (index * 50)),
        vsync: this,
      );
      _itemAnimationControllers.add(controller);
      controller.forward();
    }

    return AnimatedBuilder(
      animation: _itemAnimationControllers[index],
      builder: (context, child) {
        final slideAnimation = Tween<Offset>(
          begin: const Offset(0.3, 0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.easeOutBack,
        ));

        final scaleAnimation = Tween<double>(
          begin: 0.8,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.elasticOut,
        ));

        return SlideTransition(
          position: slideAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          if (spot.id != null) {
            context.push(AppRoutes.fishingSpotDetail, extra: {'spotId': spot.id});
          }
        },
        onLongPress: () {
          HapticFeedback.mediumImpact();
          _showSpotOptions(spot);
        },
        child: Container(
          margin: const EdgeInsets.only(bottom: 20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Color(0xFFFBFCFE),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            children: [
              // 图片区域
              if (spot.images != null && spot.images!.isNotEmpty)
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(25),
                        topRight: Radius.circular(25),
                      ),
                      child: CachedNetworkImage(
                        imageUrl: spot.images![0],
                        height: 180,
                        width: double.infinity,
                        fit: BoxFit.cover,
                      ),
                    ),
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                            ),
                          ],
                        ),
                        child: const Row(
                          children: [
                            Icon(Icons.bookmark,
                                size: 16, color: Color(0xFF667EEA)),
                            SizedBox(width: 4),
                            Text(
                              '已收藏',
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(0xFF667EEA),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),

              // 信息区域
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            spot.name ?? '未命名钓点',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1A1E25),
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                            ),
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.star,
                                  size: 14, color: Colors.white),
                              const SizedBox(width: 4),
                              Text(
                                '${spot.rating ?? 0}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // 位置信息
                    Row(
                      children: [
                        Icon(Icons.location_on,
                            size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            spot.address ?? '未知位置',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // 鱼种标签
                    if (spot.fishTypeList.isNotEmpty)
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: spot.fishTypeList.take(5).map((fishType) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  const Color(0xFF667EEA).withOpacity(0.1),
                                  const Color(0xFF764BA2).withOpacity(0.1),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              fishType.name,
                              style: const TextStyle(
                                fontSize: 12,
                                color: Color(0xFF667EEA),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernImageGrid(List<String> images) {
    if (images.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(15),
        child: CachedNetworkImage(
          imageUrl: images[0],
          height: 200,
          width: double.infinity,
          fit: BoxFit.cover,
        ),
      );
    }

    return Container(
      height: 200,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(15),
        child: GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
          ),
          itemCount: images.length > 9 ? 9 : images.length,
          itemBuilder: (context, index) {
            return CachedNetworkImage(
              imageUrl: images[index],
              fit: BoxFit.cover,
            );
          },
        ),
      ),
    );
  }

  Widget _buildModernActionItem({
    required IconData icon,
    required int count,
    required bool isActive,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: isActive
                ? const Color(0xFF667EEA).withOpacity(0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: isActive ? const Color(0xFF667EEA) : Colors.grey[600],
          ),
        ),
        const SizedBox(width: 6),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 14,
            color: isActive ? const Color(0xFF667EEA) : Colors.grey[700],
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667EEA)),
      ),
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667EEA)),
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _buildEmptyState(String type) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF667EEA).withOpacity(0.1),
                  const Color(0xFF764BA2).withOpacity(0.1),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(
              type == '动态'
                  ? Icons.article_outlined
                  : Icons.location_on_outlined,
              size: 48,
              color: const Color(0xFF667EEA),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '还没有收藏的$type',
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF1A1E25),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '快去发现更多精彩内容吧',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  void _showMomentOptions(MomentVo moment) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.bookmark_remove, color: Colors.red),
              ),
              title: const Text(
                '取消收藏',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                if (moment.id != null) {
                  HapticFeedback.mediumImpact();
                  _viewModel.unbookmarkMoment(moment.id!);
                }
              },
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF667EEA).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.share, color: Color(0xFF667EEA)),
              ),
              title: const Text(
                '分享动态',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现分享功能
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSpotOptions(FishingSpotVo spot) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.bookmark_remove, color: Colors.red),
              ),
              title: const Text(
                '取消收藏',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                if (spot.id != null) {
                  HapticFeedback.mediumImpact();
                  _viewModel.unbookmarkSpot(spot.id!);
                }
              },
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF667EEA).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.share, color: Color(0xFF667EEA)),
              ),
              title: const Text(
                '分享钓点',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现分享功能
              },
            ),
          ],
        ),
      ),
    );
  }
}
