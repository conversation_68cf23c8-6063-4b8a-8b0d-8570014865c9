import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/widgets/logged_in_user_widget.dart';
import 'package:user_app/widgets/logged_out_user_widget.dart';

class MinePage extends StatelessWidget {
  const MinePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Selector<AuthViewModel, bool>(
      selector: (context, auth) => auth.isUserLoggedIn(),
      builder: (context, isLoggedIn, child) {
        if (isLoggedIn) {
          return const LoggedInUserWidget();
        } else {
          return const LoggedOutUserWidget();
        }
      },
    );
  }
}
