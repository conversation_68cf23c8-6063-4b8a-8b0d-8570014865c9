import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/widgets/auth_required_widget.dart';

class MyQrCodePage extends StatefulWidget {
  const MyQrCodePage({super.key});

  @override
  State<MyQrCodePage> createState() => _MyQrCodePageState();
}

class _MyQrCodePageState extends State<MyQrCodePage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  final GlobalKey _repaintKey = GlobalKey();

  // 主题样式
  int _selectedTheme = 0;
  final List<QrTheme> _themes = [
    QrTheme(
      name: '经典蓝',
      backgroundColor: Colors.white,
      foregroundColor: Colors.blue[600]!,
      accentColor: Colors.blue[100]!,
    ),
    QrTheme(
      name: '暗夜黑',
      backgroundColor: const Color(0xFF1A1A1A),
      foregroundColor: Colors.white,
      accentColor: Colors.grey[800]!,
    ),
    QrTheme(
      name: '清新绿',
      backgroundColor: Colors.white,
      foregroundColor: Colors.green[600]!,
      accentColor: Colors.green[100]!,
    ),
    QrTheme(
      name: '温暖橙',
      backgroundColor: Colors.white,
      foregroundColor: Colors.orange[600]!,
      accentColor: Colors.orange[100]!,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AuthRequiredWidget(
      title: '我的二维码',
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('我的二维码'),
          elevation: 0,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black87,
          actions: [
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: _shareQrCode,
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // 二维码卡片
              FadeTransition(
                opacity: _fadeAnimation,
                child: RepaintBoundary(
                  key: _repaintKey,
                  child: _buildQrCard(),
                ),
              ),
              const SizedBox(height: 20),

              // 主题选择
              _buildThemeSelector(),
              const SizedBox(height: 20),

              // 操作按钮
              _buildActionButtons(),
              const SizedBox(height: 20),

              // 说明文字
              _buildInstructions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQrCard() {
    final theme = _themes[_selectedTheme];

    return Container(
      decoration: BoxDecoration(
        color: theme.backgroundColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // 用户信息
            Consumer<AuthViewModel>(
              builder: (context, authViewModel, child) {
                final user = authViewModel.currentUser;
                return Column(
                  children: [
                    // 头像
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: theme.foregroundColor.withOpacity(0.3),
                          width: 3,
                        ),
                      ),
                      child: CircleAvatar(
                        radius: 40,
                        backgroundImage: user?.avatarUrl != null &&
                                user!.avatarUrl!.isNotEmpty
                            ? CachedNetworkImageProvider(user.avatarUrl!)
                            : const AssetImage('assets/default_avatar.png')
                                as ImageProvider,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 昵称
                    Text(
                      user?.name ?? '钓友',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.foregroundColor,
                      ),
                    ),
                    const SizedBox(height: 4),

                    // 个性签名
                    Text(
                      user?.introduce?.isNotEmpty == true
                          ? user!.introduce!
                          : '扫一扫，加我为好友',
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.foregroundColor.withOpacity(0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 24),

            // 二维码
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.accentColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: QrImageView(
                data: _generateQrData(),
                version: QrVersions.auto,
                size: 200.0,
                backgroundColor: Colors.transparent,
                foregroundColor: theme.foregroundColor,
                errorStateBuilder: (cxt, err) {
                  return Container(
                    child: const Center(
                      child: Text(
                        '生成二维码失败',
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),

            // Logo和文字
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.phishing,
                  color: theme.foregroundColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '钓鱼之旅',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: theme.foregroundColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '选择样式',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _themes.length,
              itemBuilder: (context, index) {
                final theme = _themes[index];
                final isSelected = _selectedTheme == index;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedTheme = index;
                    });
                  },
                  child: Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: theme.backgroundColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? theme.foregroundColor
                            : Colors.grey[300]!,
                        width: isSelected ? 2 : 1,
                      ),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: theme.foregroundColor.withOpacity(0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 30,
                          height: 30,
                          decoration: BoxDecoration(
                            color: theme.foregroundColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          theme.name,
                          style: TextStyle(
                            fontSize: 12,
                            color: theme.foregroundColor,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: _buildActionButton(
            icon: Icons.save_alt,
            label: '保存图片',
            onTap: _saveQrCode,
            isPrimary: true,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildActionButton(
            icon: Icons.qr_code_scanner,
            label: '扫一扫',
            onTap: _scanQrCode,
            isPrimary: false,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required bool isPrimary,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isPrimary ? Colors.blue : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isPrimary ? Colors.blue : Colors.grey[300]!,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isPrimary ? Colors.white : Colors.grey[700],
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isPrimary ? Colors.white : Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '如何使用',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '1. 让对方扫描您的二维码即可添加好友\n'
                      '2. 您也可以保存图片分享到其他平台\n'
                      '3. 二维码包含您的用户ID，请勿泄露给陌生人',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontSize: 13,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _generateQrData() {
    final user = context.read<AuthViewModel>().currentUser;
    // 生成包含用户信息的二维码数据
    return 'fishingapp://user/${user?.id ?? 0}';
  }

  Future<void> _saveQrCode() async {
    try {
      // 检查权限
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        status = await Permission.storage.request();
        if (!status.isGranted) {
          _showSnackBar('需要存储权限才能保存图片', Colors.orange);
          return;
        }
      }

      // 获取渲染的图片
      RenderRepaintBoundary boundary = _repaintKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData != null) {
        Uint8List pngBytes = byteData.buffer.asUint8List();
        
        // 获取临时目录
        final directory = await getTemporaryDirectory();
        final imagePath = '${directory.path}/qr_code_${DateTime.now().millisecondsSinceEpoch}.png';
        final imageFile = File(imagePath);
        await imageFile.writeAsBytes(pngBytes);

        // 使用 share_plus 分享到相册
        await Share.shareXFiles(
          [XFile(imagePath)],
          text: '我的二维码',
        );

        _showSnackBar('二维码已准备好分享', Colors.green);
      }
    } catch (e) {
      _showSnackBar('保存失败: $e', Colors.red);
    }
  }

  Future<void> _shareQrCode() async {
    try {
      // 获取渲染的图片
      RenderRepaintBoundary boundary = _repaintKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData != null) {
        Uint8List pngBytes = byteData.buffer.asUint8List();
        
        // 获取临时目录
        final directory = await getTemporaryDirectory();
        final imagePath = '${directory.path}/qr_code_share_${DateTime.now().millisecondsSinceEpoch}.png';
        final imageFile = File(imagePath);
        await imageFile.writeAsBytes(pngBytes);

        final user = context.read<AuthViewModel>().currentUser;
        await Share.shareXFiles(
          [XFile(imagePath)],
          text: '扫描二维码添加我为好友 - ${user?.name ?? '钓友'}',
          subject: '钓鱼之旅 - 我的二维码',
        );
      }
    } catch (e) {
      _showSnackBar('分享失败: $e', Colors.red);
    }
  }

  void _scanQrCode() {
    // 显示扫码选项对话框
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '扫描二维码',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('打开相机扫码'),
              onTap: () {
                Navigator.pop(context);
                _showSnackBar('相机扫码功能开发中...', Colors.orange);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('从相册选择'),
              onTap: () {
                Navigator.pop(context);
                _showSnackBar('相册识别功能开发中...', Colors.orange);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSnackBar(String message, Color backgroundColor) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        margin: const EdgeInsets.all(10),
      ),
    );
  }
}

// 二维码主题数据模型
class QrTheme {
  final String name;
  final Color backgroundColor;
  final Color foregroundColor;
  final Color accentColor;

  QrTheme({
    required this.name,
    required this.backgroundColor,
    required this.foregroundColor,
    required this.accentColor,
  });
}
