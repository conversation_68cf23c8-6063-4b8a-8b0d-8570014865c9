import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class ProfileSettingPage extends StatefulWidget {
  const ProfileSettingPage({super.key});

  @override
  State<ProfileSettingPage> createState() => _ProfileSettingPageState();
}

class _ProfileSettingPageState extends State<ProfileSettingPage>
    with TickerProviderStateMixin {
  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;

  final List<AnimationController> _itemAnimationControllers = [];
  
  // 增强的状态管理
  final Map<String, bool> _processingStates = {}; // 正在处理的操作
  
  // 表单验证和提交逻辑
  final Map<String, bool> _validationStates = {}; // 验证状态
  // bool _hasUnsavedChanges = false; // 预留字段，用于未来的更改追踪
  
  // 缓存信息
  String _cacheSize = '计算中...';
  // bool _isCalculatingCache = true; // 预留字段，用于未来的缓存计算状态

  @override
  void initState() {
    super.initState();
    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // 开始动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();
    
    // 初始化数据
    _initializeData();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _pulseController.dispose();
    for (var controller in _itemAnimationControllers) {
      controller.dispose();
    }
    super.dispose();
  }
  
  // 初始化数据
  Future<void> _initializeData() async {
    await _calculateCacheSize();
    await _loadUserSettings();
  }
  
  // 计算缓存大小
  Future<void> _calculateCacheSize() async {
    setState(() {
      // _isCalculatingCache = true; // 暂时注释，因为字段被注释了
    });
    
    try {
      // 模拟计算缓存大小的过程
      await Future.delayed(const Duration(seconds: 1));
      
      // TODO: 实现真实的缓存大小计算
      final cacheSize = _generateRandomCacheSize();
      
      if (mounted) {
        setState(() {
          _cacheSize = cacheSize;
          // _isCalculatingCache = false; // 暂时注释，因为字段被注释了
        });
      }
    } catch (error) {
      debugPrint('Calculate cache size error: $error');
      if (mounted) {
        setState(() {
          _cacheSize = '计算失败';
          // _isCalculatingCache = false; // 暂时注释，因为字段被注释了
        });
      }
    }
  }
  
  // 生成随机缓存大小（模拟）
  String _generateRandomCacheSize() {
    final sizes = ['128MB', '256MB', '512MB', '1.2GB', '2.5GB'];
    sizes.shuffle();
    return sizes.first;
  }
  
  // 加载用户设置
  Future<void> _loadUserSettings() async {
    try {
      // TODO: 从本地存储或API加载用户设置
      await Future.delayed(const Duration(milliseconds: 500));
      
      if (mounted) {
        // 设置初始验证状态
        setState(() {
          _validationStates['profile'] = true;
          _validationStates['phone'] = true;
          _validationStates['password'] = true;
        });
      }
    } catch (error) {
      debugPrint('Load user settings error: $error');
      _showErrorSnackBar('加载设置失败');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: CustomScrollView(
        slivers: [
          // 现代化的 AppBar
          SliverAppBar(
            expandedHeight: 280,
            floating: false,
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                children: [
                  // 渐变背景
                  Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color(0xFF667EEA),
                          Color(0xFF764BA2),
                        ],
                      ),
                    ),
                  ),
                  // 装饰圆圈
                  Positioned(
                    top: -50,
                    left: -50,
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withOpacity(0.1),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: -30,
                    right: -30,
                    child: Container(
                      width: 150,
                      height: 150,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withOpacity(0.05),
                      ),
                    ),
                  ),
                  // 用户信息
                  SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Consumer<AuthViewModel>(
                        builder: (context, authViewModel, child) {
                          final user = authViewModel.currentUser;
                          return Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(height: 40),
                              // 头像
                              SlideTransition(
                                position: _slideAnimation,
                                child: ScaleTransition(
                                  scale: _scaleAnimation,
                                  child: GestureDetector(
                                    onTap: () {
                                      HapticFeedback.lightImpact();
                                      context.push(AppRoutes.editProfile);
                                    },
                                    child: Stack(
                                      children: [
                                        AnimatedBuilder(
                                          animation: _pulseAnimation,
                                          builder: (context, child) {
                                            return Transform.scale(
                                              scale: _pulseAnimation.value,
                                              child: Container(
                                                width: 100,
                                                height: 100,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  color: Colors.white
                                                      .withOpacity(0.2),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                        Container(
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: Colors.white,
                                              width: 3,
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black
                                                    .withOpacity(0.2),
                                                blurRadius: 15,
                                                offset: const Offset(0, 5),
                                              ),
                                            ],
                                          ),
                                          child: CircleAvatar(
                                            radius: 45,
                                            backgroundImage: user?.avatarUrl !=
                                                        null &&
                                                    user!.avatarUrl!.isNotEmpty
                                                ? CachedNetworkImageProvider(
                                                    user.avatarUrl!)
                                                : const AssetImage(
                                                        'assets/default_avatar.png')
                                                    as ImageProvider,
                                          ),
                                        ),
                                        Positioned(
                                          bottom: 0,
                                          right: 0,
                                          child: Container(
                                            padding: const EdgeInsets.all(6),
                                            decoration: BoxDecoration(
                                              gradient: const LinearGradient(
                                                colors: [
                                                  Colors.orange,
                                                  Colors.deepOrange
                                                ],
                                              ),
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                  color: Colors.white,
                                                  width: 2),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.orange
                                                      .withOpacity(0.5),
                                                  blurRadius: 10,
                                                  spreadRadius: 2,
                                                ),
                                              ],
                                            ),
                                            child: const Icon(
                                              Icons.edit,
                                              size: 16,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),
                              // 用户名
                              FadeTransition(
                                opacity: _fadeAnimation,
                                child: Text(
                                  user?.name ?? '钓友',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8),
                              // ID和二维码
                              FadeTransition(
                                opacity: _fadeAnimation,
                                child: GestureDetector(
                                  onTap: () {
                                    HapticFeedback.lightImpact();
                                    context.push(AppRoutes.myQrCode);
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 8),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: Colors.white.withOpacity(0.3),
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          'ID: ${user?.id ?? ''}',
                                          style: TextStyle(
                                            color:
                                                Colors.white.withOpacity(0.9),
                                            fontSize: 14,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Icon(
                                          Icons.qr_code,
                                          size: 18,
                                          color: Colors.white.withOpacity(0.9),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
            leading: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.arrow_back_ios, color: Colors.white),
              ),
              onPressed: () {
                HapticFeedback.lightImpact();
                Navigator.pop(context);
              },
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(30),
              child: Container(
                height: 30,
                decoration: const BoxDecoration(
                  color: Color(0xFFF8FAFC),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
              ),
            ),
          ),

          // 内容区域
          SliverPadding(
            padding: const EdgeInsets.all(20),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                // 账号与安全
                _buildModernSettingGroup(
                  title: '账号与安全',
                  icon: Icons.security,
                  items: [
                    _SettingItem(
                      icon: Icons.edit_outlined,
                      title: '编辑资料',
                      subtitle: '修改个人信息',
                      iconColor: const Color(0xFF667EEA),
                      onTap: () => context.push(AppRoutes.editProfile),
                    ),
                    _SettingItem(
                      icon: Icons.lock_outline,
                      title: '修改密码',
                      subtitle: '定期更换密码更安全',
                      iconColor: const Color(0xFF764BA2),
                      onTap: () => context.push(AppRoutes.changePassword),
                    ),
                    _SettingItem(
                      icon: Icons.phone_android_outlined,
                      title: '更换手机号',
                      subtitle: _getPhoneDisplay(),
                      iconColor: Colors.orange,
                      onTap: () => _handlePhoneChange(),
                    ),
                    _SettingItem(
                      icon: Icons.shield_outlined,
                      title: '账号安全',
                      subtitle: '管理登录设备和安全设置',
                      iconColor: Colors.green,
                      onTap: () => context.push(AppRoutes.accountSecurity),
                    ),
                    _SettingItem(
                      icon: Icons.history,
                      title: '登录历史',
                      subtitle: '查看登录记录',
                      iconColor: Colors.blue,
                      badge: '3',
                      onTap: () => context.push(AppRoutes.loginHistory),
                    ),
                  ],
                  index: 0,
                ),

                const SizedBox(height: 16),

                // 隐私设置
                _buildModernSettingGroup(
                  title: '隐私设置',
                  icon: Icons.privacy_tip,
                  items: [
                    _SettingItem(
                      icon: Icons.block_outlined,
                      title: '黑名单管理',
                      subtitle: '管理屏蔽的用户',
                      iconColor: Colors.red,
                      onTap: () => context.push(AppRoutes.blacklist),
                    ),
                    _SettingItem(
                      icon: Icons.visibility_outlined,
                      title: '隐私设置',
                      subtitle: '谁可以看我的动态',
                      iconColor: Colors.purple,
                      onTap: () => context.push(AppRoutes.privacySettings),
                    ),
                  ],
                  index: 1,
                ),

                const SizedBox(height: 16),

                // 通用设置
                _buildModernSettingGroup(
                  title: '通用设置',
                  icon: Icons.settings,
                  items: [
                    _SettingItem(
                      icon: Icons.notifications_outlined,
                      title: '消息通知',
                      subtitle: '管理推送通知',
                      iconColor: Colors.teal,
                      switchValue: true,
                      onTap: () => context.push(AppRoutes.notificationSettings),
                    ),
                    _SettingItem(
                      icon: Icons.language_outlined,
                      title: '语言设置',
                      subtitle: '简体中文',
                      iconColor: Colors.indigo,
                      onTap: () => _showLanguageDialog(context),
                    ),
                    _SettingItem(
                      icon: Icons.cleaning_services_outlined,
                      title: '清理缓存',
                      subtitle: '当前缓存：$_cacheSize',
                      iconColor: Colors.amber,
                      onTap: () => _clearCacheEnhanced(context),
                      isLoading: _processingStates['cache'] ?? false,
                    ),
                  ],
                  index: 2,
                ),

                const SizedBox(height: 16),

                // 关于
                _buildModernSettingGroup(
                  title: '关于',
                  icon: Icons.info,
                  items: [
                    _SettingItem(
                      icon: Icons.star_outline,
                      title: '给我们评分',
                      subtitle: '您的反馈很重要',
                      iconColor: Colors.yellow[700]!,
                      onTap: () => _rateApp(),
                    ),
                    _SettingItem(
                      icon: Icons.share_outlined,
                      title: '分享给朋友',
                      subtitle: '邀请好友一起钓鱼',
                      iconColor: Colors.green,
                      onTap: () => _shareApp(),
                    ),
                    _SettingItem(
                      icon: Icons.description_outlined,
                      title: '用户协议',
                      iconColor: Colors.blueGrey,
                      onTap: () => context.push(AppRoutes.userAgreement),
                    ),
                    _SettingItem(
                      icon: Icons.privacy_tip_outlined,
                      title: '隐私政策',
                      iconColor: Colors.brown,
                      onTap: () => context.push(AppRoutes.privacyPolicy),
                    ),
                    _SettingItem(
                      icon: Icons.update_outlined,
                      title: '版本更新',
                      subtitle: '当前版本 v1.0.0',
                      iconColor: Colors.red,
                      badge: '新',
                      onTap: () => _checkUpdate(context),
                    ),
                  ],
                  index: 3,
                ),

                const SizedBox(height: 30),

                // 退出登录按钮
                _buildLogoutButton(context),

                const SizedBox(height: 20),
              ]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernSettingGroup({
    required String title,
    required IconData icon,
    required List<_SettingItem> items,
    required int index,
  }) {
    // 创建动画控制器
    if (_itemAnimationControllers.length <= index) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 600 + (index * 100)),
        vsync: this,
      );
      _itemAnimationControllers.add(controller);
      controller.forward();
    }

    return AnimatedBuilder(
      animation: _itemAnimationControllers[index],
      builder: (context, child) {
        final slideAnimation = Tween<Offset>(
          begin: const Offset(0.3, 0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.easeOutBack,
        ));

        final fadeAnimation = Tween<double>(
          begin: 0,
          end: 1,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.easeOut,
        ));

        return SlideTransition(
          position: slideAnimation,
          child: FadeTransition(
            opacity: fadeAnimation,
            child: child,
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Color(0xFFFBFCFE),
            ],
          ),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF667EEA).withOpacity(0.3),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: Icon(icon, size: 20, color: Colors.white),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1A1E25),
                    ),
                  ),
                ],
              ),
            ),
            ...items.asMap().entries.map((entry) {
              final itemIndex = entry.key;
              final item = entry.value;
              final isLast = itemIndex == items.length - 1;

              return Column(
                children: [
                  if (itemIndex > 0)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Divider(
                        height: 1,
                        color: Colors.grey[200],
                      ),
                    ),
                  _buildModernSettingItem(item, isLast),
                ],
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildModernSettingItem(_SettingItem item, bool isLast) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: item.isLoading ? null : () {
          HapticFeedback.lightImpact();
          item.onTap();
        },
        borderRadius: BorderRadius.only(
          bottomLeft: isLast ? const Radius.circular(25) : Radius.zero,
          bottomRight: isLast ? const Radius.circular(25) : Radius.zero,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: item.iconColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  item.icon,
                  size: 22,
                  color: item.iconColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          item.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1A1E25),
                          ),
                        ),
                        if (item.badge != null) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFFFF6B6B), Color(0xFFFF8B8B)],
                              ),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              item.badge!,
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    if (item.subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        item.subtitle!,
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (item.switchValue != null)
                Switch(
                  value: item.switchValue!,
                  onChanged: item.isLoading ? null : (value) {
                    _handleSwitchChange(item.title, value);
                  },
                  activeColor: const Color(0xFF667EEA),
                )
              else if (item.isLoading)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667EEA)),
                  ),
                )
              else
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutButton(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: GestureDetector(
        onTap: () {
          HapticFeedback.mediumImpact();
          _showLogoutDialog(context);
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFFF6B6B), Color(0xFFFF8B8B)],
            ),
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: const Center(
            child: Text(
              '退出登录',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  // 获取手机号显示
  String _getPhoneDisplay() {
    // TODO: 从用户信息获取真实手机号
    final phone = '13888888888'; // 模拟数据
    if (phone.length >= 11) {
      return '当前手机号：${phone.substring(0, 3)}****${phone.substring(7)}';
    }
    return '未绑定手机号';
  }
  
  // 处理手机号更换
  Future<void> _handlePhoneChange() async {
    if (_processingStates['phone'] == true) return;
    
    setState(() {
      _processingStates['phone'] = true;
    });
    
    try {
      // 先验证当前密码或发送验证码
      final canProceed = await _verifyUserIdentity();
      
      if (canProceed && mounted) {
        context.push(AppRoutes.changePhone);
      }
    } catch (error) {
      debugPrint('Handle phone change error: $error');
      _showErrorSnackBar('验证失败，请重试');
    } finally {
      if (mounted) {
        setState(() {
          _processingStates['phone'] = false;
        });
      }
    }
  }
  
  // 验证用户身份
  Future<bool> _verifyUserIdentity() async {
    // 模拟身份验证过程
    await Future.delayed(const Duration(seconds: 1));
    
    // TODO: 实现真实的身份验证逻辑
    return true; // 模拟验证成功
  }
  
  // 处理开关变化
  Future<void> _handleSwitchChange(String settingName, bool value) async {
    final settingKey = settingName.toLowerCase();
    
    if (_processingStates[settingKey] == true) return;
    
    setState(() {
      _processingStates[settingKey] = true;
      // _hasUnsavedChanges = true; // 暂时注释，因为字段被注释了
    });
    
    try {
      await _saveSetting(settingKey, value);
      
      if (mounted) {
        HapticFeedback.lightImpact();
        _showSuccessSnackBar('设置已保存');
      }
    } catch (error) {
      debugPrint('Handle switch change error: $error');
      _showErrorSnackBar('设置保存失败');
    } finally {
      if (mounted) {
        setState(() {
          _processingStates[settingKey] = false;
        });
      }
    }
  }
  
  // 保存设置
  Future<void> _saveSetting(String key, dynamic value) async {
    // TODO: 实现真实的设置保存逻辑
    await Future.delayed(const Duration(milliseconds: 800));
  }
  
  // 增强的清理缓存
  Future<void> _clearCacheEnhanced(BuildContext context) async {
    if (_processingStates['cache'] == true) return;
    
    final confirmed = await _showClearCacheDialog(context);
    if (!confirmed) return;
    
    setState(() {
      _processingStates['cache'] = true;
    });
    
    try {
      HapticFeedback.mediumImpact();
      
      // 模拟清理缓存过程
      await Future.delayed(const Duration(seconds: 2));
      
      if (mounted) {
        setState(() {
          _cacheSize = '0MB';
        });
        
        HapticFeedback.lightImpact();
        _showSuccessSnackBar('缓存清理完成，已释放 $_cacheSize 空间');
        
        // 重新计算缓存大小
        await Future.delayed(const Duration(seconds: 1));
        await _calculateCacheSize();
      }
    } catch (error) {
      debugPrint('Clear cache error: $error');
      _showErrorSnackBar('清理缓存失败，请重试');
    } finally {
      if (mounted) {
        setState(() {
          _processingStates['cache'] = false;
        });
      }
    }
  }
  
  // 显示清理缓存确认对话框
  Future<bool> _showClearCacheDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.cleaning_services,
                color: Colors.amber,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              '清理缓存',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('当前缓存大小：$_cacheSize'),
            const SizedBox(height: 8),
            const Text(
              '清理后将删除：',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 4),
            Text(
              '• 图片缓存\n• 网络请求缓存\n• 临时文件',
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '清理后可能需要重新加载图片，确定继续吗？',
              style: TextStyle(fontSize: 13),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            child: const Text(
              '开始清理',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    ) ?? false;
  }
  
  // 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  // 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '选择语言',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildLanguageOption('简体中文', true),
            _buildLanguageOption('繁體中文', false),
            _buildLanguageOption('English', false),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('语言设置已保存'),
                  backgroundColor: Color(0xFF667EEA),
                ),
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption(String language, bool isSelected) {
    return ListTile(
      title: Text(language),
      leading: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected ? const Color(0xFF667EEA) : Colors.grey[400]!,
            width: 2,
          ),
          color: isSelected ? const Color(0xFF667EEA) : Colors.transparent,
        ),
        child: isSelected
            ? const Icon(Icons.check, size: 16, color: Colors.white)
            : null,
      ),
      onTap: () {
        // TODO: 切换语言
      },
    );
  }


  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '退出登录',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AuthViewModel>().logout();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('退出'),
          ),
        ],
      ),
    );
  }

  void _rateApp() {
    // TODO: 打开应用商店评分页面
    HapticFeedback.lightImpact();
  }

  void _shareApp() {
    // TODO: 分享应用
    HapticFeedback.lightImpact();
  }

  void _checkUpdate(BuildContext context) {
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已是最新版本'),
        backgroundColor: Color(0xFF667EEA),
      ),
    );
  }
}

// 设置项数据模型
class _SettingItem {
  final IconData icon;
  final String title;
  final String? subtitle;
  final Color iconColor;
  final String? badge;
  final bool? switchValue;
  final VoidCallback onTap;
  final bool isLoading;

  _SettingItem({
    required this.icon,
    required this.title,
    this.subtitle,
    required this.iconColor,
    this.badge,
    this.switchValue,
    required this.onTap,
    this.isLoading = false,
  });
}
