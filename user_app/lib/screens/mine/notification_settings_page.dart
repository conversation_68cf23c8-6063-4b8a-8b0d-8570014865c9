import 'package:flutter/material.dart';

class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() =>
      _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  // 通知总开关
  bool _enableNotifications = true;

  // 各类通知开关
  bool _enableLikeNotifications = true;
  bool _enableCommentNotifications = true;
  bool _enableFollowNotifications = true;
  bool _enableSystemNotifications = true;
  bool _enableActivityNotifications = true;

  // 通知方式
  bool _enableSound = true;
  bool _enableVibration = true;
  bool _showNotificationPreview = true;

  // 免打扰设置
  bool _enableDoNotDisturb = false;
  TimeOfDay _startTime = const TimeOfDay(hour: 23, minute: 0);
  TimeOfDay _endTime = const TimeOfDay(hour: 7, minute: 0);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('通知设置'),
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 通知总开关
            Container(
              color: Colors.white,
              child: SwitchListTile(
                title: const Text(
                  '接收通知',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  '关闭后将不会收到任何通知',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                value: _enableNotifications,
                onChanged: (value) {
                  setState(() {
                    _enableNotifications = value;
                  });
                  if (!value) {
                    _showDisableNotificationDialog();
                  }
                },
                activeColor: Colors.blue,
              ),
            ),

            // 只有开启通知才显示详细设置
            if (_enableNotifications) ...[
              const SizedBox(height: 10),

              // 通知类型设置
              _buildSection(
                title: '通知类型',
                description: '选择要接收的通知类型',
                children: [
                  _buildNotificationTypeItem(
                    icon: Icons.favorite,
                    iconColor: Colors.red,
                    title: '点赞通知',
                    subtitle: '有人点赞了你的动态或评论',
                    value: _enableLikeNotifications,
                    onChanged: (value) {
                      setState(() {
                        _enableLikeNotifications = value;
                      });
                    },
                  ),
                  _buildNotificationTypeItem(
                    icon: Icons.comment,
                    iconColor: Colors.blue,
                    title: '评论通知',
                    subtitle: '有人评论了你的动态',
                    value: _enableCommentNotifications,
                    onChanged: (value) {
                      setState(() {
                        _enableCommentNotifications = value;
                      });
                    },
                  ),
                  _buildNotificationTypeItem(
                    icon: Icons.person_add,
                    iconColor: Colors.green,
                    title: '关注通知',
                    subtitle: '有人关注了你',
                    value: _enableFollowNotifications,
                    onChanged: (value) {
                      setState(() {
                        _enableFollowNotifications = value;
                      });
                    },
                  ),
                  _buildNotificationTypeItem(
                    icon: Icons.campaign,
                    iconColor: Colors.orange,
                    title: '系统通知',
                    subtitle: '重要的系统消息和公告',
                    value: _enableSystemNotifications,
                    onChanged: (value) {
                      setState(() {
                        _enableSystemNotifications = value;
                      });
                    },
                  ),
                  _buildNotificationTypeItem(
                    icon: Icons.event,
                    iconColor: Colors.purple,
                    title: '活动通知',
                    subtitle: '活动提醒和优惠信息',
                    value: _enableActivityNotifications,
                    onChanged: (value) {
                      setState(() {
                        _enableActivityNotifications = value;
                      });
                    },
                  ),
                ],
              ),

              const SizedBox(height: 10),

              // 通知方式设置
              _buildSection(
                title: '通知方式',
                description: '设置通知的提醒方式',
                children: [
                  _buildSwitchItem(
                    icon: Icons.volume_up,
                    title: '声音',
                    subtitle: '收到通知时播放提示音',
                    value: _enableSound,
                    onChanged: (value) {
                      setState(() {
                        _enableSound = value;
                      });
                    },
                  ),
                  _buildSwitchItem(
                    icon: Icons.vibration,
                    title: '振动',
                    subtitle: '收到通知时振动提醒',
                    value: _enableVibration,
                    onChanged: (value) {
                      setState(() {
                        _enableVibration = value;
                      });
                    },
                  ),
                  _buildSwitchItem(
                    icon: Icons.preview,
                    title: '通知预览',
                    subtitle: '在通知栏显示消息内容',
                    value: _showNotificationPreview,
                    onChanged: (value) {
                      setState(() {
                        _showNotificationPreview = value;
                      });
                    },
                  ),
                ],
              ),

              const SizedBox(height: 10),

              // 免打扰设置
              _buildSection(
                title: '免打扰模式',
                description: '在指定时间段内不接收通知',
                children: [
                  SwitchListTile(
                    secondary: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.indigo.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.do_not_disturb,
                        color: Colors.indigo,
                        size: 24,
                      ),
                    ),
                    title: const Text('开启免打扰'),
                    subtitle: Text(
                      _enableDoNotDisturb
                          ? '${_formatTime(_startTime)} - ${_formatTime(_endTime)}'
                          : '关闭',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    value: _enableDoNotDisturb,
                    onChanged: (value) {
                      setState(() {
                        _enableDoNotDisturb = value;
                      });
                    },
                  ),
                  if (_enableDoNotDisturb) ...[
                    ListTile(
                      leading: const SizedBox(width: 40),
                      title: const Text('开始时间'),
                      trailing: TextButton(
                        onPressed: () => _selectTime(true),
                        child: Text(
                          _formatTime(_startTime),
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                    ListTile(
                      leading: const SizedBox(width: 40),
                      title: const Text('结束时间'),
                      trailing: TextButton(
                        onPressed: () => _selectTime(false),
                        child: Text(
                          _formatTime(_endTime),
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],

            const SizedBox(height: 30),

            // 说明文字
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                '注意：即使在应用内开启了通知，您仍需要在系统设置中允许应用发送通知',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: 16),

            // 跳转系统设置按钮
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: OutlinedButton(
                onPressed: _openSystemSettings,
                style: OutlinedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('前往系统设置'),
              ),
            ),

            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    String? description,
    required List<Widget> children,
  }) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                if (description != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildNotificationTypeItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      secondary: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: iconColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: iconColor, size: 24),
      ),
      title: Text(title),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 13,
          color: Colors.grey[600],
        ),
      ),
      value: value,
      onChanged: _enableNotifications ? onChanged : null,
      activeColor: Colors.blue,
    );
  }

  Widget _buildSwitchItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      secondary: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.blue.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: Colors.blue, size: 24),
      ),
      title: Text(title),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 13,
          color: Colors.grey[600],
        ),
      ),
      value: value,
      onChanged: _enableNotifications ? onChanged : null,
      activeColor: Colors.blue,
    );
  }

  void _showDisableNotificationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('关闭通知'),
        content: const Text('关闭通知后，您将无法及时收到重要消息提醒。确定要关闭吗？'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _enableNotifications = true;
              });
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('确定关闭'),
          ),
        ],
      ),
    );
  }

  Future<void> _selectTime(bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime ? _startTime : _endTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _startTime = picked;
        } else {
          _endTime = picked;
        }
      });
    }
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  void _openSystemSettings() {
    // TODO: 使用 app_settings 包打开系统通知设置
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('即将跳转到系统设置...')),
    );
  }
}
