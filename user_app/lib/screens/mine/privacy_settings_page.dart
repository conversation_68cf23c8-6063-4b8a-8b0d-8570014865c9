import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class PrivacySettingsPage extends StatefulWidget {
  const PrivacySettingsPage({super.key});

  @override
  State<PrivacySettingsPage> createState() => _PrivacySettingsPageState();
}

class _PrivacySettingsPageState extends State<PrivacySettingsPage> {
  // 隐私设置状态
  bool _showLocation = true;
  bool _showOnlineStatus = true;
  String _momentVisibility = '所有人可见';
  String _profileVisibility = '所有人可见';
  bool _allowStrangerMessage = false;
  bool _allowRecommendToFriends = true;
  bool _showInNearbyList = true;
  bool _allowSearchByPhone = true;
  bool _allowSearchByNickname = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('隐私设置'),
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        actions: [
          TextButton(
            onPressed: _saveSettings,
            child: const Text(
              '保存',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 信息展示设置
            _buildSection(
              title: '信息展示',
              description: '控制其他用户可以看到的信息',
              children: [
                _buildSwitchItem(
                  icon: Icons.location_on_outlined,
                  title: '显示位置信息',
                  subtitle: '在个人主页显示所在城市',
                  value: _showLocation,
                  onChanged: (value) {
                    setState(() {
                      _showLocation = value;
                    });
                  },
                ),
                _buildSwitchItem(
                  icon: Icons.circle,
                  title: '显示在线状态',
                  subtitle: '让其他用户看到你是否在线',
                  value: _showOnlineStatus,
                  onChanged: (value) {
                    setState(() {
                      _showOnlineStatus = value;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 10),

            // 内容可见性设置
            _buildSection(
              title: '内容可见性',
              description: '设置谁可以查看你的内容',
              children: [
                _buildSelectItem(
                  icon: Icons.dynamic_feed_outlined,
                  title: '动态可见范围',
                  value: _momentVisibility,
                  onTap: () => _showVisibilityOptions('动态'),
                ),
                _buildSelectItem(
                  icon: Icons.person_outline,
                  title: '个人主页可见范围',
                  value: _profileVisibility,
                  onTap: () => _showVisibilityOptions('个人主页'),
                ),
              ],
            ),

            const SizedBox(height: 10),

            // 社交权限设置
            _buildSection(
              title: '社交权限',
              description: '管理他人与你互动的权限',
              children: [
                _buildSwitchItem(
                  icon: Icons.message_outlined,
                  title: '允许陌生人私信',
                  subtitle: '未关注你的用户是否可以发送私信',
                  value: _allowStrangerMessage,
                  onChanged: (value) {
                    setState(() {
                      _allowStrangerMessage = value;
                    });
                  },
                ),
                _buildSwitchItem(
                  icon: Icons.people_outline,
                  title: '允许推荐给好友',
                  subtitle: '系统是否可以将你推荐给其他用户',
                  value: _allowRecommendToFriends,
                  onChanged: (value) {
                    setState(() {
                      _allowRecommendToFriends = value;
                    });
                  },
                ),
                _buildSwitchItem(
                  icon: Icons.near_me_outlined,
                  title: '在附近的人中展示',
                  subtitle: '其他用户是否可以在附近功能中发现你',
                  value: _showInNearbyList,
                  onChanged: (value) {
                    setState(() {
                      _showInNearbyList = value;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 10),

            // 搜索设置
            _buildSection(
              title: '搜索设置',
              description: '控制其他用户如何找到你',
              children: [
                _buildSwitchItem(
                  icon: Icons.phone_outlined,
                  title: '允许通过手机号搜索',
                  subtitle: '其他用户是否可以通过手机号找到你',
                  value: _allowSearchByPhone,
                  onChanged: (value) {
                    setState(() {
                      _allowSearchByPhone = value;
                    });
                  },
                ),
                _buildSwitchItem(
                  icon: Icons.search_outlined,
                  title: '允许通过昵称搜索',
                  subtitle: '其他用户是否可以通过昵称找到你',
                  value: _allowSearchByNickname,
                  onChanged: (value) {
                    setState(() {
                      _allowSearchByNickname = value;
                    });
                  },
                ),
              ],
            ),

            const SizedBox(height: 10),

            // 黑名单管理
            _buildSection(
              title: '黑名单管理',
              children: [
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.block_outlined,
                      color: Colors.red,
                      size: 24,
                    ),
                  ),
                  title: const Text('黑名单'),
                  subtitle: const Text('查看和管理黑名单用户'),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => context.push('/blacklist'),
                ),
              ],
            ),

            const SizedBox(height: 30),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    String? description,
    required List<Widget> children,
  }) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                if (description != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.blue.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: Colors.blue, size: 24),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 13,
          color: Colors.grey[600],
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Colors.blue,
      ),
    );
  }

  Widget _buildSelectItem({
    required IconData icon,
    required String title,
    required String value,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.blue.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: Colors.blue, size: 24),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.w500,
        ),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(width: 4),
          Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
        ],
      ),
      onTap: onTap,
    );
  }

  void _showVisibilityOptions(String type) {
    String currentValue = type == '动态' ? _momentVisibility : _profileVisibility;

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                '选择$type可见范围',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const Divider(),
            _buildVisibilityOption(
              '所有人可见',
              '任何人都可以查看',
              Icons.public,
              currentValue == '所有人可见',
              () {
                setState(() {
                  if (type == '动态') {
                    _momentVisibility = '所有人可见';
                  } else {
                    _profileVisibility = '所有人可见';
                  }
                });
                Navigator.pop(context);
              },
            ),
            _buildVisibilityOption(
              '仅关注者可见',
              '只有关注你的人可以查看',
              Icons.people,
              currentValue == '仅关注者可见',
              () {
                setState(() {
                  if (type == '动态') {
                    _momentVisibility = '仅关注者可见';
                  } else {
                    _profileVisibility = '仅关注者可见';
                  }
                });
                Navigator.pop(context);
              },
            ),
            _buildVisibilityOption(
              '仅自己可见',
              '只有你自己可以查看',
              Icons.lock,
              currentValue == '仅自己可见',
              () {
                setState(() {
                  if (type == '动态') {
                    _momentVisibility = '仅自己可见';
                  } else {
                    _profileVisibility = '仅自己可见';
                  }
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVisibilityOption(
    String title,
    String subtitle,
    IconData icon,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Colors.blue : Colors.grey[600],
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          color: isSelected ? Colors.blue : Colors.black87,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 13,
          color: Colors.grey[600],
        ),
      ),
      trailing: isSelected
          ? const Icon(Icons.check_circle, color: Colors.blue)
          : null,
      onTap: onTap,
    );
  }

  void _saveSettings() {
    // TODO: 保存隐私设置
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('隐私设置已保存'),
        backgroundColor: Colors.green,
      ),
    );
    Navigator.pop(context);
  }
}
