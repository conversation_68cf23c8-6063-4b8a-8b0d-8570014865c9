import 'package:flutter/material.dart';
import 'package:user_app/widgets/auth_required_widget.dart';

class LoginHistoryPage extends StatefulWidget {
  const LoginHistoryPage({super.key});

  @override
  State<LoginHistoryPage> createState() => _LoginHistoryPageState();
}

class _LoginHistoryPageState extends State<LoginHistoryPage> {
  // 登录记录数据
  List<LoginRecord> _loginRecords = [];
  bool _isLoading = true;

  // 筛选条件
  String _filterType = 'all'; // all, success, failed
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _loadLoginHistory();
  }

  void _loadLoginHistory() {
    // 模拟加载数据
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _loginRecords = List.generate(20, (index) {
          final isSuccess = index % 3 != 2;
          final now = DateTime.now();
          return LoginRecord(
            id: index + 1,
            deviceName: _getDeviceName(index),
            deviceType: _getDeviceType(index),
            location: _getLocation(index),
            ipAddress: '192.168.1.${100 + index}',
            loginTime: now.subtract(Duration(hours: index * 12)),
            isSuccess: isSuccess,
            failReason: isSuccess ? null : _getFailReason(index),
            isCurrentDevice: index == 0,
          );
        });
        _isLoading = false;
      });
    });
  }

  String _getDeviceName(int index) {
    final devices = [
      'iPhone 14 Pro',
      'HUAWEI P50',
      'MacBook Pro',
      'Windows PC',
      'iPad Air'
    ];
    return devices[index % devices.length];
  }

  String _getDeviceType(int index) {
    final types = ['iOS', 'Android', 'macOS', 'Windows', 'iPadOS'];
    return types[index % types.length];
  }

  String _getLocation(int index) {
    final locations = ['北京市', '上海市', '广州市', '深圳市', '杭州市'];
    return locations[index % locations.length];
  }

  String _getFailReason(int index) {
    final reasons = ['密码错误', '验证码错误', '账号被锁定'];
    return reasons[index % reasons.length];
  }

  List<LoginRecord> _getFilteredRecords() {
    var filtered = _loginRecords;

    // 按状态筛选
    if (_filterType == 'success') {
      filtered = filtered.where((r) => r.isSuccess).toList();
    } else if (_filterType == 'failed') {
      filtered = filtered.where((r) => !r.isSuccess).toList();
    }

    // 按日期筛选
    if (_startDate != null) {
      filtered =
          filtered.where((r) => r.loginTime.isAfter(_startDate!)).toList();
    }
    if (_endDate != null) {
      final endOfDay =
          DateTime(_endDate!.year, _endDate!.month, _endDate!.day, 23, 59, 59);
      filtered = filtered.where((r) => r.loginTime.isBefore(endOfDay)).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return AuthRequiredWidget(
      title: '登录历史',
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text('登录历史'),
          elevation: 0,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black87,
          actions: [
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterOptions,
            ),
          ],
        ),
        body: Column(
          children: [
            // 统计信息
            _buildStatistics(),

            // 列表内容
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildHistoryList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatistics() {
    final filteredRecords = _getFilteredRecords();
    final successCount = filteredRecords.where((r) => r.isSuccess).length;
    final failedCount = filteredRecords.where((r) => !r.isSuccess).length;

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  icon: Icons.check_circle,
                  label: '成功登录',
                  value: successCount.toString(),
                  color: Colors.green,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: Colors.grey[300],
              ),
              Expanded(
                child: _buildStatItem(
                  icon: Icons.cancel,
                  label: '失败尝试',
                  value: failedCount.toString(),
                  color: Colors.red,
                ),
              ),
            ],
          ),
          if (_filterType != 'all' ||
              _startDate != null ||
              _endDate != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.filter_alt, size: 16, color: Colors.blue[700]),
                  const SizedBox(width: 4),
                  Text(
                    _getFilterText(),
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.blue[700],
                    ),
                  ),
                  const SizedBox(width: 8),
                  InkWell(
                    onTap: _clearFilter,
                    child: Icon(Icons.close, size: 16, color: Colors.blue[700]),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildHistoryList() {
    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.only(top: 8),
      itemCount: filteredRecords.length,
      itemBuilder: (context, index) {
        final record = filteredRecords[index];
        return _buildRecordItem(record);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 80,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无登录记录',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '调整筛选条件试试',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordItem(LoginRecord record) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Stack(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: record.isSuccess
                    ? Colors.green.withOpacity(0.1)
                    : Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getDeviceIcon(record.deviceType),
                color: record.isSuccess ? Colors.green : Colors.red,
                size: 24,
              ),
            ),
            if (record.isCurrentDevice)
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: Colors.blue,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    size: 10,
                    color: Colors.white,
                  ),
                ),
              ),
          ],
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                record.deviceName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (record.isCurrentDevice)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Text(
                  '当前设备',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.location_on_outlined,
                  size: 14,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  '${record.location} · ${record.ipAddress}',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 14,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  _formatTime(record.loginTime),
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            if (!record.isSuccess && record.failReason != null) ...[
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '失败原因：${record.failReason}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.red[700],
                  ),
                ),
              ),
            ],
          ],
        ),
        trailing: IconButton(
          icon: Icon(Icons.more_vert, color: Colors.grey[600]),
          onPressed: () => _showRecordOptions(record),
        ),
      ),
    );
  }

  IconData _getDeviceIcon(String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'ios':
      case 'iphone':
        return Icons.phone_iphone;
      case 'android':
        return Icons.phone_android;
      case 'macos':
      case 'windows':
        return Icons.computer;
      case 'ipados':
        return Icons.tablet;
      default:
        return Icons.devices;
    }
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else {
      return '${time.year}年${time.month}月${time.day}日 ${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
  }

  String _getFilterText() {
    final parts = <String>[];

    if (_filterType == 'success') {
      parts.add('成功登录');
    } else if (_filterType == 'failed') {
      parts.add('失败尝试');
    }

    if (_startDate != null || _endDate != null) {
      if (_startDate != null && _endDate != null) {
        parts.add('${_formatDate(_startDate!)} - ${_formatDate(_endDate!)}');
      } else if (_startDate != null) {
        parts.add('从 ${_formatDate(_startDate!)}');
      } else if (_endDate != null) {
        parts.add('至 ${_formatDate(_endDate!)}');
      }
    }

    return parts.join(' · ');
  }

  String _formatDate(DateTime date) {
    return '${date.month}月${date.day}日';
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '筛选条件',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),

              // 状态筛选
              const Text(
                '登录状态',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                children: [
                  _buildFilterChip(
                    label: '全部',
                    value: 'all',
                    groupValue: _filterType,
                    onSelected: (value) {
                      setModalState(() {
                        _filterType = value;
                      });
                    },
                  ),
                  _buildFilterChip(
                    label: '成功',
                    value: 'success',
                    groupValue: _filterType,
                    onSelected: (value) {
                      setModalState(() {
                        _filterType = value;
                      });
                    },
                  ),
                  _buildFilterChip(
                    label: '失败',
                    value: 'failed',
                    groupValue: _filterType,
                    onSelected: (value) {
                      setModalState(() {
                        _filterType = value;
                      });
                    },
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // 日期筛选
              const Text(
                '时间范围',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildDateSelector(
                      label: '开始日期',
                      date: _startDate,
                      onTap: () async {
                        final date = await _selectDate(context, _startDate);
                        if (date != null) {
                          setModalState(() {
                            _startDate = date;
                          });
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildDateSelector(
                      label: '结束日期',
                      date: _endDate,
                      onTap: () async {
                        final date = await _selectDate(context, _endDate);
                        if (date != null) {
                          setModalState(() {
                            _endDate = date;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 32),

              // 按钮
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setModalState(() {
                          _filterType = 'all';
                          _startDate = null;
                          _endDate = null;
                        });
                      },
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('重置'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        setState(() {});
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('确定'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required String value,
    required String groupValue,
    required ValueChanged<String> onSelected,
  }) {
    final isSelected = value == groupValue;

    return ChoiceChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onSelected(value),
      selectedColor: Colors.blue,
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Colors.black87,
      ),
    );
  }

  Widget _buildDateSelector({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              size: 18,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                date != null
                    ? '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}'
                    : label,
                style: TextStyle(
                  fontSize: 14,
                  color: date != null ? Colors.black87 : Colors.grey[600],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<DateTime?> _selectDate(
      BuildContext context, DateTime? initialDate) async {
    return showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
            ),
          ),
          child: child!,
        );
      },
    );
  }

  void _clearFilter() {
    setState(() {
      _filterType = 'all';
      _startDate = null;
      _endDate = null;
    });
  }

  void _showRecordOptions(LoginRecord record) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.info_outline),
              title: const Text('查看详情'),
              onTap: () {
                Navigator.pop(context);
                _showRecordDetails(record);
              },
            ),
            if (!record.isCurrentDevice)
              ListTile(
                leading: const Icon(Icons.logout, color: Colors.red),
                title: const Text(
                  '强制下线',
                  style: TextStyle(color: Colors.red),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _forceLogout(record);
                },
              ),
          ],
        ),
      ),
    );
  }

  void _showRecordDetails(LoginRecord record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('登录详情'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailItem('设备名称', record.deviceName),
            _buildDetailItem('设备类型', record.deviceType),
            _buildDetailItem('登录地点', record.location),
            _buildDetailItem('IP地址', record.ipAddress),
            _buildDetailItem(
              '登录时间',
              '${record.loginTime.year}年${record.loginTime.month}月${record.loginTime.day}日 '
                  '${record.loginTime.hour.toString().padLeft(2, '0')}:'
                  '${record.loginTime.minute.toString().padLeft(2, '0')}:'
                  '${record.loginTime.second.toString().padLeft(2, '0')}',
            ),
            _buildDetailItem(
              '登录状态',
              record.isSuccess ? '成功' : '失败',
              valueColor: record.isSuccess ? Colors.green : Colors.red,
            ),
            if (!record.isSuccess && record.failReason != null)
              _buildDetailItem('失败原因', record.failReason!),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label：',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: valueColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _forceLogout(LoginRecord record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('强制下线'),
        content: Text('确定要让 ${record.deviceName} 下线吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: 实现强制下线
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('设备已强制下线')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}

// 登录记录数据模型
class LoginRecord {
  final int id;
  final String deviceName;
  final String deviceType;
  final String location;
  final String ipAddress;
  final DateTime loginTime;
  final bool isSuccess;
  final String? failReason;
  final bool isCurrentDevice;

  LoginRecord({
    required this.id,
    required this.deviceName,
    required this.deviceType,
    required this.location,
    required this.ipAddress,
    required this.loginTime,
    required this.isSuccess,
    this.failReason,
    required this.isCurrentDevice,
  });
}
