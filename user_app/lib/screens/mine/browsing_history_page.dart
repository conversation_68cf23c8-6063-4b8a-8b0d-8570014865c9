import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';

class BrowsingHistoryPage extends StatefulWidget {
  const BrowsingHistoryPage({super.key});

  @override
  State<BrowsingHistoryPage> createState() => _BrowsingHistoryPageState();
}

class _BrowsingHistoryPageState extends State<BrowsingHistoryPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final List<AnimationController> _itemAnimationControllers = [];

  // 历史记录数据
  List<BrowsingHistoryItem> _allHistory = [];
  Map<DateTime, List<BrowsingHistoryItem>> _groupedHistory = {};

  // 编辑模式
  bool _isEditMode = false;
  final Set<int> _selectedItems = {};

  // 搜索
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearching = false;

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    // 开始动画
    _fadeController.forward();
    _slideController.forward();

    _loadHistory();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    for (var controller in _itemAnimationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadHistory() {
    // 模拟加载数据
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _allHistory = List.generate(30, (index) {
          final types = ['moment', 'spot', 'user', 'topic'];
          final now = DateTime.now();
          final viewTime = now.subtract(Duration(hours: index * 2));

          return BrowsingHistoryItem(
            id: index + 1,
            type: types[index % 4],
            title: _getTitleByType(types[index % 4], index),
            subtitle: _getSubtitleByType(types[index % 4], index),
            imageUrl: index % 3 == 0
                ? null
                : 'https://picsum.photos/100?random=$index',
            viewTime: viewTime,
            duration: Duration(minutes: 5 + index % 10),
          );
        });

        _groupHistory();
        _isLoading = false;
      });
    });
  }

  void _groupHistory() {
    _groupedHistory.clear();
    final filteredHistory = _getFilteredHistory();

    for (final item in filteredHistory) {
      final date = DateTime(
        item.viewTime.year,
        item.viewTime.month,
        item.viewTime.day,
      );

      if (_groupedHistory.containsKey(date)) {
        _groupedHistory[date]!.add(item);
      } else {
        _groupedHistory[date] = [item];
      }
    }
  }

  String _getTitleByType(String type, int index) {
    switch (type) {
      case 'moment':
        return '今天钓到大鱼了！分享一下心得 #$index';
      case 'spot':
        return '北京朝阳公园钓点 #$index';
      case 'user':
        return '钓鱼达人${index}号';
      case 'topic':
        return '#夏季钓鱼技巧$index';
      default:
        return '浏览记录 #$index';
    }
  }

  String _getSubtitleByType(String type, int index) {
    switch (type) {
      case 'moment':
        return '${100 + index}次浏览 · ${50 + index}个赞';
      case 'spot':
        return '4.${9 - index % 10}分 · ${20 + index}条评价';
      case 'user':
        return '${1000 + index * 100}粉丝 · 发布了${50 + index}条动态';
      case 'topic':
        return '${500 + index * 50}人参与 · ${200 + index}条内容';
      default:
        return '';
    }
  }

  List<BrowsingHistoryItem> _getFilteredHistory() {
    var filtered = _allHistory;

    // 按类型筛选
    if (_tabController.index > 0) {
      final types = ['moment', 'spot', 'user'];
      filtered = filtered
          .where((item) => item.type == types[_tabController.index - 1])
          .toList();
    }

    // 按搜索词筛选
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((item) =>
              item.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              item.subtitle.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: CustomScrollView(
        slivers: [
          // 现代化的 AppBar
          SliverAppBar(
            expandedHeight: 120,
            floating: false,
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF667EEA),
                      Color(0xFF764BA2),
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 50),
                    child: Center(
                      child: SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: const Text(
                            '浏览历史',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            leading: _isEditMode
                ? IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(Icons.close, color: Colors.white),
                    ),
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      _exitEditMode();
                    },
                  )
                : IconButton(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child:
                          const Icon(Icons.arrow_back_ios, color: Colors.white),
                    ),
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      Navigator.pop(context);
                    },
                  ),
            actions: _buildAppBarActions(),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(50),
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      offset: Offset(0, -5),
                    ),
                  ],
                ),
                child: _buildModernTabBar(),
              ),
            ),
          ),

          // 内容区域
          SliverFillRemaining(
            child: Container(
              color: Colors.white,
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child:
                    _isLoading ? _buildLoadingState() : _buildHistoryContent(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildAppBarActions() {
    if (_isEditMode) {
      return [
        if (_selectedItems.isNotEmpty)
          IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.delete, color: Colors.white),
            ),
            onPressed: () {
              HapticFeedback.mediumImpact();
              _deleteSelectedItems();
            },
          ),
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.select_all, color: Colors.white),
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            _selectAll();
          },
        ),
      ];
    } else {
      return [
        IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.search, color: Colors.white),
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            setState(() => _isSearching = !_isSearching);
          },
        ),
        PopupMenuButton<String>(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.more_vert, color: Colors.white),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF667EEA).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(Icons.edit,
                        size: 20, color: Color(0xFF667EEA)),
                  ),
                  const SizedBox(width: 12),
                  const Text('编辑'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'clear_all',
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(Icons.delete_sweep,
                        size: 20, color: Colors.red),
                  ),
                  const SizedBox(width: 12),
                  const Text('清空历史', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ];
    }
  }

  Widget _buildModernTabBar() {
    return Column(
      children: [
        if (_isSearching) _buildSearchBar(),
        TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF667EEA),
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: const Color(0xFF667EEA),
          indicatorWeight: 3,
          indicatorPadding: const EdgeInsets.symmetric(horizontal: 20),
          onTap: (index) {
            setState(() {
              _groupHistory();
            });
          },
          tabs: [
            _buildModernTab('全部', Icons.apps),
            _buildModernTab('动态', Icons.article),
            _buildModernTab('钓点', Icons.location_on),
            _buildModernTab('用户', Icons.person),
          ],
        ),
      ],
    );
  }

  Widget _buildModernTab(String label, IconData icon) {
    return Tab(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 18),
          const SizedBox(width: 6),
          Text(label, style: const TextStyle(fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(25),
        ),
        child: TextField(
          controller: _searchController,
          autofocus: true,
          decoration: InputDecoration(
            hintText: '搜索浏览记录...',
            border: InputBorder.none,
            icon: const Icon(Icons.search, color: Color(0xFF667EEA)),
            suffixIcon: _searchQuery.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear, size: 20),
                    onPressed: () {
                      setState(() {
                        _searchController.clear();
                        _searchQuery = '';
                        _groupHistory();
                      });
                    },
                  )
                : null,
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
              _groupHistory();
            });
          },
        ),
      ),
    );
  }

  Widget _buildHistoryContent() {
    if (_groupedHistory.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.only(top: 20),
      itemCount: _groupedHistory.length,
      itemBuilder: (context, index) {
        final date = _groupedHistory.keys.elementAt(index);
        final items = _groupedHistory[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDateHeader(date, items.length),
            ...items.asMap().entries.map((entry) {
              final itemIndex = entry.key;
              final item = entry.value;
              return _buildModernHistoryItem(item, index * 10 + itemIndex);
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildDateHeader(DateTime date, int count) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
              ),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _formatDateHeader(date),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            '$count 条记录',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernHistoryItem(BrowsingHistoryItem item, int index) {
    // 创建动画控制器
    if (_itemAnimationControllers.length <= index) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 600 + (index * 30)),
        vsync: this,
      );
      _itemAnimationControllers.add(controller);
      controller.forward();
    }

    final isSelected = _selectedItems.contains(item.id);

    return AnimatedBuilder(
      animation: _itemAnimationControllers[index],
      builder: (context, child) {
        final slideAnimation = Tween<Offset>(
          begin: const Offset(0.3, 0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.easeOutBack,
        ));

        final scaleAnimation = Tween<double>(
          begin: 0.8,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.elasticOut,
        ));

        return SlideTransition(
          position: slideAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          if (_isEditMode) {
            setState(() {
              if (_selectedItems.contains(item.id)) {
                _selectedItems.remove(item.id);
              } else {
                _selectedItems.add(item.id);
              }
            });
          } else {
            _navigateToItem(item);
          }
        },
        onLongPress: () {
          HapticFeedback.mediumImpact();
          if (!_isEditMode) {
            _enterEditMode(item.id);
          }
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isSelected
                  ? [
                      const Color(0xFF667EEA).withOpacity(0.1),
                      const Color(0xFF764BA2).withOpacity(0.1),
                    ]
                  : [
                      Colors.white,
                      const Color(0xFFFBFCFE),
                    ],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
            border: Border.all(
              color: isSelected
                  ? const Color(0xFF667EEA).withOpacity(0.3)
                  : Colors.transparent,
              width: 2,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // 图标或图片
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: _getTypeGradient(item.type),
                    ),
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: _getTypeColor(item.type).withOpacity(0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: item.imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(15),
                          child: CachedNetworkImage(
                            imageUrl: item.imageUrl!,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Icon(
                          _getTypeIcon(item.type),
                          color: Colors.white,
                          size: 28,
                        ),
                ),
                const SizedBox(width: 16),

                // 内容
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              item.title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF1A1E25),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          _buildTypeBadge(item.type),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Text(
                        item.subtitle,
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey[600],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 14,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _formatViewTime(item.viewTime),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                          ),
                          const SizedBox(width: 16),
                          Icon(
                            Icons.timer,
                            size: 14,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _formatDuration(item.duration),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // 选择指示器或操作按钮
                if (_isEditMode)
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          isSelected ? const Color(0xFF667EEA) : Colors.white,
                      border: Border.all(
                        color: isSelected
                            ? const Color(0xFF667EEA)
                            : Colors.grey[400]!,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, size: 16, color: Colors.white)
                        : null,
                  )
                else
                  IconButton(
                    icon: Icon(Icons.more_vert, color: Colors.grey[600]),
                    onPressed: () => _showItemOptions(item),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTypeBadge(String type) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors:
              _getTypeGradient(type).map((c) => c.withOpacity(0.2)).toList(),
        ),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        _getTypeLabel(type),
        style: TextStyle(
          fontSize: 11,
          color: _getTypeColor(type),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  List<Color> _getTypeGradient(String type) {
    switch (type) {
      case 'moment':
        return [const Color(0xFF667EEA), const Color(0xFF764BA2)];
      case 'spot':
        return [const Color(0xFF4CAF50), const Color(0xFF8BC34A)];
      case 'user':
        return [const Color(0xFFFF6B6B), const Color(0xFFFFD93D)];
      case 'topic':
        return [const Color(0xFF00BCD4), const Color(0xFF03A9F4)];
      default:
        return [Colors.grey, Colors.grey[700]!];
    }
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'moment':
        return const Color(0xFF667EEA);
      case 'spot':
        return const Color(0xFF4CAF50);
      case 'user':
        return const Color(0xFFFF6B6B);
      case 'topic':
        return const Color(0xFF00BCD4);
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'moment':
        return Icons.article;
      case 'spot':
        return Icons.location_on;
      case 'user':
        return Icons.person;
      case 'topic':
        return Icons.tag;
      default:
        return Icons.history;
    }
  }

  String _getTypeLabel(String type) {
    switch (type) {
      case 'moment':
        return '动态';
      case 'spot':
        return '钓点';
      case 'user':
        return '用户';
      case 'topic':
        return '话题';
      default:
        return '其他';
    }
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667EEA)),
      ),
    );
  }

  Widget _buildEmptyState() {
    IconData icon;
    String title;
    String subtitle;

    if (_searchQuery.isNotEmpty) {
      icon = Icons.search_off;
      title = '没有找到相关记录';
      subtitle = '试试其他关键词';
    } else if (_tabController.index > 0) {
      final types = ['动态', '钓点', '用户'];
      icon = Icons.history;
      title = '还没有浏览过${types[_tabController.index - 1]}';
      subtitle = '快去探索吧';
    } else {
      icon = Icons.history;
      title = '暂无浏览记录';
      subtitle = '你浏览过的内容会显示在这里';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF667EEA).withOpacity(0.1),
                  const Color(0xFF764BA2).withOpacity(0.1),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, size: 48, color: const Color(0xFF667EEA)),
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              color: Color(0xFF1A1E25),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  void _enterEditMode(int? initialSelectedId) {
    setState(() {
      _isEditMode = true;
      if (initialSelectedId != null) {
        _selectedItems.add(initialSelectedId);
      }
    });
  }

  void _exitEditMode() {
    setState(() {
      _isEditMode = false;
      _selectedItems.clear();
    });
  }

  void _selectAll() {
    setState(() {
      if (_selectedItems.length == _getFilteredHistory().length) {
        _selectedItems.clear();
      } else {
        _selectedItems.clear();
        _selectedItems.addAll(_getFilteredHistory().map((item) => item.id));
      }
    });
  }

  void _deleteSelectedItems() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text('删除选中记录'),
        content: Text('确定要删除 ${_selectedItems.length} 条浏览记录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _allHistory
                    .removeWhere((item) => _selectedItems.contains(item.id));
                _groupHistory();
                _exitEditMode();
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('已删除 ${_selectedItems.length} 条记录'),
                  backgroundColor: const Color(0xFF667EEA),
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _deleteItem(BrowsingHistoryItem item) {
    setState(() {
      _allHistory.removeWhere((h) => h.id == item.id);
      _groupHistory();
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('已删除该记录'),
        backgroundColor: const Color(0xFF667EEA),
        action: SnackBarAction(
          label: '撤销',
          textColor: Colors.white,
          onPressed: () {
            setState(() {
              _allHistory.add(item);
              _allHistory.sort((a, b) => b.viewTime.compareTo(a.viewTime));
              _groupHistory();
            });
          },
        ),
      ),
    );
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text('清空历史记录'),
        content: const Text('确定要清空所有浏览历史吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _allHistory.clear();
                _groupedHistory.clear();
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('已清空所有历史记录'),
                  backgroundColor: Color(0xFF667EEA),
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('清空'),
          ),
        ],
      ),
    );
  }

  void _showItemOptions(BrowsingHistoryItem item) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.delete, color: Colors.red),
              ),
              title: const Text(
                '删除记录',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                HapticFeedback.mediumImpact();
                _deleteItem(item);
              },
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF667EEA).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.share, color: Color(0xFF667EEA)),
              ),
              title: const Text(
                '分享',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现分享功能
              },
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        _enterEditMode(null);
        break;
      case 'clear_all':
        _showClearAllDialog();
        break;
    }
  }

  void _navigateToItem(BrowsingHistoryItem item) {
    switch (item.type) {
      case 'moment':
        context.push('/moment-detail/${item.id}');
        break;
      case 'spot':
        context.push('/spot-detail/${item.id}');
        break;
      case 'user':
        context.push('/profile/${item.id}');
        break;
      case 'topic':
        context.push('/topic/${item.id}');
        break;
    }
  }

  String _formatDateHeader(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    if (date == today) {
      return '今天';
    } else if (date == yesterday) {
      return '昨天';
    } else if (date.year == now.year) {
      return '${date.month}月${date.day}日';
    } else {
      return '${date.year}年${date.month}月${date.day}日';
    }
  }

  String _formatViewTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  String _formatDuration(Duration duration) {
    if (duration.inMinutes < 1) {
      return '${duration.inSeconds}秒';
    } else if (duration.inMinutes < 60) {
      return '${duration.inMinutes}分钟';
    } else {
      return '${duration.inHours}小时${duration.inMinutes % 60}分';
    }
  }
}

// 浏览历史数据模型
class BrowsingHistoryItem {
  final int id;
  final String type;
  final String title;
  final String subtitle;
  final String? imageUrl;
  final DateTime viewTime;
  final Duration duration;

  BrowsingHistoryItem({
    required this.id,
    required this.type,
    required this.title,
    required this.subtitle,
    this.imageUrl,
    required this.viewTime,
    required this.duration,
  });
}
