import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class MyAlbumPage extends StatefulWidget {
  const MyAlbumPage({super.key});

  @override
  State<MyAlbumPage> createState() => _MyAlbumPageState();
}

class _MyAlbumPageState extends State<MyAlbumPage> {
  // 视图模式
  bool _isGridView = true;
  int _gridCrossAxisCount = 3;

  // 选择模式
  bool _isSelectionMode = false;
  final Set<int> _selectedPhotos = {};

  // 数据
  List<AlbumPhoto> _photos = [];
  bool _isLoading = true;

  // 筛选
  String _filterType = 'all'; // all, fish, spot, moment
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _loadPhotos();
  }

  void _loadPhotos() {
    // 模拟加载数据
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _photos = List.generate(50, (index) {
          final types = ['fish', 'spot', 'moment'];
          return AlbumPhoto(
            id: index + 1,
            url: 'https://picsum.photos/400/600?random=$index',
            thumbnailUrl: 'https://picsum.photos/200/300?random=$index',
            type: types[index % 3],
            title: '渔获照片 ${index + 1}',
            description: '今天钓到了一条大鱼！',
            location: '北京市朝阳区某某湖',
            createdAt: DateTime.now().subtract(Duration(days: index)),
            fileSize: (1.5 + index * 0.1) * 1024 * 1024,
            // MB to bytes
            width: 1920,
            height: 1080,
            tags: ['鲤鱼', '野钓', '路亚'][index % 3],
          );
        });
        _isLoading = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title:
            Text(_isSelectionMode ? '已选择 ${_selectedPhotos.length} 张' : '我的相册'),
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        leading: _isSelectionMode
            ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: _exitSelectionMode,
              )
            : IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.pop(context),
              ),
        actions: [
          if (!_isSelectionMode) ...[
            IconButton(
              icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
              onPressed: () {
                setState(() {
                  _isGridView = !_isGridView;
                });
              },
            ),
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterOptions,
            ),
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              onSelected: _handleMenuAction,
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'select',
                  child: Row(
                    children: [
                      Icon(Icons.check_circle_outline, size: 20),
                      SizedBox(width: 8),
                      Text('选择照片'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'upload',
                  child: Row(
                    children: [
                      Icon(Icons.cloud_upload_outlined, size: 20),
                      SizedBox(width: 8),
                      Text('上传照片'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'storage',
                  child: Row(
                    children: [
                      Icon(Icons.storage, size: 20),
                      SizedBox(width: 8),
                      Text('存储空间'),
                    ],
                  ),
                ),
              ],
            ),
          ] else ...[
            if (_selectedPhotos.isNotEmpty) ...[
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: _shareSelectedPhotos,
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: _deleteSelectedPhotos,
              ),
            ],
            IconButton(
              icon: const Icon(Icons.select_all),
              onPressed: _selectAll,
            ),
          ],
        ],
      ),
      body: Column(
        children: [
          // 统计信息栏
          _buildStatisticsBar(),

          // 照片内容
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _photos.isEmpty
                    ? _buildEmptyState()
                    : _isGridView
                        ? _buildGridView()
                        : _buildTimelineView(),
          ),
        ],
      ),

      // 选择模式底部操作栏
      bottomNavigationBar: _isSelectionMode ? _buildSelectionBottomBar() : null,
    );
  }

  Widget _buildStatisticsBar() {
    final filteredPhotos = _filterPhotos();
    final totalSize = filteredPhotos.fold<double>(
      0,
      (sum, photo) => sum + photo.fileSize,
    );

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(Icons.photo_library, size: 18, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text(
                '${filteredPhotos.length} 张照片',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
              ),
              if (_filterType != 'all') ...[
                const SizedBox(width: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getFilterLabel(),
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.blue,
                    ),
                  ),
                ),
              ],
            ],
          ),
          Text(
            _formatFileSize(totalSize),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 80,
            color: Colors.grey[300],
          ),
          const SizedBox(height: 16),
          Text(
            '相册还是空的',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '拍照记录你的钓鱼时光',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _uploadPhotos,
            icon: const Icon(Icons.add_a_photo, size: 20),
            label: const Text('上传照片'),
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView() {
    final filteredPhotos = _filterPhotos();

    return CustomScrollView(
      slivers: [
        // 网格切换按钮
        SliverToBoxAdapter(
          child: Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  '每行',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 8),
                SegmentedButton<int>(
                  segments: const [
                    ButtonSegment(value: 3, label: Text('3')),
                    ButtonSegment(value: 4, label: Text('4')),
                    ButtonSegment(value: 5, label: Text('5')),
                  ],
                  selected: {_gridCrossAxisCount},
                  onSelectionChanged: (value) {
                    setState(() {
                      _gridCrossAxisCount = value.first;
                    });
                  },
                  style: ButtonStyle(
                    visualDensity: VisualDensity.compact,
                    textStyle: MaterialStateProperty.all(
                      const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // 照片网格
        SliverPadding(
          padding: const EdgeInsets.all(2),
          sliver: SliverGrid(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: _gridCrossAxisCount,
              crossAxisSpacing: 2,
              mainAxisSpacing: 2,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final photo = filteredPhotos[index];
                return _buildPhotoItem(photo);
              },
              childCount: filteredPhotos.length,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimelineView() {
    final filteredPhotos = _filterPhotos();
    final groupedPhotos = _groupPhotosByDate(filteredPhotos);

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: groupedPhotos.length,
      itemBuilder: (context, index) {
        final date = groupedPhotos.keys.elementAt(index);
        final photos = groupedPhotos[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 日期标题
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Text(
                _formatDate(date),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // 照片列表
            ...photos.map((photo) => _buildTimelinePhotoItem(photo)).toList(),

            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  Widget _buildPhotoItem(AlbumPhoto photo) {
    final isSelected = _selectedPhotos.contains(photo.id);

    return GestureDetector(
      onTap: () {
        if (_isSelectionMode) {
          _togglePhotoSelection(photo.id);
        } else {
          _viewPhoto(photo);
        }
      },
      onLongPress: () {
        if (!_isSelectionMode) {
          _enterSelectionMode(photo.id);
        }
      },
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 照片
          Hero(
            tag: 'photo_${photo.id}',
            child: CachedNetworkImage(
              imageUrl: photo.thumbnailUrl,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey[200],
                child: const Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[200],
                child: const Icon(Icons.error),
              ),
            ),
          ),

          // 类型标签
          if (!_isSelectionMode)
            Positioned(
              top: 4,
              left: 4,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getPhotoTypeIcon(photo.type),
                      size: 12,
                      color: Colors.white,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      _getPhotoTypeLabel(photo.type),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // 选中状态
          if (_isSelectionMode)
            Positioned.fill(
              child: Container(
                color: isSelected
                    ? Colors.blue.withOpacity(0.3)
                    : Colors.transparent,
                child: Align(
                  alignment: Alignment.topRight,
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.blue : Colors.white,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? Colors.blue : Colors.grey,
                        width: 2,
                      ),
                    ),
                    child: isSelected
                        ? const Icon(
                            Icons.check,
                            size: 16,
                            color: Colors.white,
                          )
                        : null,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTimelinePhotoItem(AlbumPhoto photo) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _viewPhoto(photo),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 照片
            ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: CachedNetworkImage(
                  imageUrl: photo.url,
                  fit: BoxFit.cover,
                ),
              ),
            ),

            // 信息
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getPhotoTypeIcon(photo.type),
                        size: 16,
                        color: Colors.blue,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        photo.title ?? '无标题',
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  if (photo.description != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      photo.description!,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on_outlined,
                        size: 14,
                        color: Colors.grey[500],
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          photo.location ?? '未知位置',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                        ),
                      ),
                      Text(
                        _formatTime(photo.createdAt),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionBottomBar() {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildBottomAction(
            icon: Icons.share,
            label: '分享',
            onTap: _shareSelectedPhotos,
            enabled: _selectedPhotos.isNotEmpty,
          ),
          _buildBottomAction(
            icon: Icons.download,
            label: '下载',
            onTap: _downloadSelectedPhotos,
            enabled: _selectedPhotos.isNotEmpty,
          ),
          _buildBottomAction(
            icon: Icons.delete,
            label: '删除',
            onTap: _deleteSelectedPhotos,
            enabled: _selectedPhotos.isNotEmpty,
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool enabled = true,
    Color? color,
  }) {
    final effectiveColor =
        enabled ? (color ?? Theme.of(context).primaryColor) : Colors.grey[400]!;

    return InkWell(
      onTap: enabled ? onTap : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: effectiveColor),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: effectiveColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<AlbumPhoto> _filterPhotos() {
    if (_filterType == 'all') {
      return _photos;
    }
    return _photos.where((photo) => photo.type == _filterType).toList();
  }

  Map<DateTime, List<AlbumPhoto>> _groupPhotosByDate(List<AlbumPhoto> photos) {
    final grouped = <DateTime, List<AlbumPhoto>>{};

    for (final photo in photos) {
      final date = DateTime(
        photo.createdAt.year,
        photo.createdAt.month,
        photo.createdAt.day,
      );

      if (grouped.containsKey(date)) {
        grouped[date]!.add(photo);
      } else {
        grouped[date] = [photo];
      }
    }

    return Map.fromEntries(
      grouped.entries.toList()..sort((a, b) => b.key.compareTo(a.key)),
    );
  }

  String _getFilterLabel() {
    switch (_filterType) {
      case 'fish':
        return '渔获';
      case 'spot':
        return '钓点';
      case 'moment':
        return '动态';
      default:
        return '全部';
    }
  }

  IconData _getPhotoTypeIcon(String type) {
    switch (type) {
      case 'fish':
        return Icons.phishing;
      case 'spot':
        return Icons.location_on;
      case 'moment':
        return Icons.dynamic_feed;
      default:
        return Icons.photo;
    }
  }

  String _getPhotoTypeLabel(String type) {
    switch (type) {
      case 'fish':
        return '渔获';
      case 'spot':
        return '钓点';
      case 'moment':
        return '动态';
      default:
        return '照片';
    }
  }

  String _formatFileSize(double bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    if (date.isAfter(today)) {
      return '今天';
    } else if (date.isAfter(yesterday)) {
      return '昨天';
    } else if (date.year == now.year) {
      return '${date.month}月${date.day}日';
    } else {
      return '${date.year}年${date.month}月${date.day}日';
    }
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                '筛选照片',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const Divider(),
            _buildFilterOption('all', '全部照片', Icons.photo_library),
            _buildFilterOption('fish', '渔获照片', Icons.phishing),
            _buildFilterOption('spot', '钓点照片', Icons.location_on),
            _buildFilterOption('moment', '动态照片', Icons.dynamic_feed),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterOption(String value, String label, IconData icon) {
    final isSelected = _filterType == value;

    return ListTile(
      leading: Icon(icon, color: isSelected ? Colors.blue : Colors.grey[600]),
      title: Text(
        label,
        style: TextStyle(
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          color: isSelected ? Colors.blue : Colors.black87,
        ),
      ),
      trailing: isSelected
          ? const Icon(Icons.check_circle, color: Colors.blue)
          : null,
      onTap: () {
        setState(() {
          _filterType = value;
        });
        Navigator.pop(context);
      },
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'select':
        _enterSelectionMode(null);
        break;
      case 'upload':
        _uploadPhotos();
        break;
      case 'storage':
        _showStorageInfo();
        break;
    }
  }

  void _enterSelectionMode(int? photoId) {
    setState(() {
      _isSelectionMode = true;
      if (photoId != null) {
        _selectedPhotos.add(photoId);
      }
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedPhotos.clear();
    });
  }

  void _togglePhotoSelection(int photoId) {
    setState(() {
      if (_selectedPhotos.contains(photoId)) {
        _selectedPhotos.remove(photoId);
      } else {
        _selectedPhotos.add(photoId);
      }
    });
  }

  void _selectAll() {
    setState(() {
      final allPhotoIds = _filterPhotos().map((p) => p.id).toSet();
      if (_selectedPhotos.length == allPhotoIds.length) {
        _selectedPhotos.clear();
      } else {
        _selectedPhotos.addAll(allPhotoIds);
      }
    });
  }

  void _viewPhoto(AlbumPhoto photo) {
    final photos = _filterPhotos();
    final index = photos.indexOf(photo);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PhotoViewPage(
          photos: photos,
          initialIndex: index,
        ),
      ),
    );
  }

  void _uploadPhotos() {
    // TODO: 实现上传照片
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('上传功能开发中...')),
    );
  }

  void _shareSelectedPhotos() {
    // TODO: 实现分享
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('分享 ${_selectedPhotos.length} 张照片')),
    );
  }

  void _downloadSelectedPhotos() {
    // TODO: 实现下载
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('下载 ${_selectedPhotos.length} 张照片')),
    );
  }

  void _deleteSelectedPhotos() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('删除照片'),
        content: Text('确定要删除选中的 ${_selectedPhotos.length} 张照片吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _photos.removeWhere((p) => _selectedPhotos.contains(p.id));
                _exitSelectionMode();
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('照片已删除')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _showStorageInfo() {
    final totalSize = _photos.fold<double>(
      0,
      (sum, photo) => sum + photo.fileSize,
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('存储空间'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStorageItem('照片数量', '${_photos.length} 张'),
            _buildStorageItem('占用空间', _formatFileSize(totalSize)),
            _buildStorageItem('剩余空间', '8.5 GB'),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: totalSize / (10 * 1024 * 1024 * 1024), // 假设总空间10GB
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(color: Colors.grey[600])),
          Text(value, style: const TextStyle(fontWeight: FontWeight.w500)),
        ],
      ),
    );
  }
}

// 照片查看页面
class PhotoViewPage extends StatelessWidget {
  final List<AlbumPhoto> photos;
  final int initialIndex;

  const PhotoViewPage({
    super.key,
    required this.photos,
    required this.initialIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          PhotoViewGallery.builder(
            itemCount: photos.length,
            pageController: PageController(initialPage: initialIndex),
            builder: (context, index) {
              final photo = photos[index];
              return PhotoViewGalleryPageOptions(
                imageProvider: CachedNetworkImageProvider(photo.url),
                heroAttributes:
                    PhotoViewHeroAttributes(tag: 'photo_${photo.id}'),
              );
            },
            scrollPhysics: const BouncingScrollPhysics(),
            backgroundDecoration: const BoxDecoration(color: Colors.black),
          ),
          SafeArea(
            child: IconButton(
              icon: const Icon(Icons.close, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
          ),
        ],
      ),
    );
  }
}

// 相册照片数据模型
class AlbumPhoto {
  final int id;
  final String url;
  final String thumbnailUrl;
  final String type; // fish, spot, moment
  final String? title;
  final String? description;
  final String? location;
  final DateTime createdAt;
  final double fileSize;
  final int width;
  final int height;
  final String? tags;

  AlbumPhoto({
    required this.id,
    required this.url,
    required this.thumbnailUrl,
    required this.type,
    this.title,
    this.description,
    this.location,
    required this.createdAt,
    required this.fileSize,
    required this.width,
    required this.height,
    this.tags,
  });
}
