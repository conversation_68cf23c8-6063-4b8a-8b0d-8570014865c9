import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/api/fishing_spot_api.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/core/models/page_result.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/screens/create_new_spot_page.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class MySpotsPage extends StatefulWidget {
  const MySpotsPage({super.key});

  @override
  State<MySpotsPage> createState() => _MySpotsPageState();
}

class _MySpotsPageState extends State<MySpotsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _createdScrollController = ScrollController();
  final ScrollController _visitedScrollController = ScrollController();

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  final List<AnimationController> _itemAnimationControllers = [];

  // 数据
  List<FishingSpotVo> _createdSpots = [];
  List<FishingSpotVo> _visitedSpots = [];
  bool _isLoadingCreated = true;
  bool _isLoadingVisited = true;
  bool _isRefreshing = false;
  bool _isLoadingMoreCreated = false;
  bool _isLoadingMoreVisited = false;
  int _createdPage = 0;
  int _visitedPage = 0;
  static const int _pageSize = 20;
  String? _error;

  // 分页总数信息
  int _createdTotal = 0;
  int _visitedTotal = 0;

  // API实例
  late FishingSpotApi _fishingSpotApi;
  late AuthViewModel _authViewModel;

  // 排序和筛选
  String _sortBy = 'newest';
  String _filterType = 'all';

  // 底部菜单动画
  late AnimationController _bottomSheetController;
  late Animation<double> _bottomSheetAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 初始化API和ViewModel
    _fishingSpotApi = getIt<FishingSpotApi>();
    _authViewModel = getIt<AuthViewModel>();

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _bottomSheetController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    _bottomSheetAnimation = CurvedAnimation(
      parent: _bottomSheetController,
      curve: Curves.easeOutBack,
    );

    // 开始动画
    _fadeController.forward();
    _scaleController.forward();

    // 添加滚动监听器
    _createdScrollController.addListener(_onCreatedScroll);
    _visitedScrollController.addListener(_onVisitedScroll);
    
    // 添加标签切换监听器，用于更新统计显示
    _tabController.addListener(() {
      if (mounted) {
        setState(() {
          // 当标签切换时重新渲染统计信息
        });
      }
    });

    _loadSpots();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _createdScrollController.dispose();
    _visitedScrollController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    _bottomSheetController.dispose();
    for (var controller in _itemAnimationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadSpots({bool isRefresh = false}) async {
    if (!_authViewModel.isUserLoggedIn()) {
      setState(() {
        _error = '请先登录';
        _isLoadingCreated = false;
        _isLoadingVisited = false;
        _isRefreshing = false;
      });
      return;
    }

    if (isRefresh) {
      setState(() {
        _isRefreshing = true;
        _createdPage = 0;
        _visitedPage = 0;
        _error = null;
      });
    }

    try {
      // 同时加载创建的钓点和访问的钓点  
      final results = await Future.wait([
        _fishingSpotApi.getMyCreatedSpots(page: _createdPage, pageSize: _pageSize),
        _fishingSpotApi.getRecentCheckins(page: _visitedPage, pageSize: _pageSize),
      ]);

      setState(() {
        // 更新总数信息
        _createdTotal = results[0].total;
        _visitedTotal = results[1].total;
        
        if (isRefresh) {
          _createdSpots = results[0].records;
          _visitedSpots = results[1].records;
        } else {
          if (results[0].records.isNotEmpty) {
            _createdSpots.addAll(results[0].records);
          }
          if (results[1].records.isNotEmpty) {
            _visitedSpots.addAll(results[1].records);
          }
        }
        _isLoadingCreated = false;
        _isLoadingVisited = false;
        _isRefreshing = false;
        _error = null;
        _sortAndFilterSpots();
      });
    } catch (e) {
      debugPrint('加载钓点数据失败: $e');
      setState(() {
        _error = '加载数据失败: $e';
        _isLoadingCreated = false;
        _isLoadingVisited = false;
        _isRefreshing = false;
      });
    }
  }

  // 滚动监听器
  void _onCreatedScroll() {
    if (_createdScrollController.position.pixels >=
            _createdScrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMoreCreated &&
        !_isLoadingCreated &&
        _createdSpots.isNotEmpty) {
      _loadMoreCreated();
    }
  }

  void _onVisitedScroll() {
    if (_visitedScrollController.position.pixels >=
            _visitedScrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMoreVisited &&
        !_isLoadingVisited &&
        _visitedSpots.isNotEmpty) {
      _loadMoreVisited();
    }
  }

  // 加载更多创建的钓点
  Future<void> _loadMoreCreated() async {
    if (_isLoadingMoreCreated || !_authViewModel.isUserLoggedIn()) return;

    setState(() {
      _isLoadingMoreCreated = true;
    });

    try {
      _createdPage++;
      final newSpots = await _fishingSpotApi.getMyCreatedSpots(
        page: _createdPage,
        pageSize: _pageSize,
      );

      setState(() {
        if (newSpots.records.isNotEmpty) {
          _createdSpots.addAll(newSpots.records);
          _sortAndFilterSpots();
        }
        _isLoadingMoreCreated = false;
      });
    } catch (e) {
      debugPrint('加载更多创建钓点失败: $e');
      setState(() {
        _isLoadingMoreCreated = false;
        _createdPage--; // 回退页码
      });
    }
  }

  // 加载更多访问的钓点
  Future<void> _loadMoreVisited() async {
    if (_isLoadingMoreVisited || !_authViewModel.isUserLoggedIn()) return;

    setState(() {
      _isLoadingMoreVisited = true;
    });

    try {
      _visitedPage++;
      final newSpots = await _fishingSpotApi.getRecentCheckins(
        page: _visitedPage,  
        pageSize: _pageSize,
      );

      setState(() {
        if (newSpots.records.isNotEmpty) {
          _visitedSpots.addAll(newSpots.records);
          _sortAndFilterSpots();
        }
        _isLoadingMoreVisited = false;
      });
    } catch (e) {
      debugPrint('加载更多访问钓点失败: $e');
      setState(() {
        _isLoadingMoreVisited = false;
        _visitedPage--; // 回退页码
      });
    }
  }

  void _sortAndFilterSpots() {
    // 筛选（注意FishingSpotVo没有isPublic字段，所以基于isOfficial字段筛选）
    List<FishingSpotVo> filteredCreated = List.from(_createdSpots);
    List<FishingSpotVo> filteredVisited = List.from(_visitedSpots);

    if (_filterType == 'public') {
      filteredCreated = filteredCreated.where((spot) => spot.isOfficial).toList();
    } else if (_filterType == 'private') {
      filteredCreated = filteredCreated.where((spot) => !spot.isOfficial).toList();
    }

    // 排序（由于FishingSpotVo结构不同，需要调整排序逻辑）
    switch (_sortBy) {
      case 'newest':
        // 按ID倒序排列（假设ID越大越新）
        filteredCreated.sort((a, b) => b.id.compareTo(a.id));
        filteredVisited.sort((a, b) => b.id.compareTo(a.id));
        break;
      case 'mostVisited':
        filteredCreated.sort((a, b) => b.visitorCount.compareTo(a.visitorCount));
        filteredVisited.sort((a, b) => b.visitorCount.compareTo(a.visitorCount));
        break;
      case 'rating':
        filteredCreated.sort((a, b) => b.rating.compareTo(a.rating));
        filteredVisited.sort((a, b) => b.rating.compareTo(a.rating));
        break;
    }

    setState(() {
      _createdSpots = filteredCreated;
      _visitedSpots = filteredVisited;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: CustomScrollView(
        slivers: [
          // 现代化的 AppBar
          SliverAppBar(
            expandedHeight: 180,
            floating: false,
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF667EEA),
                      Color(0xFF764BA2),
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: ScaleTransition(
                            scale: _scaleAnimation,
                            child: Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.location_on,
                                size: 40,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: const Text(
                            '我的钓点',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            leading: IconButton(
              icon: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.arrow_back_ios, color: Colors.white),
              ),
              onPressed: () {
                HapticFeedback.lightImpact();
                Navigator.pop(context);
              },
            ),
            actions: [
              IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.add, color: Colors.white),
                ),
                onPressed: () {
                  HapticFeedback.lightImpact();
                  _navigateToCreateSpot();
                },
              ),
            ],
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(100),
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      offset: Offset(0, -5),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // 筛选和排序栏
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 12),
                      child: Column(
                        children: [
                          // 第一行：筛选和排序按钮
                          Row(
                            children: [
                              Expanded(
                                flex: 1,
                                child: _buildFilterChip(),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                flex: 1,
                                child: _buildSortChip(),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          // 第二行：统计信息
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              _buildStatsInfo(),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // Tab栏
                    TabBar(
                      controller: _tabController,
                      labelColor: const Color(0xFF667EEA),
                      unselectedLabelColor: Colors.grey[600],
                      indicatorColor: const Color(0xFF667EEA),
                      indicatorWeight: 3,
                      indicatorPadding:
                          const EdgeInsets.symmetric(horizontal: 20),
                      tabs: [
                        _buildModernTab('我创建的', _createdTotal),
                        _buildModernTab('我访问的', _visitedTotal),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),

          // 内容区域
          SliverFillRemaining(
            child: Container(
              color: Colors.white,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildCreatedTab(),
                  _buildVisitedTab(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showFilterOptions();
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              const Color(0xFF667EEA).withOpacity(0.1),
              const Color(0xFF764BA2).withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: const Color(0xFF667EEA).withOpacity(0.3),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.filter_list, size: 16, color: Color(0xFF667EEA)),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                _getFilterText(),
                style: const TextStyle(
                  fontSize: 13,
                  color: Color(0xFF667EEA),
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 4),
            const Icon(Icons.arrow_drop_down,
                size: 16, color: Color(0xFF667EEA)),
          ],
        ),
      ),
    );
  }

  Widget _buildSortChip() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showSortOptions();
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              const Color(0xFF764BA2).withOpacity(0.1),
              const Color(0xFF667EEA).withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: const Color(0xFF764BA2).withOpacity(0.3),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.sort, size: 16, color: Color(0xFF764BA2)),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                _getSortText(),
                style: const TextStyle(
                  fontSize: 13,
                  color: Color(0xFF764BA2),
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 4),
            const Icon(Icons.arrow_drop_down,
                size: 16, color: Color(0xFF764BA2)),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsInfo() {
    final currentTabIndex = _tabController.index;
    final total = currentTabIndex == 0 ? _createdTotal : _visitedTotal;
    final label = currentTabIndex == 0 ? '创建' : '访问'; 
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        '共 $total 个${label}钓点',
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[700],
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildModernTab(String label, int count) {
    return Tab(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(label,
              style:
                  const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          const SizedBox(width: 8),
          if (count > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                count.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCreatedTab() {
    if (_error != null && !_isRefreshing) {
      return _buildErrorState(_error!);
    }

    if (_isLoadingCreated && _createdSpots.isEmpty) {
      return _buildLoadingState();
    }

    return RefreshIndicator(
      onRefresh: () => _loadSpots(isRefresh: true),
      child: _createdSpots.isEmpty && !_isLoadingCreated
          ? _buildEmptyState('created')
          : ListView.builder(
              controller: _createdScrollController,
              padding: const EdgeInsets.all(20),
              itemCount: _createdSpots.length + (_isLoadingMoreCreated ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == _createdSpots.length) {
                  return _buildLoadMoreIndicator();
                }
                return _buildModernSpotCard(_createdSpots[index], index, true);
              },
            ),
    );
  }

  Widget _buildVisitedTab() {
    if (_error != null && !_isRefreshing) {
      return _buildErrorState(_error!);
    }

    if (_isLoadingVisited && _visitedSpots.isEmpty) {
      return _buildLoadingState();
    }

    return RefreshIndicator(
      onRefresh: () => _loadSpots(isRefresh: true),
      child: _visitedSpots.isEmpty && !_isLoadingVisited
          ? _buildEmptyState('visited')
          : ListView.builder(
              controller: _visitedScrollController,
              padding: const EdgeInsets.all(20),
              itemCount: _visitedSpots.length + (_isLoadingMoreVisited ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == _visitedSpots.length) {
                  return _buildLoadMoreIndicator();
                }
                return _buildModernSpotCard(_visitedSpots[index], index, false);
              },
            ),
    );
  }

  Widget _buildModernSpotCard(FishingSpotVo spot, int index, bool isCreated) {
    // 创建动画控制器
    if (_itemAnimationControllers.length <= index) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 600 + (index * 50)),
        vsync: this,
      );
      _itemAnimationControllers.add(controller);
      controller.forward();
    }

    return AnimatedBuilder(
      animation: _itemAnimationControllers[index],
      builder: (context, child) {
        final slideAnimation = Tween<Offset>(
          begin: const Offset(0.3, 0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.easeOutBack,
        ));

        final scaleAnimation = Tween<double>(
          begin: 0.8,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.elasticOut,
        ));

        return SlideTransition(
          position: slideAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          context.push('/fishingSpotDetail', extra: {'spotId': spot.id});
        },
        onLongPress: () {
          HapticFeedback.mediumImpact();
          // 只有创建的钓点才能进行编辑、删除等操作
          if (isCreated) {
            _showSpotOptions(spot);
          } else {
            // 访问的钓点只能查看详情或收藏
            _showVisitedSpotOptions(spot);
          }
        },
        child: Container(
          margin: const EdgeInsets.only(bottom: 20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Color(0xFFFBFCFE),
              ],
            ),
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            children: [
              // 图片区域
              Container(
                height: 180,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                  ),
                  gradient: (spot.images == null || spot.images!.isEmpty)
                      ? const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Color(0xFF667EEA),
                            Color(0xFF764BA2),
                          ],
                        )
                      : null,
                ),
                child: Stack(
                  children: [
                    if (spot.images != null && spot.images!.isNotEmpty)
                      ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(25),
                          topRight: Radius.circular(25),
                        ),
                        child: CachedNetworkImage(
                          imageUrl: spot.images!.first,
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                          errorWidget: (context, url, error) => Container(
                            decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Color(0xFF667EEA),
                                  Color(0xFF764BA2),
                                ],
                              ),
                            ),
                            child: const Center(
                              child: Icon(
                                Icons.location_on,
                                size: 60,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      )
                    else
                      Center(
                        child: Icon(
                          Icons.location_on,
                          size: 60,
                          color: Colors.white.withOpacity(0.5),
                        ),
                      ),

                    // 渐变遮罩
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: 80,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.transparent,
                              Colors.black.withOpacity(0.5),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // 顶部标签
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 10,
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            Icon(
                              isCreated ? Icons.create : Icons.directions_walk,
                              size: 14,
                              color: Colors.white,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              isCreated ? '我创建的' : '访问过',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // 官方/用户标签
                    if (isCreated)
                      Positioned(
                        top: 12,
                        right: 12,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: spot.isOfficial
                                ? Colors.green.withOpacity(0.9)
                                : Colors.orange.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                spot.isOfficial ? Icons.verified : Icons.location_on,
                                size: 12,
                                color: Colors.white,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                spot.isOfficial ? '官方' : '用户',
                                style: const TextStyle(
                                  fontSize: 11,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                    // 评分
                    Positioned(
                      bottom: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 10,
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.star,
                                size: 16, color: Colors.amber),
                            const SizedBox(width: 4),
                            Text(
                              spot.rating.toStringAsFixed(1),
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF1A1E25),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 信息区域
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题行
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            spot.name,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1A1E25),
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                const Color(0xFF667EEA).withOpacity(0.1),
                                const Color(0xFF764BA2).withOpacity(0.1),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Text(
                            spot.fishTypeList.isNotEmpty 
                                ? spot.fishTypeList.first.name 
                                : spot.isPaid ? '付费' : '免费',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0xFF667EEA),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // 地址
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.location_on_outlined,
                            size: 16,
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            spot.address,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[700],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // 统计信息
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.grey[50]!,
                            Colors.grey[100]!,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildStatItem(
                            icon: Icons.directions_walk,
                            label: '到访',
                            value: '${spot.visitorCount}次',
                            color: const Color(0xFF667EEA),
                          ),
                          Container(
                            height: 30,
                            width: 1,
                            color: Colors.grey[300],
                          ),
                          _buildStatItem(
                            icon: Icons.how_to_reg,
                            label: '签到',
                            value: '${spot.checkinCount}次',
                            color: const Color(0xFF764BA2),
                          ),
                          Container(
                            height: 30,
                            width: 1,
                            color: Colors.grey[300],
                          ),
                          _buildStatItem(
                            icon: Icons.star,
                            label: '等级',
                            value: '${spot.verificationLevel}级',
                            color: Colors.orange,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, size: 20, color: color),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF667EEA)),
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.red.withOpacity(0.1),
                  Colors.orange.withOpacity(0.1),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.error_outline,
              size: 50,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            '加载失败',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF1A1E25),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _loadSpots();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF667EEA),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              elevation: 5,
            ),
            child: const Text(
              '重新加载',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String type) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF667EEA).withOpacity(0.1),
                  const Color(0xFF764BA2).withOpacity(0.1),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(
              type == 'created' ? Icons.add_location : Icons.explore,
              size: 50,
              color: const Color(0xFF667EEA),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            type == 'created' ? '还没有创建钓点' : '还没有访问记录',
            style: const TextStyle(
              fontSize: 18,
              color: Color(0xFF1A1E25),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            type == 'created' ? '创建你的专属钓点吧' : '去钓点签到打卡，记录你的钓鱼足迹',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              if (type == 'created') {
                _navigateToCreateSpot();
              } else {
                context.push('/fishing_spots');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF667EEA),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              elevation: 5,
            ),
            child: Text(
              type == 'created' ? '创建钓点' : '探索钓点',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterOptions() {
    _bottomSheetController.forward();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => AnimatedBuilder(
        animation: _bottomSheetAnimation,
        builder: (context, child) => Transform.scale(
          scale: _bottomSheetAnimation.value,
          alignment: Alignment.bottomCenter,
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    '筛选钓点',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1A1E25),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                _buildFilterOption('all', '全部类型', Icons.apps),
                _buildFilterOption('public', '官方钓点', Icons.verified),
                _buildFilterOption('private', '用户钓点', Icons.person),
              ],
            ),
          ),
        ),
      ),
    ).then((_) => _bottomSheetController.reverse());
  }

  Widget _buildFilterOption(String value, String label, IconData icon) {
    final isSelected = _filterType == value;
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          gradient: isSelected
              ? const LinearGradient(
                  colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                )
              : null,
          color: isSelected ? null : Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          icon,
          color: isSelected ? Colors.white : Colors.grey[700],
        ),
      ),
      title: Text(
        label,
        style: TextStyle(
          fontSize: 16,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? const Color(0xFF667EEA) : Colors.black87,
        ),
      ),
      trailing: isSelected
          ? const Icon(Icons.check_circle, color: Color(0xFF667EEA))
          : null,
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _filterType = value;
          _sortAndFilterSpots();
        });
        Navigator.pop(context);
      },
    );
  }

  void _showSortOptions() {
    _bottomSheetController.forward();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => AnimatedBuilder(
        animation: _bottomSheetAnimation,
        builder: (context, child) => Transform.scale(
          scale: _bottomSheetAnimation.value,
          alignment: Alignment.bottomCenter,
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(25),
                topRight: Radius.circular(25),
              ),
            ),
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    '排序方式',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1A1E25),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                _buildSortOption('newest', '最新创建', Icons.access_time),
                _buildSortOption('mostVisited', '访问最多', Icons.trending_up),
                _buildSortOption('rating', '评分最高', Icons.star),
              ],
            ),
          ),
        ),
      ),
    ).then((_) => _bottomSheetController.reverse());
  }

  Widget _buildSortOption(String value, String label, IconData icon) {
    final isSelected = _sortBy == value;
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          gradient: isSelected
              ? const LinearGradient(
                  colors: [Color(0xFF764BA2), Color(0xFF667EEA)],
                )
              : null,
          color: isSelected ? null : Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(
          icon,
          color: isSelected ? Colors.white : Colors.grey[700],
        ),
      ),
      title: Text(
        label,
        style: TextStyle(
          fontSize: 16,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected ? const Color(0xFF764BA2) : Colors.black87,
        ),
      ),
      trailing: isSelected
          ? const Icon(Icons.check_circle, color: Color(0xFF764BA2))
          : null,
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _sortBy = value;
          _sortAndFilterSpots();
        });
        Navigator.pop(context);
      },
    );
  }

  void _showSpotOptions(FishingSpotVo spot) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF667EEA).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.edit, color: Color(0xFF667EEA)),
              ),
              title: const Text(
                '编辑钓点',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _editSpot(spot);
              },
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF764BA2).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.share, color: Color(0xFF764BA2)),
              ),
              title: const Text(
                '分享钓点',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _shareSpot(spot);
              },
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.delete, color: Colors.red),
              ),
              title: const Text(
                '删除钓点',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.red,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                HapticFeedback.mediumImpact();
                _showDeleteConfirmation(spot);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showVisitedSpotOptions(FishingSpotVo spot) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF667EEA).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.info_outline, color: Color(0xFF667EEA)),
              ),
              title: const Text(
                '查看详情',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                context.push('/fishingSpotDetail', extra: {'spotId': spot.id});
              },
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF764BA2).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.share, color: Color(0xFF764BA2)),
              ),
              title: const Text(
                '分享钓点',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _shareSpot(spot);
              },
            ),
            ListTile(
              leading: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.favorite_outline, color: Colors.orange),
              ),
              title: const Text(
                '收藏钓点',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _favoriteSpot(spot);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(FishingSpotVo spot) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text('删除钓点'),
        content: Text('确定要删除"${spot.name}"吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteSpot(spot);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  String _getFilterText() {
    switch (_filterType) {
      case 'public':
        return '官方钓点';
      case 'private':
        return '用户钓点';
      default:
        return '全部类型';
    }
  }

  String _getSortText() {
    switch (_sortBy) {
      case 'newest':
        return '最新创建';
      case 'mostVisited':
        return '访问最多';
      case 'rating':
        return '评分最高';
      default:
        return '默认排序';
    }
  }

  // 编辑钓点
  void _editSpot(FishingSpotVo spot) {
    // TODO: 实现编辑钓点功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('编辑钓点 "${spot.name}" 功能开发中...'),
        backgroundColor: const Color(0xFF667EEA),
      ),
    );
  }

  // 分享钓点
  void _shareSpot(FishingSpotVo spot) {
    // TODO: 实现分享钓点功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('分享钓点 "${spot.name}" 功能开发中...'),
        backgroundColor: const Color(0xFF764BA2),
      ),
    );
  }

  // 删除钓点
  Future<void> _deleteSpot(FishingSpotVo spot) async {
    try {
      // 调用删除API
      await _fishingSpotApi.deleteFishingSpot(spot.id);
      
      setState(() {
        _createdSpots.removeWhere((s) => s.id == spot.id);
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('钓点已删除'),
            backgroundColor: Color(0xFF667EEA),
          ),
        );
      }
    } catch (e) {
      debugPrint('删除钓点失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('删除失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 导航到创建钓点页面
  void _navigateToCreateSpot() {
    if (!_authViewModel.isUserLoggedIn()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先登录后再创建钓点'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CreateNewSpotPage(
          onSpotCreated: (spotId) {
            // 创建成功后刷新数据
            _loadSpots(isRefresh: true);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('钓点创建成功！'),
                backgroundColor: Color(0xFF667EEA),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLoadMoreIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[400]!),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            '加载更多...',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // 收藏钓点
  void _favoriteSpot(FishingSpotVo spot) {
    // TODO: 实现收藏钓点功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('收藏钓点 "${spot.name}" 功能开发中...'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}