import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';
import 'package:url_launcher/url_launcher.dart';

class RoutePlanningPage extends StatefulWidget {
  final String address;
  final LngLat destination;

  const RoutePlanningPage({
    super.key,
    required this.address,
    required this.destination,
  });

  @override
  State<RoutePlanningPage> createState() => _RoutePlanningPageState();
}

class _RoutePlanningPageState extends State<RoutePlanningPage> {
  late AMap2DController? _mapController;
  DrivingResultModel? _result;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('路线规划'),
      ),
      body: Stack(
        children: [
          AMap2DView(
            onAMap2DViewCreated: (controller) async {
              _mapController = controller;
            },
            onGetLocation: (LngLat location) async {
              final startPoint = LngLat(location.longitude, location.latitude);
              final endPoint = widget.destination;
              await _mapController!.searchDrivingRoute(
                fromLat: startPoint.latitude,
                fromLon: startPoint.longitude,
                toLat: endPoint.latitude,
                toLon: endPoint.longitude,
              );
            },
            onDrivingRouteSearched: (DrivingResultModel result) async {
              setState(() {
                _result = result;
              });
              List<LngLat> points = [];
              for (final route in result.routes) {
                for (final step in route.steps) {
                  points.addAll(step.path);
                }
              }
              await _mapController!
                  .polyline(result.origin, result.destination, points);
            },
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '目的地',
                    style: Theme.of(context).textTheme.titleSmall,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.address,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 16),
                  if (_result != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Text(
                          '距离：${_result!.routes.first.distance > 1000 ? '${(_result!.routes.first.distance / 1000).toStringAsFixed(2)}公里' : '${_result!.routes.first.distance}米'}'),
                    ),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () async {
                        final lat = widget.destination.latitude;
                        final lon = widget.destination.longitude;

                        if (kIsWeb) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('请在手机端使用导航功能'),
                            ),
                          );
                        } else {
                          if (Platform.isIOS) {
                            final url =
                                'iosamap://path?sourceApplication=yunmiao&dlat=$lat&dlon=$lon&dname=${Uri.encodeComponent(widget.address)}&dev=0&t=0';
                            if (await canLaunchUrl(Uri.parse(url))) {
                              await launchUrl(Uri.parse(url));
                            }
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('请安装高德地图app')),
                              );
                            }
                          } else if (Platform.isAndroid) {
                            final url =
                                'androidamap://route?sourceApplication=yunmiao&dlat=$lat&dlon=$lon&dname=${Uri.encodeComponent(widget.address)}&dev=0&t=0';
                            if (await canLaunchUrl(Uri.parse(url))) {
                              await launchUrl(Uri.parse(url));
                            }
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('导航'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
