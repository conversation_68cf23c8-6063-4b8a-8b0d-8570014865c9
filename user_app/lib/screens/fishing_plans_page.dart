import 'dart:math' show sin, pi;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class FishingPlansPage extends StatefulWidget {
  const FishingPlansPage({super.key});

  @override
  State<FishingPlansPage> createState() => _FishingPlansPageState();
}

class _FishingPlansPageState extends State<FishingPlansPage>
    with TickerProviderStateMixin {
  // Tab控制器
  late TabController _tabController;

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _rotateController;
  late AnimationController _waveController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _rotateAnimation;
  late Animation<double> _waveAnimation;

  // 列表项动画
  final List<AnimationController> _itemControllers = [];

  // 搜索和筛选
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String _searchQuery = '';
  String _selectedFilter = 'all'; // all, upcoming, completed, cancelled

  // 计划数据
  final List<FishingPlan> _plans = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _rotateController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _waveController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    _rotateAnimation = Tween<double>(
      begin: 0,
      end: 0.05,
    ).animate(CurvedAnimation(
      parent: _rotateController,
      curve: Curves.easeInOut,
    ));
    _waveAnimation = CurvedAnimation(
      parent: _waveController,
      curve: Curves.easeInOut,
    );

    // 启动动画
    _fadeController.forward();
    _slideController.forward();
    _rotateController.forward();

    // 加载数据
    _loadPlans();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _rotateController.dispose();
    _waveController.dispose();
    _searchController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadPlans() {
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _plans.addAll([
          FishingPlan(
            id: 1,
            title: '东湖夜钓大作战',
            location: '东湖公园钓场',
            planDate: DateTime.now().add(const Duration(days: 2)),
            timeRange: '19:00 - 23:00',
            maxParticipants: 5,
            currentParticipants: 3,
            weather: '多云',
            temperature: '22°C - 18°C',
            description: '夜钓鲫鱼和鲤鱼，需要带好夜钓装备和照明设备。钓位已预定，费用AA制。',
            ownerId: 1,
            ownerName: '钓鱼达人小李',
            spotId: 101,
            contactInfo: '微信: fisher_lee',
            isPublic: true,
            status: 'upcoming',
            isJoined: true,
          ),
          FishingPlan(
            id: 2,
            title: '西山水库周末垂钓',
            location: '西山水库南岸',
            planDate: DateTime.now().add(const Duration(days: 5)),
            timeRange: '06:00 - 12:00',
            maxParticipants: 4,
            currentParticipants: 2,
            weather: '晴天',
            temperature: '25°C - 20°C',
            description: '早晨垂钓草鱼和鲤鱼，水库环境优美，鱼情较好。自带早餐。',
            ownerId: 2,
            ownerName: '老王爱钓鱼',
            spotId: 102,
            contactInfo: '电话: 138xxxx5678',
            isPublic: true,
            status: 'upcoming',
            isJoined: false,
          ),
          FishingPlan(
            id: 3,
            title: '南河钓友聚会',
            location: '南河休闲钓场',
            planDate: DateTime.now().subtract(const Duration(days: 3)),
            timeRange: '08:00 - 16:00',
            maxParticipants: 6,
            currentParticipants: 6,
            weather: '阴天',
            temperature: '18°C - 15°C',
            description: '钓友聚会活动圆满结束，大家收获颇丰，期待下次再聚！',
            ownerId: 1,
            ownerName: '钓鱼达人小李',
            spotId: 103,
            contactInfo: '微信群: 南河钓友会',
            isPublic: true,
            status: 'completed',
            isJoined: true,
          ),
        ]);
        _isLoading = false;
      });
    });
  }

  AnimationController _createItemController(int index) {
    // 确保列表有足够的控制器
    while (index >= _itemControllers.length) {
      final controllerIndex = _itemControllers.length;
      final controller = AnimationController(
        duration: Duration(milliseconds: 400 + (controllerIndex * 100)),
        vsync: this,
      );
      _itemControllers.add(controller);
      controller.forward();
    }
    return _itemControllers[index];
  }

  Future<void> _onRefresh() async {
    HapticFeedback.mediumImpact();
    await Future.delayed(const Duration(seconds: 2));
    // TODO: 刷新数据
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: Stack(
        children: [
          // 渐变背景
          Container(
            height: 320,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFFFF8066),
                  Color(0xFFFF6B6B),
                ],
              ),
            ),
          ),
          // 波浪装饰
          Positioned(
            bottom: -50,
            left: 0,
            right: 0,
            child: AnimatedBuilder(
              animation: _waveAnimation,
              builder: (context, child) {
                return CustomPaint(
                  size: Size(MediaQuery.of(context).size.width, 100),
                  painter: WavePainter(_waveAnimation.value),
                );
              },
            ),
          ),
          // 主内容
          SafeArea(
            child: Column(
              children: [
                // 自定义AppBar
                _buildCustomAppBar(),
                // Tab栏
                if (!_isSearching) _buildTabBar(),
                // 内容区域
                Expanded(
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: _buildContent(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _rotateAnimation,
        builder: (context, child) {
          return Transform.rotate(
            angle: _rotateAnimation.value,
            child: FloatingActionButton.extended(
              onPressed: () {
                HapticFeedback.lightImpact();
                _createNewPlan();
              },
              backgroundColor: const Color(0xFFFF8066),
              icon: const Icon(Icons.add, color: Colors.white),
              label: const Text(
                '创建计划',
                style:
                    TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // 返回按钮
          IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.arrow_back_ios,
                  color: Colors.white, size: 20),
            ),
            onPressed: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
          ),
          // 标题或搜索框
          Expanded(
            child: _isSearching ? _buildSearchBar() : _buildTitle(),
          ),
          // 搜索/筛选按钮
          Row(
            children: [
              IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _isSearching ? Icons.close : Icons.search,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                onPressed: () {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _isSearching = !_isSearching;
                    if (!_isSearching) {
                      _searchController.clear();
                      _searchQuery = '';
                    }
                  });
                },
              ),
              IconButton(
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.filter_list,
                      color: Colors.white, size: 20),
                ),
                onPressed: () {
                  HapticFeedback.lightImpact();
                  _showFilterOptions();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return const Text(
      '钓鱼计划',
      style: TextStyle(
        color: Colors.white,
        fontSize: 24,
        fontWeight: FontWeight.bold,
        letterSpacing: 0.8,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
      ),
      child: TextField(
        controller: _searchController,
        autofocus: true,
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          hintText: '搜索钓鱼计划...',
          hintStyle: TextStyle(color: Colors.white.withOpacity(0.7)),
          border: InputBorder.none,
          icon: const Icon(Icons.search, color: Colors.white, size: 20),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: const Color(0xFFFF8066),
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: const Color(0xFFFF8066),
        indicatorWeight: 3,
        indicatorPadding: const EdgeInsets.symmetric(horizontal: 8),
        tabs: const [
          Tab(text: '即将开始'),
          Tab(text: '我的计划'),
          Tab(text: '已完成'),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildPlansList('upcoming'),
        _buildPlansList('my'),
        _buildPlansList('completed'),
      ],
    );
  }

  Widget _buildPlansList(String type) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFFFF8066)),
      );
    }

    List<FishingPlan> filteredPlans = _plans.where((plan) {
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!plan.title.toLowerCase().contains(query) &&
            !plan.location.toLowerCase().contains(query)) {
          return false;
        }
      }

      switch (type) {
        case 'upcoming':
          return plan.status == 'upcoming';
        case 'my':
          return plan.isJoined || plan.ownerId == 1; // 假设当前用户ID为1
        case 'completed':
          return plan.status == 'completed';
        default:
          return true;
      }
    }).toList();

    if (filteredPlans.isEmpty) {
      return _buildEmptyState(type);
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      color: const Color(0xFFFF8066),
      child: ListView.builder(
        padding: const EdgeInsets.only(bottom: 80),
        itemCount: filteredPlans.length,
        itemBuilder: (context, index) {
          return _buildPlanCard(filteredPlans[index], index);
        },
      ),
    );
  }

  Widget _buildPlanCard(FishingPlan plan, int index) {
    final controller = _createItemController(index);
    final isCompleted = plan.status == 'completed';
    final isCancelled = plan.status == 'cancelled';
    final isOwner = plan.ownerId == 1; // 假设当前用户ID为1

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (controller.value * 0.2),
          child: Opacity(
            opacity: controller.value,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isCompleted
                      ? [Colors.grey[300]!, Colors.grey[400]!]
                      : isCancelled
                          ? [Colors.red[100]!, Colors.red[200]!]
                          : [Colors.white, const Color(0xFFFBFCFE)],
                ),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: InkWell(
                onTap: () => _viewPlanDetail(plan),
                borderRadius: BorderRadius.circular(24),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 头部信息
                      Row(
                        children: [
                          // 日期标签
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: isCompleted
                                    ? [Colors.grey[500]!, Colors.grey[600]!]
                                    : [
                                        const Color(0xFFFF8066),
                                        const Color(0xFFFF6B6B)
                                      ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              children: [
                                Text(
                                  plan.planDate.day.toString(),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  _getMonthName(plan.planDate.month),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 16),
                          // 标题和创建者
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  plan.title,
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: isCompleted
                                        ? Colors.grey[700]
                                        : Colors.grey[800],
                                    decoration: isCancelled
                                        ? TextDecoration.lineThrough
                                        : null,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Icon(Icons.person,
                                        size: 14, color: Colors.grey[600]),
                                    const SizedBox(width: 4),
                                    Text(
                                      plan.ownerName,
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    if (isOwner) ...[
                                      const SizedBox(width: 8),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 6, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: const Color(0xFFFF8066)
                                              .withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: const Text(
                                          '创建者',
                                          style: TextStyle(
                                            fontSize: 11,
                                            color: Color(0xFFFF8066),
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ],
                            ),
                          ),
                          // 状态标签
                          if (plan.status != 'upcoming')
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: _getStatusColor(plan.status)
                                    .withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                _getStatusText(plan.status),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: _getStatusColor(plan.status),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // 详细信息
                      _buildInfoRow(Icons.location_on, plan.location),
                      const SizedBox(height: 8),
                      _buildInfoRow(Icons.access_time, plan.timeRange),
                      const SizedBox(height: 8),
                      _buildInfoRow(Icons.wb_sunny,
                          '${plan.weather} ${plan.temperature}'),
                      const SizedBox(height: 12),
                      // 描述
                      Text(
                        plan.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                          height: 1.4,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 16),
                      // 底部操作栏
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // 参与人数
                          Row(
                            children: [
                              Icon(Icons.group,
                                  size: 20, color: Colors.grey[600]),
                              const SizedBox(width: 6),
                              Text(
                                '${plan.currentParticipants}/${plan.maxParticipants}人',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[700],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(width: 8),
                              // 参与者头像
                              ...List.generate(
                                plan.currentParticipants > 3
                                    ? 3
                                    : plan.currentParticipants,
                                (index) => Container(
                                  margin: const EdgeInsets.only(left: 4),
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [
                                        Color(0xFFFF8066),
                                        Color(0xFFFF6B6B)
                                      ],
                                    ),
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 2,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      'U',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              if (plan.currentParticipants > 3)
                                Container(
                                  margin: const EdgeInsets.only(left: 4),
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[300],
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 2,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      '+${plan.currentParticipants - 3}',
                                      style: TextStyle(
                                        color: Colors.grey[700],
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          // 操作按钮
                          if (!isCompleted && !isCancelled)
                            _buildActionButton(plan, isOwner),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(FishingPlan plan, bool isOwner) {
    if (isOwner) {
      return PopupMenuButton<String>(
        onSelected: (value) => _handleOwnerAction(value, plan),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFFF8066), Color(0xFFFF6B6B)],
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '管理',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(width: 4),
              Icon(Icons.arrow_drop_down, color: Colors.white, size: 20),
            ],
          ),
        ),
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'edit',
            child: Row(
              children: [
                Icon(Icons.edit, size: 20),
                SizedBox(width: 8),
                Text('编辑'),
              ],
            ),
          ),
          const PopupMenuItem(
            value: 'cancel',
            child: Row(
              children: [
                Icon(Icons.cancel, size: 20, color: Colors.red),
                SizedBox(width: 8),
                Text('取消计划', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
        ],
      );
    } else if (plan.isJoined) {
      return GestureDetector(
        onTap: () => _leavePlan(plan),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[400]!),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            '退出',
            style: TextStyle(
              color: Colors.grey[700],
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );
    } else if (plan.currentParticipants < plan.maxParticipants) {
      return GestureDetector(
        onTap: () => _joinPlan(plan),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFFF8066), Color(0xFFFF6B6B)],
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Text(
            '加入',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          '已满',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }
  }

  Widget _buildEmptyState(String type) {
    String message;
    IconData icon;

    switch (type) {
      case 'upcoming':
        message = '暂无即将开始的计划';
        icon = Icons.event_available;
        break;
      case 'my':
        message = '您还没有参与任何计划';
        icon = Icons.event_note;
        break;
      case 'completed':
        message = '暂无已完成的计划';
        icon = Icons.event_available;
        break;
      default:
        message = '暂无计划';
        icon = Icons.event;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFFFF8066).withOpacity(0.1),
                  const Color(0xFFFF6B6B).withOpacity(0.1),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 60,
              color: const Color(0xFFFF8066),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            message,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1A1E25),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '创建或加入钓鱼计划',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  void _createNewPlan() {
    // TODO: 导航到创建计划页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('创建计划功能开发中')),
    );
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              '筛选计划',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            _buildFilterOption('全部', 'all'),
            _buildFilterOption('即将开始', 'upcoming'),
            _buildFilterOption('已完成', 'completed'),
            _buildFilterOption('已取消', 'cancelled'),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterOption(String label, String value) {
    final isSelected = _selectedFilter == value;

    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _selectedFilter = value;
        });
        Navigator.pop(context);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isSelected ? const Color(0xFFFF8066) : Colors.grey[400]!,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: Color(0xFFFF8066),
                        ),
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                color: isSelected ? const Color(0xFFFF8066) : Colors.grey[800],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      '',
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月'
    ];
    return months[month];
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return const Color(0xFFFF8066);
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      default:
        return '即将开始';
    }
  }

  void _viewPlanDetail(FishingPlan plan) {
    // TODO: 导航到计划详情页面
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('查看 ${plan.title} 详情')),
    );
  }

  void _handleOwnerAction(String action, FishingPlan plan) {
    switch (action) {
      case 'edit':
        // TODO: 编辑计划
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('编辑 ${plan.title}')),
        );
        break;
      case 'cancel':
        // TODO: 取消计划
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('取消 ${plan.title}')),
        );
        break;
    }
  }

  void _joinPlan(FishingPlan plan) {
    setState(() {
      plan.isJoined = true;
      plan.currentParticipants++;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('已加入 ${plan.title}')),
    );
  }

  void _leavePlan(FishingPlan plan) {
    setState(() {
      plan.isJoined = false;
      plan.currentParticipants--;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('已退出 ${plan.title}')),
    );
  }
}

// 钓鱼计划数据模型
class FishingPlan {
  final int id;
  final String title;
  final String location;
  final DateTime planDate;
  final String timeRange;
  final int maxParticipants;
  int currentParticipants;
  final String weather;
  final String temperature;
  final String description;
  final int ownerId;
  final String ownerName;
  final int? spotId;
  final String? contactInfo;
  final bool isPublic;
  final String status;
  bool isJoined;

  FishingPlan({
    required this.id,
    required this.title,
    required this.location,
    required this.planDate,
    required this.timeRange,
    required this.maxParticipants,
    required this.currentParticipants,
    required this.weather,
    required this.temperature,
    required this.description,
    required this.ownerId,
    required this.ownerName,
    this.spotId,
    this.contactInfo,
    required this.isPublic,
    required this.status,
    required this.isJoined,
  });
}

// 波浪画笔
class WavePainter extends CustomPainter {
  final double animationValue;

  WavePainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = 20.0;
    final waveLength = size.width / 3;

    path.moveTo(0, size.height);

    for (double x = 0; x <= size.width; x++) {
      final y = waveHeight *
              sin((x / waveLength * 2 * pi) + (animationValue * 2 * pi)) +
          size.height / 2;
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
