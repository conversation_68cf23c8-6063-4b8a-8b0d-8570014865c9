// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'page_result.dart';

class PageResultMapper extends ClassMapperBase<PageResult> {
  PageResultMapper._();

  static PageResultMapper? _instance;
  static PageResultMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = PageResultMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'PageResult';
  @override
  Function get typeFactory => <T>(f) => f<PageResult<T>>();

  static List<dynamic> _$records(PageResult v) => v.records;
  static dynamic _arg$records<T>(f) => f<List<T>>();
  static const Field<PageResult, List<dynamic>> _f$records =
      Field('records', _$records, arg: _arg$records);
  static int _$total(PageResult v) => v.total;
  static const Field<PageResult, int> _f$total = Field('total', _$total);
  static int _$current(PageResult v) => v.current;
  static const Field<PageResult, int> _f$current = Field('current', _$current);
  static int _$size(PageResult v) => v.size;
  static const Field<PageResult, int> _f$size = Field('size', _$size);
  static int _$pages(PageResult v) => v.pages;
  static const Field<PageResult, int> _f$pages = Field('pages', _$pages);

  @override
  final MappableFields<PageResult> fields = const {
    #records: _f$records,
    #total: _f$total,
    #current: _f$current,
    #size: _f$size,
    #pages: _f$pages,
  };

  static PageResult<T> _instantiate<T>(DecodingData data) {
    return PageResult(
        records: data.dec(_f$records),
        total: data.dec(_f$total),
        current: data.dec(_f$current),
        size: data.dec(_f$size),
        pages: data.dec(_f$pages));
  }

  @override
  final Function instantiate = _instantiate;

  static PageResult<T> fromMap<T>(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<PageResult<T>>(map);
  }

  static PageResult<T> fromJson<T>(String json) {
    return ensureInitialized().decodeJson<PageResult<T>>(json);
  }
}

mixin PageResultMappable<T> {
  String toJson() {
    return PageResultMapper.ensureInitialized()
        .encodeJson<PageResult<T>>(this as PageResult<T>);
  }

  Map<String, dynamic> toMap() {
    return PageResultMapper.ensureInitialized()
        .encodeMap<PageResult<T>>(this as PageResult<T>);
  }

  PageResultCopyWith<PageResult<T>, PageResult<T>, PageResult<T>, T>
      get copyWith => _PageResultCopyWithImpl<PageResult<T>, PageResult<T>, T>(
          this as PageResult<T>, $identity, $identity);
  @override
  String toString() {
    return PageResultMapper.ensureInitialized()
        .stringifyValue(this as PageResult<T>);
  }

  @override
  bool operator ==(Object other) {
    return PageResultMapper.ensureInitialized()
        .equalsValue(this as PageResult<T>, other);
  }

  @override
  int get hashCode {
    return PageResultMapper.ensureInitialized()
        .hashValue(this as PageResult<T>);
  }
}

extension PageResultValueCopy<$R, $Out, T>
    on ObjectCopyWith<$R, PageResult<T>, $Out> {
  PageResultCopyWith<$R, PageResult<T>, $Out, T> get $asPageResult =>
      $base.as((v, t, t2) => _PageResultCopyWithImpl<$R, $Out, T>(v, t, t2));
}

abstract class PageResultCopyWith<$R, $In extends PageResult<T>, $Out, T>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, T, ObjectCopyWith<$R, T, T>> get records;
  $R call({List<T>? records, int? total, int? current, int? size, int? pages});
  PageResultCopyWith<$R2, $In, $Out2, T> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _PageResultCopyWithImpl<$R, $Out, T>
    extends ClassCopyWithBase<$R, PageResult<T>, $Out>
    implements PageResultCopyWith<$R, PageResult<T>, $Out, T> {
  _PageResultCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<PageResult> $mapper =
      PageResultMapper.ensureInitialized();
  @override
  ListCopyWith<$R, T, ObjectCopyWith<$R, T, T>> get records => ListCopyWith(
      $value.records,
      (v, t) => ObjectCopyWith(v, $identity, t),
      (v) => call(records: v));
  @override
  $R call(
          {List<T>? records,
          int? total,
          int? current,
          int? size,
          int? pages}) =>
      $apply(FieldCopyWithData({
        if (records != null) #records: records,
        if (total != null) #total: total,
        if (current != null) #current: current,
        if (size != null) #size: size,
        if (pages != null) #pages: pages
      }));
  @override
  PageResult<T> $make(CopyWithData data) => PageResult(
      records: data.get(#records, or: $value.records),
      total: data.get(#total, or: $value.total),
      current: data.get(#current, or: $value.current),
      size: data.get(#size, or: $value.size),
      pages: data.get(#pages, or: $value.pages));

  @override
  PageResultCopyWith<$R2, PageResult<T>, $Out2, T> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _PageResultCopyWithImpl<$R2, $Out2, T>($value, $cast, t);
}
