import 'package:collection/collection.dart'; // For firstWhereOrNull

class PaginatedState<T> {
  final List<T> items;
  final bool isLoading;
  final bool
      isRefreshing; // Added to distinguish initial/refresh load from 'load more'
  final bool hasMore;
  final int currentPage; // Represents the *last successfully loaded* page
  final String? error;

  // Calculated property for convenience
  bool get hasError => error != null;

  bool get isInitialLoad =>
      currentPage == 0 && isLoading; // Page 0 means nothing loaded yet
  bool get isEmpty => items.isEmpty && !isLoading && !hasError;

  const PaginatedState({
    this.items = const [],
    this.isLoading = false,
    this.isRefreshing = false,
    this.hasMore = true, // Assume there's more initially
    this.currentPage = 0, // Start at page 0 (meaning no page loaded yet)
    this.error,
  });

  PaginatedState<T> copyWith({
    List<T>? items,
    bool? isLoading,
    bool? isRefreshing,
    bool? hasMore,
    int? currentPage,
    String? error, // Allow setting error to null explicitly
    bool clearError = false, // Helper flag to explicitly clear error
  }) {
    return PaginatedState<T>(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      error: clearError ? null : error ?? this.error, // Handle error clearing
    );
  }

  PaginatedState<T> reset() {
    return PaginatedState<T>(
      items: const [],
      isLoading: false,
      isRefreshing: false,
      hasMore: true,
      currentPage: 0,
      error: null,
    );
  }

  /// Sets the state to loading, optionally indicating a refresh.
  /// Clears any previous error.
  PaginatedState<T> startLoading({bool isRefreshing = false}) {
    return copyWith(
      isLoading: true,
      isRefreshing: isRefreshing,
      clearError: true, // Clear previous errors on new load attempt
    );
  }

  /// Updates the state upon successful data loading.
  /// - `newItems`: The list of items fetched for the current page.
  /// - `pageLoaded`: The page number that was successfully loaded (e.g., 1, 2, ...).
  /// - `hasMore`: Indicates if there are potentially more pages after this one.
  PaginatedState<T> loadSuccess(List<T> newItems,
      {required int pageLoaded, required bool hasMore}) {
    // If it was a refresh (page 1) or the first load ever, replace items.
    // Otherwise, append.
    final updatedItems = (pageLoaded == 1) ? newItems : [...items, ...newItems];

    return copyWith(
      items: updatedItems,
      isLoading: false,
      isRefreshing: false,
      // Loading finished
      hasMore: hasMore,
      currentPage: pageLoaded,
      // Set to the page that was just loaded
      clearError: true, // Clear any previous error on success
    );
  }

  /// Updates the state upon a data loading failure.
  PaginatedState<T> loadFailure(String errorMessage) {
    return copyWith(
      isLoading: false,
      isRefreshing: false, // Loading finished (with failure)
      error: errorMessage,
      // Keep existing items, hasMore, and currentPage
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaginatedState<T> &&
          runtimeType == other.runtimeType &&
          const ListEquality()
              .equals(items, other.items) && // Deep list equality
          isLoading == other.isLoading &&
          isRefreshing == other.isRefreshing &&
          hasMore == other.hasMore &&
          currentPage == other.currentPage &&
          error == other.error;

  @override
  int get hashCode =>
      const ListEquality().hash(items) ^ // Use list hash
      isLoading.hashCode ^
      isRefreshing.hashCode ^
      hasMore.hashCode ^
      currentPage.hashCode ^
      error.hashCode;

  @override
  String toString() {
    return 'PaginatedState<${T.toString()}>('
        'items: ${items.length}, '
        'isLoading: $isLoading, '
        'isRefreshing: $isRefreshing, '
        'hasMore: $hasMore, '
        'currentPage: $currentPage, '
        'error: $error'
        ')';
  }
}

/// Extension method to update a specific item within the PaginatedState list.
/// Useful for cases like marking an item as favorite/unfavorite without a full reload.
extension PaginatedStateUpdate<T> on PaginatedState<T> {
  PaginatedState<T> updateItem(T updatedItem, bool Function(T item) test) {
    final index = items.indexWhere(test);
    if (index != -1) {
      final newItems = List<T>.from(items); // Create a mutable copy
      newItems[index] = updatedItem;
      // Return a new state instance with the updated list
      return copyWith(items: newItems);
    }
    // Return the original state if the item wasn't found
    return this;
  }

  /// Removes an item matching the test condition.
  PaginatedState<T> removeItem(bool Function(T item) test) {
    final index = items.indexWhere(test);
    if (index != -1) {
      final newItems = List<T>.from(items); // Create a mutable copy
      newItems.removeAt(index);
      // Return a new state instance with the updated list
      return copyWith(items: newItems);
    }
    // Return the original state if the item wasn't found
    return this;
  }

  /// Finds the first item matching the test or returns null.
  T? findItem(bool Function(T item) test) {
    return items.firstWhereOrNull(test);
  }
}
