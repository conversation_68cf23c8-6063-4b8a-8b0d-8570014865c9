import 'package:dart_mappable/dart_mappable.dart';

part 'page_result.mapper.dart';

@MappableClass()
class PageResult<T> with PageResultMappable<T> {
  final List<T> records;
  final int total;
  final int current;
  final int size;
  final int pages;

  const PageResult({
    required this.records,
    required this.total,
    required this.current,
    required this.size,
    required this.pages,
  });

  bool get hasNext => current < pages;
  bool get hasPrevious => current > 1;
  bool get isEmpty => records.isEmpty;
  bool get isNotEmpty => records.isNotEmpty;

  // 自定义fromMap方法来处理泛型转换
  static PageResult<T> fromMap<T>(Map<String, dynamic> map, T Function(dynamic) converter) {
    final recordsList = (map['records'] as List<dynamic>? ?? [])
        .map(converter)
        .toList();
    
    return PageResult<T>(
      records: recordsList,
      total: map['total'] as int? ?? 0,
      current: map['current'] as int? ?? 0,
      size: map['size'] as int? ?? 0,
      pages: map['pages'] as int? ?? 0,
    );
  }

  static const fromJson = PageResultMapper.fromJson;
}