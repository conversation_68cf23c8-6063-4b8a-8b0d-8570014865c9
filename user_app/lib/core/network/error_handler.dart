import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:user_app/core/network/api_error.dart';

/// 统一的错误处理器
class ErrorHandler {
  /// 处理Dio异常
  static ApiError handleDioException(DioException e) {
    debugPrint('🔴 Dio异常: ${e.type} - ${e.message}');
    
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return ApiError(
          code: 408,
          message: '连接超时，请检查网络连接',
        );
        
      case DioExceptionType.sendTimeout:
        return ApiError(
          code: 408,
          message: '发送请求超时，请重试',
        );
        
      case DioExceptionType.receiveTimeout:
        return ApiError(
          code: 408,
          message: '服务器响应超时，请重试',
        );
        
      case DioExceptionType.badResponse:
        return _handleBadResponse(e);
        
      case DioExceptionType.cancel:
        return ApiError(
          code: 499,
          message: '请求已取消',
        );
        
      case DioExceptionType.connectionError:
        return ApiError(
          code: 503,
          message: '网络连接错误，请检查网络状态',
        );
        
      case DioExceptionType.badCertificate:
        return ApiError(
          code: 495,
          message: 'SSL证书验证失败',
        );
        
      case DioExceptionType.unknown:
        return ApiError(
          code: 500,
          message: '未知网络错误: ${e.message ?? "Unknown error"}',
        );
    }
  }

  /// 处理HTTP响应错误
  static ApiError _handleBadResponse(DioException e) {
    final response = e.response;
    if (response == null) {
      return ApiError(
        code: 500,
        message: '服务器无响应',
      );
    }

    final statusCode = response.statusCode ?? 500;
    String message = _getHttpErrorMessage(statusCode);

    // 尝试从响应中提取具体错误信息
    try {
      final data = response.data;
      if (data is Map<String, dynamic>) {
        // 检查标准错误格式
        if (data.containsKey('message')) {
          message = data['message'] as String? ?? message;
        }
        // 检查其他常见错误字段
        else if (data.containsKey('error')) {
          message = data['error'] as String? ?? message;
        }
        else if (data.containsKey('msg')) {
          message = data['msg'] as String? ?? message;
        }
      }
    } catch (e) {
      debugPrint('⚠️ 解析错误响应失败: $e');
    }

    return ApiError(
      code: statusCode,
      message: message,
    );
  }

  /// 获取HTTP状态码对应的错误信息
  static String _getHttpErrorMessage(int statusCode) {
    switch (statusCode) {
      case 400:
        return '请求参数有误';
      case 401:
        return '未授权，请重新登录';
      case 403:
        return '无权限访问此资源';
      case 404:
        return '请求的资源不存在';
      case 405:
        return '不支持的请求方法';
      case 408:
        return '请求超时';
      case 409:
        return '资源冲突';
      case 410:
        return '资源已被删除';
      case 422:
        return '请求参数验证失败';
      case 429:
        return '请求过于频繁，请稍后重试';
      case 500:
        return '服务器内部错误';
      case 502:
        return '网关错误';
      case 503:
        return '服务暂不可用';
      case 504:
        return '网关超时';
      default:
        return '服务器错误 ($statusCode)';
    }
  }

  /// 处理业务逻辑错误
  static ApiError handleBusinessError(int code, String message) {
    // 根据业务错误码返回用户友好的错误信息
    switch (code) {
      case 10001:
        return ApiError(code: code, message: '用户不存在');
      case 10002:
        return ApiError(code: code, message: '密码错误');
      case 10003:
        return ApiError(code: code, message: '验证码错误');
      case 10004:
        return ApiError(code: code, message: '验证码已过期');
      case 10005:
        return ApiError(code: code, message: '用户已存在');
      case 20001:
        return ApiError(code: code, message: '内容不存在');
      case 20002:
        return ApiError(code: code, message: '内容已被删除');
      case 30001:
        return ApiError(code: code, message: '操作频率过快');
      case 30002:
        return ApiError(code: code, message: '权限不足');
      default:
        return ApiError(code: code, message: message);
    }
  }

  /// 判断错误是否可重试
  static bool isRetryable(ApiError error) {
    final retryableCodes = [408, 429, 500, 502, 503, 504];
    return retryableCodes.contains(error.code);
  }

  /// 判断是否需要重新登录
  static bool needsReAuth(ApiError error) {
    return error.code == 401;
  }

  /// 格式化错误信息用于用户显示
  static String formatErrorForUser(ApiError error) {
    // 隐藏技术细节，只显示用户友好的信息
    if (error.code >= 500) {
      return '服务暂时不可用，请稍后重试';
    }
    
    return error.message;
  }

  /// 记录错误日志
  static void logError(ApiError error, [String? context]) {
    final contextInfo = context != null ? '[$context] ' : '';
    debugPrint('🔴 ${contextInfo}API错误: ${error.code} - ${error.message}');
  }
}