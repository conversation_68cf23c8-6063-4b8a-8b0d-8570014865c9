class ApiResponse<T> {
  final int code;
  final String message;
  final T? data;

  ApiResponse({
    required this.code,
    required this.message,
    this.data,
  });

  bool get isSuccess => code == 200;

  factory ApiResponse.fromJson(
      Map<String, dynamic> json, T? Function(dynamic)? dataConverter) {
    return ApiResponse(
      code: json['code'] as int? ?? 500,
      message: json['message'] as String? ?? 'Unknown error',
      data: json['data'] != null && dataConverter != null
          ? dataConverter(json['data'])
          : json['data'] as T?,
    );
  }
}
