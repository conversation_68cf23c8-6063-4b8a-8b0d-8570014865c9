import 'package:flutter/foundation.dart';
import 'package:user_app/core/network/api_response.dart';

/// API响应适配器，用于处理不同格式的后端响应
class ApiAdapter {
  /// 标准化API响应格式
  /// 
  /// 后端可能返回不同的响应格式：
  /// 1. 标准格式: {code: 200, message: "success", data: {...}}
  /// 2. 直接数据格式: {...} 
  /// 3. 列表格式: [...]
  static ApiResponse<T> standardizeResponse<T>(
    dynamic rawResponse,
    T Function(dynamic)? dataConverter,
  ) {
    try {
      // 处理null响应
      if (rawResponse == null) {
        return ApiResponse<T>(
          code: 200,
          message: 'Success',
          data: null,
        );
      }

      // 处理标准API响应格式
      if (rawResponse is Map<String, dynamic>) {
        if (rawResponse.containsKey('code') && rawResponse.containsKey('message')) {
          // 标准格式
          final code = rawResponse['code'] as int? ?? 200;
          final message = rawResponse['message'] as String? ?? 'Success';
          final data = rawResponse['data'];
          
          return ApiResponse<T>(
            code: code,
            message: message,
            data: dataConverter != null && data != null 
                ? dataConverter(data) 
                : data as T?,
          );
        } else {
          // 直接数据格式
          debugPrint('⚠️ 检测到非标准API响应格式，自动适配为标准格式');
          return ApiResponse<T>(
            code: 200,
            message: 'Success',
            data: dataConverter != null 
                ? dataConverter(rawResponse) 
                : rawResponse as T?,
          );
        }
      }

      // 处理列表格式
      if (rawResponse is List) {
        return ApiResponse<T>(
          code: 200,
          message: 'Success',
          data: dataConverter != null 
              ? dataConverter(rawResponse) 
              : rawResponse as T?,
        );
      }

      // 处理基本类型
      return ApiResponse<T>(
        code: 200,
        message: 'Success',
        data: dataConverter != null 
            ? dataConverter(rawResponse) 
            : rawResponse as T?,
      );
    } catch (e) {
      debugPrint('❌ API响应适配失败: $e');
      return ApiResponse<T>(
        code: 500,
        message: 'Response parsing error: ${e.toString()}',
        data: null,
      );
    }
  }

  /// 处理分页响应
  static ApiResponse<List<T>> handlePagedResponse<T>(
    dynamic rawResponse,
    T Function(dynamic) itemConverter,
  ) {
    try {
      if (rawResponse is Map<String, dynamic>) {
        // 检查是否有标准分页字段
        if (rawResponse.containsKey('records') || 
            rawResponse.containsKey('list') || 
            rawResponse.containsKey('data')) {
          
          List<dynamic>? items;
          if (rawResponse.containsKey('records')) {
            items = rawResponse['records'] as List<dynamic>?;
          } else if (rawResponse.containsKey('list')) {
            items = rawResponse['list'] as List<dynamic>?;
          } else if (rawResponse['data'] is List) {
            items = rawResponse['data'] as List<dynamic>?;
          }

          final convertedItems = items?.map(itemConverter).toList() ?? <T>[];
          
          return ApiResponse<List<T>>(
            code: rawResponse['code'] as int? ?? 200,
            message: rawResponse['message'] as String? ?? 'Success',
            data: convertedItems,
          );
        }
      }

      // 回退到标准处理
      return standardizeResponse<List<T>>(
        rawResponse,
        (data) {
          if (data is List) {
            return data.map(itemConverter).toList();
          }
          return <T>[];
        },
      );
    } catch (e) {
      debugPrint('❌ 分页响应处理失败: $e');
      return ApiResponse<List<T>>(
        code: 500,
        message: 'Paged response parsing error: ${e.toString()}',
        data: <T>[],
      );
    }
  }

  /// 处理计数响应
  static ApiResponse<int> handleCountResponse(dynamic rawResponse) {
    try {
      if (rawResponse is Map<String, dynamic>) {
        // 检查常见的计数字段名
        final possibleFields = ['count', 'total', 'num', 'number', 'size'];
        
        for (final field in possibleFields) {
          if (rawResponse.containsKey(field)) {
            final count = rawResponse[field];
            if (count is int) {
              return ApiResponse<int>(
                code: rawResponse['code'] as int? ?? 200,
                message: rawResponse['message'] as String? ?? 'Success',
                data: count,
              );
            }
          }
        }

        // 检查是否在data字段中
        if (rawResponse.containsKey('data')) {
          final data = rawResponse['data'];
          if (data is int) {
            return ApiResponse<int>(
              code: rawResponse['code'] as int? ?? 200,
              message: rawResponse['message'] as String? ?? 'Success',
              data: data,
            );
          } else if (data is Map<String, dynamic>) {
            for (final field in possibleFields) {
              if (data.containsKey(field) && data[field] is int) {
                return ApiResponse<int>(
                  code: rawResponse['code'] as int? ?? 200,
                  message: rawResponse['message'] as String? ?? 'Success',
                  data: data[field] as int,
                );
              }
            }
          }
        }
      }

      // 如果是直接的数字
      if (rawResponse is int) {
        return ApiResponse<int>(
          code: 200,
          message: 'Success',
          data: rawResponse,
        );
      }

      // 默认返回0
      return ApiResponse<int>(
        code: 200,
        message: 'Success',
        data: 0,
      );
    } catch (e) {
      debugPrint('❌ 计数响应处理失败: $e');
      return ApiResponse<int>(
        code: 500,
        message: 'Count response parsing error: ${e.toString()}',
        data: 0,
      );
    }
  }

  /// 验证必需字段
  static bool validateRequiredFields(Map<String, dynamic> data, List<String> requiredFields) {
    for (final field in requiredFields) {
      if (!data.containsKey(field) || data[field] == null) {
        debugPrint('❌ 缺少必需字段: $field');
        return false;
      }
    }
    return true;
  }

  /// 安全的类型转换
  static T? safeCast<T>(dynamic value) {
    try {
      if (value is T) {
        return value;
      }
      return null;
    } catch (e) {
      debugPrint('❌ 类型转换失败: ${value.runtimeType} -> $T');
      return null;
    }
  }
}