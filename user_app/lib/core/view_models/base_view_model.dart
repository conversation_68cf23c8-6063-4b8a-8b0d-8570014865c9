import 'package:flutter/foundation.dart';
import 'package:user_app/mixins/error_handling_mixin.dart';

abstract class BaseViewModel extends ChangeNotifier with ErrorHandlingMixin {
  // State tracking
  bool _busy = false;

  bool get busy => _busy;

  // Alternative name for the same concept (for better readability in some contexts)
  bool get isLoading => _busy;

  // Track disposal state
  bool _disposed = false;

  bool get disposed => _disposed;

  // Update loading/busy state and notify listeners
  void setBusy(bool value) {
    if (_busy != value) {
      _busy = value;
      notifyListeners();
    }
  }

  // Alternative setter for isLoading (same as setBusy)
  set isLoading(bool value) {
    setBusy(value);
  }

  // Override notifyListeners to prevent calling after disposal
  @override
  void notifyListeners() {
    if (!_disposed) {
      super.notifyListeners();
    }
  }

  // Override dispose to mark as disposed
  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }
}
