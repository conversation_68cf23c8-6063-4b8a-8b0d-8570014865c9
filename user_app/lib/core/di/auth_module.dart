import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/features/auth/providers/base_auth_view_model.dart';
import 'package:user_app/features/auth/providers/login_view_model.dart';
import 'package:user_app/features/auth/providers/register_view_model.dart';
import 'package:user_app/features/auth/providers/reset_password_view_model.dart';
import 'package:user_app/features/auth/providers/user_profile_view_model.dart';
import 'package:user_app/features/auth/services/verification_code_service.dart';
import 'package:user_app/features/fishing_spots/services/fishing_recommend_service.dart';
import 'package:user_app/features/fishing_spots/view_models/weather_card_view_model.dart';
import 'package:user_app/services/map_service.dart';
import 'package:user_app/services/session_service.dart';
import 'package:user_app/services/sms_service.dart';
import 'package:user_app/services/statistics_service.dart';
import 'package:user_app/services/user_service.dart';

class AuthModule {
  static void registerDependencies(GetIt getIt) {
    // Register base auth view model
    getIt.registerLazySingleton<BaseAuthViewModel>(() => BaseAuthViewModel(
          userService: getIt<UserService>(),
          sharedPreferences: getIt<SharedPreferences>(),
        ));

    // Register verification code service
    getIt.registerLazySingleton<VerificationCodeService>(
        () => VerificationCodeService(
              smsService: getIt<SmsService>(),
              sharedPreferences: getIt<SharedPreferences>(),
            ));

    // Register login view model
    getIt.registerFactory<LoginViewModel>(() => LoginViewModel(
          baseAuthViewModel: getIt<BaseAuthViewModel>(),
          sessionService: getIt<SessionService>(),
        ));

    // Register register view model
    getIt.registerFactory<RegisterViewModel>(() => RegisterViewModel(
          baseAuthViewModel: getIt<BaseAuthViewModel>(),
          sessionService: getIt<SessionService>(),
          verificationCodeService: getIt<VerificationCodeService>(),
        ));

    // Register reset password view model
    getIt.registerFactory<ResetPasswordViewModel>(() => ResetPasswordViewModel(
          baseAuthViewModel: getIt<BaseAuthViewModel>(),
          sessionService: getIt<SessionService>(),
          verificationCodeService: getIt<VerificationCodeService>(),
        ));

    // Register user profile view model
    getIt.registerFactory<UserProfileViewModel>(() => UserProfileViewModel(
          baseAuthViewModel: getIt<BaseAuthViewModel>(),
          statisticsService: getIt<StatisticsService>(),
        ));

    // Register weather card view model
    getIt.registerFactory<WeatherCardViewModel>(() => WeatherCardViewModel(
          mapService: getIt<MapService>(),
          fishingRecommendService: getIt<FishingRecommendService>(),
        ));
  }
}
