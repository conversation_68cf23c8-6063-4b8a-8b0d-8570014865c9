import 'package:flutter/material.dart';

class PublishMomentConstants {
  static const int maxImages = 9;
  static const int maxContentLength = 1000;

  static const List<String> momentTypes = ['钓获分享', '装备展示', '技巧分享', '问答求助'];

  static const Map<String, String> visibilityOptions = {
    'public': '公开',
    'followers': '关注者可见',
    'private': '仅自己可见',
  };

  static final Map<String, IconData> momentTypeIcons = {
    '钓获分享': Icons.set_meal,
    '装备展示': Icons.backpack,
    '技巧分享': Icons.tips_and_updates,
    '问答求助': Icons.help_outline,
  };

  static final Map<String, IconData> visibilityIcons = {
    'public': Icons.public,
    'followers': Icons.people,
    'private': Icons.lock,
  };
}
