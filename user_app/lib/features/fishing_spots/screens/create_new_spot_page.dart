import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/di/injection.dart' as app_di;
import 'package:user_app/features/fishing_spots/view_models/create_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/create_spot_step_0.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/create_spot_step_1.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/create_spot_step_2.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/map_modal_content.dart';
import 'package:user_app/utils/ui_helpers.dart';

class CreateNewSpotPage extends StatefulWidget {
  final Function(int) onSpotCreated;

  const CreateNewSpotPage({super.key, required this.onSpotCreated});

  @override
  State<CreateNewSpotPage> createState() => _CreateNewSpotPageState();
}

class _CreateNewSpotPageState extends State<CreateNewSpotPage> {
  final _formKey = GlobalKey<FormState>();
  late CreateSpotViewModel _viewModel;
  bool _isInitialized = false;

  int _currentStep = 0;
  final int _totalSteps = 3;

  @override
  void initState() {
    super.initState();
    _viewModel = app_di.getIt<CreateSpotViewModel>();

    // Initialize data
    _viewModel.initializeFormData();
    _viewModel.loadFishTypes();

    _isInitialized = true;
  }

  Future<void> _showMapModal() async {
    FocusScope.of(context).unfocus();

    final result = await showModalBottomSheet<LocationResult>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext modalContext) {
        return MapModalContent(
          key: UniqueKey(),
          initialLatitude: _viewModel.latitude,
          initialLongitude: _viewModel.longitude,
          initialFormattedAddress: _viewModel.formattedAddress,
        );
      },
    );

    if (result != null && mounted) {
      _viewModel.updateLocation(
        latitude: result.latitude,
        longitude: result.longitude,
        formattedAddress: result.formattedAddress,
        province: result.province,
        city: result.city,
        county: result.county,
        useCurrentLocation: result.useCurrentLocation,
      );
      // Trigger validation
      _formKey.currentState?.validate();
    }
  }

  String _getSuggestedName() {
    if (_viewModel.formattedAddress.isEmpty) return '新钓场';
    String suggestion = '';

    if (_viewModel.formattedAddress.contains('湖')) {
      suggestion = '${_extractFeatureName('湖')}钓场';
    } else if (_viewModel.formattedAddress.contains('水库')) {
      suggestion = _extractFeatureName('水库');
    } else if (_viewModel.formattedAddress.contains('江')) {
      suggestion = '${_extractFeatureName('江')}钓场';
    } else if (_viewModel.formattedAddress.contains('河')) {
      suggestion = '${_extractFeatureName('河')}钓场';
    } else if (_viewModel.formattedAddress.contains('溪')) {
      suggestion = '${_extractFeatureName('溪')}钓场';
    } else {
      suggestion = '${_viewModel.formattedAddress.split(' ').first}钓场';
    }
    return suggestion;
  }

  String _extractFeatureName(String feature) {
    final index = _viewModel.formattedAddress.indexOf(feature);
    if (index == -1) return '';
    int startIndex = index;
    while (startIndex > 0 &&
        !'省市区县镇乡村路街道 '.contains(_viewModel.formattedAddress[startIndex - 1])) {
      startIndex--;
    }
    int endIndex = index + feature.length;
    if (startIndex < 0) startIndex = 0;
    if (endIndex > _viewModel.formattedAddress.length) {
      endIndex = _viewModel.formattedAddress.length;
    }
    if (startIndex >= endIndex) return ''; // Avoid errors

    return _viewModel.formattedAddress.substring(startIndex, endIndex);
  }

  void _showValidationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.redAccent,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: const EdgeInsets.all(10),
      ),
    );
  }

  Future<void> _submitSpot() async {
    FocusScope.of(context).unfocus(); // Hide keyboard

    // Use ViewModel's validation
    if (!_viewModel.validateAllSteps()) {
      if (_viewModel.latitude == 0 || _viewModel.longitude == 0) {
        _showValidationError('请先在地图上选择钓点位置');
        setState(() => _currentStep = 0);
        return;
      }
      if (!_viewModel.validateImagesAndFishTypes()) {
        if (_viewModel.spotImages.isEmpty) {
          _showValidationError('请至少上传一张图片');
        } else if (_viewModel.selectedFishTypeIds.isEmpty) {
          _showValidationError('请至少选择或添加一种鱼类');
        }
        setState(() => _currentStep = 1);
        return;
      }
      if (!_viewModel.validateFacilitiesAndPrices()) {
        _showValidationError('请设置有效的钓场价格');
        setState(() => _currentStep = 2);
        return;
      }
      return;
    }

    try {
      final newSpot = await _viewModel.submitSpot();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('钓点创建成功！'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.green),
        );
        widget.onSpotCreated(newSpot);
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('创建失败: $e'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return ChangeNotifierProvider<CreateSpotViewModel>.value(
      value: _viewModel,
      child:
          Consumer<CreateSpotViewModel>(builder: (context, viewModel, child) {
        return Scaffold(
          backgroundColor: Colors.grey.shade50,
          appBar: AppBar(
            title: const Text('创建新钓点'),
            centerTitle: true,
            backgroundColor: Colors.white,
            elevation: 1,
            shadowColor: Colors.black.withOpacity(0.1),
            actions: [
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                // child: TextButton(
                //   onPressed: viewModel.isSubmitting ? null : _submitSpot,
                //   child: viewModel.isSubmitting
                //       ? const SizedBox(
                //           width: 20,
                //           height: 20,
                //           child: CircularProgressIndicator(strokeWidth: 2))
                //       : const Text('发布',
                //           style: TextStyle(fontWeight: FontWeight.bold)),
                // ),
                child: Selector<CreateSpotViewModel, bool>(
                  selector: (context, viewModel) => viewModel.isSubmitting,
                  builder: (context, isSubmitting, child) {
                    return TextButton(
                      onPressed: isSubmitting ? null : _submitSpot,
                      child: isSubmitting
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2))
                          : const Text('发布',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                    );
                  },
                ),
              ),
            ],
          ),
          body: SafeArea(
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  LinearProgressIndicator(
                    value: (_currentStep + 1) / _totalSteps,
                    backgroundColor: Colors.grey.shade200,
                    color: Theme.of(context).colorScheme.primary,
                    minHeight: 6,
                  ),

                  // Step Counter
                  Container(
                    color: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '步骤 ${_currentStep + 1} / $_totalSteps',
                          style: TextStyle(
                              color: Colors.grey.shade600, fontSize: 14),
                        ),
                        Text(
                          ['基本信息和位置', '图片和鱼类', '设施和价格'][_currentStep],
                          style: TextStyle(
                              fontWeight: FontWeight.w500,
                              fontSize: 15,
                              color: Theme.of(context).colorScheme.primary),
                        ),
                      ],
                    ),
                  ),
                  const Divider(height: 1, thickness: 1),

                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.fromLTRB(16, 20, 16, 16),
                      // child: _buildStepContent(_currentStep, viewModel),
                      child: Consumer<CreateSpotViewModel>(
                        // Or preferably multiple Selectors inside _buildStepContent
                        builder: (context, viewModel, _) =>
                            _buildStepContent(_currentStep, viewModel),
                      ),
                    ),
                  ),

                  Container(
                    padding: const EdgeInsets.fromLTRB(16, 12, 16, 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border:
                          Border(top: BorderSide(color: Colors.grey.shade200)),
                    ),
                    child: Row(
                      children: [
                        // Back Button
                        if (_currentStep > 0)
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () => setState(() => _currentStep--),
                              style: OutlinedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 14),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                side: BorderSide(color: Colors.grey.shade400),
                              ),
                              child: const Text('上一步',
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold)),
                            ),
                          ),

                        if (_currentStep > 0) const SizedBox(width: 16),

                        // Next / Submit Button
                        Expanded(
                          child: FilledButton(
                            onPressed: viewModel.isSubmitting
                                ? null
                                : () {
                                    FocusScope.of(context).unfocus();
                                    if (_currentStep < _totalSteps - 1) {
                                      if (_validateCurrentStep()) {
                                        setState(() => _currentStep++);
                                      }
                                    } else {
                                      _submitSpot();
                                    }
                                  },
                            style: FilledButton.styleFrom(
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child: viewModel.isSubmitting &&
                                    _currentStep == _totalSteps - 1
                                ? const SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 3,
                                    ),
                                  )
                                : Text(
                                    _currentStep == _totalSteps - 1
                                        ? '提交钓点'
                                        : '下一步',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  // Helper to build the content for the current step
  // Inside the CreateNewSpotPage class, update the _buildStepContent method

  Widget _buildStepContent(int step, CreateSpotViewModel viewModel) {
    switch (step) {
      case 0:
        return BasicInfoLocationStep(
          formKey: _formKey,
          nameController: viewModel.nameController,
          addressController: viewModel.addressController,
          descriptionController: viewModel.descriptionController,
          latitude: viewModel.latitude,
          longitude: viewModel.longitude,
          formattedAddress: viewModel.formattedAddress,
          useCurrentLocation: viewModel.useCurrentLocation,
          isLoadingLocation: false,
          onShowMapModal: _showMapModal,
          getSuggestedName: _getSuggestedName,
        );
      case 1:
        return ImagesFishTypesStep(
          spotImages: viewModel.spotImages,
          availableFishTypes:
              viewModel.customFishTypes.isEmpty && viewModel.isLoadingFishTypes
                  ? []
                  : viewModel.getAvailableFishTypes(),
          customFishTypes: viewModel.customFishTypes,
          selectedFishTypeIds: viewModel.selectedFishTypeIds,
          isLoadingFishTypes: viewModel.isLoadingFishTypes,
          onPickImages: viewModel.pickImages,
          onRemoveImage: viewModel.removeImage,
          onFishTypeToggle: viewModel.toggleFishType,
          onCustomFishTypeAdded: viewModel.addCustomFishType,
        );
      case 2:
        return FacilitiesPriceStep(
          hasFacilities: viewModel.hasFacilities,
          isPaid: viewModel.isPaid,
          isOfficial: viewModel.isOfficial,
          facilities: viewModel.facilities,
          prices: viewModel.prices,
          priceController: viewModel.priceController,
          spotVisibility: viewModel.spotVisibility,
          certificationDocuments: viewModel.certificationDocuments,
          isUploadingDocuments: viewModel.isUploadingDocuments,
          availableFishTypes: [
            ...viewModel.getAvailableFishTypes(),
            ...viewModel.customFishTypes
          ],
          onHasFacilitiesChanged: (value) {
            viewModel.hasFacilities = value;
            viewModel.notifyListeners();
          },
          onToggleFacility: viewModel.toggleFacility,
          onAddNewFacility: () => _addNewFacility(viewModel),
          onIsPaidChanged: (value) {
            viewModel.isPaid = value;
            viewModel.notifyListeners();
          },
          onAddPrice: viewModel.addPrice,
          onRemovePrice: viewModel.removePrice,
          onIsOfficialChanged: (value) {
            viewModel.isOfficial = value;
            viewModel.notifyListeners();
          },
          onUploadDocuments: () => _handleUploadDocuments(viewModel),
          onVisibilityChanged: viewModel.updateVisibility,
          onRemoveDocument: viewModel.removeDocument,
        );
      default:
        return const SizedBox.shrink();
    }
  }

// Add a new method to handle document upload
  void _handleUploadDocuments(CreateSpotViewModel viewModel) async {
    if (viewModel.isUploadingDocuments) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('正在上传文件，请稍候...'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    try {
      await viewModel.pickDocumentImages();
      if (viewModel.certificationDocuments.isNotEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('已上传 ${viewModel.certificationDocuments.length} 个证明文件'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('上传证明文件失败: $e'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.red,
        ),
      );
    }
  }

// Also update the validation method to check for documents if official is selected
  bool _validateCurrentStep() {
    if (_currentStep == 0) {
      if (_viewModel.latitude == 0 || _viewModel.longitude == 0) {
        _showValidationError('请先在地图上选择钓点位置');
        return false;
      }
      if (!_formKey.currentState!.validate()) {
        return false; // Let the form validator handle specific field messages
      }
    } else if (_currentStep == 1) {
      if (_viewModel.spotImages.isEmpty) {
        _showValidationError('请至少上传一张图片');
        return false;
      }
      if (_viewModel.spotImages.any((img) => img.hasError)) {
        _showValidationError('部分图片上传失败，请删除或重试');
        return false;
      }
      if (_viewModel.spotImages.any((img) => img.isUploading)) {
        _showValidationError('图片正在上传中，请稍候');
        return false;
      }
      if (_viewModel.selectedFishTypeIds.isEmpty) {
        _showValidationError('请至少选择或添加一种鱼类');
        return false;
      }
    } else if (_currentStep == 2) {
      if (_viewModel.isPaid) {
        bool priceValid = false;
        if (_viewModel.prices.isNotEmpty) {
          priceValid = true; // Multi-price exists
        } else if (_viewModel.priceController.text.isNotEmpty &&
            double.tryParse(_viewModel.priceController.text.trim()) != null &&
            double.parse(_viewModel.priceController.text.trim()) > 0) {
          priceValid = true; // Single price is valid
        }
        if (!priceValid) {
          _showValidationError('请设置有效的钓场价格');
          return false;
        }
      }
      if (_viewModel.hasFacilities &&
          !_viewModel.facilities.any((f) => f.isSelected)) {
        _showValidationError('请至少选择一个场地设施');
        return false;
      }

      // Add validation for certification documents
      if (_viewModel.isOfficial && _viewModel.certificationDocuments.isEmpty) {
        _showValidationError('请上传官方认证所需的证明文件');
        return false;
      }

      if (_viewModel.isOfficial &&
          _viewModel.certificationDocuments.any((doc) => doc.isUploading)) {
        _showValidationError('证明文件正在上传中，请稍候');
        return false;
      }

      if (_viewModel.isOfficial &&
          _viewModel.certificationDocuments.any((doc) => doc.hasError)) {
        _showValidationError('部分证明文件上传失败，请删除或重试');
        return false;
      }
    }

    return true; // Assume valid if checks pass
  }

  void _addNewFacility(CreateSpotViewModel viewModel) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    String selectedIcon = 'apps'; // Default icon

    final List<Map<String, String>> iconOptions = [
      {'name': '默认', 'icon': 'apps'},
      {'name': '烧烤', 'icon': 'outdoor_grill'},
      {'name': '船只', 'icon': 'directions_boat'},
      {'name': '游泳', 'icon': 'pool'},
      {'name': '网络', 'icon': 'wifi'},
      {'name': '淋浴', 'icon': 'shower'},
    ];

    bool hasError = false;

    void updateNameFromIcon(String iconName) {
      final selectedIconData = iconOptions.firstWhere(
        (icon) => icon['icon'] == iconName,
        orElse: () => {'name': '自定义设施', 'icon': 'apps'},
      );

      // Only update name if it's empty or matches a previous icon name
      if (nameController.text.isEmpty ||
          iconOptions.any((icon) => icon['name'] == nameController.text)) {
        nameController.text = selectedIconData['name'] ?? '自定义设施';
      }
    }

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setStateDialog) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                constraints: const BoxConstraints(maxWidth: 500),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Dialog header
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.add_circle_outline,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            '添加场地设施',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      Flexible(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.palette_outlined,
                                      size: 18,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary),
                                  const SizedBox(width: 8),
                                  Text(
                                    "选择图标",
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              GridView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 4,
                                  mainAxisSpacing: 12,
                                  crossAxisSpacing: 12,
                                  childAspectRatio: 0.9,
                                ),
                                itemCount: iconOptions.length,
                                itemBuilder: (context, index) {
                                  final iconData = iconOptions[index];
                                  final iconName = iconData['icon'] ?? 'apps';
                                  final isSelected = selectedIcon == iconName;
                                  return _buildIconSelectionItem(
                                      context, iconData, isSelected, () {
                                    setStateDialog(() {
                                      selectedIcon = iconName;
                                      updateNameFromIcon(iconName);
                                    });
                                  });
                                },
                              ),
                              const SizedBox(height: 24),
                              Row(
                                children: [
                                  Icon(Icons.edit_outlined,
                                      size: 18,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary),
                                  const SizedBox(width: 8),
                                  Text(
                                    "设施名称",
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                controller: nameController,
                                decoration: InputDecoration(
                                  hintText: '设施名称',
                                  border: const OutlineInputBorder(),
                                  errorText: hasError ? '请填写设施名称' : null,
                                ),
                                onChanged: (_) => setStateDialog(() {
                                  if (hasError) hasError = false;
                                }),
                              ),
                              const SizedBox(height: 20),
                              Row(
                                children: [
                                  Icon(Icons.description_outlined,
                                      size: 18,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary),
                                  const SizedBox(width: 8),
                                  Text(
                                    "描述 (可选)",
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                controller: descriptionController,
                                decoration: const InputDecoration(
                                  hintText: '添加设施详情',
                                  border: OutlineInputBorder(),
                                ),
                                maxLines: 3,
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                            ),
                            child: const Text('取消'),
                          ),
                          const SizedBox(width: 12),
                          FilledButton.icon(
                            icon: const Icon(Icons.check),
                            label: const Text('添加'),
                            style: FilledButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                            ),
                            onPressed: () {
                              final name = nameController.text.trim();
                              final description =
                                  descriptionController.text.trim();
                              if (name.isEmpty) {
                                setStateDialog(() {
                                  hasError = true;
                                });
                                return;
                              }

                              viewModel.addFacility(
                                name: name,
                                icon: selectedIcon,
                                description:
                                    description.isNotEmpty ? description : null,
                              );

                              Navigator.pop(context);
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildIconSelectionItem(
      BuildContext context, Map iconData, bool isSelected, VoidCallback onTap) {
    return Tooltip(
      message: iconData['name'] as String,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                : Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey.shade300,
                width: isSelected ? 2 : 1),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Theme.of(context)
                          .colorScheme
                          .primary
                          .withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    )
                  ]
                : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                getIconData(iconData['icon'] as String),
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey.shade700,
                size: 28,
              ),
              const SizedBox(height: 8),
              Text(
                iconData['name'] as String,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey.shade700,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
