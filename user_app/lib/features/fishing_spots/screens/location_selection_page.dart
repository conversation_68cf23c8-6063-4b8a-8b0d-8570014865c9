import 'dart:async';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/models/paginated_state.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/view_models/location_selection_view_model.dart';

class LocationSelectionPage extends StatefulWidget {
  const LocationSelectionPage({super.key});

  @override
  State<LocationSelectionPage> createState() => _LocationSelectionPageState();
}

class _LocationSelectionPageState extends State<LocationSelectionPage> {
  late LocationSelectionViewModel _viewModel;

  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  Timer? _debounceTimer;
  bool _isSearchMode = false;
  String _searchQuery = '';

  bool _favoritesExpanded = true;
  bool _myCreatedExpanded = true;

  @override
  void initState() {
    super.initState();
    _viewModel = context.read<LocationSelectionViewModel>();

    _viewModel.resetAll();

    _loadInitialData();
    _checkLocationAndLoadNearby();

    // Add scroll listener
    _scrollController.addListener(_onScroll);
  }

  // Initial data loading sequence
  Future<void> _loadInitialData() async {
    // No need to wait, let them run concurrently
    _viewModel.loadRecentCheckins(refresh: true);
    if (_favoritesExpanded) _viewModel.loadFavorites(refresh: true);
    if (_myCreatedExpanded) _viewModel.loadMyCreatedSpots(refresh: true);
  }

  Future<void> _checkLocationAndLoadNearby() async {
    final status = await _viewModel.checkAndLoadNearby(refresh: true);
    if (!mounted) return;
    switch (status) {
      case LocationStatus.serviceDisabled:
        ScaffoldMessenger.of(context)
            .showSnackBar(const SnackBar(content: Text('请开启定位服务')));
        break;
      case LocationStatus.permissionDenied:
        ScaffoldMessenger.of(context)
            .showSnackBar(const SnackBar(content: Text('获取位置权限被拒绝')));
        break;
      case LocationStatus.permissionDeniedForever:
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('位置权限已被永久拒绝，请在应用设置中开启')));
        break;
      case LocationStatus.error:
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(_viewModel.locationError ?? '获取位置失败')));
        break;
      case LocationStatus.success:
      case LocationStatus.loading: // Already handled by progress indicators
      case LocationStatus.initial: // Shouldn't end in initial
        break; // Do nothing or show success message if desired
    }
  }

  // Scroll listener for pagination
  void _onScroll() {
    // Load more when near the bottom of the scroll view
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 300) {
      // Threshold
      if (!_isSearchMode) {
        _loadMoreData();
      } else {
        // Load more search results if applicable
        if (_viewModel.searchResults.hasMore &&
            !_viewModel.searchResults.isLoading) {
          _viewModel.searchSpots(
              query: _searchQuery); // refresh = false (default)
        }
      }
    }
  }

  // Load more data for the visible/expanded sections
  void _loadMoreData() {
    final vm = _viewModel; // Alias for brevity

    // Check Recent Check-ins
    if (vm.recentSpots.hasMore && !vm.recentSpots.isLoading) {
      vm.loadRecentCheckins();
      return;
    }
    // Check Nearby Spots - only if location succeeded previously
    if (vm.locationStatus == LocationStatus.success &&
        vm.nearbySpotsState.hasMore &&
        !vm.nearbySpotsState.isLoading) {
      // Call the combined check & load function (refresh = false)
      vm.checkAndLoadNearby(refresh: false);
      return;
    }
    // Check Favorites (if expanded)
    if (_favoritesExpanded &&
        vm.favoriteSpots.hasMore &&
        !vm.favoriteSpots.isLoading) {
      vm.loadFavorites();
      return;
    }
    // Check My Created Spots (if expanded)
    if (_myCreatedExpanded &&
        vm.myCreatedSpots.hasMore &&
        !vm.myCreatedSpots.isLoading) {
      vm.loadMyCreatedSpots();
      return;
    }
  }

  // --- Search Logic ---

  void _onSearchChanged(String query) {
    if (_debounceTimer?.isActive ?? false) _debounceTimer!.cancel();
    _searchQuery = query.trim();

    if (_searchQuery.isEmpty) {
      if (_isSearchMode) {
        // Clear only if currently in search mode
        _clearSearch();
      }
      return;
    }

    if (!_isSearchMode) {
      // Update UI state directly
      if (mounted) {
        setState(() {
          _isSearchMode = true;
        });
      }
    }

    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      // Trigger search in ViewModel (always refresh on new query)
      _viewModel.searchSpots(query: _searchQuery, refresh: true);
    });
  }

  void _clearSearch() {
    _debounceTimer?.cancel();
    _searchController.clear();
    _searchQuery = '';
    // Update UI state directly
    if (mounted) {
      setState(() {
        _isSearchMode = false;
      });
    }
    _viewModel.clearSearchResults(); // Clear results in VM
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _debounceTimer?.cancel();
    // ViewModel is managed by Provider, no need to dispose here
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Use Consumer or context.watch to listen to ViewModel changes
    return Consumer<LocationSelectionViewModel>(
      builder: (context, viewModel, child) {
        return Scaffold(
          appBar: AppBar(
            title: _buildSearchField(),
            actions: [
              if (_isSearchMode)
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _clearSearch, // Call clear search method
                ),
            ],
          ),
          body: _buildBody(viewModel), // Pass ViewModel to body builder
          floatingActionButton: FloatingActionButton.extended(
            onPressed: () {
              // Navigate to create new spot page (Use appropriate navigation)
              // Example: Navigator.pushNamed(context, '/create_spot');
              debugPrint("Navigate to create spot page"); // Placeholder action
            },
            icon: const Icon(Icons.add_location_alt),
            label: const Text('添加新钓点'),
          ),
        );
      },
    );
  }

  Widget _buildSearchField() {
    return TextField(
      controller: _searchController,
      decoration: InputDecoration(
        hintText: '搜索钓点...',
        isDense: true,
        // Makes it slightly smaller
        filled: true,
        fillColor: Colors.grey.shade200,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(30), // More rounded
          borderSide: BorderSide.none,
        ),
        prefixIcon: const Icon(Icons.search, size: 20),
        suffixIcon: _searchController.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear, size: 20),
                onPressed: _clearSearch,
              )
            : null,
        contentPadding: const EdgeInsets.symmetric(
            vertical: 0, horizontal: 15), // Adjust padding
      ),
      onChanged: _onSearchChanged,
      onSubmitted: (value) {
        // Optionally trigger search on submit
        _debounceTimer?.cancel(); // Cancel debounce if user submits
        if (value.trim().isNotEmpty) {
          _viewModel.searchSpots(query: value.trim(), refresh: true);
        } else {
          _clearSearch(); // Clear if submitted empty
        }
      },
    );
  }

  Widget _buildBody(LocationSelectionViewModel viewModel) {
    if (_isSearchMode) {
      // Pass the search results state from the ViewModel
      return _buildSearchResults(viewModel.searchResults);
    }

    // Determine overall loading and empty states based on ViewModel
    final bool hasAnyData = viewModel.recentSpots.items.isNotEmpty ||
        viewModel.nearbySpotsState.items.isNotEmpty ||
        viewModel.favoriteSpots.items.isNotEmpty ||
        viewModel.myCreatedSpots.items.isNotEmpty;

    // Check if any section is doing its initial load
    // Consider nearby special case: don't show initial loader if permission denied/error occurred
    final bool isInitialLoadingNearby = viewModel.nearbySpotsState.isLoading &&
        viewModel.nearbySpotsState.currentPage == 0 &&
        !viewModel.hasLocationPermissionIssue &&
        viewModel.locationStatus != LocationStatus.error;

    final bool isInitialLoadingAny = (viewModel.recentSpots.isLoading &&
            viewModel.recentSpots.currentPage == 0) ||
        isInitialLoadingNearby ||
        (_favoritesExpanded &&
            viewModel.favoriteSpots.isLoading &&
            viewModel.favoriteSpots.currentPage == 0) ||
        (_myCreatedExpanded &&
            viewModel.myCreatedSpots.isLoading &&
            viewModel.myCreatedSpots.currentPage == 0);

    // Check if any section is loading in general (initial or more)
    final bool isLoadingAny = viewModel.recentSpots.isLoading ||
        viewModel.nearbySpotsState.isLoading ||
        viewModel.favoriteSpots.isLoading ||
        viewModel.myCreatedSpots.isLoading;

    // 检查是否正在加载附近钓点且已经超过了一定时间
    // 如果加载时间过长，我们不再显示loading组件
    final bool shouldShowLoading = isInitialLoadingAny && !hasAnyData;

    // 添加一个短暂的loading显示，避免闪烁
    if (shouldShowLoading) {
      // 只在真正需要时显示loading指示器
      return const Center(child: CircularProgressIndicator());
    }

    // Show empty state only if nothing is loading AND there's no data at all
    if (!isLoadingAny && !hasAnyData) {
      // Check if the reason for no data is a location issue
      if (viewModel.locationStatus != LocationStatus.success &&
          viewModel.locationStatus != LocationStatus.initial &&
          viewModel.nearbySpotsState.items.isEmpty) {
        // If location failed and other lists are also empty, show specific error related to location first
        return _buildLocationErrorState(viewModel);
      } else {
        // Otherwise, show the generic empty state
        return _buildEmptyState();
      }
    }

    // Build the main list view
    return RefreshIndicator(
      onRefresh: () async {
        _viewModel.resetAll(); // Reset VM state first
        // Trigger refreshes - Use await to ensure they start
        await Future.wait([
          _viewModel.loadRecentCheckins(refresh: true),
          _viewModel.checkAndLoadNearby(refresh: true),
          // Reload nearby with location check
          // Reload favorites/created only if they were expanded? Or always? Let's always reload.
          _viewModel.loadFavorites(refresh: true),
          _viewModel.loadMyCreatedSpots(refresh: true),
        ]);
      },
      child: ListView(
        controller: _scrollController,
        padding: const EdgeInsets.all(8),
        children: [
          // --- Recent Check-ins ---
          _buildSectionHeader('最近签到', Icons.access_time),
          _buildPaginatedList<FishingSpotVo>(
            // Explicit type
            state: viewModel.recentSpots,
            emptyTitle: '暂无签到记录',
            emptySubtitle: '在钓点签到打卡，帮助其他钓友了解水情',
            emptyIcon: Icons.checklist_rtl,
            itemBuilder: (spot) => _buildSpotListItem(
              spot: spot,
              onTap: () => Navigator.pop(context, spot),
              // Return selected spot
              badge: '最近签到',
              badgeColor: Colors.green,
            ),
            // For empty/error state, retry should trigger a refresh
            onRetry: () => _viewModel.loadRecentCheckins(refresh: true),
            // Load more triggers loading next page
            onLoadMore: () => _viewModel.loadRecentCheckins(),
          ),
          const SizedBox(height: 20),

          // --- Nearby Spots ---
          _buildSectionHeader('附近钓点', Icons.near_me),
          _buildPaginatedList<FishingSpotVo>(
            // Explicit type
            state: viewModel.nearbySpotsState,
            emptyTitle: '附近暂无钓点',
            // Get specific error message based on location status from VM
            emptySubtitle: _getNearbyEmptySubtitle(viewModel),
            emptyIcon: viewModel.hasLocationPermissionIssue
                ? Icons.location_disabled
                : Icons.location_off,
            itemBuilder: (spot) {
              String distanceStr = '未知距离';
              // Calculate distance based on VM's current position if available
              if (viewModel.currentPosition != null) {
                // Ensure coordinates are valid before calculating
                if (spot.latitude != 0 &&
                    spot.longitude != 0 &&
                    viewModel.currentPosition!.latitude != 0 &&
                    viewModel.currentPosition!.longitude != 0) {
                  try {
                    double distKm = Geolocator.distanceBetween(
                            viewModel.currentPosition!.latitude,
                            viewModel.currentPosition!.longitude,
                            spot.latitude,
                            spot.longitude) /
                        1000;
                    distanceStr = distKm > 1
                        ? '${distKm.toStringAsFixed(1)}km'
                        : '${(distKm * 1000).toStringAsFixed(0)}m';
                  } catch (e) {
                    // Use debugPrint instead of print
                    debugPrint("Error calculating distance: $e");
                    distanceStr = "距离计算错误";
                  }
                } else {
                  distanceStr = "坐标无效";
                }
              }
              return _buildSpotListItem(
                spot: spot,
                onTap: () => Navigator.pop(context, spot),
                // Return selected spot
                badge: distanceStr,
                badgeColor: Colors.blueAccent,
              );
            },
            // Retry nearby always triggers the check and load sequence
            onRetry: () => _viewModel.checkAndLoadNearby(refresh: true),
            // Load more nearby also uses checkAndLoadNearby (refresh=false)
            onLoadMore: () => _viewModel.checkAndLoadNearby(refresh: false),
          ),
          const SizedBox(height: 20),

          // --- Favorites ---
          _buildExpandableSection(
            title: '我的收藏',
            icon: Icons.favorite,
            isExpanded: _favoritesExpanded,
            onToggle: () {
              // Update UI state for expansion
              setState(() => _favoritesExpanded = !_favoritesExpanded);
              // Trigger load only if expanding and data isn't loaded/loading
              if (_favoritesExpanded &&
                  viewModel.favoriteSpots.items.isEmpty &&
                  !viewModel.favoriteSpots.isLoading) {
                viewModel.loadFavorites(refresh: true);
              }
            },
            childrenBuilder: () => _buildPaginatedList<FishingSpotVo>(
              // Explicit type
              state: viewModel.favoriteSpots,
              emptyTitle: '暂无收藏钓点',
              emptySubtitle: '收藏喜欢的钓点，方便下次快速选择',
              emptyIcon: Icons.favorite_border,
              itemBuilder: (spot) => _buildSpotListItem(
                spot: spot,
                onTap: () => Navigator.pop(context, spot),
                // Return selected spot
                badge: '已收藏',
                badgeColor: Colors.pink.shade300,
              ),
              onRetry: () => _viewModel.loadFavorites(refresh: true),
              onLoadMore: () => _viewModel.loadFavorites(),
            ),
          ),
          const SizedBox(height: 20),

          // --- My Created Spots ---
          _buildExpandableSection(
            title: '我创建的',
            icon: Icons.create,
            isExpanded: _myCreatedExpanded,
            onToggle: () {
              // Update UI state for expansion
              setState(() => _myCreatedExpanded = !_myCreatedExpanded);
              // Trigger load only if expanding and data isn't loaded/loading
              if (_myCreatedExpanded &&
                  viewModel.myCreatedSpots.items.isEmpty &&
                  !viewModel.myCreatedSpots.isLoading) {
                viewModel.loadMyCreatedSpots(refresh: true);
              }
            },
            childrenBuilder: () => _buildPaginatedList<FishingSpotVo>(
              // Explicit type
              state: viewModel.myCreatedSpots,
              emptyTitle: '暂无创建钓点',
              emptySubtitle: '创建并分享您发现的好钓点',
              emptyIcon: Icons.add_location,
              itemBuilder: (spot) => _buildSpotListItem(
                spot: spot,
                onTap: () => Navigator.pop(context, spot),
                // Return selected spot
                badge: '我创建的',
                badgeColor: Colors.orange.shade700,
              ),
              onRetry: () => _viewModel.loadMyCreatedSpots(refresh: true),
              onLoadMore: () => _viewModel.loadMyCreatedSpots(),
            ),
          ),

          // Bottom padding for FAB
          const SizedBox(height: 80),
        ],
      ),
    );
  }

  // Helper to get appropriate subtitle for nearby section based on location status
  String _getNearbyEmptySubtitle(LocationSelectionViewModel viewModel) {
    switch (viewModel.locationStatus) {
      case LocationStatus.serviceDisabled:
        return '请开启定位服务以查看附近钓点';
      case LocationStatus.permissionDenied:
      case LocationStatus.permissionDeniedForever:
        return '请授予位置权限以查看附近钓点';
      case LocationStatus.error:
        return viewModel.locationError ?? '加载附近钓点失败';
      case LocationStatus.success: // Success but no spots found
        return '附近没有找到钓点';
      case LocationStatus.initial: // Shouldn't normally show empty state here
      case LocationStatus.loading:
        return '正在获取位置并加载附近钓点...'; // Default or loading message
    }
  }

  // --- Helper Widgets (_buildPaginatedList, Headers, Sections, Empty/Error states, Item) ---

  Widget _buildPaginatedList<T>({
    required PaginatedState<T> state,
    required String emptyTitle,
    required String emptySubtitle,
    required IconData emptyIcon,
    required Widget Function(T item) itemBuilder,
    required VoidCallback onLoadMore, // Function to load the next page
    required VoidCallback
        onRetry, // Function to retry loading (usually refresh)
  }) {
    // 1. Initial Loading State (First time loading this section)
    // Show loader only if it's truly the first load (currentPage == 0)
    if (state.isLoading && state.currentPage == 0) {
      // Use a slightly smaller vertical padding for initial load indicator within lists
      return const Center(
          child: Padding(
              padding: EdgeInsets.symmetric(vertical: 24.0),
              child: CircularProgressIndicator()));
    }

    // 2. Empty State with Error (Initial load failed)
    // Show error section only if the initial load resulted in an error and there are no items
    if (state.hasError && state.items.isEmpty && !state.isLoading) {
      // Use the dedicated error section builder, passing the retry callback
      return _buildErrorSection(state.error ?? '加载失败', onRetry);
    }

    // 3. Empty State after successful load (No items found)
    // Show empty section only if loading is complete, there are no errors, and items list is empty
    if (state.items.isEmpty && !state.isLoading && !state.hasError) {
      // Pass onRetry in case the empty state itself allows retrying (e.g., for location permission issues shown here)
      return _buildEmptySection(emptyTitle, emptySubtitle, emptyIcon,
          onRetry: onRetry);
    }

    // 4. Display List Items
    return Column(
      // Using Column allows adding the loading/error indicator below the list
      children: [
        // Build the list of items
        ListView.builder(
          shrinkWrap: true,
          // Essential inside another scroll view like ListView or CustomScrollView
          physics: const NeverScrollableScrollPhysics(),
          // Disable nested scrolling for this inner list
          itemCount: state.items.length,
          itemBuilder: (context, index) => itemBuilder(state.items[index]),
        ),

        // 5. "Loading More" Indicator (Loading next page, not refreshing)
        // Show only if loading is active AND it's not a refresh (meaning it's loading more)
        if (state.isLoading && !state.isRefreshing)
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: Center(
                child: CircularProgressIndicator(
                    strokeWidth: 2.0)), // Smaller indicator
          ),

        // 6. "Load More" Error Indicator (Error occurred while loading next page)
        // Show only if an error exists, there are already items, and it's not currently loading again
        if (state.hasError && state.items.isNotEmpty && !state.isLoading)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.warning_amber_rounded,
                      color: Colors.orange.shade600, size: 20),
                  const SizedBox(height: 4),
                  Text(state.error ?? '加载更多失败',
                      style:
                          TextStyle(color: Colors.grey.shade600, fontSize: 13)),
                  const SizedBox(height: 4),
                  // Retry button for "load more" error should trigger onLoadMore, not onRetry(refresh)
                  TextButton(
                    onPressed: onLoadMore, // Retry loading the *next* page
                    style: TextButton.styleFrom(
                        visualDensity: VisualDensity.compact),
                    child: const Text('重试'),
                  )
                ],
              ),
            ),
          ),

        // 7. End of List Indicator (Optional)
        // Show if loading is complete, there's no error, but hasMore is false
        if (!state.isLoading &&
            !state.hasError &&
            !state.hasMore &&
            state.items.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20.0),
            child: Center(
                child: Text('没有更多了',
                    style: TextStyle(color: Colors.grey.shade500))),
          ),
      ],
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(left: 8, right: 8, top: 16, bottom: 8),
      child: Row(
        children: [
          Icon(icon, color: Theme.of(context).colorScheme.primary, size: 20),
          const SizedBox(width: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 17, // Slightly smaller
              fontWeight: FontWeight.bold,
              color: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.color, // Use theme color
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandableSection({
    required String title,
    required IconData icon,
    required bool isExpanded,
    required VoidCallback onToggle,
    required Widget Function() childrenBuilder, // Use a builder
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap: onToggle,
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
            child: Row(
              children: [
                Icon(icon,
                    color: Theme.of(context).colorScheme.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).textTheme.titleLarge?.color,
                  ),
                ),
                const Spacer(),
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
          ),
        ),
        // Use AnimatedCrossFade for smoother expand/collapse
        AnimatedCrossFade(
          duration: const Duration(milliseconds: 300),
          firstChild: Container(),
          // Empty container when collapsed
          secondChild: isExpanded ? childrenBuilder() : Container(),
          // Build children only when expanded
          crossFadeState:
              isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
          // Optional: Adjust alignment and size curves for better animation
          firstCurve: Curves.easeOut,
          secondCurve: Curves.easeIn,
          sizeCurve: Curves.bounceOut, // Example curve
        )
      ],
    );
  }

  Widget _buildEmptySection(String title, String subtitle, IconData icon,
      {VoidCallback? onRetry}) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 24, horizontal: 8),
      // Increased vertical margin
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.shade50, // Lighter background
        borderRadius: BorderRadius.circular(12),
        // border: Border.all(color: Colors.grey.shade200), // Softer border or remove
      ),
      child: Column(
        children: [
          Icon(icon, size: 48, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600, // Semi-bold
              color: Colors.grey.shade700,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey.shade500, fontSize: 14),
          ),
          // Show retry button only if onRetry is provided (e.g., for nearby location errors)
          if (onRetry != null) ...[
            const SizedBox(height: 16),
            TextButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh, size: 18),
                label: const Text('重试'),
                style:
                    TextButton.styleFrom(visualDensity: VisualDensity.compact))
          ]
        ],
      ),
    );
  }

  Widget _buildErrorSection(String message, VoidCallback onRetry) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 24, horizontal: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        // border: Border.all(color: Colors.red.shade100), // Optional border
      ),
      child: Column(
        children: [
          Icon(Icons.error_outline, size: 48, color: Colors.red.shade300),
          const SizedBox(height: 16),
          Text(
            '加载出错',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.red.shade700,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.red.shade500, fontSize: 14),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
              onPressed: onRetry, // Use the provided retry callback
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
              style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade100, // Button background
                  foregroundColor: Colors.red.shade800 // Text and icon color
                  ))
        ],
      ),
    );
  }

  // Specific empty state when location check fails and other lists are empty
  Widget _buildLocationErrorState(LocationSelectionViewModel viewModel) {
    IconData icon;
    String title;
    String subtitle;
    bool showRetry = true;

    switch (viewModel.locationStatus) {
      case LocationStatus.serviceDisabled:
        icon = Icons.location_disabled;
        title = '定位服务未开启';
        subtitle = '请在系统设置中开启定位服务以查看附近钓点。';
        break;
      case LocationStatus.permissionDenied:
        icon = Icons.location_disabled;
        title = '位置权限被拒绝';
        subtitle = '请授予应用位置权限以查看附近钓点。';
        break;
      case LocationStatus.permissionDeniedForever:
        icon = Icons.location_disabled;
        title = '位置权限被永久拒绝';
        subtitle = '请在应用设置中手动开启位置权限。';
        // Retry button might not be helpful here, user needs to go to settings
        // showRetry = false; // Consider this
        break;
      case LocationStatus.error:
      default:
        icon = Icons.error_outline;
        title = '无法获取位置';
        subtitle = viewModel.locationError ?? '加载附近钓点时发生错误。';
        break;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 72, color: Colors.orange.shade400),
            const SizedBox(height: 24),
            Text(
              title,
              style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
            ),
            if (showRetry) ...[
              const SizedBox(height: 32),
              ElevatedButton.icon(
                // Retry button triggers the location check again
                onPressed: () => _viewModel.checkAndLoadNearby(refresh: true),
                icon: const Icon(Icons.refresh),
                label: const Text('重试'),
                style: ElevatedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ]
          ],
        ),
      ),
    );
  }

  // Generic empty state when all lists are empty and location is okay (or not checked yet)
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.explore_off_outlined,
                size: 72, color: Colors.grey.shade400),
            const SizedBox(height: 24),
            Text(
              '暂无钓点信息',
              style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700),
            ),
            const SizedBox(height: 12),
            Text(
              '尝试搜索或创建您的第一个钓点吧！',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                // Navigate to create spot page
                debugPrint("Navigate to create spot page"); // Placeholder
              },
              icon: const Icon(Icons.add_location_alt),
              label: const Text('创建新钓点'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Update to receive PaginatedState<FishingSpotVo> for search results
  Widget _buildSearchResults(PaginatedState<FishingSpotVo> searchState) {
    // Use the same paginated list builder for consistency
    return _buildPaginatedList<FishingSpotVo>(
      state: searchState,
      emptyTitle: '未找到匹配的钓点',
      emptySubtitle: '尝试更换关键词或创建新钓点',
      emptyIcon: Icons.search_off,
      itemBuilder: (spot) => _buildSpotListItem(
        spot: spot,
        onTap: () => Navigator.pop(context, spot),
        // Return selected spot
        badge: '搜索结果',
        badgeColor: Colors.purple.shade300,
      ),
      // Retry for search should re-trigger the search with refresh=true
      onRetry: () {
        if (_searchQuery.isNotEmpty) {
          _viewModel.searchSpots(query: _searchQuery, refresh: true);
        }
      },
      // Load more for search triggers loading next page
      onLoadMore: () {
        if (_searchQuery.isNotEmpty) {
          _viewModel.searchSpots(query: _searchQuery); // refresh = false
        }
      },
    );
  }

  // List Item Widget
  Widget _buildSpotListItem({
    required FishingSpotVo spot,
    required VoidCallback onTap,
    String? badge,
    Color? badgeColor,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 5, horizontal: 0),
      // Reduced vertical margin
      elevation: 1.5,
      // Subtle shadow
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Icon with background
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .primary
                      .withAlpha(25), // Fixed opacity
                  borderRadius: BorderRadius.circular(8), // Rounded square
                ),
                child: Icon(
                  Icons.location_on_outlined, // Use outlined icon
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              // Text content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Top row: Name and official badge
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Text(
                            spot.name,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600, // Semi-bold
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (spot.isOfficial)
                          Padding(
                            padding: const EdgeInsets.only(left: 6),
                            // Using a pre-made badge widget might be cleaner if available
                            child: Icon(Icons.verified,
                                size: 16, color: Colors.blueAccent),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    // Address
                    Text(
                      spot.address.isNotEmpty ? spot.address : '地址未提供',
                      maxLines: 1, // Keep address concise
                      overflow: TextOverflow.ellipsis,
                      style:
                          TextStyle(fontSize: 13, color: Colors.grey.shade600),
                    ),
                    const SizedBox(height: 8),
                    // Bottom row for fish types and category badge
                    Row(
                      children: [
                        // Fish types tag (if available)
                        if (spot.fishTypeList.isNotEmpty)
                          _buildChip(
                            label: spot.fishTypeList.length > 1
                                ? '${spot.fishTypeList.first.name}等${spot.fishTypeList.length}种'
                                : spot.fishTypeList.first.name,
                            icon: Icons.phishing, // Fish icon
                            color: Colors.cyan.shade700,
                          ),

                        const Spacer(),
                        // Pushes the category badge to the right

                        // Category Badge (if available)
                        if (badge != null)
                          _buildChip(
                            label: badge,
                            color: badgeColor ?? Colors.grey.shade600,
                            isBadgeStyle: true, // Use badge style
                          ),
                      ],
                    )
                  ],
                ),
              ),
              // Right arrow icon
              const Icon(Icons.chevron_right, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }

  // Helper for creating small chips/tags
  Widget _buildChip(
      {required String label,
      IconData? icon,
      required Color color,
      bool isBadgeStyle = false}) {
    return Container(
      padding:
          EdgeInsets.symmetric(horizontal: isBadgeStyle ? 8 : 6, vertical: 3),
      decoration: BoxDecoration(
        color: color.withAlpha(30), // Fixed opacity
        borderRadius: BorderRadius.circular(12),
        // Add a subtle border for badge style
        border: isBadgeStyle
            ? Border.all(
                color: color.withAlpha(128), width: 0.5) // Fixed opacity
            : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Show icon only if provided and not in badge style
          if (icon != null && !isBadgeStyle) ...[
            Icon(icon, size: 13, color: color),
            const SizedBox(width: 4),
          ],
          // Label text
          Text(
            label,
            style: TextStyle(
                fontSize: 11,
                color: color, // Use the provided color for text
                fontWeight: FontWeight.w500 // Slightly bolder
                ),
          ),
        ],
      ),
    );
  }
} // End of _LocationSelectionPageState
