import 'package:flutter/material.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/fishing_spots/models/spot_map_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/screens/create_new_spot_page.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/map_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/search_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/weather_card_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/advanced_filter_sheet.dart';
import 'package:user_app/features/fishing_spots/widgets/filter_section.dart';
import 'package:user_app/features/fishing_spots/widgets/fishing_spot_details_modal.dart';
import 'package:user_app/features/fishing_spots/widgets/floating_action_section.dart';
import 'package:user_app/features/fishing_spots/widgets/search_results_page.dart';
import 'package:user_app/features/fishing_spots/widgets/search_sheet.dart';
import 'package:user_app/features/fishing_spots/widgets/spot_list_view.dart';
import 'package:user_app/features/fishing_spots/widgets/spot_map_view.dart';
import 'package:user_app/features/fishing_spots/widgets/weather_details_sheet.dart';
import 'package:user_app/screens/route_planning_page.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class FishingSpotsPage extends StatefulWidget {
  const FishingSpotsPage({super.key});

  @override
  State<FishingSpotsPage> createState() => _FishingSpotsPageState();
}

class _FishingSpotsPageState extends State<FishingSpotsPage>
    with TickerProviderStateMixin {
  // Controllers
  final ScrollController _scrollController = ScrollController();
  late AnimationController _fabAnimationController;
  late AnimationController _viewSwitchController;
  AMap2DController? _mapController;

  // State
  bool _isMapView = false;
  bool _isMapReady = false;
  String _selectedFilter = "全部";
  final List<String> _filterOptions = ["全部", "官方认证", "用户推荐", "免费钓场", "付费钓场"];
  List<String> _selectedFishTypes = [];
  bool _filterHasFacilities = false;
  bool _filterHasParking = false;

  // Header visibility state
  bool _isHeaderVisible = true;
  double _lastScrollOffset = 0.0;

  // View Models
  late FishingSpotViewModel _spotViewModel;
  late MapViewModel _mapViewModel;
  late SearchViewModel _searchViewModel;

  @override
  void initState() {
    super.initState();
    _spotViewModel = context.read<FishingSpotViewModel>();
    _mapViewModel =
        MapViewModel(fishingSpotService: context.read<FishingSpotService>());
    _searchViewModel = GetIt.instance<SearchViewModel>();
    _initializeAnimations();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initialLoad();
    });
    _scrollController.addListener(_scrollListener);
  }

  void _initializeAnimations() {
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fabAnimationController.forward();

    _viewSwitchController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
  }

  Future<void> _initialLoad() async {
    await _spotViewModel.loadFishTypes();
    await _loadData(refresh: true);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _fabAnimationController.dispose();
    _viewSwitchController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    // 处理分页加载
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_spotViewModel.isBusy && _spotViewModel.hasMore) {
        _loadData();
      }
    }

    // 处理头部隐藏/显示 (只在列表视图时生效)
    if (!_isMapView) {
      _handleHeaderVisibility();
    }
  }

  void _handleHeaderVisibility() {
    final currentOffset = _scrollController.offset;
    final isScrollingDown = currentOffset > _lastScrollOffset;
    final isScrollingUp = currentOffset < _lastScrollOffset;

    // 只有在滚动距离超过一定阈值时才触发动画
    if ((currentOffset - _lastScrollOffset).abs() > 5) {
      if (isScrollingUp && !_isHeaderVisible) {
        // 上滑显示 header 和 filter
        setState(() {
          _isHeaderVisible = true;
        });
      } else if (isScrollingDown && _isHeaderVisible && currentOffset > 100) {
        // 下滑隐藏 header 和 filter (滚动超过100像素后才开始隐藏)
        setState(() {
          _isHeaderVisible = false;
        });
      }
      _lastScrollOffset = currentOffset;
    }
  }

  Future<void> _loadData({bool refresh = false}) async {
    if (refresh) {
      _spotViewModel.applyFilters(
        filter: _selectedFilter,
        fishTypes: _selectedFishTypes,
        hasFacilities: _filterHasFacilities,
        hasParking: _filterHasParking,
      );
    }
    await _spotViewModel.loadFishingSpotSummaries(refresh: refresh);
  }

  void _toggleView() {
    setState(() {
      _isMapView = !_isMapView;
      // 切换视图时总是显示头部
      _isHeaderVisible = true;
    });
    if (_isMapView) {
      _viewSwitchController.forward();
      debugPrint('🗺️ [FishingSpotsPage] Switched to map view');
      // 切换到地图视图时，立即开始业务流程：获取位置 -> 加载数据
      _initializeMapView();
    } else {
      _viewSwitchController.reverse();
    }
  }

  /// 初始化地图视图的业务流程
  Future<void> _initializeMapView() async {
    debugPrint('🗺️ [FishingSpotsPage] Initializing map view...');

    try {
      // 尝试获取用户位置
      final position = await _getUserLocation();

      if (position != null) {
        // 使用真实位置初始化
        await _mapViewModel.initializeMapView(
          defaultLatitude: position.latitude,
          defaultLongitude: position.longitude,
        );

        // 移动地图到用户位置（如果地图已准备好）
        if (_mapController != null) {
          await _mapController!.move(
            position.latitude.toString(),
            position.longitude.toString(),
          );
          await _mapController!.setZoom(zoomLevel: 15);
        }
      } else {
        // 使用默认位置初始化
        const defaultLat = 39.9042; // 北京
        const defaultLng = 116.4074;

        await _mapViewModel.initializeMapView(
          defaultLatitude: defaultLat,
          defaultLongitude: defaultLng,
        );

        // 移动地图到默认位置（如果地图已准备好）
        if (_mapController != null) {
          await _mapController!.move(
            defaultLat.toString(),
            defaultLng.toString(),
          );
          await _mapController!.setZoom(zoomLevel: 12);
        }
      }
    } catch (e) {
      debugPrint('❌ [FishingSpotsPage] Map view initialization failed: $e');
    }
  }

  /// 获取用户位置（不抛出异常）
  Future<Position?> _getUserLocation() async {
    try {
      debugPrint('🗺️ [FishingSpotsPage] Requesting user location...');

      // 检查位置权限
      final permission = await Permission.location.request();
      if (!permission.isGranted) {
        debugPrint('❌ [FishingSpotsPage] Location permission denied');
        return null;
      }

      // 获取当前位置
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      debugPrint(
          '✅ [FishingSpotsPage] Got user location: ${position.latitude}, ${position.longitude}');
      return position;
    } catch (e) {
      debugPrint('❌ [FishingSpotsPage] Error getting user location: $e');
      return null;
    }
  }

  Future<void> _loadMapData() async {
    // 检查是否启用了基于位置的加载
    if (_mapViewModel.isLocationBasedLoading &&
        _mapViewModel.currentLatitude != null &&
        _mapViewModel.currentLongitude != null) {
      debugPrint('🗺️ [FishingSpotsPage] Loading map data using user location');
      // 使用用户位置加载附近钓点
      await _mapViewModel.loadNearbySpots(
        latitude: _mapViewModel.currentLatitude!,
        longitude: _mapViewModel.currentLongitude!,
        refresh: true,
      );
    } else {
      debugPrint(
          '🗺️ [FishingSpotsPage] Loading map data using general API (fallback)');
      // 备用方案：应用当前筛选条件到地图 ViewModel
      _mapViewModel.applyFilters(
        filter: _selectedFilter,
        fishTypes: _selectedFishTypes,
        hasFacilities: _filterHasFacilities,
        hasParking: _filterHasParking,
      );
      // 加载轻量级的地图数据
      await _mapViewModel.loadMapSpots(refresh: true);
    }
  }

  void _onFilterChanged(String filter) {
    setState(() {
      _selectedFilter = filter;
    });
    _loadData(refresh: true);
  }

  void _showAdvancedFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AdvancedFilterSheet(
        selectedFishTypes: _selectedFishTypes,
        hasFacilities: _filterHasFacilities,
        hasParking: _filterHasParking,
        availableFishTypes: _spotViewModel.availableFishTypesObjects,
        onApply: (fishTypes, facilities, parking) {
          setState(() {
            _selectedFishTypes = fishTypes;
            _filterHasFacilities = facilities;
            _filterHasParking = parking;
          });
          _loadData(refresh: true);
        },
      ),
    );
  }

  void _showSearchSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SearchSheet(
        onSearch: (query) {
          Navigator.pop(context);
        },
        onSearchResults: (results, {String? query}) {
          debugPrint('🔍 [FishingSpotsPage] onSearchResults called with ${results.length} results');
          debugPrint('🔍 [FishingSpotsPage] Search query: $query');
          debugPrint('🔍 [FishingSpotsPage] Results details: ${results.map((r) => r.name).toList()}');
          debugPrint('🔍 [FishingSpotsPage] Current ViewModel results: ${_searchViewModel.searchResults.length}');
          Navigator.pop(context);
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SearchResultsPage(
                initialResults: results,
                searchQuery: query ?? (_searchViewModel.searchHistory.isNotEmpty
                    ? _searchViewModel.searchHistory.first
                    : '搜索结果'),
                searchViewModel: _searchViewModel,
              ),
            ),
          );
        },
      ),
    );
  }

  void _showAddSpotDialog() {
    final authViewModel = context.read<AuthViewModel>();
    if (!authViewModel.isUserLoggedIn()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先登录')),
      );
      context.push(
          '/login?from=${Uri.encodeComponent(context.namedLocation('fishing-spots'))}');
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateNewSpotPage(
          onSpotCreated: (spotId) {
            _loadData(refresh: true);
          },
        ),
      ),
    );
  }

  void _showSpotDetailsById(int spotId) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => FishingSpotDetailsModal(
          spotId: spotId,
          scrollController: scrollController,
        ),
      ),
    );
  }

  void _showSpotDetails(SpotSummaryVo spot) {
    // 直接使用明确的类型，无需类型判断
    final int spotId = spot.id;
    final String address = spot.address;
    final double latitude = spot.latitude;
    final double longitude = spot.longitude;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => FishingSpotDetailsModal(
          spotId: spotId,
          scrollController: scrollController,
          onNavigationPressed: () {
            Navigator.pop(context);
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => RoutePlanningPage(
                  address: address,
                  destination: LngLat(longitude, latitude),
                ),
              ),
            );
          },
          onCheckinPressed: () {
            // Handle checkin
          },
        ),
      ),
    );
  }

  /// 导航到钓点
  void _navigateToSpot(SpotMapVo mapSpot) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RoutePlanningPage(
          address: mapSpot.address,
          destination: LngLat(mapSpot.longitude, mapSpot.latitude),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<FishingSpotViewModel>(
      builder: (context, spotViewModel, child) {
        return Scaffold(
          backgroundColor: const Color(0xFFF5F7FA),
          body: Stack(
            children: [
              // Main Content
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 400),
                child: _isMapView
                    ? ChangeNotifierProvider.value(
                        value: _mapViewModel,
                        child: Consumer<MapViewModel>(
                          builder: (context, mapViewModel, child) {
                            return SpotMapView(
                              key: const ValueKey('map'),
                              spots: mapViewModel.mapSpots,
                              // 使用轻量级地图数据
                              onMapCreated: (controller) async {
                                setState(() {
                                  _mapController = controller;
                                  _isMapReady = true;
                                });
                                debugPrint(
                                    '🗺️ [FishingSpotsPage] Map created and ready');
                                // 地图创建完成，但不在这里触发业务逻辑
                                // 业务逻辑已经在 _initializeMapView() 中处理
                              },
                              onSpotTapped: (mapSpot) {
                                // 保持原有的点击行为作为备用
                                debugPrint(
                                    '🗺️ [FishingSpotsPage] Spot tapped: ${mapSpot.name}');
                              },
                              onSpotDetailsRequested: (mapSpot) {
                                // 通过气泡的详情按钮触发
                                _showSpotDetailsById(mapSpot.id);
                              },
                              onSpotNavigationRequested: (mapSpot) {
                                // 通过气泡的导航按钮触发
                                _navigateToSpot(mapSpot);
                              },
                              onRegionChange: (bounds, zoom) {
                                // 当地图区域变化时更新ViewModel
                                mapViewModel.updateMapRegion(bounds, zoom);
                              },
                              enableLocationButton: true,
                              enableZoomControls: true,
                              enableClustering:
                                  mapViewModel.mapSpots.length > 20,
                            );
                          },
                        ),
                      )
                    : SpotListView(
                        key: const ValueKey('list'),
                        scrollController: _scrollController,
                        spots: spotViewModel.fishingSpotSummaries,
                        // 列表视图使用摘要数据
                        isLoading: spotViewModel.isBusy,
                        hasMore: spotViewModel.hasMore,
                        error: spotViewModel.errorMessage,
                        onSpotTapped: _showSpotDetails,
                        onRefresh: () => _loadData(refresh: true),
                        onRetry: () => _loadData(refresh: true),
                      ),
              ),

              // Top Bar with animation
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  height: _isHeaderVisible ? null : 0,
                  curve: Curves.easeInOut,
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 200),
                    opacity: _isHeaderVisible ? 1.0 : 0.0,
                    child: _isHeaderVisible
                        ? Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.white,
                                  Colors.white.withValues(alpha: 0.95),
                                  Colors.white.withValues(alpha: 0),
                                ],
                                stops: const [0, 0.8, 1],
                              ),
                            ),
                            child: SafeArea(
                              bottom: false,
                              child: Column(
                                children: [
                                  // Compact Header
                                  Container(
                                    padding:
                                        const EdgeInsets.fromLTRB(16, 8, 16, 8),
                                    child: Row(
                                      children: [
                                        // Weather Info (Compact)
                                        Expanded(
                                          child: Consumer<WeatherCardViewModel>(
                                            builder:
                                                (context, weatherViewModel, _) {
                                              return GestureDetector(
                                                onTap: () {
                                                  showModalBottomSheet(
                                                    context: context,
                                                    isScrollControlled: true,
                                                    backgroundColor:
                                                        Colors.transparent,
                                                    builder: (context) =>
                                                        const WeatherDetailsSheet(),
                                                  );
                                                },
                                                child: Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 16,
                                                      vertical: 12),
                                                  decoration: BoxDecoration(
                                                    color: Colors.white,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            16),
                                                    boxShadow: [
                                                      BoxShadow(
                                                        color: Colors.black
                                                            .withValues(
                                                                alpha: 0.05),
                                                        blurRadius: 10,
                                                        offset:
                                                            const Offset(0, 4),
                                                      ),
                                                    ],
                                                  ),
                                                  child: Row(
                                                    children: [
                                                      // Weather Icon
                                                      Container(
                                                        width: 40,
                                                        height: 40,
                                                        decoration:
                                                            BoxDecoration(
                                                          gradient:
                                                              LinearGradient(
                                                            colors: [
                                                              Colors.blue
                                                                  .shade300,
                                                              Colors
                                                                  .blue.shade500
                                                            ],
                                                          ),
                                                          shape:
                                                              BoxShape.circle,
                                                        ),
                                                        child: const Icon(
                                                          Icons.wb_sunny,
                                                          color: Colors.white,
                                                          size: 24,
                                                        ),
                                                      ),
                                                      const SizedBox(width: 12),
                                                      // Weather Info
                                                      Expanded(
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Text(
                                                              '${weatherViewModel.weatherData.temperature ?? '20'}°C ${weatherViewModel.weatherData?.weather ?? '晴'}',
                                                              style:
                                                                  const TextStyle(
                                                                fontSize: 16,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                              ),
                                                            ),
                                                            Text(
                                                              '${weatherViewModel.weatherData.windPower} ${weatherViewModel.weatherData?.humidity ?? '60'}%湿度',
                                                              style: TextStyle(
                                                                fontSize: 13,
                                                                color: Colors
                                                                    .grey
                                                                    .shade600,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      Icon(
                                                        Icons.chevron_right,
                                                        color: Colors
                                                            .grey.shade400,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        // Search Button
                                        Container(
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(16),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black
                                                    .withValues(alpha: 0.05),
                                                blurRadius: 10,
                                                offset: const Offset(0, 4),
                                              ),
                                            ],
                                          ),
                                          child: Material(
                                            color: Colors.transparent,
                                            child: InkWell(
                                              onTap: _showSearchSheet,
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.all(12),
                                                child: Icon(
                                                  Icons.search,
                                                  color: Colors.grey.shade700,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // Filter Section
                                  FilterSection(
                                    selectedFilter: _selectedFilter,
                                    filterOptions: _filterOptions,
                                    hasActiveAdvancedFilters:
                                        _selectedFishTypes.isNotEmpty ||
                                            _filterHasFacilities ||
                                            _filterHasParking,
                                    activeAdvancedFiltersCount:
                                        _selectedFishTypes.length +
                                            (_filterHasFacilities ? 1 : 0) +
                                            (_filterHasParking ? 1 : 0),
                                    onFilterChanged: _onFilterChanged,
                                    onAdvancedFilterTapped:
                                        _showAdvancedFilterSheet,
                                  ),
                                ],
                              ),
                            ),
                          )
                        : const SizedBox.shrink(),
                  ),
                ),
              ),

              // Floating Actions
              FloatingActionSection(
                fabAnimationController: _fabAnimationController,
                isMapView: _isMapView,
                isVisible: _isHeaderVisible,
                onAddSpot: _showAddSpotDialog,
                onToggleView: _toggleView,
              ),
            ],
          ),
        );
      },
    );
  }
}
