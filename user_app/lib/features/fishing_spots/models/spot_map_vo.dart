import 'package:dart_mappable/dart_mappable.dart';

part 'spot_map_vo.mapper.dart';

/// 地图视图专用的钓点数据模型 - 只包含地图显示必需的字段
@MappableClass()
class SpotMapVo with SpotMapVoMappable {
  /// 钓点ID
  final int id;

  /// 钓点名称
  final String name;

  /// 地址
  final String address;

  /// 纬度
  final double latitude;

  /// 经度
  final double longitude;

  /// 钓点类型 (官方认证、用户推荐等)
  final String type;

  /// 评分 (用于地图标记颜色区分)
  final double? rating;

  /// 评论数量
  final int reviewCount;

  /// 是否付费
  final bool isPaid;

  /// 是否有设施
  final bool hasFacilities;

  /// 鱼类列表
  final List<String> fishTypes;

  /// 图片URL (可选)
  final String? imageUrl;

  /// 距离 (可选，仅在基于位置搜索时有值)
  final double? distance;

  /// 签到次数
  final int checkinCount;

  const SpotMapVo({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.type,
    this.rating,
    required this.reviewCount,
    required this.isPaid,
    required this.hasFacilities,
    required this.fishTypes,
    this.imageUrl,
    this.distance,
    this.checkinCount = 0,
  });

  static const fromMap = SpotMapVoMapper.fromMap;
  static const fromJson = SpotMapVoMapper.fromJson;
}
