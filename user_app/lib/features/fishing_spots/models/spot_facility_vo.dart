import 'package:dart_mappable/dart_mappable.dart';

part 'spot_facility_vo.mapper.dart';

@MappableClass()
class SpotFacilityVo with SpotFacilityVoMappable {
  ///设施ID
  final num id;

  ///设施名称
  final String name;

  ///设施图标
  final String icon;

  ///设施描述
  final String? description;

  ///该钓点特定设施的详细信息，如"收费"、"24小时开放"等
  final String? details;

  const SpotFacilityVo({
    required this.id,
    required this.name,
    required this.icon,
    this.description,
    this.details,
  });

  static final fromMap = SpotFacilityVoMapper.fromMap;
}
