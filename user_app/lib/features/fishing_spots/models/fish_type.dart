import 'package:dart_mappable/dart_mappable.dart';

part 'fish_type.mapper.dart';

@MappableClass()
class FishType with FishTypeMappable {
  final num id;
  final String name;
  final String? description;
  final String? imageUrl;
  final bool seasonSpring;
  final bool seasonSummer;
  final bool seasonAutumn;
  final bool seasonWinter;

  FishType({
    required this.id,
    required this.name,
    this.description,
    this.imageUrl,
    this.seasonSpring = false,
    this.seasonSummer = false,
    this.seasonAutumn = false,
    this.seasonWinter = false,
  });

  static final fromMap = FishTypeMapper.fromMap;
}
