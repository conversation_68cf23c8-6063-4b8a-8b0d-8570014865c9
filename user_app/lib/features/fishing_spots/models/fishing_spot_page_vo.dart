import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_map_vo.dart';

part 'fishing_spot_page_vo.mapper.dart';

/// 钓点完整信息分页响应模型 - 用于详细列表展示（legacy）
@MappableClass()
class FishingSpotPageVo with FishingSpotPageVoMappable {
  final List<FishingSpotVo> content;
  final int totalPages;
  final int totalElements;
  final int size;
  final int number;
  final bool first;
  final bool last;

  const FishingSpotPageVo({
    required this.content,
    required this.totalPages,
    required this.totalElements,
    required this.size,
    required this.number,
    required this.first,
    required this.last,
  });

  static final fromMap = FishingSpotPageVoMapper.fromMap;
}

/// 钓点摘要分页响应模型 - 用于列表展示（推荐）
@MappableClass()
class SpotSummaryPageVo with SpotSummaryPageVoMappable {
  final List<SpotSummaryVo> content;
  final int totalPages;
  final int totalElements;
  final int size;
  final int number;
  final bool first;
  final bool last;

  const SpotSummaryPageVo({
    required this.content,
    required this.totalPages,
    required this.totalElements,
    required this.size,
    required this.number,
    required this.first,
    required this.last,
  });

  static final fromMap = SpotSummaryPageVoMapper.fromMap;

  /// 转换为FishingSpotPageVo以兼容现有代码
  FishingSpotPageVo toFishingSpotPageVo() {
    return FishingSpotPageVo(
      content: content.map((spot) => spot.toFishingSpotVo()).toList(),
      totalPages: totalPages,
      totalElements: totalElements,
      size: size,
      number: number,
      first: first,
      last: last,
    );
  }
}

/// 钓点地图分页响应模型 - 用于地图展示（推荐）
@MappableClass()
class SpotMapPageVo with SpotMapPageVoMappable {
  final List<SpotMapVo> content;
  final int totalPages;
  final int totalElements;
  final int size;
  final int number;
  final bool first;
  final bool last;

  const SpotMapPageVo({
    required this.content,
    required this.totalPages,
    required this.totalElements,
    required this.size,
    required this.number,
    required this.first,
    required this.last,
  });

  static final fromMap = SpotMapPageVoMapper.fromMap;
}
