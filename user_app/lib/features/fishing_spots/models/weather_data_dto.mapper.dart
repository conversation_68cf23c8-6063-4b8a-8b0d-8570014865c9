// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'weather_data_dto.dart';

class WeatherDataDtoMapper extends ClassMapperBase<WeatherDataDto> {
  WeatherDataDtoMapper._();

  static WeatherDataDtoMapper? _instance;
  static WeatherDataDtoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = WeatherDataDtoMapper._());
      ForeCastMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'WeatherDataDto';

  static String _$province(WeatherDataDto v) => v.province;
  static const Field<WeatherDataDto, String> _f$province =
      Field('province', _$province);
  static String _$city(WeatherDataDto v) => v.city;
  static const Field<WeatherDataDto, String> _f$city = Field('city', _$city);
  static String _$adCode(WeatherDataDto v) => v.adCode;
  static const Field<WeatherDataDto, String> _f$adCode =
      Field('adCode', _$adCode, key: r'ad_code');
  static String _$weather(WeatherDataDto v) => v.weather;
  static const Field<WeatherDataDto, String> _f$weather =
      Field('weather', _$weather);
  static String _$temperature(WeatherDataDto v) => v.temperature;
  static const Field<WeatherDataDto, String> _f$temperature =
      Field('temperature', _$temperature);
  static String _$windDirection(WeatherDataDto v) => v.windDirection;
  static const Field<WeatherDataDto, String> _f$windDirection =
      Field('windDirection', _$windDirection, key: r'wind_direction');
  static String _$windPower(WeatherDataDto v) => v.windPower;
  static const Field<WeatherDataDto, String> _f$windPower =
      Field('windPower', _$windPower, key: r'wind_power');
  static String _$humidity(WeatherDataDto v) => v.humidity;
  static const Field<WeatherDataDto, String> _f$humidity =
      Field('humidity', _$humidity);
  static String _$reportTime(WeatherDataDto v) => v.reportTime;
  static const Field<WeatherDataDto, String> _f$reportTime =
      Field('reportTime', _$reportTime, key: r'report_time');
  static String _$temperatureFloat(WeatherDataDto v) => v.temperatureFloat;
  static const Field<WeatherDataDto, String> _f$temperatureFloat =
      Field('temperatureFloat', _$temperatureFloat, key: r'temperature_float');
  static String _$humidityFloat(WeatherDataDto v) => v.humidityFloat;
  static const Field<WeatherDataDto, String> _f$humidityFloat =
      Field('humidityFloat', _$humidityFloat, key: r'humidity_float');
  static List<ForeCast>? _$foreCast(WeatherDataDto v) => v.foreCast;
  static const Field<WeatherDataDto, List<ForeCast>> _f$foreCast =
      Field('foreCast', _$foreCast, key: r'fore_cast', opt: true);

  @override
  final MappableFields<WeatherDataDto> fields = const {
    #province: _f$province,
    #city: _f$city,
    #adCode: _f$adCode,
    #weather: _f$weather,
    #temperature: _f$temperature,
    #windDirection: _f$windDirection,
    #windPower: _f$windPower,
    #humidity: _f$humidity,
    #reportTime: _f$reportTime,
    #temperatureFloat: _f$temperatureFloat,
    #humidityFloat: _f$humidityFloat,
    #foreCast: _f$foreCast,
  };

  static WeatherDataDto _instantiate(DecodingData data) {
    return WeatherDataDto(
        province: data.dec(_f$province),
        city: data.dec(_f$city),
        adCode: data.dec(_f$adCode),
        weather: data.dec(_f$weather),
        temperature: data.dec(_f$temperature),
        windDirection: data.dec(_f$windDirection),
        windPower: data.dec(_f$windPower),
        humidity: data.dec(_f$humidity),
        reportTime: data.dec(_f$reportTime),
        temperatureFloat: data.dec(_f$temperatureFloat),
        humidityFloat: data.dec(_f$humidityFloat),
        foreCast: data.dec(_f$foreCast));
  }

  @override
  final Function instantiate = _instantiate;

  static WeatherDataDto fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<WeatherDataDto>(map);
  }

  static WeatherDataDto fromJson(String json) {
    return ensureInitialized().decodeJson<WeatherDataDto>(json);
  }
}

mixin WeatherDataDtoMappable {
  String toJson() {
    return WeatherDataDtoMapper.ensureInitialized()
        .encodeJson<WeatherDataDto>(this as WeatherDataDto);
  }

  Map<String, dynamic> toMap() {
    return WeatherDataDtoMapper.ensureInitialized()
        .encodeMap<WeatherDataDto>(this as WeatherDataDto);
  }

  WeatherDataDtoCopyWith<WeatherDataDto, WeatherDataDto, WeatherDataDto>
      get copyWith =>
          _WeatherDataDtoCopyWithImpl<WeatherDataDto, WeatherDataDto>(
              this as WeatherDataDto, $identity, $identity);
  @override
  String toString() {
    return WeatherDataDtoMapper.ensureInitialized()
        .stringifyValue(this as WeatherDataDto);
  }

  @override
  bool operator ==(Object other) {
    return WeatherDataDtoMapper.ensureInitialized()
        .equalsValue(this as WeatherDataDto, other);
  }

  @override
  int get hashCode {
    return WeatherDataDtoMapper.ensureInitialized()
        .hashValue(this as WeatherDataDto);
  }
}

extension WeatherDataDtoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, WeatherDataDto, $Out> {
  WeatherDataDtoCopyWith<$R, WeatherDataDto, $Out> get $asWeatherDataDto =>
      $base.as((v, t, t2) => _WeatherDataDtoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class WeatherDataDtoCopyWith<$R, $In extends WeatherDataDto, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, ForeCast, ForeCastCopyWith<$R, ForeCast, ForeCast>>?
      get foreCast;
  $R call(
      {String? province,
      String? city,
      String? adCode,
      String? weather,
      String? temperature,
      String? windDirection,
      String? windPower,
      String? humidity,
      String? reportTime,
      String? temperatureFloat,
      String? humidityFloat,
      List<ForeCast>? foreCast});
  WeatherDataDtoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _WeatherDataDtoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, WeatherDataDto, $Out>
    implements WeatherDataDtoCopyWith<$R, WeatherDataDto, $Out> {
  _WeatherDataDtoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<WeatherDataDto> $mapper =
      WeatherDataDtoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, ForeCast, ForeCastCopyWith<$R, ForeCast, ForeCast>>?
      get foreCast => $value.foreCast != null
          ? ListCopyWith($value.foreCast!, (v, t) => v.copyWith.$chain(t),
              (v) => call(foreCast: v))
          : null;
  @override
  $R call(
          {String? province,
          String? city,
          String? adCode,
          String? weather,
          String? temperature,
          String? windDirection,
          String? windPower,
          String? humidity,
          String? reportTime,
          String? temperatureFloat,
          String? humidityFloat,
          Object? foreCast = $none}) =>
      $apply(FieldCopyWithData({
        if (province != null) #province: province,
        if (city != null) #city: city,
        if (adCode != null) #adCode: adCode,
        if (weather != null) #weather: weather,
        if (temperature != null) #temperature: temperature,
        if (windDirection != null) #windDirection: windDirection,
        if (windPower != null) #windPower: windPower,
        if (humidity != null) #humidity: humidity,
        if (reportTime != null) #reportTime: reportTime,
        if (temperatureFloat != null) #temperatureFloat: temperatureFloat,
        if (humidityFloat != null) #humidityFloat: humidityFloat,
        if (foreCast != $none) #foreCast: foreCast
      }));
  @override
  WeatherDataDto $make(CopyWithData data) => WeatherDataDto(
      province: data.get(#province, or: $value.province),
      city: data.get(#city, or: $value.city),
      adCode: data.get(#adCode, or: $value.adCode),
      weather: data.get(#weather, or: $value.weather),
      temperature: data.get(#temperature, or: $value.temperature),
      windDirection: data.get(#windDirection, or: $value.windDirection),
      windPower: data.get(#windPower, or: $value.windPower),
      humidity: data.get(#humidity, or: $value.humidity),
      reportTime: data.get(#reportTime, or: $value.reportTime),
      temperatureFloat:
          data.get(#temperatureFloat, or: $value.temperatureFloat),
      humidityFloat: data.get(#humidityFloat, or: $value.humidityFloat),
      foreCast: data.get(#foreCast, or: $value.foreCast));

  @override
  WeatherDataDtoCopyWith<$R2, WeatherDataDto, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _WeatherDataDtoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class ForeCastMapper extends ClassMapperBase<ForeCast> {
  ForeCastMapper._();

  static ForeCastMapper? _instance;
  static ForeCastMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = ForeCastMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'ForeCast';

  static String _$date(ForeCast v) => v.date;
  static const Field<ForeCast, String> _f$date = Field('date', _$date);
  static String _$week(ForeCast v) => v.week;
  static const Field<ForeCast, String> _f$week = Field('week', _$week);
  static String _$dayWeather(ForeCast v) => v.dayWeather;
  static const Field<ForeCast, String> _f$dayWeather =
      Field('dayWeather', _$dayWeather, key: r'day_weather');
  static String _$nightWeather(ForeCast v) => v.nightWeather;
  static const Field<ForeCast, String> _f$nightWeather =
      Field('nightWeather', _$nightWeather, key: r'night_weather');
  static String _$dayTemp(ForeCast v) => v.dayTemp;
  static const Field<ForeCast, String> _f$dayTemp =
      Field('dayTemp', _$dayTemp, key: r'day_temp');
  static String _$nightTemp(ForeCast v) => v.nightTemp;
  static const Field<ForeCast, String> _f$nightTemp =
      Field('nightTemp', _$nightTemp, key: r'night_temp');
  static String _$dayWindDirection(ForeCast v) => v.dayWindDirection;
  static const Field<ForeCast, String> _f$dayWindDirection =
      Field('dayWindDirection', _$dayWindDirection, key: r'day_wind_direction');
  static String _$nightWindDirection(ForeCast v) => v.nightWindDirection;
  static const Field<ForeCast, String> _f$nightWindDirection = Field(
      'nightWindDirection', _$nightWindDirection,
      key: r'night_wind_direction');
  static String _$dayWindPower(ForeCast v) => v.dayWindPower;
  static const Field<ForeCast, String> _f$dayWindPower =
      Field('dayWindPower', _$dayWindPower, key: r'day_wind_power');
  static String _$nightWindPower(ForeCast v) => v.nightWindPower;
  static const Field<ForeCast, String> _f$nightWindPower =
      Field('nightWindPower', _$nightWindPower, key: r'night_wind_power');

  @override
  final MappableFields<ForeCast> fields = const {
    #date: _f$date,
    #week: _f$week,
    #dayWeather: _f$dayWeather,
    #nightWeather: _f$nightWeather,
    #dayTemp: _f$dayTemp,
    #nightTemp: _f$nightTemp,
    #dayWindDirection: _f$dayWindDirection,
    #nightWindDirection: _f$nightWindDirection,
    #dayWindPower: _f$dayWindPower,
    #nightWindPower: _f$nightWindPower,
  };

  static ForeCast _instantiate(DecodingData data) {
    return ForeCast(
        date: data.dec(_f$date),
        week: data.dec(_f$week),
        dayWeather: data.dec(_f$dayWeather),
        nightWeather: data.dec(_f$nightWeather),
        dayTemp: data.dec(_f$dayTemp),
        nightTemp: data.dec(_f$nightTemp),
        dayWindDirection: data.dec(_f$dayWindDirection),
        nightWindDirection: data.dec(_f$nightWindDirection),
        dayWindPower: data.dec(_f$dayWindPower),
        nightWindPower: data.dec(_f$nightWindPower));
  }

  @override
  final Function instantiate = _instantiate;

  static ForeCast fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<ForeCast>(map);
  }

  static ForeCast fromJson(String json) {
    return ensureInitialized().decodeJson<ForeCast>(json);
  }
}

mixin ForeCastMappable {
  String toJson() {
    return ForeCastMapper.ensureInitialized()
        .encodeJson<ForeCast>(this as ForeCast);
  }

  Map<String, dynamic> toMap() {
    return ForeCastMapper.ensureInitialized()
        .encodeMap<ForeCast>(this as ForeCast);
  }

  ForeCastCopyWith<ForeCast, ForeCast, ForeCast> get copyWith =>
      _ForeCastCopyWithImpl<ForeCast, ForeCast>(
          this as ForeCast, $identity, $identity);
  @override
  String toString() {
    return ForeCastMapper.ensureInitialized().stringifyValue(this as ForeCast);
  }

  @override
  bool operator ==(Object other) {
    return ForeCastMapper.ensureInitialized()
        .equalsValue(this as ForeCast, other);
  }

  @override
  int get hashCode {
    return ForeCastMapper.ensureInitialized().hashValue(this as ForeCast);
  }
}

extension ForeCastValueCopy<$R, $Out> on ObjectCopyWith<$R, ForeCast, $Out> {
  ForeCastCopyWith<$R, ForeCast, $Out> get $asForeCast =>
      $base.as((v, t, t2) => _ForeCastCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class ForeCastCopyWith<$R, $In extends ForeCast, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {String? date,
      String? week,
      String? dayWeather,
      String? nightWeather,
      String? dayTemp,
      String? nightTemp,
      String? dayWindDirection,
      String? nightWindDirection,
      String? dayWindPower,
      String? nightWindPower});
  ForeCastCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _ForeCastCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, ForeCast, $Out>
    implements ForeCastCopyWith<$R, ForeCast, $Out> {
  _ForeCastCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<ForeCast> $mapper =
      ForeCastMapper.ensureInitialized();
  @override
  $R call(
          {String? date,
          String? week,
          String? dayWeather,
          String? nightWeather,
          String? dayTemp,
          String? nightTemp,
          String? dayWindDirection,
          String? nightWindDirection,
          String? dayWindPower,
          String? nightWindPower}) =>
      $apply(FieldCopyWithData({
        if (date != null) #date: date,
        if (week != null) #week: week,
        if (dayWeather != null) #dayWeather: dayWeather,
        if (nightWeather != null) #nightWeather: nightWeather,
        if (dayTemp != null) #dayTemp: dayTemp,
        if (nightTemp != null) #nightTemp: nightTemp,
        if (dayWindDirection != null) #dayWindDirection: dayWindDirection,
        if (nightWindDirection != null) #nightWindDirection: nightWindDirection,
        if (dayWindPower != null) #dayWindPower: dayWindPower,
        if (nightWindPower != null) #nightWindPower: nightWindPower
      }));
  @override
  ForeCast $make(CopyWithData data) => ForeCast(
      date: data.get(#date, or: $value.date),
      week: data.get(#week, or: $value.week),
      dayWeather: data.get(#dayWeather, or: $value.dayWeather),
      nightWeather: data.get(#nightWeather, or: $value.nightWeather),
      dayTemp: data.get(#dayTemp, or: $value.dayTemp),
      nightTemp: data.get(#nightTemp, or: $value.nightTemp),
      dayWindDirection:
          data.get(#dayWindDirection, or: $value.dayWindDirection),
      nightWindDirection:
          data.get(#nightWindDirection, or: $value.nightWindDirection),
      dayWindPower: data.get(#dayWindPower, or: $value.dayWindPower),
      nightWindPower: data.get(#nightWindPower, or: $value.nightWindPower));

  @override
  ForeCastCopyWith<$R2, ForeCast, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _ForeCastCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
