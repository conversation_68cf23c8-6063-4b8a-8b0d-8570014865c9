// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'fishing_recommendation.dart';

class FishingRecommendationMapper
    extends ClassMapperBase<FishingRecommendation> {
  FishingRecommendationMapper._();

  static FishingRecommendationMapper? _instance;
  static FishingRecommendationMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = FishingRecommendationMapper._());
      FishTypeMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'FishingRecommendation';

  static double _$fishingIndex(FishingRecommendation v) => v.fishingIndex;
  static const Field<FishingRecommendation, double> _f$fishingIndex =
      Field('fishingIndex', _$fishingIndex, key: r'fishing_index');
  static List<FishType> _$recommendedFish(FishingRecommendation v) =>
      v.recommendedFish;
  static const Field<FishingRecommendation, List<FishType>> _f$recommendedFish =
      Field('recommendedFish', _$recommendedFish, key: r'recommended_fish');
  static String _$weatherAnalysis(FishingRecommendation v) => v.weatherAnalysis;
  static const Field<FishingRecommendation, String> _f$weatherAnalysis =
      Field('weatherAnalysis', _$weatherAnalysis, key: r'weather_analysis');

  @override
  final MappableFields<FishingRecommendation> fields = const {
    #fishingIndex: _f$fishingIndex,
    #recommendedFish: _f$recommendedFish,
    #weatherAnalysis: _f$weatherAnalysis,
  };

  static FishingRecommendation _instantiate(DecodingData data) {
    return FishingRecommendation(
        fishingIndex: data.dec(_f$fishingIndex),
        recommendedFish: data.dec(_f$recommendedFish),
        weatherAnalysis: data.dec(_f$weatherAnalysis));
  }

  @override
  final Function instantiate = _instantiate;

  static FishingRecommendation fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<FishingRecommendation>(map);
  }

  static FishingRecommendation fromJson(String json) {
    return ensureInitialized().decodeJson<FishingRecommendation>(json);
  }
}

mixin FishingRecommendationMappable {
  String toJson() {
    return FishingRecommendationMapper.ensureInitialized()
        .encodeJson<FishingRecommendation>(this as FishingRecommendation);
  }

  Map<String, dynamic> toMap() {
    return FishingRecommendationMapper.ensureInitialized()
        .encodeMap<FishingRecommendation>(this as FishingRecommendation);
  }

  FishingRecommendationCopyWith<FishingRecommendation, FishingRecommendation,
      FishingRecommendation> get copyWith => _FishingRecommendationCopyWithImpl<
          FishingRecommendation, FishingRecommendation>(
      this as FishingRecommendation, $identity, $identity);
  @override
  String toString() {
    return FishingRecommendationMapper.ensureInitialized()
        .stringifyValue(this as FishingRecommendation);
  }

  @override
  bool operator ==(Object other) {
    return FishingRecommendationMapper.ensureInitialized()
        .equalsValue(this as FishingRecommendation, other);
  }

  @override
  int get hashCode {
    return FishingRecommendationMapper.ensureInitialized()
        .hashValue(this as FishingRecommendation);
  }
}

extension FishingRecommendationValueCopy<$R, $Out>
    on ObjectCopyWith<$R, FishingRecommendation, $Out> {
  FishingRecommendationCopyWith<$R, FishingRecommendation, $Out>
      get $asFishingRecommendation => $base.as(
          (v, t, t2) => _FishingRecommendationCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class FishingRecommendationCopyWith<
    $R,
    $In extends FishingRecommendation,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, FishType, FishTypeCopyWith<$R, FishType, FishType>>
      get recommendedFish;
  $R call(
      {double? fishingIndex,
      List<FishType>? recommendedFish,
      String? weatherAnalysis});
  FishingRecommendationCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _FishingRecommendationCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, FishingRecommendation, $Out>
    implements FishingRecommendationCopyWith<$R, FishingRecommendation, $Out> {
  _FishingRecommendationCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<FishingRecommendation> $mapper =
      FishingRecommendationMapper.ensureInitialized();
  @override
  ListCopyWith<$R, FishType, FishTypeCopyWith<$R, FishType, FishType>>
      get recommendedFish => ListCopyWith($value.recommendedFish,
          (v, t) => v.copyWith.$chain(t), (v) => call(recommendedFish: v));
  @override
  $R call(
          {double? fishingIndex,
          List<FishType>? recommendedFish,
          String? weatherAnalysis}) =>
      $apply(FieldCopyWithData({
        if (fishingIndex != null) #fishingIndex: fishingIndex,
        if (recommendedFish != null) #recommendedFish: recommendedFish,
        if (weatherAnalysis != null) #weatherAnalysis: weatherAnalysis
      }));
  @override
  FishingRecommendation $make(CopyWithData data) => FishingRecommendation(
      fishingIndex: data.get(#fishingIndex, or: $value.fishingIndex),
      recommendedFish: data.get(#recommendedFish, or: $value.recommendedFish),
      weatherAnalysis: data.get(#weatherAnalysis, or: $value.weatherAnalysis));

  @override
  FishingRecommendationCopyWith<$R2, FishingRecommendation, $Out2>
      $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
          _FishingRecommendationCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
