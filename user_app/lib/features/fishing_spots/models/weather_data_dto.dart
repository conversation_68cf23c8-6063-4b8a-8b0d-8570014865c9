import 'package:dart_mappable/dart_mappable.dart';

part 'weather_data_dto.mapper.dart';

@MappableClass()
class WeatherDataDto with WeatherDataDtoMappable {
  final String province;
  final String city;
  final String adCode;
  final String weather;
  final String temperature;
  final String windDirection;
  final String windPower;
  final String humidity;
  final String reportTime;
  final String temperatureFloat;
  final String humidityFloat;
  final List<ForeCast>? foreCast;

  WeatherDataDto({
    required this.province,
    required this.city,
    required this.adCode,
    required this.weather,
    required this.temperature,
    required this.windDirection,
    required this.windPower,
    required this.humidity,
    required this.reportTime,
    required this.temperatureFloat,
    required this.humidityFloat,
    this.foreCast,
  });

  static final fromMap = WeatherDataDtoMapper.fromMap;
}

@MappableClass()
class ForeCast with ForeCastMappable {
  final String date;
  final String week;
  final String dayWeather;
  final String nightWeather;
  final String dayTemp;
  final String nightTemp;
  final String dayWindDirection;
  final String nightWindDirection;
  final String dayWindPower;
  final String nightWindPower;

  ForeCast({
    required this.date,
    required this.week,
    required this.dayWeather,
    required this.nightWeather,
    required this.dayTemp,
    required this.nightTemp,
    required this.dayWindDirection,
    required this.nightWindDirection,
    required this.dayWindPower,
    required this.nightWindPower,
  });

  static final fromMap = ForeCastMapper.fromMap;
}
