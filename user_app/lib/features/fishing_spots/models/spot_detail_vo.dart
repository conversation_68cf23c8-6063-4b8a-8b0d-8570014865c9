import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/models/spot_price_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_facility_vo.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/user.dart';

part 'spot_detail_vo.mapper.dart';

/// 钓点详情信息VO - 对应后端SpotDetailVO，用于详情展示
@MappableClass()
class SpotDetailVo with SpotDetailVoMappable {
  final int id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String province;
  final String city;
  final String county;
  final bool isOfficial;
  final int verificationLevel;
  final String? description;
  final int visitorCount;
  final double rating;
  final bool hasFacilities;
  final bool isPaid;
  final int checkinCount;
  final String? extraFishTypes;
  final String? extraFacilities;
  final int? createdBy;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? status;
  final String? source;
  final String? visibility;

  final List<FishType> fishTypeList;

  /// 钓点图片URL列表
  final List<String> images;

  /// 认证文件URL列表
  final List<String> certificationDocuments;

  /// 钓点价格列表
  final List<SpotPriceVo> prices;

  /// 钓点设施列表
  final List<SpotFacilityVo> facilities;

  final double? distanceKm;

  /// 最近动态数量（最近7天）
  final int recentMomentsCount;

  /// 最新动态
  final MomentVo? latestMoment;

  /// 创建者信息
  final User? creator;

  const SpotDetailVo({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.province,
    required this.city,
    required this.county,
    required this.isOfficial,
    required this.verificationLevel,
    this.description,
    required this.visitorCount,
    required this.rating,
    required this.hasFacilities,
    required this.isPaid,
    required this.checkinCount,
    this.extraFishTypes,
    this.extraFacilities,
    this.createdBy,
    this.createdAt,
    this.updatedAt,
    this.status,
    this.source,
    this.visibility,
    this.fishTypeList = const [],
    this.images = const [],
    this.certificationDocuments = const [],
    this.prices = const [],
    this.facilities = const [],
    this.distanceKm,
    this.recentMomentsCount = 0,
    this.latestMoment,
    this.creator,
  });

  static final fromMap = SpotDetailVoMapper.fromMap;

  /// 获取解析后的自定义鱼类列表
  List<String> getExtraFishTypesList() {
    if (extraFishTypes == null || extraFishTypes!.isEmpty) {
      return [];
    }
    return extraFishTypes!
        .split(',')
        .map((e) => e.trim())
        .where((e) => e.isNotEmpty)
        .toList();
  }

  /// 获取解析后的自定义设施列表
  List<String> getExtraFacilitiesList() {
    if (extraFacilities == null || extraFacilities!.isEmpty) {
      return [];
    }
    return extraFacilities!
        .split(',')
        .map((e) => e.trim())
        .where((e) => e.isNotEmpty)
        .toList();
  }

  /// 转换为FishingSpotVo以兼容现有代码
  FishingSpotVo toFishingSpotVo() {
    // 计算季节信息
    Set<String> seasonsSet = {};
    for (final fishType in fishTypeList) {
      if (fishType.seasonSpring == true) seasonsSet.add('春');
      if (fishType.seasonSummer == true) seasonsSet.add('夏');
      if (fishType.seasonAutumn == true) seasonsSet.add('秋');
      if (fishType.seasonWinter == true) seasonsSet.add('冬');
    }

    return FishingSpotVo(
      id: id,
      name: name,
      address: address,
      latitude: latitude,
      longitude: longitude,
      province: province,
      city: city,
      county: county,
      isOfficial: isOfficial,
      verificationLevel: verificationLevel,
      description: description,
      images: images,
      fishTypeList: fishTypeList,
      visitorCount: visitorCount,
      rating: rating,
      hasFacilities: hasFacilities,
      isPaid: isPaid,
      checkinCount: checkinCount,
      seasons: seasonsSet.toList(),
      prices: prices,
      facilities: facilities,
      extraFishTypes: extraFishTypes,
      extraFishTypesList: getExtraFishTypesList(),
      extraFacilities: extraFacilities,
      extraFacilitiesList: getExtraFacilitiesList(),
      recentMomentsCount: recentMomentsCount,
      createdBy: createdBy,
      creator: creator,
    );
  }
}
