import 'package:dart_mappable/dart_mappable.dart';

// 使用以下命令生成mapper文件:
// flutter pub run build_runner build --delete-conflicting-outputs
part 'spot_price.mapper.dart';

@MappableClass()
class SpotPrice with SpotPriceMappable {
  static const PRICE_TYPE_DAY = 1;
  static const PRICE_TYPE_HOUR = 2;

  final int priceType; // 1: per day, 2: per hour

  @MappableField(key: 'price_type_name')
  final String priceTypeName; // "按天计费", "按小时计费"

  final double price;
  final int? hours; // Only used when priceTypeId is PRICE_TYPE_HOUR
  final String? description;

  @MappableField(key: 'fish_type_id')
  final int? fishTypeId; // Optional - if null, applies to all fish types

  @MappableField(key: 'fish_type_name')
  final String? fishTypeName; // Fish type name for display

  const SpotPrice({
    required this.priceType,
    required this.priceTypeName,
    required this.price,
    this.hours,
    this.description,
    this.fishTypeId,
    this.fishTypeName,
  });

  static const fromMap = SpotPriceMapper.fromMap;
}
