// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'create_spot_dto.dart';

class CreateSpotDtoMapper extends ClassMapperBase<CreateSpotDto> {
  CreateSpotDtoMapper._();

  static CreateSpotDtoMapper? _instance;
  static CreateSpotDtoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = CreateSpotDtoMapper._());
      SpotFacilityMapper.ensureInitialized();
      SpotPriceMapper.ensureInitialized();
      UploadedImageMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'CreateSpotDto';

  static String _$name(CreateSpotDto v) => v.name;
  static const Field<CreateSpotDto, String> _f$name = Field('name', _$name);
  static String _$description(CreateSpotDto v) => v.description;
  static const Field<CreateSpotDto, String> _f$description =
      Field('description', _$description);
  static List<String> _$imageUrls(CreateSpotDto v) => v.imageUrls;
  static const Field<CreateSpotDto, List<String>> _f$imageUrls =
      Field('imageUrls', _$imageUrls, key: r'image_urls');
  static double _$latitude(CreateSpotDto v) => v.latitude;
  static const Field<CreateSpotDto, double> _f$latitude =
      Field('latitude', _$latitude);
  static double _$longitude(CreateSpotDto v) => v.longitude;
  static const Field<CreateSpotDto, double> _f$longitude =
      Field('longitude', _$longitude);
  static String _$address(CreateSpotDto v) => v.address;
  static const Field<CreateSpotDto, String> _f$address =
      Field('address', _$address);
  static String? _$province(CreateSpotDto v) => v.province;
  static const Field<CreateSpotDto, String> _f$province =
      Field('province', _$province, opt: true);
  static String? _$city(CreateSpotDto v) => v.city;
  static const Field<CreateSpotDto, String> _f$city =
      Field('city', _$city, opt: true);
  static String? _$county(CreateSpotDto v) => v.county;
  static const Field<CreateSpotDto, String> _f$county =
      Field('county', _$county, opt: true);
  static List<String>? _$fishTypeNames(CreateSpotDto v) => v.fishTypeNames;
  static const Field<CreateSpotDto, List<String>> _f$fishTypeNames = Field(
      'fishTypeNames', _$fishTypeNames,
      key: r'fish_type_names', opt: true);
  static List<String>? _$extraFishTypes(CreateSpotDto v) => v.extraFishTypes;
  static const Field<CreateSpotDto, List<String>> _f$extraFishTypes = Field(
      'extraFishTypes', _$extraFishTypes,
      key: r'extra_fish_types', opt: true);
  static bool _$hasFacilities(CreateSpotDto v) => v.hasFacilities;
  static const Field<CreateSpotDto, bool> _f$hasFacilities =
      Field('hasFacilities', _$hasFacilities, key: r'has_facilities');
  static List<SpotFacility> _$facilities(CreateSpotDto v) => v.facilities;
  static const Field<CreateSpotDto, List<SpotFacility>> _f$facilities =
      Field('facilities', _$facilities);
  static List<String>? _$extraFacilities(CreateSpotDto v) => v.extraFacilities;
  static const Field<CreateSpotDto, List<String>> _f$extraFacilities = Field(
      'extraFacilities', _$extraFacilities,
      key: r'extra_facilities', opt: true);
  static bool _$isPaid(CreateSpotDto v) => v.isPaid;
  static const Field<CreateSpotDto, bool> _f$isPaid =
      Field('isPaid', _$isPaid, key: r'is_paid');
  static List<SpotPrice> _$prices(CreateSpotDto v) => v.prices;
  static const Field<CreateSpotDto, List<SpotPrice>> _f$prices =
      Field('prices', _$prices);
  static bool _$isOfficial(CreateSpotDto v) => v.isOfficial;
  static const Field<CreateSpotDto, bool> _f$isOfficial =
      Field('isOfficial', _$isOfficial, key: r'is_official');
  static List<UploadedImage> _$certificationDocuments(CreateSpotDto v) =>
      v.certificationDocuments;
  static const Field<CreateSpotDto, List<UploadedImage>>
      _f$certificationDocuments = Field(
          'certificationDocuments', _$certificationDocuments,
          key: r'certification_documents');
  static String _$visibility(CreateSpotDto v) => v.visibility;
  static const Field<CreateSpotDto, String> _f$visibility =
      Field('visibility', _$visibility);

  @override
  final MappableFields<CreateSpotDto> fields = const {
    #name: _f$name,
    #description: _f$description,
    #imageUrls: _f$imageUrls,
    #latitude: _f$latitude,
    #longitude: _f$longitude,
    #address: _f$address,
    #province: _f$province,
    #city: _f$city,
    #county: _f$county,
    #fishTypeNames: _f$fishTypeNames,
    #extraFishTypes: _f$extraFishTypes,
    #hasFacilities: _f$hasFacilities,
    #facilities: _f$facilities,
    #extraFacilities: _f$extraFacilities,
    #isPaid: _f$isPaid,
    #prices: _f$prices,
    #isOfficial: _f$isOfficial,
    #certificationDocuments: _f$certificationDocuments,
    #visibility: _f$visibility,
  };

  static CreateSpotDto _instantiate(DecodingData data) {
    return CreateSpotDto(
        name: data.dec(_f$name),
        description: data.dec(_f$description),
        imageUrls: data.dec(_f$imageUrls),
        latitude: data.dec(_f$latitude),
        longitude: data.dec(_f$longitude),
        address: data.dec(_f$address),
        province: data.dec(_f$province),
        city: data.dec(_f$city),
        county: data.dec(_f$county),
        fishTypeNames: data.dec(_f$fishTypeNames),
        extraFishTypes: data.dec(_f$extraFishTypes),
        hasFacilities: data.dec(_f$hasFacilities),
        facilities: data.dec(_f$facilities),
        extraFacilities: data.dec(_f$extraFacilities),
        isPaid: data.dec(_f$isPaid),
        prices: data.dec(_f$prices),
        isOfficial: data.dec(_f$isOfficial),
        certificationDocuments: data.dec(_f$certificationDocuments),
        visibility: data.dec(_f$visibility));
  }

  @override
  final Function instantiate = _instantiate;

  static CreateSpotDto fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<CreateSpotDto>(map);
  }

  static CreateSpotDto fromJson(String json) {
    return ensureInitialized().decodeJson<CreateSpotDto>(json);
  }
}

mixin CreateSpotDtoMappable {
  String toJson() {
    return CreateSpotDtoMapper.ensureInitialized()
        .encodeJson<CreateSpotDto>(this as CreateSpotDto);
  }

  Map<String, dynamic> toMap() {
    return CreateSpotDtoMapper.ensureInitialized()
        .encodeMap<CreateSpotDto>(this as CreateSpotDto);
  }

  CreateSpotDtoCopyWith<CreateSpotDto, CreateSpotDto, CreateSpotDto>
      get copyWith => _CreateSpotDtoCopyWithImpl<CreateSpotDto, CreateSpotDto>(
          this as CreateSpotDto, $identity, $identity);
  @override
  String toString() {
    return CreateSpotDtoMapper.ensureInitialized()
        .stringifyValue(this as CreateSpotDto);
  }

  @override
  bool operator ==(Object other) {
    return CreateSpotDtoMapper.ensureInitialized()
        .equalsValue(this as CreateSpotDto, other);
  }

  @override
  int get hashCode {
    return CreateSpotDtoMapper.ensureInitialized()
        .hashValue(this as CreateSpotDto);
  }
}

extension CreateSpotDtoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, CreateSpotDto, $Out> {
  CreateSpotDtoCopyWith<$R, CreateSpotDto, $Out> get $asCreateSpotDto =>
      $base.as((v, t, t2) => _CreateSpotDtoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class CreateSpotDtoCopyWith<$R, $In extends CreateSpotDto, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get imageUrls;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>?
      get fishTypeNames;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>?
      get extraFishTypes;
  ListCopyWith<$R, SpotFacility,
      SpotFacilityCopyWith<$R, SpotFacility, SpotFacility>> get facilities;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>?
      get extraFacilities;
  ListCopyWith<$R, SpotPrice, SpotPriceCopyWith<$R, SpotPrice, SpotPrice>>
      get prices;
  ListCopyWith<$R, UploadedImage,
          UploadedImageCopyWith<$R, UploadedImage, UploadedImage>>
      get certificationDocuments;
  $R call(
      {String? name,
      String? description,
      List<String>? imageUrls,
      double? latitude,
      double? longitude,
      String? address,
      String? province,
      String? city,
      String? county,
      List<String>? fishTypeNames,
      List<String>? extraFishTypes,
      bool? hasFacilities,
      List<SpotFacility>? facilities,
      List<String>? extraFacilities,
      bool? isPaid,
      List<SpotPrice>? prices,
      bool? isOfficial,
      List<UploadedImage>? certificationDocuments,
      String? visibility});
  CreateSpotDtoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _CreateSpotDtoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, CreateSpotDto, $Out>
    implements CreateSpotDtoCopyWith<$R, CreateSpotDto, $Out> {
  _CreateSpotDtoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<CreateSpotDto> $mapper =
      CreateSpotDtoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get imageUrls =>
      ListCopyWith($value.imageUrls, (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(imageUrls: v));
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>?
      get fishTypeNames => $value.fishTypeNames != null
          ? ListCopyWith(
              $value.fishTypeNames!,
              (v, t) => ObjectCopyWith(v, $identity, t),
              (v) => call(fishTypeNames: v))
          : null;
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>?
      get extraFishTypes => $value.extraFishTypes != null
          ? ListCopyWith(
              $value.extraFishTypes!,
              (v, t) => ObjectCopyWith(v, $identity, t),
              (v) => call(extraFishTypes: v))
          : null;
  @override
  ListCopyWith<$R, SpotFacility,
          SpotFacilityCopyWith<$R, SpotFacility, SpotFacility>>
      get facilities => ListCopyWith($value.facilities,
          (v, t) => v.copyWith.$chain(t), (v) => call(facilities: v));
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>?
      get extraFacilities => $value.extraFacilities != null
          ? ListCopyWith(
              $value.extraFacilities!,
              (v, t) => ObjectCopyWith(v, $identity, t),
              (v) => call(extraFacilities: v))
          : null;
  @override
  ListCopyWith<$R, SpotPrice, SpotPriceCopyWith<$R, SpotPrice, SpotPrice>>
      get prices => ListCopyWith($value.prices, (v, t) => v.copyWith.$chain(t),
          (v) => call(prices: v));
  @override
  ListCopyWith<$R, UploadedImage,
          UploadedImageCopyWith<$R, UploadedImage, UploadedImage>>
      get certificationDocuments => ListCopyWith(
          $value.certificationDocuments,
          (v, t) => v.copyWith.$chain(t),
          (v) => call(certificationDocuments: v));
  @override
  $R call(
          {String? name,
          String? description,
          List<String>? imageUrls,
          double? latitude,
          double? longitude,
          String? address,
          Object? province = $none,
          Object? city = $none,
          Object? county = $none,
          Object? fishTypeNames = $none,
          Object? extraFishTypes = $none,
          bool? hasFacilities,
          List<SpotFacility>? facilities,
          Object? extraFacilities = $none,
          bool? isPaid,
          List<SpotPrice>? prices,
          bool? isOfficial,
          List<UploadedImage>? certificationDocuments,
          String? visibility}) =>
      $apply(FieldCopyWithData({
        if (name != null) #name: name,
        if (description != null) #description: description,
        if (imageUrls != null) #imageUrls: imageUrls,
        if (latitude != null) #latitude: latitude,
        if (longitude != null) #longitude: longitude,
        if (address != null) #address: address,
        if (province != $none) #province: province,
        if (city != $none) #city: city,
        if (county != $none) #county: county,
        if (fishTypeNames != $none) #fishTypeNames: fishTypeNames,
        if (extraFishTypes != $none) #extraFishTypes: extraFishTypes,
        if (hasFacilities != null) #hasFacilities: hasFacilities,
        if (facilities != null) #facilities: facilities,
        if (extraFacilities != $none) #extraFacilities: extraFacilities,
        if (isPaid != null) #isPaid: isPaid,
        if (prices != null) #prices: prices,
        if (isOfficial != null) #isOfficial: isOfficial,
        if (certificationDocuments != null)
          #certificationDocuments: certificationDocuments,
        if (visibility != null) #visibility: visibility
      }));
  @override
  CreateSpotDto $make(CopyWithData data) => CreateSpotDto(
      name: data.get(#name, or: $value.name),
      description: data.get(#description, or: $value.description),
      imageUrls: data.get(#imageUrls, or: $value.imageUrls),
      latitude: data.get(#latitude, or: $value.latitude),
      longitude: data.get(#longitude, or: $value.longitude),
      address: data.get(#address, or: $value.address),
      province: data.get(#province, or: $value.province),
      city: data.get(#city, or: $value.city),
      county: data.get(#county, or: $value.county),
      fishTypeNames: data.get(#fishTypeNames, or: $value.fishTypeNames),
      extraFishTypes: data.get(#extraFishTypes, or: $value.extraFishTypes),
      hasFacilities: data.get(#hasFacilities, or: $value.hasFacilities),
      facilities: data.get(#facilities, or: $value.facilities),
      extraFacilities: data.get(#extraFacilities, or: $value.extraFacilities),
      isPaid: data.get(#isPaid, or: $value.isPaid),
      prices: data.get(#prices, or: $value.prices),
      isOfficial: data.get(#isOfficial, or: $value.isOfficial),
      certificationDocuments:
          data.get(#certificationDocuments, or: $value.certificationDocuments),
      visibility: data.get(#visibility, or: $value.visibility));

  @override
  CreateSpotDtoCopyWith<$R2, CreateSpotDto, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _CreateSpotDtoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
