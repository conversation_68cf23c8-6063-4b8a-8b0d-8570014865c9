import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/spot_facility.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/spot_price.dart';
import 'package:user_app/models/image/uploaded_image.dart';

// 使用以下命令生成mapper文件:
// flutter pub run build_runner build --delete-conflicting-outputs
part 'create_spot_dto.mapper.dart';

/// 创建钓点的数据传输对象
@MappableClass()
class CreateSpotDto with CreateSpotDtoMappable {
  // 基本信息
  final String name; // 钓点名称
  final String description; // 钓点描述

  @MappableField(key: 'image_urls')
  final List<String> imageUrls; // 钓点图片

  // 位置信息
  final double latitude; // 纬度
  final double longitude; // 经度
  final String address; // 详细地址
  final String? province; // 省份
  final String? city; // 城市
  final String? county; // 区县

  @MappableField(key: 'fish_type_names')
  final List<String>? fishTypeNames; // 系统中已有的鱼类名称列表

  @MappableField(key: 'extra_fish_types')
  final List<String>? extraFishTypes; // 用户自定义的、系统中不存在的鱼类名称列表

  // 设施信息
  @MappableField(key: 'has_facilities')
  final bool hasFacilities; // 是否有设施

  final List<SpotFacility> facilities; // 设施列表

  @MappableField(key: 'extra_facilities')
  final List<String>? extraFacilities;

  // 价格信息
  @MappableField(key: 'is_paid')
  final bool isPaid; // 是否收费

  final List<SpotPrice> prices; // 价格列表

  // 认证信息
  @MappableField(key: 'is_official')
  final bool isOfficial; // 是否官方认证

  @MappableField(key: 'certification_documents')
  final List<UploadedImage> certificationDocuments; // 认证文件

  // 可见性控制
  final String visibility; // 可见范围: public, followers, private

  const CreateSpotDto({
    required this.name,
    required this.description,
    required this.imageUrls,
    required this.latitude,
    required this.longitude,
    required this.address,
    this.province,
    this.city,
    this.county,
    this.fishTypeNames,
    this.extraFishTypes,
    required this.hasFacilities,
    required this.facilities,
    this.extraFacilities,
    required this.isPaid,
    required this.prices,
    required this.isOfficial,
    required this.certificationDocuments,
    required this.visibility,
  });

  /// 创建一个空的钓点DTO实例（用于初始状态）
  factory CreateSpotDto.empty() {
    return const CreateSpotDto(
      name: '',
      description: '',
      imageUrls: [],
      latitude: 0,
      longitude: 0,
      address: '',
      fishTypeNames: null,
      extraFishTypes: null,
      hasFacilities: false,
      facilities: [],
      isPaid: false,
      prices: [],
      isOfficial: false,
      certificationDocuments: [],
      visibility: 'public',
    );
  }

  /// 将上传的图片列表转换为URL列表
  static List<String> getImageUrls(List<UploadedImage> uploadedImages) {
    return uploadedImages
        .where((img) => img.url != null)
        .map((img) => img.url!)
        .toList();
  }
}
