// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'spot_price.dart';

class SpotPriceMapper extends ClassMapperBase<SpotPrice> {
  SpotPriceMapper._();

  static SpotPriceMapper? _instance;
  static SpotPriceMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotPriceMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'SpotPrice';

  static int _$priceType(SpotPrice v) => v.priceType;
  static const Field<SpotPrice, int> _f$priceType =
      Field('priceType', _$priceType, key: r'price_type');
  static String _$priceTypeName(SpotPrice v) => v.priceTypeName;
  static const Field<SpotPrice, String> _f$priceTypeName =
      Field('priceTypeName', _$priceTypeName, key: r'price_type_name');
  static double _$price(SpotPrice v) => v.price;
  static const Field<SpotPrice, double> _f$price = Field('price', _$price);
  static int? _$hours(SpotPrice v) => v.hours;
  static const Field<SpotPrice, int> _f$hours =
      Field('hours', _$hours, opt: true);
  static String? _$description(SpotPrice v) => v.description;
  static const Field<SpotPrice, String> _f$description =
      Field('description', _$description, opt: true);
  static int? _$fishTypeId(SpotPrice v) => v.fishTypeId;
  static const Field<SpotPrice, int> _f$fishTypeId =
      Field('fishTypeId', _$fishTypeId, key: r'fish_type_id', opt: true);
  static String? _$fishTypeName(SpotPrice v) => v.fishTypeName;
  static const Field<SpotPrice, String> _f$fishTypeName =
      Field('fishTypeName', _$fishTypeName, key: r'fish_type_name', opt: true);

  @override
  final MappableFields<SpotPrice> fields = const {
    #priceType: _f$priceType,
    #priceTypeName: _f$priceTypeName,
    #price: _f$price,
    #hours: _f$hours,
    #description: _f$description,
    #fishTypeId: _f$fishTypeId,
    #fishTypeName: _f$fishTypeName,
  };

  static SpotPrice _instantiate(DecodingData data) {
    return SpotPrice(
        priceType: data.dec(_f$priceType),
        priceTypeName: data.dec(_f$priceTypeName),
        price: data.dec(_f$price),
        hours: data.dec(_f$hours),
        description: data.dec(_f$description),
        fishTypeId: data.dec(_f$fishTypeId),
        fishTypeName: data.dec(_f$fishTypeName));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotPrice fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotPrice>(map);
  }

  static SpotPrice fromJson(String json) {
    return ensureInitialized().decodeJson<SpotPrice>(json);
  }
}

mixin SpotPriceMappable {
  String toJson() {
    return SpotPriceMapper.ensureInitialized()
        .encodeJson<SpotPrice>(this as SpotPrice);
  }

  Map<String, dynamic> toMap() {
    return SpotPriceMapper.ensureInitialized()
        .encodeMap<SpotPrice>(this as SpotPrice);
  }

  SpotPriceCopyWith<SpotPrice, SpotPrice, SpotPrice> get copyWith =>
      _SpotPriceCopyWithImpl<SpotPrice, SpotPrice>(
          this as SpotPrice, $identity, $identity);
  @override
  String toString() {
    return SpotPriceMapper.ensureInitialized()
        .stringifyValue(this as SpotPrice);
  }

  @override
  bool operator ==(Object other) {
    return SpotPriceMapper.ensureInitialized()
        .equalsValue(this as SpotPrice, other);
  }

  @override
  int get hashCode {
    return SpotPriceMapper.ensureInitialized().hashValue(this as SpotPrice);
  }
}

extension SpotPriceValueCopy<$R, $Out> on ObjectCopyWith<$R, SpotPrice, $Out> {
  SpotPriceCopyWith<$R, SpotPrice, $Out> get $asSpotPrice =>
      $base.as((v, t, t2) => _SpotPriceCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotPriceCopyWith<$R, $In extends SpotPrice, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? priceType,
      String? priceTypeName,
      double? price,
      int? hours,
      String? description,
      int? fishTypeId,
      String? fishTypeName});
  SpotPriceCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _SpotPriceCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotPrice, $Out>
    implements SpotPriceCopyWith<$R, SpotPrice, $Out> {
  _SpotPriceCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotPrice> $mapper =
      SpotPriceMapper.ensureInitialized();
  @override
  $R call(
          {int? priceType,
          String? priceTypeName,
          double? price,
          Object? hours = $none,
          Object? description = $none,
          Object? fishTypeId = $none,
          Object? fishTypeName = $none}) =>
      $apply(FieldCopyWithData({
        if (priceType != null) #priceType: priceType,
        if (priceTypeName != null) #priceTypeName: priceTypeName,
        if (price != null) #price: price,
        if (hours != $none) #hours: hours,
        if (description != $none) #description: description,
        if (fishTypeId != $none) #fishTypeId: fishTypeId,
        if (fishTypeName != $none) #fishTypeName: fishTypeName
      }));
  @override
  SpotPrice $make(CopyWithData data) => SpotPrice(
      priceType: data.get(#priceType, or: $value.priceType),
      priceTypeName: data.get(#priceTypeName, or: $value.priceTypeName),
      price: data.get(#price, or: $value.price),
      hours: data.get(#hours, or: $value.hours),
      description: data.get(#description, or: $value.description),
      fishTypeId: data.get(#fishTypeId, or: $value.fishTypeId),
      fishTypeName: data.get(#fishTypeName, or: $value.fishTypeName));

  @override
  SpotPriceCopyWith<$R2, SpotPrice, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _SpotPriceCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
