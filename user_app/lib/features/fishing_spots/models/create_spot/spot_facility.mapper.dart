// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'spot_facility.dart';

class SpotFacilityMapper extends ClassMapperBase<SpotFacility> {
  SpotFacilityMapper._();

  static SpotFacilityMapper? _instance;
  static SpotFacilityMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotFacilityMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'SpotFacility';

  static String _$name(SpotFacility v) => v.name;
  static const Field<SpotFacility, String> _f$name = Field('name', _$name);
  static String _$icon(SpotFacility v) => v.icon;
  static const Field<SpotFacility, String> _f$icon = Field('icon', _$icon);
  static String? _$description(SpotFacility v) => v.description;
  static const Field<SpotFacility, String> _f$description =
      Field('description', _$description, opt: true);
  static bool _$isSelected(SpotFacility v) => v.isSelected;
  static const Field<SpotFacility, bool> _f$isSelected = Field(
      'isSelected', _$isSelected,
      key: r'is_selected', opt: true, def: false);

  @override
  final MappableFields<SpotFacility> fields = const {
    #name: _f$name,
    #icon: _f$icon,
    #description: _f$description,
    #isSelected: _f$isSelected,
  };

  static SpotFacility _instantiate(DecodingData data) {
    return SpotFacility(
        name: data.dec(_f$name),
        icon: data.dec(_f$icon),
        description: data.dec(_f$description),
        isSelected: data.dec(_f$isSelected));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotFacility fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotFacility>(map);
  }

  static SpotFacility fromJson(String json) {
    return ensureInitialized().decodeJson<SpotFacility>(json);
  }
}

mixin SpotFacilityMappable {
  String toJson() {
    return SpotFacilityMapper.ensureInitialized()
        .encodeJson<SpotFacility>(this as SpotFacility);
  }

  Map<String, dynamic> toMap() {
    return SpotFacilityMapper.ensureInitialized()
        .encodeMap<SpotFacility>(this as SpotFacility);
  }

  SpotFacilityCopyWith<SpotFacility, SpotFacility, SpotFacility> get copyWith =>
      _SpotFacilityCopyWithImpl<SpotFacility, SpotFacility>(
          this as SpotFacility, $identity, $identity);
  @override
  String toString() {
    return SpotFacilityMapper.ensureInitialized()
        .stringifyValue(this as SpotFacility);
  }

  @override
  bool operator ==(Object other) {
    return SpotFacilityMapper.ensureInitialized()
        .equalsValue(this as SpotFacility, other);
  }

  @override
  int get hashCode {
    return SpotFacilityMapper.ensureInitialized()
        .hashValue(this as SpotFacility);
  }
}

extension SpotFacilityValueCopy<$R, $Out>
    on ObjectCopyWith<$R, SpotFacility, $Out> {
  SpotFacilityCopyWith<$R, SpotFacility, $Out> get $asSpotFacility =>
      $base.as((v, t, t2) => _SpotFacilityCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotFacilityCopyWith<$R, $In extends SpotFacility, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call({String? name, String? icon, String? description, bool? isSelected});
  SpotFacilityCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _SpotFacilityCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotFacility, $Out>
    implements SpotFacilityCopyWith<$R, SpotFacility, $Out> {
  _SpotFacilityCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotFacility> $mapper =
      SpotFacilityMapper.ensureInitialized();
  @override
  $R call(
          {String? name,
          String? icon,
          Object? description = $none,
          bool? isSelected}) =>
      $apply(FieldCopyWithData({
        if (name != null) #name: name,
        if (icon != null) #icon: icon,
        if (description != $none) #description: description,
        if (isSelected != null) #isSelected: isSelected
      }));
  @override
  SpotFacility $make(CopyWithData data) => SpotFacility(
      name: data.get(#name, or: $value.name),
      icon: data.get(#icon, or: $value.icon),
      description: data.get(#description, or: $value.description),
      isSelected: data.get(#isSelected, or: $value.isSelected));

  @override
  SpotFacilityCopyWith<$R2, SpotFacility, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _SpotFacilityCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
