import 'package:dart_mappable/dart_mappable.dart';

// 使用以下命令生成mapper文件:
// flutter pub run build_runner build --delete-conflicting-outputs
part 'spot_facility.mapper.dart';

@MappableClass()
class SpotFacility with SpotFacilityMappable {
  final String name;
  final String icon;
  final String? description;

  @MappableField(key: 'is_selected')
  bool isSelected;

  SpotFacility({
    required this.name,
    required this.icon,
    this.description,
    this.isSelected = false,
  });

  static const fromMap = SpotFacilityMapper.fromMap;
}
