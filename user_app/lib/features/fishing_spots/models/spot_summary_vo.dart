import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';

part 'spot_summary_vo.mapper.dart';

/// 钓点摘要信息VO - 对应后端SpotSummaryVO，用于列表展示
@MappableClass()
class SpotSummaryVo with SpotSummaryVoMappable {
  // 基础标识
  final int id;
  final String name;
  final String address;

  // 地理信息
  final double latitude;
  final double longitude;
  final String province;
  final String city;
  final String county;

  // 标签和评级
  final double rating;
  final bool official;
  final bool paid;
  final bool hasFacilities;
  final int verificationLevel;

  // 统计数据
  final int visitorCount;
  final int checkinCount;
  final int recentMomentsCount;

  // 优化的展示字段
  final String? mainImage; // 只需要第一张图
  final String? priceText; // 如 "免费" or "¥50起"
  final List<String> fishTypeNames; // 只需要鱼类名称列表

  const SpotSummaryVo({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.province,
    required this.city,
    required this.county,
    required this.rating,
    required this.official,
    required this.paid,
    required this.hasFacilities,
    required this.verificationLevel,
    required this.visitorCount,
    required this.checkinCount,
    required this.recentMomentsCount,
    this.mainImage,
    this.priceText,
    this.fishTypeNames = const [],
  });

  static final fromMap = SpotSummaryVoMapper.fromMap;

  /// 转换为FishingSpotVo以兼容现有代码
  FishingSpotVo toFishingSpotVo() {
    return FishingSpotVo(
      id: id,
      name: name,
      address: address,
      latitude: latitude,
      longitude: longitude,
      province: province,
      city: city,
      county: county,
      isOfficial: official,
      verificationLevel: verificationLevel,
      images: mainImage != null ? [mainImage!] : [],
      visitorCount: visitorCount,
      rating: rating,
      hasFacilities: hasFacilities,
      isPaid: paid,
      price: priceText,
      checkinCount: checkinCount,
      recentMomentsCount: recentMomentsCount,
      // 从鱼类名称列表生成季节信息（简化处理）
      seasons: _generateSeasonsFromFishTypes(fishTypeNames),
    );
  }

  /// 从鱼类名称生成季节信息（简化处理）
  List<String> _generateSeasonsFromFishTypes(List<String> fishTypeNames) {
    // 这里可以根据鱼类名称推断季节，或者返回全季节
    // 简化处理：如果有鱼类就返回全季节，否则返回空
    if (fishTypeNames.isNotEmpty) {
      return ['春', '夏', '秋', '冬'];
    }
    return [];
  }
}
