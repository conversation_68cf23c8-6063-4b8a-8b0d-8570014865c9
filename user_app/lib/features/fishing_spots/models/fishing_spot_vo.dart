import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/models/spot_facility_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_price_vo.dart';
import 'package:user_app/models/user.dart';

part 'fishing_spot_vo.mapper.dart';

@MappableClass()
class FishingSpotVo with FishingSpotVoMappable {
  final int id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String province;
  final String city;
  final String county;
  final bool isOfficial;
  final int verificationLevel;
  final String? description;
  final List<String>? images;
  final List<FishType> fishTypeList;
  final int visitorCount;
  final double rating;
  final bool hasFacilities;
  @MappableField(key: 'paid')
  final bool isPaid;
  final dynamic price;
  final int checkinCount;
  final List<String> seasons;
  final List<SpotPriceVo> prices;
  final List<SpotFacilityVo> facilities;
  final String? extraFishTypes;
  final List<String> extraFishTypesList;
  final String? extraFacilities;
  final List<String> extraFacilitiesList;
  // 动态相关字段
  final int recentMomentsCount;
  // 创建者相关字段
  final int? createdBy;
  final User? creator;

  const FishingSpotVo({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.province,
    required this.city,
    required this.county,
    required this.isOfficial,
    required this.verificationLevel,
    this.description,
    this.images,
    this.fishTypeList = const [],
    required this.visitorCount,
    required this.rating,
    required this.hasFacilities,
    required this.isPaid,
    this.price,
    required this.checkinCount,
    this.seasons = const [],
    this.prices = const [],
    this.facilities = const [],
    this.extraFishTypes,
    this.extraFishTypesList = const [],
    this.extraFacilities,
    this.extraFacilitiesList = const [],
    this.recentMomentsCount = 0,
    this.createdBy,
    this.creator,
  });

  static final fromMap = FishingSpotVoMapper.fromMap;
}
