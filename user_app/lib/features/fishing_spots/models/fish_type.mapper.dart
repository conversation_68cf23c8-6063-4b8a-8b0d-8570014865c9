// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'fish_type.dart';

class FishTypeMapper extends ClassMapperBase<FishType> {
  FishTypeMapper._();

  static FishTypeMapper? _instance;
  static FishTypeMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = FishTypeMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'FishType';

  static num _$id(FishType v) => v.id;
  static const Field<FishType, num> _f$id = Field('id', _$id);
  static String _$name(FishType v) => v.name;
  static const Field<FishType, String> _f$name = Field('name', _$name);
  static String? _$description(FishType v) => v.description;
  static const Field<FishType, String> _f$description =
      Field('description', _$description, opt: true);
  static String? _$imageUrl(FishType v) => v.imageUrl;
  static const Field<FishType, String> _f$imageUrl =
      Field('imageUrl', _$imageUrl, key: r'image_url', opt: true);
  static bool _$seasonSpring(FishType v) => v.seasonSpring;
  static const Field<FishType, bool> _f$seasonSpring = Field(
      'seasonSpring', _$seasonSpring,
      key: r'season_spring', opt: true, def: false);
  static bool _$seasonSummer(FishType v) => v.seasonSummer;
  static const Field<FishType, bool> _f$seasonSummer = Field(
      'seasonSummer', _$seasonSummer,
      key: r'season_summer', opt: true, def: false);
  static bool _$seasonAutumn(FishType v) => v.seasonAutumn;
  static const Field<FishType, bool> _f$seasonAutumn = Field(
      'seasonAutumn', _$seasonAutumn,
      key: r'season_autumn', opt: true, def: false);
  static bool _$seasonWinter(FishType v) => v.seasonWinter;
  static const Field<FishType, bool> _f$seasonWinter = Field(
      'seasonWinter', _$seasonWinter,
      key: r'season_winter', opt: true, def: false);

  @override
  final MappableFields<FishType> fields = const {
    #id: _f$id,
    #name: _f$name,
    #description: _f$description,
    #imageUrl: _f$imageUrl,
    #seasonSpring: _f$seasonSpring,
    #seasonSummer: _f$seasonSummer,
    #seasonAutumn: _f$seasonAutumn,
    #seasonWinter: _f$seasonWinter,
  };

  static FishType _instantiate(DecodingData data) {
    return FishType(
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        description: data.dec(_f$description),
        imageUrl: data.dec(_f$imageUrl),
        seasonSpring: data.dec(_f$seasonSpring),
        seasonSummer: data.dec(_f$seasonSummer),
        seasonAutumn: data.dec(_f$seasonAutumn),
        seasonWinter: data.dec(_f$seasonWinter));
  }

  @override
  final Function instantiate = _instantiate;

  static FishType fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<FishType>(map);
  }

  static FishType fromJson(String json) {
    return ensureInitialized().decodeJson<FishType>(json);
  }
}

mixin FishTypeMappable {
  String toJson() {
    return FishTypeMapper.ensureInitialized()
        .encodeJson<FishType>(this as FishType);
  }

  Map<String, dynamic> toMap() {
    return FishTypeMapper.ensureInitialized()
        .encodeMap<FishType>(this as FishType);
  }

  FishTypeCopyWith<FishType, FishType, FishType> get copyWith =>
      _FishTypeCopyWithImpl<FishType, FishType>(
          this as FishType, $identity, $identity);
  @override
  String toString() {
    return FishTypeMapper.ensureInitialized().stringifyValue(this as FishType);
  }

  @override
  bool operator ==(Object other) {
    return FishTypeMapper.ensureInitialized()
        .equalsValue(this as FishType, other);
  }

  @override
  int get hashCode {
    return FishTypeMapper.ensureInitialized().hashValue(this as FishType);
  }
}

extension FishTypeValueCopy<$R, $Out> on ObjectCopyWith<$R, FishType, $Out> {
  FishTypeCopyWith<$R, FishType, $Out> get $asFishType =>
      $base.as((v, t, t2) => _FishTypeCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class FishTypeCopyWith<$R, $In extends FishType, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {num? id,
      String? name,
      String? description,
      String? imageUrl,
      bool? seasonSpring,
      bool? seasonSummer,
      bool? seasonAutumn,
      bool? seasonWinter});
  FishTypeCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _FishTypeCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, FishType, $Out>
    implements FishTypeCopyWith<$R, FishType, $Out> {
  _FishTypeCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<FishType> $mapper =
      FishTypeMapper.ensureInitialized();
  @override
  $R call(
          {num? id,
          String? name,
          Object? description = $none,
          Object? imageUrl = $none,
          bool? seasonSpring,
          bool? seasonSummer,
          bool? seasonAutumn,
          bool? seasonWinter}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (description != $none) #description: description,
        if (imageUrl != $none) #imageUrl: imageUrl,
        if (seasonSpring != null) #seasonSpring: seasonSpring,
        if (seasonSummer != null) #seasonSummer: seasonSummer,
        if (seasonAutumn != null) #seasonAutumn: seasonAutumn,
        if (seasonWinter != null) #seasonWinter: seasonWinter
      }));
  @override
  FishType $make(CopyWithData data) => FishType(
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      description: data.get(#description, or: $value.description),
      imageUrl: data.get(#imageUrl, or: $value.imageUrl),
      seasonSpring: data.get(#seasonSpring, or: $value.seasonSpring),
      seasonSummer: data.get(#seasonSummer, or: $value.seasonSummer),
      seasonAutumn: data.get(#seasonAutumn, or: $value.seasonAutumn),
      seasonWinter: data.get(#seasonWinter, or: $value.seasonWinter));

  @override
  FishTypeCopyWith<$R2, FishType, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _FishTypeCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
