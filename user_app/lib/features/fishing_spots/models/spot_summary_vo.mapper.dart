// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'spot_summary_vo.dart';

class SpotSummaryVoMapper extends ClassMapperBase<SpotSummaryVo> {
  SpotSummaryVoMapper._();

  static SpotSummaryVoMapper? _instance;
  static SpotSummaryVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotSummaryVoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'SpotSummaryVo';

  static int _$id(SpotSummaryVo v) => v.id;
  static const Field<SpotSummaryVo, int> _f$id = Field('id', _$id);
  static String _$name(SpotSummaryVo v) => v.name;
  static const Field<SpotSummaryVo, String> _f$name = Field('name', _$name);
  static String _$address(SpotSummaryVo v) => v.address;
  static const Field<SpotSummaryVo, String> _f$address =
      Field('address', _$address);
  static double _$latitude(SpotSummaryVo v) => v.latitude;
  static const Field<SpotSummaryVo, double> _f$latitude =
      Field('latitude', _$latitude);
  static double _$longitude(SpotSummaryVo v) => v.longitude;
  static const Field<SpotSummaryVo, double> _f$longitude =
      Field('longitude', _$longitude);
  static String _$province(SpotSummaryVo v) => v.province;
  static const Field<SpotSummaryVo, String> _f$province =
      Field('province', _$province);
  static String _$city(SpotSummaryVo v) => v.city;
  static const Field<SpotSummaryVo, String> _f$city = Field('city', _$city);
  static String _$county(SpotSummaryVo v) => v.county;
  static const Field<SpotSummaryVo, String> _f$county =
      Field('county', _$county);
  static double _$rating(SpotSummaryVo v) => v.rating;
  static const Field<SpotSummaryVo, double> _f$rating =
      Field('rating', _$rating);
  static bool _$official(SpotSummaryVo v) => v.official;
  static const Field<SpotSummaryVo, bool> _f$official =
      Field('official', _$official);
  static bool _$paid(SpotSummaryVo v) => v.paid;
  static const Field<SpotSummaryVo, bool> _f$paid = Field('paid', _$paid);
  static bool _$hasFacilities(SpotSummaryVo v) => v.hasFacilities;
  static const Field<SpotSummaryVo, bool> _f$hasFacilities =
      Field('hasFacilities', _$hasFacilities, key: r'has_facilities');
  static int _$verificationLevel(SpotSummaryVo v) => v.verificationLevel;
  static const Field<SpotSummaryVo, int> _f$verificationLevel = Field(
      'verificationLevel', _$verificationLevel,
      key: r'verification_level');
  static int _$visitorCount(SpotSummaryVo v) => v.visitorCount;
  static const Field<SpotSummaryVo, int> _f$visitorCount =
      Field('visitorCount', _$visitorCount, key: r'visitor_count');
  static int _$checkinCount(SpotSummaryVo v) => v.checkinCount;
  static const Field<SpotSummaryVo, int> _f$checkinCount =
      Field('checkinCount', _$checkinCount, key: r'checkin_count');
  static int _$recentMomentsCount(SpotSummaryVo v) => v.recentMomentsCount;
  static const Field<SpotSummaryVo, int> _f$recentMomentsCount = Field(
      'recentMomentsCount', _$recentMomentsCount,
      key: r'recent_moments_count');
  static String? _$mainImage(SpotSummaryVo v) => v.mainImage;
  static const Field<SpotSummaryVo, String> _f$mainImage =
      Field('mainImage', _$mainImage, key: r'main_image', opt: true);
  static String? _$priceText(SpotSummaryVo v) => v.priceText;
  static const Field<SpotSummaryVo, String> _f$priceText =
      Field('priceText', _$priceText, key: r'price_text', opt: true);
  static List<String> _$fishTypeNames(SpotSummaryVo v) => v.fishTypeNames;
  static const Field<SpotSummaryVo, List<String>> _f$fishTypeNames = Field(
      'fishTypeNames', _$fishTypeNames,
      key: r'fish_type_names', opt: true, def: const []);

  @override
  final MappableFields<SpotSummaryVo> fields = const {
    #id: _f$id,
    #name: _f$name,
    #address: _f$address,
    #latitude: _f$latitude,
    #longitude: _f$longitude,
    #province: _f$province,
    #city: _f$city,
    #county: _f$county,
    #rating: _f$rating,
    #official: _f$official,
    #paid: _f$paid,
    #hasFacilities: _f$hasFacilities,
    #verificationLevel: _f$verificationLevel,
    #visitorCount: _f$visitorCount,
    #checkinCount: _f$checkinCount,
    #recentMomentsCount: _f$recentMomentsCount,
    #mainImage: _f$mainImage,
    #priceText: _f$priceText,
    #fishTypeNames: _f$fishTypeNames,
  };

  static SpotSummaryVo _instantiate(DecodingData data) {
    return SpotSummaryVo(
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        address: data.dec(_f$address),
        latitude: data.dec(_f$latitude),
        longitude: data.dec(_f$longitude),
        province: data.dec(_f$province),
        city: data.dec(_f$city),
        county: data.dec(_f$county),
        rating: data.dec(_f$rating),
        official: data.dec(_f$official),
        paid: data.dec(_f$paid),
        hasFacilities: data.dec(_f$hasFacilities),
        verificationLevel: data.dec(_f$verificationLevel),
        visitorCount: data.dec(_f$visitorCount),
        checkinCount: data.dec(_f$checkinCount),
        recentMomentsCount: data.dec(_f$recentMomentsCount),
        mainImage: data.dec(_f$mainImage),
        priceText: data.dec(_f$priceText),
        fishTypeNames: data.dec(_f$fishTypeNames));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotSummaryVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotSummaryVo>(map);
  }

  static SpotSummaryVo fromJson(String json) {
    return ensureInitialized().decodeJson<SpotSummaryVo>(json);
  }
}

mixin SpotSummaryVoMappable {
  String toJson() {
    return SpotSummaryVoMapper.ensureInitialized()
        .encodeJson<SpotSummaryVo>(this as SpotSummaryVo);
  }

  Map<String, dynamic> toMap() {
    return SpotSummaryVoMapper.ensureInitialized()
        .encodeMap<SpotSummaryVo>(this as SpotSummaryVo);
  }

  SpotSummaryVoCopyWith<SpotSummaryVo, SpotSummaryVo, SpotSummaryVo>
      get copyWith => _SpotSummaryVoCopyWithImpl<SpotSummaryVo, SpotSummaryVo>(
          this as SpotSummaryVo, $identity, $identity);
  @override
  String toString() {
    return SpotSummaryVoMapper.ensureInitialized()
        .stringifyValue(this as SpotSummaryVo);
  }

  @override
  bool operator ==(Object other) {
    return SpotSummaryVoMapper.ensureInitialized()
        .equalsValue(this as SpotSummaryVo, other);
  }

  @override
  int get hashCode {
    return SpotSummaryVoMapper.ensureInitialized()
        .hashValue(this as SpotSummaryVo);
  }
}

extension SpotSummaryVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, SpotSummaryVo, $Out> {
  SpotSummaryVoCopyWith<$R, SpotSummaryVo, $Out> get $asSpotSummaryVo =>
      $base.as((v, t, t2) => _SpotSummaryVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotSummaryVoCopyWith<$R, $In extends SpotSummaryVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get fishTypeNames;
  $R call(
      {int? id,
      String? name,
      String? address,
      double? latitude,
      double? longitude,
      String? province,
      String? city,
      String? county,
      double? rating,
      bool? official,
      bool? paid,
      bool? hasFacilities,
      int? verificationLevel,
      int? visitorCount,
      int? checkinCount,
      int? recentMomentsCount,
      String? mainImage,
      String? priceText,
      List<String>? fishTypeNames});
  SpotSummaryVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _SpotSummaryVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotSummaryVo, $Out>
    implements SpotSummaryVoCopyWith<$R, SpotSummaryVo, $Out> {
  _SpotSummaryVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotSummaryVo> $mapper =
      SpotSummaryVoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get fishTypeNames => ListCopyWith(
          $value.fishTypeNames,
          (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(fishTypeNames: v));
  @override
  $R call(
          {int? id,
          String? name,
          String? address,
          double? latitude,
          double? longitude,
          String? province,
          String? city,
          String? county,
          double? rating,
          bool? official,
          bool? paid,
          bool? hasFacilities,
          int? verificationLevel,
          int? visitorCount,
          int? checkinCount,
          int? recentMomentsCount,
          Object? mainImage = $none,
          Object? priceText = $none,
          List<String>? fishTypeNames}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (address != null) #address: address,
        if (latitude != null) #latitude: latitude,
        if (longitude != null) #longitude: longitude,
        if (province != null) #province: province,
        if (city != null) #city: city,
        if (county != null) #county: county,
        if (rating != null) #rating: rating,
        if (official != null) #official: official,
        if (paid != null) #paid: paid,
        if (hasFacilities != null) #hasFacilities: hasFacilities,
        if (verificationLevel != null) #verificationLevel: verificationLevel,
        if (visitorCount != null) #visitorCount: visitorCount,
        if (checkinCount != null) #checkinCount: checkinCount,
        if (recentMomentsCount != null) #recentMomentsCount: recentMomentsCount,
        if (mainImage != $none) #mainImage: mainImage,
        if (priceText != $none) #priceText: priceText,
        if (fishTypeNames != null) #fishTypeNames: fishTypeNames
      }));
  @override
  SpotSummaryVo $make(CopyWithData data) => SpotSummaryVo(
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      address: data.get(#address, or: $value.address),
      latitude: data.get(#latitude, or: $value.latitude),
      longitude: data.get(#longitude, or: $value.longitude),
      province: data.get(#province, or: $value.province),
      city: data.get(#city, or: $value.city),
      county: data.get(#county, or: $value.county),
      rating: data.get(#rating, or: $value.rating),
      official: data.get(#official, or: $value.official),
      paid: data.get(#paid, or: $value.paid),
      hasFacilities: data.get(#hasFacilities, or: $value.hasFacilities),
      verificationLevel:
          data.get(#verificationLevel, or: $value.verificationLevel),
      visitorCount: data.get(#visitorCount, or: $value.visitorCount),
      checkinCount: data.get(#checkinCount, or: $value.checkinCount),
      recentMomentsCount:
          data.get(#recentMomentsCount, or: $value.recentMomentsCount),
      mainImage: data.get(#mainImage, or: $value.mainImage),
      priceText: data.get(#priceText, or: $value.priceText),
      fishTypeNames: data.get(#fishTypeNames, or: $value.fishTypeNames));

  @override
  SpotSummaryVoCopyWith<$R2, SpotSummaryVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _SpotSummaryVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
