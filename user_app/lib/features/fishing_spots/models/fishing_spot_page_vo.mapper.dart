// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'fishing_spot_page_vo.dart';

class FishingSpotPageVoMapper extends ClassMapperBase<FishingSpotPageVo> {
  FishingSpotPageVoMapper._();

  static FishingSpotPageVoMapper? _instance;
  static FishingSpotPageVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = FishingSpotPageVoMapper._());
      FishingSpotVoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'FishingSpotPageVo';

  static List<FishingSpotVo> _$content(FishingSpotPageVo v) => v.content;
  static const Field<FishingSpotPageVo, List<FishingSpotVo>> _f$content =
      Field('content', _$content);
  static int _$totalPages(FishingSpotPageVo v) => v.totalPages;
  static const Field<FishingSpotPageVo, int> _f$totalPages =
      Field('totalPages', _$totalPages, key: r'total_pages');
  static int _$totalElements(FishingSpotPageVo v) => v.totalElements;
  static const Field<FishingSpotPageVo, int> _f$totalElements =
      Field('totalElements', _$totalElements, key: r'total_elements');
  static int _$size(FishingSpotPageVo v) => v.size;
  static const Field<FishingSpotPageVo, int> _f$size = Field('size', _$size);
  static int _$number(FishingSpotPageVo v) => v.number;
  static const Field<FishingSpotPageVo, int> _f$number =
      Field('number', _$number);
  static bool _$first(FishingSpotPageVo v) => v.first;
  static const Field<FishingSpotPageVo, bool> _f$first =
      Field('first', _$first);
  static bool _$last(FishingSpotPageVo v) => v.last;
  static const Field<FishingSpotPageVo, bool> _f$last = Field('last', _$last);

  @override
  final MappableFields<FishingSpotPageVo> fields = const {
    #content: _f$content,
    #totalPages: _f$totalPages,
    #totalElements: _f$totalElements,
    #size: _f$size,
    #number: _f$number,
    #first: _f$first,
    #last: _f$last,
  };

  static FishingSpotPageVo _instantiate(DecodingData data) {
    return FishingSpotPageVo(
        content: data.dec(_f$content),
        totalPages: data.dec(_f$totalPages),
        totalElements: data.dec(_f$totalElements),
        size: data.dec(_f$size),
        number: data.dec(_f$number),
        first: data.dec(_f$first),
        last: data.dec(_f$last));
  }

  @override
  final Function instantiate = _instantiate;

  static FishingSpotPageVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<FishingSpotPageVo>(map);
  }

  static FishingSpotPageVo fromJson(String json) {
    return ensureInitialized().decodeJson<FishingSpotPageVo>(json);
  }
}

mixin FishingSpotPageVoMappable {
  String toJson() {
    return FishingSpotPageVoMapper.ensureInitialized()
        .encodeJson<FishingSpotPageVo>(this as FishingSpotPageVo);
  }

  Map<String, dynamic> toMap() {
    return FishingSpotPageVoMapper.ensureInitialized()
        .encodeMap<FishingSpotPageVo>(this as FishingSpotPageVo);
  }

  FishingSpotPageVoCopyWith<FishingSpotPageVo, FishingSpotPageVo,
          FishingSpotPageVo>
      get copyWith =>
          _FishingSpotPageVoCopyWithImpl<FishingSpotPageVo, FishingSpotPageVo>(
              this as FishingSpotPageVo, $identity, $identity);
  @override
  String toString() {
    return FishingSpotPageVoMapper.ensureInitialized()
        .stringifyValue(this as FishingSpotPageVo);
  }

  @override
  bool operator ==(Object other) {
    return FishingSpotPageVoMapper.ensureInitialized()
        .equalsValue(this as FishingSpotPageVo, other);
  }

  @override
  int get hashCode {
    return FishingSpotPageVoMapper.ensureInitialized()
        .hashValue(this as FishingSpotPageVo);
  }
}

extension FishingSpotPageVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, FishingSpotPageVo, $Out> {
  FishingSpotPageVoCopyWith<$R, FishingSpotPageVo, $Out>
      get $asFishingSpotPageVo => $base
          .as((v, t, t2) => _FishingSpotPageVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class FishingSpotPageVoCopyWith<$R, $In extends FishingSpotPageVo,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, FishingSpotVo,
      FishingSpotVoCopyWith<$R, FishingSpotVo, FishingSpotVo>> get content;
  $R call(
      {List<FishingSpotVo>? content,
      int? totalPages,
      int? totalElements,
      int? size,
      int? number,
      bool? first,
      bool? last});
  FishingSpotPageVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _FishingSpotPageVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, FishingSpotPageVo, $Out>
    implements FishingSpotPageVoCopyWith<$R, FishingSpotPageVo, $Out> {
  _FishingSpotPageVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<FishingSpotPageVo> $mapper =
      FishingSpotPageVoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, FishingSpotVo,
          FishingSpotVoCopyWith<$R, FishingSpotVo, FishingSpotVo>>
      get content => ListCopyWith($value.content,
          (v, t) => v.copyWith.$chain(t), (v) => call(content: v));
  @override
  $R call(
          {List<FishingSpotVo>? content,
          int? totalPages,
          int? totalElements,
          int? size,
          int? number,
          bool? first,
          bool? last}) =>
      $apply(FieldCopyWithData({
        if (content != null) #content: content,
        if (totalPages != null) #totalPages: totalPages,
        if (totalElements != null) #totalElements: totalElements,
        if (size != null) #size: size,
        if (number != null) #number: number,
        if (first != null) #first: first,
        if (last != null) #last: last
      }));
  @override
  FishingSpotPageVo $make(CopyWithData data) => FishingSpotPageVo(
      content: data.get(#content, or: $value.content),
      totalPages: data.get(#totalPages, or: $value.totalPages),
      totalElements: data.get(#totalElements, or: $value.totalElements),
      size: data.get(#size, or: $value.size),
      number: data.get(#number, or: $value.number),
      first: data.get(#first, or: $value.first),
      last: data.get(#last, or: $value.last));

  @override
  FishingSpotPageVoCopyWith<$R2, FishingSpotPageVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _FishingSpotPageVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class SpotSummaryPageVoMapper extends ClassMapperBase<SpotSummaryPageVo> {
  SpotSummaryPageVoMapper._();

  static SpotSummaryPageVoMapper? _instance;
  static SpotSummaryPageVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotSummaryPageVoMapper._());
      SpotSummaryVoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'SpotSummaryPageVo';

  static List<SpotSummaryVo> _$content(SpotSummaryPageVo v) => v.content;
  static const Field<SpotSummaryPageVo, List<SpotSummaryVo>> _f$content =
      Field('content', _$content);
  static int _$totalPages(SpotSummaryPageVo v) => v.totalPages;
  static const Field<SpotSummaryPageVo, int> _f$totalPages =
      Field('totalPages', _$totalPages, key: r'total_pages');
  static int _$totalElements(SpotSummaryPageVo v) => v.totalElements;
  static const Field<SpotSummaryPageVo, int> _f$totalElements =
      Field('totalElements', _$totalElements, key: r'total_elements');
  static int _$size(SpotSummaryPageVo v) => v.size;
  static const Field<SpotSummaryPageVo, int> _f$size = Field('size', _$size);
  static int _$number(SpotSummaryPageVo v) => v.number;
  static const Field<SpotSummaryPageVo, int> _f$number =
      Field('number', _$number);
  static bool _$first(SpotSummaryPageVo v) => v.first;
  static const Field<SpotSummaryPageVo, bool> _f$first =
      Field('first', _$first);
  static bool _$last(SpotSummaryPageVo v) => v.last;
  static const Field<SpotSummaryPageVo, bool> _f$last = Field('last', _$last);

  @override
  final MappableFields<SpotSummaryPageVo> fields = const {
    #content: _f$content,
    #totalPages: _f$totalPages,
    #totalElements: _f$totalElements,
    #size: _f$size,
    #number: _f$number,
    #first: _f$first,
    #last: _f$last,
  };

  static SpotSummaryPageVo _instantiate(DecodingData data) {
    return SpotSummaryPageVo(
        content: data.dec(_f$content),
        totalPages: data.dec(_f$totalPages),
        totalElements: data.dec(_f$totalElements),
        size: data.dec(_f$size),
        number: data.dec(_f$number),
        first: data.dec(_f$first),
        last: data.dec(_f$last));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotSummaryPageVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotSummaryPageVo>(map);
  }

  static SpotSummaryPageVo fromJson(String json) {
    return ensureInitialized().decodeJson<SpotSummaryPageVo>(json);
  }
}

mixin SpotSummaryPageVoMappable {
  String toJson() {
    return SpotSummaryPageVoMapper.ensureInitialized()
        .encodeJson<SpotSummaryPageVo>(this as SpotSummaryPageVo);
  }

  Map<String, dynamic> toMap() {
    return SpotSummaryPageVoMapper.ensureInitialized()
        .encodeMap<SpotSummaryPageVo>(this as SpotSummaryPageVo);
  }

  SpotSummaryPageVoCopyWith<SpotSummaryPageVo, SpotSummaryPageVo,
          SpotSummaryPageVo>
      get copyWith =>
          _SpotSummaryPageVoCopyWithImpl<SpotSummaryPageVo, SpotSummaryPageVo>(
              this as SpotSummaryPageVo, $identity, $identity);
  @override
  String toString() {
    return SpotSummaryPageVoMapper.ensureInitialized()
        .stringifyValue(this as SpotSummaryPageVo);
  }

  @override
  bool operator ==(Object other) {
    return SpotSummaryPageVoMapper.ensureInitialized()
        .equalsValue(this as SpotSummaryPageVo, other);
  }

  @override
  int get hashCode {
    return SpotSummaryPageVoMapper.ensureInitialized()
        .hashValue(this as SpotSummaryPageVo);
  }
}

extension SpotSummaryPageVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, SpotSummaryPageVo, $Out> {
  SpotSummaryPageVoCopyWith<$R, SpotSummaryPageVo, $Out>
      get $asSpotSummaryPageVo => $base
          .as((v, t, t2) => _SpotSummaryPageVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotSummaryPageVoCopyWith<$R, $In extends SpotSummaryPageVo,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, SpotSummaryVo,
      SpotSummaryVoCopyWith<$R, SpotSummaryVo, SpotSummaryVo>> get content;
  $R call(
      {List<SpotSummaryVo>? content,
      int? totalPages,
      int? totalElements,
      int? size,
      int? number,
      bool? first,
      bool? last});
  SpotSummaryPageVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _SpotSummaryPageVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotSummaryPageVo, $Out>
    implements SpotSummaryPageVoCopyWith<$R, SpotSummaryPageVo, $Out> {
  _SpotSummaryPageVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotSummaryPageVo> $mapper =
      SpotSummaryPageVoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, SpotSummaryVo,
          SpotSummaryVoCopyWith<$R, SpotSummaryVo, SpotSummaryVo>>
      get content => ListCopyWith($value.content,
          (v, t) => v.copyWith.$chain(t), (v) => call(content: v));
  @override
  $R call(
          {List<SpotSummaryVo>? content,
          int? totalPages,
          int? totalElements,
          int? size,
          int? number,
          bool? first,
          bool? last}) =>
      $apply(FieldCopyWithData({
        if (content != null) #content: content,
        if (totalPages != null) #totalPages: totalPages,
        if (totalElements != null) #totalElements: totalElements,
        if (size != null) #size: size,
        if (number != null) #number: number,
        if (first != null) #first: first,
        if (last != null) #last: last
      }));
  @override
  SpotSummaryPageVo $make(CopyWithData data) => SpotSummaryPageVo(
      content: data.get(#content, or: $value.content),
      totalPages: data.get(#totalPages, or: $value.totalPages),
      totalElements: data.get(#totalElements, or: $value.totalElements),
      size: data.get(#size, or: $value.size),
      number: data.get(#number, or: $value.number),
      first: data.get(#first, or: $value.first),
      last: data.get(#last, or: $value.last));

  @override
  SpotSummaryPageVoCopyWith<$R2, SpotSummaryPageVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _SpotSummaryPageVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class SpotMapPageVoMapper extends ClassMapperBase<SpotMapPageVo> {
  SpotMapPageVoMapper._();

  static SpotMapPageVoMapper? _instance;
  static SpotMapPageVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotMapPageVoMapper._());
      SpotMapVoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'SpotMapPageVo';

  static List<SpotMapVo> _$content(SpotMapPageVo v) => v.content;
  static const Field<SpotMapPageVo, List<SpotMapVo>> _f$content =
      Field('content', _$content);
  static int _$totalPages(SpotMapPageVo v) => v.totalPages;
  static const Field<SpotMapPageVo, int> _f$totalPages =
      Field('totalPages', _$totalPages, key: r'total_pages');
  static int _$totalElements(SpotMapPageVo v) => v.totalElements;
  static const Field<SpotMapPageVo, int> _f$totalElements =
      Field('totalElements', _$totalElements, key: r'total_elements');
  static int _$size(SpotMapPageVo v) => v.size;
  static const Field<SpotMapPageVo, int> _f$size = Field('size', _$size);
  static int _$number(SpotMapPageVo v) => v.number;
  static const Field<SpotMapPageVo, int> _f$number = Field('number', _$number);
  static bool _$first(SpotMapPageVo v) => v.first;
  static const Field<SpotMapPageVo, bool> _f$first = Field('first', _$first);
  static bool _$last(SpotMapPageVo v) => v.last;
  static const Field<SpotMapPageVo, bool> _f$last = Field('last', _$last);

  @override
  final MappableFields<SpotMapPageVo> fields = const {
    #content: _f$content,
    #totalPages: _f$totalPages,
    #totalElements: _f$totalElements,
    #size: _f$size,
    #number: _f$number,
    #first: _f$first,
    #last: _f$last,
  };

  static SpotMapPageVo _instantiate(DecodingData data) {
    return SpotMapPageVo(
        content: data.dec(_f$content),
        totalPages: data.dec(_f$totalPages),
        totalElements: data.dec(_f$totalElements),
        size: data.dec(_f$size),
        number: data.dec(_f$number),
        first: data.dec(_f$first),
        last: data.dec(_f$last));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotMapPageVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotMapPageVo>(map);
  }

  static SpotMapPageVo fromJson(String json) {
    return ensureInitialized().decodeJson<SpotMapPageVo>(json);
  }
}

mixin SpotMapPageVoMappable {
  String toJson() {
    return SpotMapPageVoMapper.ensureInitialized()
        .encodeJson<SpotMapPageVo>(this as SpotMapPageVo);
  }

  Map<String, dynamic> toMap() {
    return SpotMapPageVoMapper.ensureInitialized()
        .encodeMap<SpotMapPageVo>(this as SpotMapPageVo);
  }

  SpotMapPageVoCopyWith<SpotMapPageVo, SpotMapPageVo, SpotMapPageVo>
      get copyWith => _SpotMapPageVoCopyWithImpl<SpotMapPageVo, SpotMapPageVo>(
          this as SpotMapPageVo, $identity, $identity);
  @override
  String toString() {
    return SpotMapPageVoMapper.ensureInitialized()
        .stringifyValue(this as SpotMapPageVo);
  }

  @override
  bool operator ==(Object other) {
    return SpotMapPageVoMapper.ensureInitialized()
        .equalsValue(this as SpotMapPageVo, other);
  }

  @override
  int get hashCode {
    return SpotMapPageVoMapper.ensureInitialized()
        .hashValue(this as SpotMapPageVo);
  }
}

extension SpotMapPageVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, SpotMapPageVo, $Out> {
  SpotMapPageVoCopyWith<$R, SpotMapPageVo, $Out> get $asSpotMapPageVo =>
      $base.as((v, t, t2) => _SpotMapPageVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotMapPageVoCopyWith<$R, $In extends SpotMapPageVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, SpotMapVo, SpotMapVoCopyWith<$R, SpotMapVo, SpotMapVo>>
      get content;
  $R call(
      {List<SpotMapVo>? content,
      int? totalPages,
      int? totalElements,
      int? size,
      int? number,
      bool? first,
      bool? last});
  SpotMapPageVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _SpotMapPageVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotMapPageVo, $Out>
    implements SpotMapPageVoCopyWith<$R, SpotMapPageVo, $Out> {
  _SpotMapPageVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotMapPageVo> $mapper =
      SpotMapPageVoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, SpotMapVo, SpotMapVoCopyWith<$R, SpotMapVo, SpotMapVo>>
      get content => ListCopyWith($value.content,
          (v, t) => v.copyWith.$chain(t), (v) => call(content: v));
  @override
  $R call(
          {List<SpotMapVo>? content,
          int? totalPages,
          int? totalElements,
          int? size,
          int? number,
          bool? first,
          bool? last}) =>
      $apply(FieldCopyWithData({
        if (content != null) #content: content,
        if (totalPages != null) #totalPages: totalPages,
        if (totalElements != null) #totalElements: totalElements,
        if (size != null) #size: size,
        if (number != null) #number: number,
        if (first != null) #first: first,
        if (last != null) #last: last
      }));
  @override
  SpotMapPageVo $make(CopyWithData data) => SpotMapPageVo(
      content: data.get(#content, or: $value.content),
      totalPages: data.get(#totalPages, or: $value.totalPages),
      totalElements: data.get(#totalElements, or: $value.totalElements),
      size: data.get(#size, or: $value.size),
      number: data.get(#number, or: $value.number),
      first: data.get(#first, or: $value.first),
      last: data.get(#last, or: $value.last));

  @override
  SpotMapPageVoCopyWith<$R2, SpotMapPageVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _SpotMapPageVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
