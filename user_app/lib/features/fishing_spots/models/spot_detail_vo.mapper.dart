// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'spot_detail_vo.dart';

class SpotDetailVoMapper extends ClassMapperBase<SpotDetailVo> {
  SpotDetailVoMapper._();

  static SpotDetailVoMapper? _instance;
  static SpotDetailVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotDetailVoMapper._());
      FishTypeMapper.ensureInitialized();
      SpotPriceVoMapper.ensureInitialized();
      SpotFacilityVoMapper.ensureInitialized();
      MomentVoMapper.ensureInitialized();
      UserMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'SpotDetailVo';

  static int _$id(SpotDetailVo v) => v.id;
  static const Field<SpotDetailVo, int> _f$id = Field('id', _$id);
  static String _$name(SpotDetailVo v) => v.name;
  static const Field<SpotDetailVo, String> _f$name = Field('name', _$name);
  static String _$address(SpotDetailVo v) => v.address;
  static const Field<SpotDetailVo, String> _f$address =
      Field('address', _$address);
  static double _$latitude(SpotDetailVo v) => v.latitude;
  static const Field<SpotDetailVo, double> _f$latitude =
      Field('latitude', _$latitude);
  static double _$longitude(SpotDetailVo v) => v.longitude;
  static const Field<SpotDetailVo, double> _f$longitude =
      Field('longitude', _$longitude);
  static String _$province(SpotDetailVo v) => v.province;
  static const Field<SpotDetailVo, String> _f$province =
      Field('province', _$province);
  static String _$city(SpotDetailVo v) => v.city;
  static const Field<SpotDetailVo, String> _f$city = Field('city', _$city);
  static String _$county(SpotDetailVo v) => v.county;
  static const Field<SpotDetailVo, String> _f$county =
      Field('county', _$county);
  static bool _$isOfficial(SpotDetailVo v) => v.isOfficial;
  static const Field<SpotDetailVo, bool> _f$isOfficial =
      Field('isOfficial', _$isOfficial, key: r'is_official');
  static int _$verificationLevel(SpotDetailVo v) => v.verificationLevel;
  static const Field<SpotDetailVo, int> _f$verificationLevel = Field(
      'verificationLevel', _$verificationLevel,
      key: r'verification_level');
  static String? _$description(SpotDetailVo v) => v.description;
  static const Field<SpotDetailVo, String> _f$description =
      Field('description', _$description, opt: true);
  static int _$visitorCount(SpotDetailVo v) => v.visitorCount;
  static const Field<SpotDetailVo, int> _f$visitorCount =
      Field('visitorCount', _$visitorCount, key: r'visitor_count');
  static double _$rating(SpotDetailVo v) => v.rating;
  static const Field<SpotDetailVo, double> _f$rating =
      Field('rating', _$rating);
  static bool _$hasFacilities(SpotDetailVo v) => v.hasFacilities;
  static const Field<SpotDetailVo, bool> _f$hasFacilities =
      Field('hasFacilities', _$hasFacilities, key: r'has_facilities');
  static bool _$isPaid(SpotDetailVo v) => v.isPaid;
  static const Field<SpotDetailVo, bool> _f$isPaid =
      Field('isPaid', _$isPaid, key: r'is_paid');
  static int _$checkinCount(SpotDetailVo v) => v.checkinCount;
  static const Field<SpotDetailVo, int> _f$checkinCount =
      Field('checkinCount', _$checkinCount, key: r'checkin_count');
  static String? _$extraFishTypes(SpotDetailVo v) => v.extraFishTypes;
  static const Field<SpotDetailVo, String> _f$extraFishTypes = Field(
      'extraFishTypes', _$extraFishTypes,
      key: r'extra_fish_types', opt: true);
  static String? _$extraFacilities(SpotDetailVo v) => v.extraFacilities;
  static const Field<SpotDetailVo, String> _f$extraFacilities = Field(
      'extraFacilities', _$extraFacilities,
      key: r'extra_facilities', opt: true);
  static int? _$createdBy(SpotDetailVo v) => v.createdBy;
  static const Field<SpotDetailVo, int> _f$createdBy =
      Field('createdBy', _$createdBy, key: r'created_by', opt: true);
  static DateTime? _$createdAt(SpotDetailVo v) => v.createdAt;
  static const Field<SpotDetailVo, DateTime> _f$createdAt =
      Field('createdAt', _$createdAt, key: r'created_at', opt: true);
  static DateTime? _$updatedAt(SpotDetailVo v) => v.updatedAt;
  static const Field<SpotDetailVo, DateTime> _f$updatedAt =
      Field('updatedAt', _$updatedAt, key: r'updated_at', opt: true);
  static int? _$status(SpotDetailVo v) => v.status;
  static const Field<SpotDetailVo, int> _f$status =
      Field('status', _$status, opt: true);
  static String? _$source(SpotDetailVo v) => v.source;
  static const Field<SpotDetailVo, String> _f$source =
      Field('source', _$source, opt: true);
  static String? _$visibility(SpotDetailVo v) => v.visibility;
  static const Field<SpotDetailVo, String> _f$visibility =
      Field('visibility', _$visibility, opt: true);
  static List<FishType> _$fishTypeList(SpotDetailVo v) => v.fishTypeList;
  static const Field<SpotDetailVo, List<FishType>> _f$fishTypeList = Field(
      'fishTypeList', _$fishTypeList,
      key: r'fish_type_list', opt: true, def: const []);
  static List<String> _$images(SpotDetailVo v) => v.images;
  static const Field<SpotDetailVo, List<String>> _f$images =
      Field('images', _$images, opt: true, def: const []);
  static List<String> _$certificationDocuments(SpotDetailVo v) =>
      v.certificationDocuments;
  static const Field<SpotDetailVo, List<String>> _f$certificationDocuments =
      Field('certificationDocuments', _$certificationDocuments,
          key: r'certification_documents', opt: true, def: const []);
  static List<SpotPriceVo> _$prices(SpotDetailVo v) => v.prices;
  static const Field<SpotDetailVo, List<SpotPriceVo>> _f$prices =
      Field('prices', _$prices, opt: true, def: const []);
  static List<SpotFacilityVo> _$facilities(SpotDetailVo v) => v.facilities;
  static const Field<SpotDetailVo, List<SpotFacilityVo>> _f$facilities =
      Field('facilities', _$facilities, opt: true, def: const []);
  static double? _$distanceKm(SpotDetailVo v) => v.distanceKm;
  static const Field<SpotDetailVo, double> _f$distanceKm =
      Field('distanceKm', _$distanceKm, key: r'distance_km', opt: true);
  static int _$recentMomentsCount(SpotDetailVo v) => v.recentMomentsCount;
  static const Field<SpotDetailVo, int> _f$recentMomentsCount = Field(
      'recentMomentsCount', _$recentMomentsCount,
      key: r'recent_moments_count', opt: true, def: 0);
  static MomentVo? _$latestMoment(SpotDetailVo v) => v.latestMoment;
  static const Field<SpotDetailVo, MomentVo> _f$latestMoment =
      Field('latestMoment', _$latestMoment, key: r'latest_moment', opt: true);
  static User? _$creator(SpotDetailVo v) => v.creator;
  static const Field<SpotDetailVo, User> _f$creator =
      Field('creator', _$creator, opt: true);

  @override
  final MappableFields<SpotDetailVo> fields = const {
    #id: _f$id,
    #name: _f$name,
    #address: _f$address,
    #latitude: _f$latitude,
    #longitude: _f$longitude,
    #province: _f$province,
    #city: _f$city,
    #county: _f$county,
    #isOfficial: _f$isOfficial,
    #verificationLevel: _f$verificationLevel,
    #description: _f$description,
    #visitorCount: _f$visitorCount,
    #rating: _f$rating,
    #hasFacilities: _f$hasFacilities,
    #isPaid: _f$isPaid,
    #checkinCount: _f$checkinCount,
    #extraFishTypes: _f$extraFishTypes,
    #extraFacilities: _f$extraFacilities,
    #createdBy: _f$createdBy,
    #createdAt: _f$createdAt,
    #updatedAt: _f$updatedAt,
    #status: _f$status,
    #source: _f$source,
    #visibility: _f$visibility,
    #fishTypeList: _f$fishTypeList,
    #images: _f$images,
    #certificationDocuments: _f$certificationDocuments,
    #prices: _f$prices,
    #facilities: _f$facilities,
    #distanceKm: _f$distanceKm,
    #recentMomentsCount: _f$recentMomentsCount,
    #latestMoment: _f$latestMoment,
    #creator: _f$creator,
  };

  static SpotDetailVo _instantiate(DecodingData data) {
    return SpotDetailVo(
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        address: data.dec(_f$address),
        latitude: data.dec(_f$latitude),
        longitude: data.dec(_f$longitude),
        province: data.dec(_f$province),
        city: data.dec(_f$city),
        county: data.dec(_f$county),
        isOfficial: data.dec(_f$isOfficial),
        verificationLevel: data.dec(_f$verificationLevel),
        description: data.dec(_f$description),
        visitorCount: data.dec(_f$visitorCount),
        rating: data.dec(_f$rating),
        hasFacilities: data.dec(_f$hasFacilities),
        isPaid: data.dec(_f$isPaid),
        checkinCount: data.dec(_f$checkinCount),
        extraFishTypes: data.dec(_f$extraFishTypes),
        extraFacilities: data.dec(_f$extraFacilities),
        createdBy: data.dec(_f$createdBy),
        createdAt: data.dec(_f$createdAt),
        updatedAt: data.dec(_f$updatedAt),
        status: data.dec(_f$status),
        source: data.dec(_f$source),
        visibility: data.dec(_f$visibility),
        fishTypeList: data.dec(_f$fishTypeList),
        images: data.dec(_f$images),
        certificationDocuments: data.dec(_f$certificationDocuments),
        prices: data.dec(_f$prices),
        facilities: data.dec(_f$facilities),
        distanceKm: data.dec(_f$distanceKm),
        recentMomentsCount: data.dec(_f$recentMomentsCount),
        latestMoment: data.dec(_f$latestMoment),
        creator: data.dec(_f$creator));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotDetailVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotDetailVo>(map);
  }

  static SpotDetailVo fromJson(String json) {
    return ensureInitialized().decodeJson<SpotDetailVo>(json);
  }
}

mixin SpotDetailVoMappable {
  String toJson() {
    return SpotDetailVoMapper.ensureInitialized()
        .encodeJson<SpotDetailVo>(this as SpotDetailVo);
  }

  Map<String, dynamic> toMap() {
    return SpotDetailVoMapper.ensureInitialized()
        .encodeMap<SpotDetailVo>(this as SpotDetailVo);
  }

  SpotDetailVoCopyWith<SpotDetailVo, SpotDetailVo, SpotDetailVo> get copyWith =>
      _SpotDetailVoCopyWithImpl<SpotDetailVo, SpotDetailVo>(
          this as SpotDetailVo, $identity, $identity);
  @override
  String toString() {
    return SpotDetailVoMapper.ensureInitialized()
        .stringifyValue(this as SpotDetailVo);
  }

  @override
  bool operator ==(Object other) {
    return SpotDetailVoMapper.ensureInitialized()
        .equalsValue(this as SpotDetailVo, other);
  }

  @override
  int get hashCode {
    return SpotDetailVoMapper.ensureInitialized()
        .hashValue(this as SpotDetailVo);
  }
}

extension SpotDetailVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, SpotDetailVo, $Out> {
  SpotDetailVoCopyWith<$R, SpotDetailVo, $Out> get $asSpotDetailVo =>
      $base.as((v, t, t2) => _SpotDetailVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotDetailVoCopyWith<$R, $In extends SpotDetailVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, FishType, FishTypeCopyWith<$R, FishType, FishType>>
      get fishTypeList;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get images;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get certificationDocuments;
  ListCopyWith<$R, SpotPriceVo,
      SpotPriceVoCopyWith<$R, SpotPriceVo, SpotPriceVo>> get prices;
  ListCopyWith<$R, SpotFacilityVo,
          SpotFacilityVoCopyWith<$R, SpotFacilityVo, SpotFacilityVo>>
      get facilities;
  MomentVoCopyWith<$R, MomentVo, MomentVo>? get latestMoment;
  UserCopyWith<$R, User, User>? get creator;
  $R call(
      {int? id,
      String? name,
      String? address,
      double? latitude,
      double? longitude,
      String? province,
      String? city,
      String? county,
      bool? isOfficial,
      int? verificationLevel,
      String? description,
      int? visitorCount,
      double? rating,
      bool? hasFacilities,
      bool? isPaid,
      int? checkinCount,
      String? extraFishTypes,
      String? extraFacilities,
      int? createdBy,
      DateTime? createdAt,
      DateTime? updatedAt,
      int? status,
      String? source,
      String? visibility,
      List<FishType>? fishTypeList,
      List<String>? images,
      List<String>? certificationDocuments,
      List<SpotPriceVo>? prices,
      List<SpotFacilityVo>? facilities,
      double? distanceKm,
      int? recentMomentsCount,
      MomentVo? latestMoment,
      User? creator});
  SpotDetailVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _SpotDetailVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotDetailVo, $Out>
    implements SpotDetailVoCopyWith<$R, SpotDetailVo, $Out> {
  _SpotDetailVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotDetailVo> $mapper =
      SpotDetailVoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, FishType, FishTypeCopyWith<$R, FishType, FishType>>
      get fishTypeList => ListCopyWith($value.fishTypeList,
          (v, t) => v.copyWith.$chain(t), (v) => call(fishTypeList: v));
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get images =>
      ListCopyWith($value.images, (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(images: v));
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get certificationDocuments => ListCopyWith(
          $value.certificationDocuments,
          (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(certificationDocuments: v));
  @override
  ListCopyWith<$R, SpotPriceVo,
          SpotPriceVoCopyWith<$R, SpotPriceVo, SpotPriceVo>>
      get prices => ListCopyWith($value.prices, (v, t) => v.copyWith.$chain(t),
          (v) => call(prices: v));
  @override
  ListCopyWith<$R, SpotFacilityVo,
          SpotFacilityVoCopyWith<$R, SpotFacilityVo, SpotFacilityVo>>
      get facilities => ListCopyWith($value.facilities,
          (v, t) => v.copyWith.$chain(t), (v) => call(facilities: v));
  @override
  MomentVoCopyWith<$R, MomentVo, MomentVo>? get latestMoment =>
      $value.latestMoment?.copyWith.$chain((v) => call(latestMoment: v));
  @override
  UserCopyWith<$R, User, User>? get creator =>
      $value.creator?.copyWith.$chain((v) => call(creator: v));
  @override
  $R call(
          {int? id,
          String? name,
          String? address,
          double? latitude,
          double? longitude,
          String? province,
          String? city,
          String? county,
          bool? isOfficial,
          int? verificationLevel,
          Object? description = $none,
          int? visitorCount,
          double? rating,
          bool? hasFacilities,
          bool? isPaid,
          int? checkinCount,
          Object? extraFishTypes = $none,
          Object? extraFacilities = $none,
          Object? createdBy = $none,
          Object? createdAt = $none,
          Object? updatedAt = $none,
          Object? status = $none,
          Object? source = $none,
          Object? visibility = $none,
          List<FishType>? fishTypeList,
          List<String>? images,
          List<String>? certificationDocuments,
          List<SpotPriceVo>? prices,
          List<SpotFacilityVo>? facilities,
          Object? distanceKm = $none,
          int? recentMomentsCount,
          Object? latestMoment = $none,
          Object? creator = $none}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (address != null) #address: address,
        if (latitude != null) #latitude: latitude,
        if (longitude != null) #longitude: longitude,
        if (province != null) #province: province,
        if (city != null) #city: city,
        if (county != null) #county: county,
        if (isOfficial != null) #isOfficial: isOfficial,
        if (verificationLevel != null) #verificationLevel: verificationLevel,
        if (description != $none) #description: description,
        if (visitorCount != null) #visitorCount: visitorCount,
        if (rating != null) #rating: rating,
        if (hasFacilities != null) #hasFacilities: hasFacilities,
        if (isPaid != null) #isPaid: isPaid,
        if (checkinCount != null) #checkinCount: checkinCount,
        if (extraFishTypes != $none) #extraFishTypes: extraFishTypes,
        if (extraFacilities != $none) #extraFacilities: extraFacilities,
        if (createdBy != $none) #createdBy: createdBy,
        if (createdAt != $none) #createdAt: createdAt,
        if (updatedAt != $none) #updatedAt: updatedAt,
        if (status != $none) #status: status,
        if (source != $none) #source: source,
        if (visibility != $none) #visibility: visibility,
        if (fishTypeList != null) #fishTypeList: fishTypeList,
        if (images != null) #images: images,
        if (certificationDocuments != null)
          #certificationDocuments: certificationDocuments,
        if (prices != null) #prices: prices,
        if (facilities != null) #facilities: facilities,
        if (distanceKm != $none) #distanceKm: distanceKm,
        if (recentMomentsCount != null) #recentMomentsCount: recentMomentsCount,
        if (latestMoment != $none) #latestMoment: latestMoment,
        if (creator != $none) #creator: creator
      }));
  @override
  SpotDetailVo $make(CopyWithData data) => SpotDetailVo(
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      address: data.get(#address, or: $value.address),
      latitude: data.get(#latitude, or: $value.latitude),
      longitude: data.get(#longitude, or: $value.longitude),
      province: data.get(#province, or: $value.province),
      city: data.get(#city, or: $value.city),
      county: data.get(#county, or: $value.county),
      isOfficial: data.get(#isOfficial, or: $value.isOfficial),
      verificationLevel:
          data.get(#verificationLevel, or: $value.verificationLevel),
      description: data.get(#description, or: $value.description),
      visitorCount: data.get(#visitorCount, or: $value.visitorCount),
      rating: data.get(#rating, or: $value.rating),
      hasFacilities: data.get(#hasFacilities, or: $value.hasFacilities),
      isPaid: data.get(#isPaid, or: $value.isPaid),
      checkinCount: data.get(#checkinCount, or: $value.checkinCount),
      extraFishTypes: data.get(#extraFishTypes, or: $value.extraFishTypes),
      extraFacilities: data.get(#extraFacilities, or: $value.extraFacilities),
      createdBy: data.get(#createdBy, or: $value.createdBy),
      createdAt: data.get(#createdAt, or: $value.createdAt),
      updatedAt: data.get(#updatedAt, or: $value.updatedAt),
      status: data.get(#status, or: $value.status),
      source: data.get(#source, or: $value.source),
      visibility: data.get(#visibility, or: $value.visibility),
      fishTypeList: data.get(#fishTypeList, or: $value.fishTypeList),
      images: data.get(#images, or: $value.images),
      certificationDocuments:
          data.get(#certificationDocuments, or: $value.certificationDocuments),
      prices: data.get(#prices, or: $value.prices),
      facilities: data.get(#facilities, or: $value.facilities),
      distanceKm: data.get(#distanceKm, or: $value.distanceKm),
      recentMomentsCount:
          data.get(#recentMomentsCount, or: $value.recentMomentsCount),
      latestMoment: data.get(#latestMoment, or: $value.latestMoment),
      creator: data.get(#creator, or: $value.creator));

  @override
  SpotDetailVoCopyWith<$R2, SpotDetailVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _SpotDetailVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
