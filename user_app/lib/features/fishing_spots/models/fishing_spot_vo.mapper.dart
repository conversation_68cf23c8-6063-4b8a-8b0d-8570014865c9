// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'fishing_spot_vo.dart';

class FishingSpotVoMapper extends ClassMapperBase<FishingSpotVo> {
  FishingSpotVoMapper._();

  static FishingSpotVoMapper? _instance;
  static FishingSpotVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = FishingSpotVoMapper._());
      FishTypeMapper.ensureInitialized();
      SpotPriceVoMapper.ensureInitialized();
      SpotFacilityVoMapper.ensureInitialized();
      UserMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'FishingSpotVo';

  static int _$id(FishingSpotVo v) => v.id;
  static const Field<FishingSpotVo, int> _f$id = Field('id', _$id);
  static String _$name(FishingSpotVo v) => v.name;
  static const Field<FishingSpotVo, String> _f$name = Field('name', _$name);
  static String _$address(FishingSpotVo v) => v.address;
  static const Field<FishingSpotVo, String> _f$address =
      Field('address', _$address);
  static double _$latitude(FishingSpotVo v) => v.latitude;
  static const Field<FishingSpotVo, double> _f$latitude =
      Field('latitude', _$latitude);
  static double _$longitude(FishingSpotVo v) => v.longitude;
  static const Field<FishingSpotVo, double> _f$longitude =
      Field('longitude', _$longitude);
  static String _$province(FishingSpotVo v) => v.province;
  static const Field<FishingSpotVo, String> _f$province =
      Field('province', _$province);
  static String _$city(FishingSpotVo v) => v.city;
  static const Field<FishingSpotVo, String> _f$city = Field('city', _$city);
  static String _$county(FishingSpotVo v) => v.county;
  static const Field<FishingSpotVo, String> _f$county =
      Field('county', _$county);
  static bool _$isOfficial(FishingSpotVo v) => v.isOfficial;
  static const Field<FishingSpotVo, bool> _f$isOfficial =
      Field('isOfficial', _$isOfficial, key: r'is_official');
  static int _$verificationLevel(FishingSpotVo v) => v.verificationLevel;
  static const Field<FishingSpotVo, int> _f$verificationLevel = Field(
      'verificationLevel', _$verificationLevel,
      key: r'verification_level');
  static String? _$description(FishingSpotVo v) => v.description;
  static const Field<FishingSpotVo, String> _f$description =
      Field('description', _$description, opt: true);
  static List<String>? _$images(FishingSpotVo v) => v.images;
  static const Field<FishingSpotVo, List<String>> _f$images =
      Field('images', _$images, opt: true);
  static List<FishType> _$fishTypeList(FishingSpotVo v) => v.fishTypeList;
  static const Field<FishingSpotVo, List<FishType>> _f$fishTypeList = Field(
      'fishTypeList', _$fishTypeList,
      key: r'fish_type_list', opt: true, def: const []);
  static int _$visitorCount(FishingSpotVo v) => v.visitorCount;
  static const Field<FishingSpotVo, int> _f$visitorCount =
      Field('visitorCount', _$visitorCount, key: r'visitor_count');
  static double _$rating(FishingSpotVo v) => v.rating;
  static const Field<FishingSpotVo, double> _f$rating =
      Field('rating', _$rating);
  static bool _$hasFacilities(FishingSpotVo v) => v.hasFacilities;
  static const Field<FishingSpotVo, bool> _f$hasFacilities =
      Field('hasFacilities', _$hasFacilities, key: r'has_facilities');
  static bool _$isPaid(FishingSpotVo v) => v.isPaid;
  static const Field<FishingSpotVo, bool> _f$isPaid =
      Field('isPaid', _$isPaid, key: r'paid');
  static dynamic _$price(FishingSpotVo v) => v.price;
  static const Field<FishingSpotVo, dynamic> _f$price =
      Field('price', _$price, opt: true);
  static int _$checkinCount(FishingSpotVo v) => v.checkinCount;
  static const Field<FishingSpotVo, int> _f$checkinCount =
      Field('checkinCount', _$checkinCount, key: r'checkin_count');
  static List<String> _$seasons(FishingSpotVo v) => v.seasons;
  static const Field<FishingSpotVo, List<String>> _f$seasons =
      Field('seasons', _$seasons, opt: true, def: const []);
  static List<SpotPriceVo> _$prices(FishingSpotVo v) => v.prices;
  static const Field<FishingSpotVo, List<SpotPriceVo>> _f$prices =
      Field('prices', _$prices, opt: true, def: const []);
  static List<SpotFacilityVo> _$facilities(FishingSpotVo v) => v.facilities;
  static const Field<FishingSpotVo, List<SpotFacilityVo>> _f$facilities =
      Field('facilities', _$facilities, opt: true, def: const []);
  static String? _$extraFishTypes(FishingSpotVo v) => v.extraFishTypes;
  static const Field<FishingSpotVo, String> _f$extraFishTypes = Field(
      'extraFishTypes', _$extraFishTypes,
      key: r'extra_fish_types', opt: true);
  static List<String> _$extraFishTypesList(FishingSpotVo v) =>
      v.extraFishTypesList;
  static const Field<FishingSpotVo, List<String>> _f$extraFishTypesList = Field(
      'extraFishTypesList', _$extraFishTypesList,
      key: r'extra_fish_types_list', opt: true, def: const []);
  static String? _$extraFacilities(FishingSpotVo v) => v.extraFacilities;
  static const Field<FishingSpotVo, String> _f$extraFacilities = Field(
      'extraFacilities', _$extraFacilities,
      key: r'extra_facilities', opt: true);
  static List<String> _$extraFacilitiesList(FishingSpotVo v) =>
      v.extraFacilitiesList;
  static const Field<FishingSpotVo, List<String>> _f$extraFacilitiesList =
      Field('extraFacilitiesList', _$extraFacilitiesList,
          key: r'extra_facilities_list', opt: true, def: const []);
  static int _$recentMomentsCount(FishingSpotVo v) => v.recentMomentsCount;
  static const Field<FishingSpotVo, int> _f$recentMomentsCount = Field(
      'recentMomentsCount', _$recentMomentsCount,
      key: r'recent_moments_count', opt: true, def: 0);
  static int? _$createdBy(FishingSpotVo v) => v.createdBy;
  static const Field<FishingSpotVo, int> _f$createdBy =
      Field('createdBy', _$createdBy, key: r'created_by', opt: true);
  static User? _$creator(FishingSpotVo v) => v.creator;
  static const Field<FishingSpotVo, User> _f$creator =
      Field('creator', _$creator, opt: true);

  @override
  final MappableFields<FishingSpotVo> fields = const {
    #id: _f$id,
    #name: _f$name,
    #address: _f$address,
    #latitude: _f$latitude,
    #longitude: _f$longitude,
    #province: _f$province,
    #city: _f$city,
    #county: _f$county,
    #isOfficial: _f$isOfficial,
    #verificationLevel: _f$verificationLevel,
    #description: _f$description,
    #images: _f$images,
    #fishTypeList: _f$fishTypeList,
    #visitorCount: _f$visitorCount,
    #rating: _f$rating,
    #hasFacilities: _f$hasFacilities,
    #isPaid: _f$isPaid,
    #price: _f$price,
    #checkinCount: _f$checkinCount,
    #seasons: _f$seasons,
    #prices: _f$prices,
    #facilities: _f$facilities,
    #extraFishTypes: _f$extraFishTypes,
    #extraFishTypesList: _f$extraFishTypesList,
    #extraFacilities: _f$extraFacilities,
    #extraFacilitiesList: _f$extraFacilitiesList,
    #recentMomentsCount: _f$recentMomentsCount,
    #createdBy: _f$createdBy,
    #creator: _f$creator,
  };

  static FishingSpotVo _instantiate(DecodingData data) {
    return FishingSpotVo(
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        address: data.dec(_f$address),
        latitude: data.dec(_f$latitude),
        longitude: data.dec(_f$longitude),
        province: data.dec(_f$province),
        city: data.dec(_f$city),
        county: data.dec(_f$county),
        isOfficial: data.dec(_f$isOfficial),
        verificationLevel: data.dec(_f$verificationLevel),
        description: data.dec(_f$description),
        images: data.dec(_f$images),
        fishTypeList: data.dec(_f$fishTypeList),
        visitorCount: data.dec(_f$visitorCount),
        rating: data.dec(_f$rating),
        hasFacilities: data.dec(_f$hasFacilities),
        isPaid: data.dec(_f$isPaid),
        price: data.dec(_f$price),
        checkinCount: data.dec(_f$checkinCount),
        seasons: data.dec(_f$seasons),
        prices: data.dec(_f$prices),
        facilities: data.dec(_f$facilities),
        extraFishTypes: data.dec(_f$extraFishTypes),
        extraFishTypesList: data.dec(_f$extraFishTypesList),
        extraFacilities: data.dec(_f$extraFacilities),
        extraFacilitiesList: data.dec(_f$extraFacilitiesList),
        recentMomentsCount: data.dec(_f$recentMomentsCount),
        createdBy: data.dec(_f$createdBy),
        creator: data.dec(_f$creator));
  }

  @override
  final Function instantiate = _instantiate;

  static FishingSpotVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<FishingSpotVo>(map);
  }

  static FishingSpotVo fromJson(String json) {
    return ensureInitialized().decodeJson<FishingSpotVo>(json);
  }
}

mixin FishingSpotVoMappable {
  String toJson() {
    return FishingSpotVoMapper.ensureInitialized()
        .encodeJson<FishingSpotVo>(this as FishingSpotVo);
  }

  Map<String, dynamic> toMap() {
    return FishingSpotVoMapper.ensureInitialized()
        .encodeMap<FishingSpotVo>(this as FishingSpotVo);
  }

  FishingSpotVoCopyWith<FishingSpotVo, FishingSpotVo, FishingSpotVo>
      get copyWith => _FishingSpotVoCopyWithImpl<FishingSpotVo, FishingSpotVo>(
          this as FishingSpotVo, $identity, $identity);
  @override
  String toString() {
    return FishingSpotVoMapper.ensureInitialized()
        .stringifyValue(this as FishingSpotVo);
  }

  @override
  bool operator ==(Object other) {
    return FishingSpotVoMapper.ensureInitialized()
        .equalsValue(this as FishingSpotVo, other);
  }

  @override
  int get hashCode {
    return FishingSpotVoMapper.ensureInitialized()
        .hashValue(this as FishingSpotVo);
  }
}

extension FishingSpotVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, FishingSpotVo, $Out> {
  FishingSpotVoCopyWith<$R, FishingSpotVo, $Out> get $asFishingSpotVo =>
      $base.as((v, t, t2) => _FishingSpotVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class FishingSpotVoCopyWith<$R, $In extends FishingSpotVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>? get images;
  ListCopyWith<$R, FishType, FishTypeCopyWith<$R, FishType, FishType>>
      get fishTypeList;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get seasons;
  ListCopyWith<$R, SpotPriceVo,
      SpotPriceVoCopyWith<$R, SpotPriceVo, SpotPriceVo>> get prices;
  ListCopyWith<$R, SpotFacilityVo,
          SpotFacilityVoCopyWith<$R, SpotFacilityVo, SpotFacilityVo>>
      get facilities;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get extraFishTypesList;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get extraFacilitiesList;
  UserCopyWith<$R, User, User>? get creator;
  $R call(
      {int? id,
      String? name,
      String? address,
      double? latitude,
      double? longitude,
      String? province,
      String? city,
      String? county,
      bool? isOfficial,
      int? verificationLevel,
      String? description,
      List<String>? images,
      List<FishType>? fishTypeList,
      int? visitorCount,
      double? rating,
      bool? hasFacilities,
      bool? isPaid,
      dynamic price,
      int? checkinCount,
      List<String>? seasons,
      List<SpotPriceVo>? prices,
      List<SpotFacilityVo>? facilities,
      String? extraFishTypes,
      List<String>? extraFishTypesList,
      String? extraFacilities,
      List<String>? extraFacilitiesList,
      int? recentMomentsCount,
      int? createdBy,
      User? creator});
  FishingSpotVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _FishingSpotVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, FishingSpotVo, $Out>
    implements FishingSpotVoCopyWith<$R, FishingSpotVo, $Out> {
  _FishingSpotVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<FishingSpotVo> $mapper =
      FishingSpotVoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>? get images =>
      $value.images != null
          ? ListCopyWith($value.images!,
              (v, t) => ObjectCopyWith(v, $identity, t), (v) => call(images: v))
          : null;
  @override
  ListCopyWith<$R, FishType, FishTypeCopyWith<$R, FishType, FishType>>
      get fishTypeList => ListCopyWith($value.fishTypeList,
          (v, t) => v.copyWith.$chain(t), (v) => call(fishTypeList: v));
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get seasons =>
      ListCopyWith($value.seasons, (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(seasons: v));
  @override
  ListCopyWith<$R, SpotPriceVo,
          SpotPriceVoCopyWith<$R, SpotPriceVo, SpotPriceVo>>
      get prices => ListCopyWith($value.prices, (v, t) => v.copyWith.$chain(t),
          (v) => call(prices: v));
  @override
  ListCopyWith<$R, SpotFacilityVo,
          SpotFacilityVoCopyWith<$R, SpotFacilityVo, SpotFacilityVo>>
      get facilities => ListCopyWith($value.facilities,
          (v, t) => v.copyWith.$chain(t), (v) => call(facilities: v));
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get extraFishTypesList => ListCopyWith(
          $value.extraFishTypesList,
          (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(extraFishTypesList: v));
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get extraFacilitiesList => ListCopyWith(
          $value.extraFacilitiesList,
          (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(extraFacilitiesList: v));
  @override
  UserCopyWith<$R, User, User>? get creator =>
      $value.creator?.copyWith.$chain((v) => call(creator: v));
  @override
  $R call(
          {int? id,
          String? name,
          String? address,
          double? latitude,
          double? longitude,
          String? province,
          String? city,
          String? county,
          bool? isOfficial,
          int? verificationLevel,
          Object? description = $none,
          Object? images = $none,
          List<FishType>? fishTypeList,
          int? visitorCount,
          double? rating,
          bool? hasFacilities,
          bool? isPaid,
          Object? price = $none,
          int? checkinCount,
          List<String>? seasons,
          List<SpotPriceVo>? prices,
          List<SpotFacilityVo>? facilities,
          Object? extraFishTypes = $none,
          List<String>? extraFishTypesList,
          Object? extraFacilities = $none,
          List<String>? extraFacilitiesList,
          int? recentMomentsCount,
          Object? createdBy = $none,
          Object? creator = $none}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (address != null) #address: address,
        if (latitude != null) #latitude: latitude,
        if (longitude != null) #longitude: longitude,
        if (province != null) #province: province,
        if (city != null) #city: city,
        if (county != null) #county: county,
        if (isOfficial != null) #isOfficial: isOfficial,
        if (verificationLevel != null) #verificationLevel: verificationLevel,
        if (description != $none) #description: description,
        if (images != $none) #images: images,
        if (fishTypeList != null) #fishTypeList: fishTypeList,
        if (visitorCount != null) #visitorCount: visitorCount,
        if (rating != null) #rating: rating,
        if (hasFacilities != null) #hasFacilities: hasFacilities,
        if (isPaid != null) #isPaid: isPaid,
        if (price != $none) #price: price,
        if (checkinCount != null) #checkinCount: checkinCount,
        if (seasons != null) #seasons: seasons,
        if (prices != null) #prices: prices,
        if (facilities != null) #facilities: facilities,
        if (extraFishTypes != $none) #extraFishTypes: extraFishTypes,
        if (extraFishTypesList != null) #extraFishTypesList: extraFishTypesList,
        if (extraFacilities != $none) #extraFacilities: extraFacilities,
        if (extraFacilitiesList != null)
          #extraFacilitiesList: extraFacilitiesList,
        if (recentMomentsCount != null) #recentMomentsCount: recentMomentsCount,
        if (createdBy != $none) #createdBy: createdBy,
        if (creator != $none) #creator: creator
      }));
  @override
  FishingSpotVo $make(CopyWithData data) => FishingSpotVo(
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      address: data.get(#address, or: $value.address),
      latitude: data.get(#latitude, or: $value.latitude),
      longitude: data.get(#longitude, or: $value.longitude),
      province: data.get(#province, or: $value.province),
      city: data.get(#city, or: $value.city),
      county: data.get(#county, or: $value.county),
      isOfficial: data.get(#isOfficial, or: $value.isOfficial),
      verificationLevel:
          data.get(#verificationLevel, or: $value.verificationLevel),
      description: data.get(#description, or: $value.description),
      images: data.get(#images, or: $value.images),
      fishTypeList: data.get(#fishTypeList, or: $value.fishTypeList),
      visitorCount: data.get(#visitorCount, or: $value.visitorCount),
      rating: data.get(#rating, or: $value.rating),
      hasFacilities: data.get(#hasFacilities, or: $value.hasFacilities),
      isPaid: data.get(#isPaid, or: $value.isPaid),
      price: data.get(#price, or: $value.price),
      checkinCount: data.get(#checkinCount, or: $value.checkinCount),
      seasons: data.get(#seasons, or: $value.seasons),
      prices: data.get(#prices, or: $value.prices),
      facilities: data.get(#facilities, or: $value.facilities),
      extraFishTypes: data.get(#extraFishTypes, or: $value.extraFishTypes),
      extraFishTypesList:
          data.get(#extraFishTypesList, or: $value.extraFishTypesList),
      extraFacilities: data.get(#extraFacilities, or: $value.extraFacilities),
      extraFacilitiesList:
          data.get(#extraFacilitiesList, or: $value.extraFacilitiesList),
      recentMomentsCount:
          data.get(#recentMomentsCount, or: $value.recentMomentsCount),
      createdBy: data.get(#createdBy, or: $value.createdBy),
      creator: data.get(#creator, or: $value.creator));

  @override
  FishingSpotVoCopyWith<$R2, FishingSpotVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _FishingSpotVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
