import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';

part 'spot_price_vo.mapper.dart';

@MappableClass()
class SpotPriceVo with SpotPriceVoMappable {
  final num id;
  final num? fishTypeId;
  final FishType? fishType;
  final int priceType;
  final String priceTypeName;
  final num price;
  final int? hours;
  final String? description;

  const SpotPriceVo({
    required this.id,
    this.fishTypeId,
    this.fishType,
    required this.priceType,
    required this.priceTypeName,
    required this.price,
    this.hours,
    this.description,
  });

  static final fromMap = SpotPriceVoMapper.fromMap;
}
