import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';

part 'fishing_recommendation.mapper.dart';

@MappableClass()
class FishingRecommendation with FishingRecommendationMappable {
  final double fishingIndex;
  final List<FishType> recommendedFish;
  final String weatherAnalysis;

  FishingRecommendation({
    required this.fishingIndex,
    required this.recommendedFish,
    required this.weatherAnalysis,
  });

  static final fromMap = FishingRecommendationMapper.fromMap;
}
