// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'spot_facility_vo.dart';

class SpotFacilityVoMapper extends ClassMapperBase<SpotFacilityVo> {
  SpotFacilityVoMapper._();

  static SpotFacilityVoMapper? _instance;
  static SpotFacilityVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotFacilityVoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'SpotFacilityVo';

  static num _$id(SpotFacilityVo v) => v.id;
  static const Field<SpotFacilityVo, num> _f$id = Field('id', _$id);
  static String _$name(SpotFacilityVo v) => v.name;
  static const Field<SpotFacilityVo, String> _f$name = Field('name', _$name);
  static String _$icon(SpotFacilityVo v) => v.icon;
  static const Field<SpotFacilityVo, String> _f$icon = Field('icon', _$icon);
  static String? _$description(SpotFacilityVo v) => v.description;
  static const Field<SpotFacilityVo, String> _f$description =
      Field('description', _$description, opt: true);
  static String? _$details(SpotFacilityVo v) => v.details;
  static const Field<SpotFacilityVo, String> _f$details =
      Field('details', _$details, opt: true);

  @override
  final MappableFields<SpotFacilityVo> fields = const {
    #id: _f$id,
    #name: _f$name,
    #icon: _f$icon,
    #description: _f$description,
    #details: _f$details,
  };

  static SpotFacilityVo _instantiate(DecodingData data) {
    return SpotFacilityVo(
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        icon: data.dec(_f$icon),
        description: data.dec(_f$description),
        details: data.dec(_f$details));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotFacilityVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotFacilityVo>(map);
  }

  static SpotFacilityVo fromJson(String json) {
    return ensureInitialized().decodeJson<SpotFacilityVo>(json);
  }
}

mixin SpotFacilityVoMappable {
  String toJson() {
    return SpotFacilityVoMapper.ensureInitialized()
        .encodeJson<SpotFacilityVo>(this as SpotFacilityVo);
  }

  Map<String, dynamic> toMap() {
    return SpotFacilityVoMapper.ensureInitialized()
        .encodeMap<SpotFacilityVo>(this as SpotFacilityVo);
  }

  SpotFacilityVoCopyWith<SpotFacilityVo, SpotFacilityVo, SpotFacilityVo>
      get copyWith =>
          _SpotFacilityVoCopyWithImpl<SpotFacilityVo, SpotFacilityVo>(
              this as SpotFacilityVo, $identity, $identity);
  @override
  String toString() {
    return SpotFacilityVoMapper.ensureInitialized()
        .stringifyValue(this as SpotFacilityVo);
  }

  @override
  bool operator ==(Object other) {
    return SpotFacilityVoMapper.ensureInitialized()
        .equalsValue(this as SpotFacilityVo, other);
  }

  @override
  int get hashCode {
    return SpotFacilityVoMapper.ensureInitialized()
        .hashValue(this as SpotFacilityVo);
  }
}

extension SpotFacilityVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, SpotFacilityVo, $Out> {
  SpotFacilityVoCopyWith<$R, SpotFacilityVo, $Out> get $asSpotFacilityVo =>
      $base.as((v, t, t2) => _SpotFacilityVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotFacilityVoCopyWith<$R, $In extends SpotFacilityVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {num? id,
      String? name,
      String? icon,
      String? description,
      String? details});
  SpotFacilityVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _SpotFacilityVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotFacilityVo, $Out>
    implements SpotFacilityVoCopyWith<$R, SpotFacilityVo, $Out> {
  _SpotFacilityVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotFacilityVo> $mapper =
      SpotFacilityVoMapper.ensureInitialized();
  @override
  $R call(
          {num? id,
          String? name,
          String? icon,
          Object? description = $none,
          Object? details = $none}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (icon != null) #icon: icon,
        if (description != $none) #description: description,
        if (details != $none) #details: details
      }));
  @override
  SpotFacilityVo $make(CopyWithData data) => SpotFacilityVo(
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      icon: data.get(#icon, or: $value.icon),
      description: data.get(#description, or: $value.description),
      details: data.get(#details, or: $value.details));

  @override
  SpotFacilityVoCopyWith<$R2, SpotFacilityVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _SpotFacilityVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
