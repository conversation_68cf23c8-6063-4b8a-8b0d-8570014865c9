import 'package:dart_mappable/dart_mappable.dart';
import 'fishing_spot_detail_dto.dart';

part 'fishing_spot_detail_response_dto.mapper.dart';

/// 钓点详情API响应DTO
@MappableClass()
class FishingSpotDetailResponseDto with FishingSpotDetailResponseDtoMappable {
  final int code;
  final String message;
  final FishingSpotDetailDto data;

  const FishingSpotDetailResponseDto({
    required this.code,
    required this.message,
    required this.data,
  });

  static final fromMap = FishingSpotDetailResponseDtoMapper.fromMap;
}