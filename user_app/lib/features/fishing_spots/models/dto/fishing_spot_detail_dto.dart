import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/models/spot_facility_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_price_vo.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/models/user.dart';

part 'fishing_spot_detail_dto.mapper.dart';

/// API响应的钓点详情DTO
@MappableClass()
class FishingSpotDetailDto with FishingSpotDetailDtoMappable {
  /// 钓点基本信息
  final int id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final String province;
  final String city;
  final String county;
  @MappableField(key: 'is_official')
  final bool isOfficial;
  @MappableField(key: 'verification_level')
  final int verificationLevel;
  final String? description;
  @MappableField(key: 'visitor_count')
  final int visitorCount;
  final double rating;
  @MappableField(key: 'has_facilities')
  final bool hasFacilities;
  @MappableField(key: 'is_paid')
  final bool isPaid;
  @MappableField(key: 'checkin_count')
  final int checkinCount;
  @MappableField(key: 'extra_fish_types')
  final String? extraFishTypes;
  @MappableField(key: 'extra_facilities')
  final String? extraFacilities;
  @MappableField(key: 'created_by')
  final int? createdBy;
  @MappableField(key: 'created_at')
  final String? createdAt;
  @MappableField(key: 'updated_at')
  final String? updatedAt;
  final int? status;
  final String? source;
  final String? visibility;

  /// 关联数据
  @MappableField(key: 'fish_type_list')
  final List<FishTypeDto> fishTypeList;
  final List<String> images;
  @MappableField(key: 'certification_documents')
  final List<String> certificationDocuments;
  final List<SpotPriceDtoFromApi> prices;
  final List<SpotFacilityDtoFromApi> facilities;
  @MappableField(key: 'distance_km')
  final double? distanceKm;
  @MappableField(key: 'recent_moments_count')
  final int? recentMomentsCount;
  @MappableField(key: 'extra_fish_types_list')
  final List<String> extraFishTypesList;
  @MappableField(key: 'extra_facilities_list')
  final List<String> extraFacilitiesList;
  final User? creator;

  const FishingSpotDetailDto({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.province,
    required this.city,
    required this.county,
    required this.isOfficial,
    required this.verificationLevel,
    this.description,
    required this.visitorCount,
    required this.rating,
    required this.hasFacilities,
    required this.isPaid,
    required this.checkinCount,
    this.extraFishTypes,
    this.extraFacilities,
    this.createdBy,
    this.createdAt,
    this.updatedAt,
    this.status,
    this.source,
    this.visibility,
    this.fishTypeList = const [],
    this.images = const [],
    this.certificationDocuments = const [],
    this.prices = const [],
    this.facilities = const [],
    this.distanceKm,
    this.recentMomentsCount,
    this.extraFishTypesList = const [],
    this.extraFacilitiesList = const [],
    this.creator,
  });

  /// 转换为FishingSpotVo
  FishingSpotVo toFishingSpotVo() {
    // 计算季节信息
    final Set<String> seasonsSet = {};
    for (final fishType in fishTypeList) {
      if (fishType.seasonSpring == true) seasonsSet.add('春');
      if (fishType.seasonSummer == true) seasonsSet.add('夏');
      if (fishType.seasonAutumn == true) seasonsSet.add('秋');
      if (fishType.seasonWinter == true) seasonsSet.add('冬');
    }
    final seasons =
        seasonsSet.isEmpty ? ['春', '夏', '秋', '冬'] : seasonsSet.toList();

    // 转换鱼类信息
    final fishTypes = fishTypeList.map((dto) => dto.toFishType()).toList();

    // 转换价格信息
    final spotPrices = prices.map((dto) => dto.toSpotPriceVo()).toList();

    // 转换设施信息
    final spotFacilities =
        facilities.map((dto) => dto.toSpotFacilityVo()).toList();

    return FishingSpotVo(
      id: id,
      name: name,
      address: address,
      latitude: latitude,
      longitude: longitude,
      province: province,
      city: city,
      county: county,
      isOfficial: isOfficial,
      verificationLevel: verificationLevel,
      description: description,
      images: images,
      fishTypeList: fishTypes,
      visitorCount: visitorCount,
      rating: rating,
      hasFacilities: hasFacilities,
      isPaid: isPaid,
      price: null, // FishingSpotVo中的price字段设为null，实际价格信息在prices列表中
      checkinCount: checkinCount,
      seasons: seasons,
      prices: spotPrices,
      facilities: spotFacilities,
      extraFishTypes: extraFishTypes,
      extraFishTypesList: extraFishTypesList,
      extraFacilities: extraFacilities,
      extraFacilitiesList: extraFacilitiesList,
      recentMomentsCount: recentMomentsCount ?? 0,
      createdBy: createdBy,
      creator: creator,
    );
  }

  static final fromMap = FishingSpotDetailDtoMapper.fromMap;
}

/// 从API返回的鱼类DTO
@MappableClass()
class FishTypeDto with FishTypeDtoMappable {
  final dynamic spots;
  final dynamic season;
  final int id;
  final String name;
  final String? description;
  @MappableField(key: 'image_url')
  final String? imageUrl;
  @MappableField(key: 'season_spring')
  final bool seasonSpring;
  @MappableField(key: 'season_summer')
  final bool seasonSummer;
  @MappableField(key: 'season_autumn')
  final bool seasonAutumn;
  @MappableField(key: 'season_winter')
  final bool seasonWinter;

  const FishTypeDto({
    this.spots,
    this.season,
    required this.id,
    required this.name,
    this.description,
    this.imageUrl,
    required this.seasonSpring,
    required this.seasonSummer,
    required this.seasonAutumn,
    required this.seasonWinter,
  });

  /// 转换为FishType
  FishType toFishType() {
    return FishType(
      id: id,
      name: name,
      description: description ?? '',
      imageUrl: imageUrl,
      seasonSpring: seasonSpring,
      seasonSummer: seasonSummer,
      seasonAutumn: seasonAutumn,
      seasonWinter: seasonWinter,
    );
  }

  static final fromMap = FishTypeDtoMapper.fromMap;
}

/// 从API返回的价格DTO
@MappableClass()
class SpotPriceDtoFromApi with SpotPriceDtoFromApiMappable {
  final int id;
  @MappableField(key: 'fish_type_id')
  final int? fishTypeId;
  @MappableField(key: 'fish_type')
  final FishTypeDto? fishType;
  @MappableField(key: 'price_type')
  final int priceType;
  @MappableField(key: 'price_type_name')
  final String priceTypeName;
  final double price;
  final int? hours;
  final String? description;

  const SpotPriceDtoFromApi({
    required this.id,
    this.fishTypeId,
    this.fishType,
    required this.priceType,
    required this.priceTypeName,
    required this.price,
    this.hours,
    this.description,
  });

  /// 转换为SpotPriceVo
  SpotPriceVo toSpotPriceVo() {
    return SpotPriceVo(
      id: id,
      fishTypeId: fishTypeId,
      fishType: fishType?.toFishType(),
      priceType: priceType,
      priceTypeName: priceTypeName,
      price: price,
      hours: hours,
      description: description,
    );
  }

  static final fromMap = SpotPriceDtoFromApiMapper.fromMap;
}

/// 从API返回的设施DTO
@MappableClass()
class SpotFacilityDtoFromApi with SpotFacilityDtoFromApiMappable {
  final int id;
  final String name;
  final String? icon;
  final String? description;
  final String? details;

  const SpotFacilityDtoFromApi({
    required this.id,
    required this.name,
    this.icon,
    this.description,
    this.details,
  });

  /// 转换为SpotFacilityVo
  SpotFacilityVo toSpotFacilityVo() {
    return SpotFacilityVo(
      id: id,
      name: name,
      icon: icon ?? 'help', // 如果没有图标，使用默认图标
      description: description,
      details: details,
    );
  }

  static final fromMap = SpotFacilityDtoFromApiMapper.fromMap;
}
