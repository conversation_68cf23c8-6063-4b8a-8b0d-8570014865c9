// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'fishing_spot_detail_response_dto.dart';

class FishingSpotDetailResponseDtoMapper
    extends ClassMapperBase<FishingSpotDetailResponseDto> {
  FishingSpotDetailResponseDtoMapper._();

  static FishingSpotDetailResponseDtoMapper? _instance;
  static FishingSpotDetailResponseDtoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals
          .use(_instance = FishingSpotDetailResponseDtoMapper._());
      FishingSpotDetailDtoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'FishingSpotDetailResponseDto';

  static int _$code(FishingSpotDetailResponseDto v) => v.code;
  static const Field<FishingSpotDetailResponseDto, int> _f$code =
      Field('code', _$code);
  static String _$message(FishingSpotDetailResponseDto v) => v.message;
  static const Field<FishingSpotDetailResponseDto, String> _f$message =
      Field('message', _$message);
  static FishingSpotDetailDto _$data(FishingSpotDetailResponseDto v) => v.data;
  static const Field<FishingSpotDetailResponseDto, FishingSpotDetailDto>
      _f$data = Field('data', _$data);

  @override
  final MappableFields<FishingSpotDetailResponseDto> fields = const {
    #code: _f$code,
    #message: _f$message,
    #data: _f$data,
  };

  static FishingSpotDetailResponseDto _instantiate(DecodingData data) {
    return FishingSpotDetailResponseDto(
        code: data.dec(_f$code),
        message: data.dec(_f$message),
        data: data.dec(_f$data));
  }

  @override
  final Function instantiate = _instantiate;

  static FishingSpotDetailResponseDto fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<FishingSpotDetailResponseDto>(map);
  }

  static FishingSpotDetailResponseDto fromJson(String json) {
    return ensureInitialized().decodeJson<FishingSpotDetailResponseDto>(json);
  }
}

mixin FishingSpotDetailResponseDtoMappable {
  String toJson() {
    return FishingSpotDetailResponseDtoMapper.ensureInitialized()
        .encodeJson<FishingSpotDetailResponseDto>(
            this as FishingSpotDetailResponseDto);
  }

  Map<String, dynamic> toMap() {
    return FishingSpotDetailResponseDtoMapper.ensureInitialized()
        .encodeMap<FishingSpotDetailResponseDto>(
            this as FishingSpotDetailResponseDto);
  }

  FishingSpotDetailResponseDtoCopyWith<FishingSpotDetailResponseDto,
          FishingSpotDetailResponseDto, FishingSpotDetailResponseDto>
      get copyWith => _FishingSpotDetailResponseDtoCopyWithImpl<
              FishingSpotDetailResponseDto, FishingSpotDetailResponseDto>(
          this as FishingSpotDetailResponseDto, $identity, $identity);
  @override
  String toString() {
    return FishingSpotDetailResponseDtoMapper.ensureInitialized()
        .stringifyValue(this as FishingSpotDetailResponseDto);
  }

  @override
  bool operator ==(Object other) {
    return FishingSpotDetailResponseDtoMapper.ensureInitialized()
        .equalsValue(this as FishingSpotDetailResponseDto, other);
  }

  @override
  int get hashCode {
    return FishingSpotDetailResponseDtoMapper.ensureInitialized()
        .hashValue(this as FishingSpotDetailResponseDto);
  }
}

extension FishingSpotDetailResponseDtoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, FishingSpotDetailResponseDto, $Out> {
  FishingSpotDetailResponseDtoCopyWith<$R, FishingSpotDetailResponseDto, $Out>
      get $asFishingSpotDetailResponseDto => $base.as((v, t, t2) =>
          _FishingSpotDetailResponseDtoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class FishingSpotDetailResponseDtoCopyWith<
    $R,
    $In extends FishingSpotDetailResponseDto,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  FishingSpotDetailDtoCopyWith<$R, FishingSpotDetailDto, FishingSpotDetailDto>
      get data;
  $R call({int? code, String? message, FishingSpotDetailDto? data});
  FishingSpotDetailResponseDtoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _FishingSpotDetailResponseDtoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, FishingSpotDetailResponseDto, $Out>
    implements
        FishingSpotDetailResponseDtoCopyWith<$R, FishingSpotDetailResponseDto,
            $Out> {
  _FishingSpotDetailResponseDtoCopyWithImpl(
      super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<FishingSpotDetailResponseDto> $mapper =
      FishingSpotDetailResponseDtoMapper.ensureInitialized();
  @override
  FishingSpotDetailDtoCopyWith<$R, FishingSpotDetailDto, FishingSpotDetailDto>
      get data => $value.data.copyWith.$chain((v) => call(data: v));
  @override
  $R call({int? code, String? message, FishingSpotDetailDto? data}) =>
      $apply(FieldCopyWithData({
        if (code != null) #code: code,
        if (message != null) #message: message,
        if (data != null) #data: data
      }));
  @override
  FishingSpotDetailResponseDto $make(CopyWithData data) =>
      FishingSpotDetailResponseDto(
          code: data.get(#code, or: $value.code),
          message: data.get(#message, or: $value.message),
          data: data.get(#data, or: $value.data));

  @override
  FishingSpotDetailResponseDtoCopyWith<$R2, FishingSpotDetailResponseDto, $Out2>
      $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
          _FishingSpotDetailResponseDtoCopyWithImpl<$R2, $Out2>(
              $value, $cast, t);
}
