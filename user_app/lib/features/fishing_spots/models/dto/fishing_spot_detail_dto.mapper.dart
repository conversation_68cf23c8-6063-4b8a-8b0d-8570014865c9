// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'fishing_spot_detail_dto.dart';

class FishingSpotDetailDtoMapper extends ClassMapperBase<FishingSpotDetailDto> {
  FishingSpotDetailDtoMapper._();

  static FishingSpotDetailDtoMapper? _instance;
  static FishingSpotDetailDtoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = FishingSpotDetailDtoMapper._());
      FishTypeDtoMapper.ensureInitialized();
      SpotPriceDtoFromApiMapper.ensureInitialized();
      SpotFacilityDtoFromApiMapper.ensureInitialized();
      UserMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'FishingSpotDetailDto';

  static int _$id(FishingSpotDetailDto v) => v.id;
  static const Field<FishingSpotDetailDto, int> _f$id = Field('id', _$id);
  static String _$name(FishingSpotDetailDto v) => v.name;
  static const Field<FishingSpotDetailDto, String> _f$name =
      Field('name', _$name);
  static String _$address(FishingSpotDetailDto v) => v.address;
  static const Field<FishingSpotDetailDto, String> _f$address =
      Field('address', _$address);
  static double _$latitude(FishingSpotDetailDto v) => v.latitude;
  static const Field<FishingSpotDetailDto, double> _f$latitude =
      Field('latitude', _$latitude);
  static double _$longitude(FishingSpotDetailDto v) => v.longitude;
  static const Field<FishingSpotDetailDto, double> _f$longitude =
      Field('longitude', _$longitude);
  static String _$province(FishingSpotDetailDto v) => v.province;
  static const Field<FishingSpotDetailDto, String> _f$province =
      Field('province', _$province);
  static String _$city(FishingSpotDetailDto v) => v.city;
  static const Field<FishingSpotDetailDto, String> _f$city =
      Field('city', _$city);
  static String _$county(FishingSpotDetailDto v) => v.county;
  static const Field<FishingSpotDetailDto, String> _f$county =
      Field('county', _$county);
  static bool _$isOfficial(FishingSpotDetailDto v) => v.isOfficial;
  static const Field<FishingSpotDetailDto, bool> _f$isOfficial =
      Field('isOfficial', _$isOfficial, key: r'is_official');
  static int _$verificationLevel(FishingSpotDetailDto v) => v.verificationLevel;
  static const Field<FishingSpotDetailDto, int> _f$verificationLevel = Field(
      'verificationLevel', _$verificationLevel,
      key: r'verification_level');
  static String? _$description(FishingSpotDetailDto v) => v.description;
  static const Field<FishingSpotDetailDto, String> _f$description =
      Field('description', _$description, opt: true);
  static int _$visitorCount(FishingSpotDetailDto v) => v.visitorCount;
  static const Field<FishingSpotDetailDto, int> _f$visitorCount =
      Field('visitorCount', _$visitorCount, key: r'visitor_count');
  static double _$rating(FishingSpotDetailDto v) => v.rating;
  static const Field<FishingSpotDetailDto, double> _f$rating =
      Field('rating', _$rating);
  static bool _$hasFacilities(FishingSpotDetailDto v) => v.hasFacilities;
  static const Field<FishingSpotDetailDto, bool> _f$hasFacilities =
      Field('hasFacilities', _$hasFacilities, key: r'has_facilities');
  static bool _$isPaid(FishingSpotDetailDto v) => v.isPaid;
  static const Field<FishingSpotDetailDto, bool> _f$isPaid =
      Field('isPaid', _$isPaid, key: r'is_paid');
  static int _$checkinCount(FishingSpotDetailDto v) => v.checkinCount;
  static const Field<FishingSpotDetailDto, int> _f$checkinCount =
      Field('checkinCount', _$checkinCount, key: r'checkin_count');
  static String? _$extraFishTypes(FishingSpotDetailDto v) => v.extraFishTypes;
  static const Field<FishingSpotDetailDto, String> _f$extraFishTypes = Field(
      'extraFishTypes', _$extraFishTypes,
      key: r'extra_fish_types', opt: true);
  static String? _$extraFacilities(FishingSpotDetailDto v) => v.extraFacilities;
  static const Field<FishingSpotDetailDto, String> _f$extraFacilities = Field(
      'extraFacilities', _$extraFacilities,
      key: r'extra_facilities', opt: true);
  static int? _$createdBy(FishingSpotDetailDto v) => v.createdBy;
  static const Field<FishingSpotDetailDto, int> _f$createdBy =
      Field('createdBy', _$createdBy, key: r'created_by', opt: true);
  static String? _$createdAt(FishingSpotDetailDto v) => v.createdAt;
  static const Field<FishingSpotDetailDto, String> _f$createdAt =
      Field('createdAt', _$createdAt, key: r'created_at', opt: true);
  static String? _$updatedAt(FishingSpotDetailDto v) => v.updatedAt;
  static const Field<FishingSpotDetailDto, String> _f$updatedAt =
      Field('updatedAt', _$updatedAt, key: r'updated_at', opt: true);
  static int? _$status(FishingSpotDetailDto v) => v.status;
  static const Field<FishingSpotDetailDto, int> _f$status =
      Field('status', _$status, opt: true);
  static String? _$source(FishingSpotDetailDto v) => v.source;
  static const Field<FishingSpotDetailDto, String> _f$source =
      Field('source', _$source, opt: true);
  static String? _$visibility(FishingSpotDetailDto v) => v.visibility;
  static const Field<FishingSpotDetailDto, String> _f$visibility =
      Field('visibility', _$visibility, opt: true);
  static List<FishTypeDto> _$fishTypeList(FishingSpotDetailDto v) =>
      v.fishTypeList;
  static const Field<FishingSpotDetailDto, List<FishTypeDto>> _f$fishTypeList =
      Field('fishTypeList', _$fishTypeList,
          key: r'fish_type_list', opt: true, def: const []);
  static List<String> _$images(FishingSpotDetailDto v) => v.images;
  static const Field<FishingSpotDetailDto, List<String>> _f$images =
      Field('images', _$images, opt: true, def: const []);
  static List<String> _$certificationDocuments(FishingSpotDetailDto v) =>
      v.certificationDocuments;
  static const Field<FishingSpotDetailDto, List<String>>
      _f$certificationDocuments = Field(
          'certificationDocuments', _$certificationDocuments,
          key: r'certification_documents', opt: true, def: const []);
  static List<SpotPriceDtoFromApi> _$prices(FishingSpotDetailDto v) => v.prices;
  static const Field<FishingSpotDetailDto, List<SpotPriceDtoFromApi>>
      _f$prices = Field('prices', _$prices, opt: true, def: const []);
  static List<SpotFacilityDtoFromApi> _$facilities(FishingSpotDetailDto v) =>
      v.facilities;
  static const Field<FishingSpotDetailDto, List<SpotFacilityDtoFromApi>>
      _f$facilities =
      Field('facilities', _$facilities, opt: true, def: const []);
  static double? _$distanceKm(FishingSpotDetailDto v) => v.distanceKm;
  static const Field<FishingSpotDetailDto, double> _f$distanceKm =
      Field('distanceKm', _$distanceKm, key: r'distance_km', opt: true);
  static int? _$recentMomentsCount(FishingSpotDetailDto v) =>
      v.recentMomentsCount;
  static const Field<FishingSpotDetailDto, int> _f$recentMomentsCount = Field(
      'recentMomentsCount', _$recentMomentsCount,
      key: r'recent_moments_count', opt: true);
  static List<String> _$extraFishTypesList(FishingSpotDetailDto v) =>
      v.extraFishTypesList;
  static const Field<FishingSpotDetailDto, List<String>> _f$extraFishTypesList =
      Field('extraFishTypesList', _$extraFishTypesList,
          key: r'extra_fish_types_list', opt: true, def: const []);
  static List<String> _$extraFacilitiesList(FishingSpotDetailDto v) =>
      v.extraFacilitiesList;
  static const Field<FishingSpotDetailDto, List<String>>
      _f$extraFacilitiesList = Field(
          'extraFacilitiesList', _$extraFacilitiesList,
          key: r'extra_facilities_list', opt: true, def: const []);
  static User? _$creator(FishingSpotDetailDto v) => v.creator;
  static const Field<FishingSpotDetailDto, User> _f$creator =
      Field('creator', _$creator, opt: true);

  @override
  final MappableFields<FishingSpotDetailDto> fields = const {
    #id: _f$id,
    #name: _f$name,
    #address: _f$address,
    #latitude: _f$latitude,
    #longitude: _f$longitude,
    #province: _f$province,
    #city: _f$city,
    #county: _f$county,
    #isOfficial: _f$isOfficial,
    #verificationLevel: _f$verificationLevel,
    #description: _f$description,
    #visitorCount: _f$visitorCount,
    #rating: _f$rating,
    #hasFacilities: _f$hasFacilities,
    #isPaid: _f$isPaid,
    #checkinCount: _f$checkinCount,
    #extraFishTypes: _f$extraFishTypes,
    #extraFacilities: _f$extraFacilities,
    #createdBy: _f$createdBy,
    #createdAt: _f$createdAt,
    #updatedAt: _f$updatedAt,
    #status: _f$status,
    #source: _f$source,
    #visibility: _f$visibility,
    #fishTypeList: _f$fishTypeList,
    #images: _f$images,
    #certificationDocuments: _f$certificationDocuments,
    #prices: _f$prices,
    #facilities: _f$facilities,
    #distanceKm: _f$distanceKm,
    #recentMomentsCount: _f$recentMomentsCount,
    #extraFishTypesList: _f$extraFishTypesList,
    #extraFacilitiesList: _f$extraFacilitiesList,
    #creator: _f$creator,
  };

  static FishingSpotDetailDto _instantiate(DecodingData data) {
    return FishingSpotDetailDto(
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        address: data.dec(_f$address),
        latitude: data.dec(_f$latitude),
        longitude: data.dec(_f$longitude),
        province: data.dec(_f$province),
        city: data.dec(_f$city),
        county: data.dec(_f$county),
        isOfficial: data.dec(_f$isOfficial),
        verificationLevel: data.dec(_f$verificationLevel),
        description: data.dec(_f$description),
        visitorCount: data.dec(_f$visitorCount),
        rating: data.dec(_f$rating),
        hasFacilities: data.dec(_f$hasFacilities),
        isPaid: data.dec(_f$isPaid),
        checkinCount: data.dec(_f$checkinCount),
        extraFishTypes: data.dec(_f$extraFishTypes),
        extraFacilities: data.dec(_f$extraFacilities),
        createdBy: data.dec(_f$createdBy),
        createdAt: data.dec(_f$createdAt),
        updatedAt: data.dec(_f$updatedAt),
        status: data.dec(_f$status),
        source: data.dec(_f$source),
        visibility: data.dec(_f$visibility),
        fishTypeList: data.dec(_f$fishTypeList),
        images: data.dec(_f$images),
        certificationDocuments: data.dec(_f$certificationDocuments),
        prices: data.dec(_f$prices),
        facilities: data.dec(_f$facilities),
        distanceKm: data.dec(_f$distanceKm),
        recentMomentsCount: data.dec(_f$recentMomentsCount),
        extraFishTypesList: data.dec(_f$extraFishTypesList),
        extraFacilitiesList: data.dec(_f$extraFacilitiesList),
        creator: data.dec(_f$creator));
  }

  @override
  final Function instantiate = _instantiate;

  static FishingSpotDetailDto fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<FishingSpotDetailDto>(map);
  }

  static FishingSpotDetailDto fromJson(String json) {
    return ensureInitialized().decodeJson<FishingSpotDetailDto>(json);
  }
}

mixin FishingSpotDetailDtoMappable {
  String toJson() {
    return FishingSpotDetailDtoMapper.ensureInitialized()
        .encodeJson<FishingSpotDetailDto>(this as FishingSpotDetailDto);
  }

  Map<String, dynamic> toMap() {
    return FishingSpotDetailDtoMapper.ensureInitialized()
        .encodeMap<FishingSpotDetailDto>(this as FishingSpotDetailDto);
  }

  FishingSpotDetailDtoCopyWith<FishingSpotDetailDto, FishingSpotDetailDto,
      FishingSpotDetailDto> get copyWith => _FishingSpotDetailDtoCopyWithImpl<
          FishingSpotDetailDto, FishingSpotDetailDto>(
      this as FishingSpotDetailDto, $identity, $identity);
  @override
  String toString() {
    return FishingSpotDetailDtoMapper.ensureInitialized()
        .stringifyValue(this as FishingSpotDetailDto);
  }

  @override
  bool operator ==(Object other) {
    return FishingSpotDetailDtoMapper.ensureInitialized()
        .equalsValue(this as FishingSpotDetailDto, other);
  }

  @override
  int get hashCode {
    return FishingSpotDetailDtoMapper.ensureInitialized()
        .hashValue(this as FishingSpotDetailDto);
  }
}

extension FishingSpotDetailDtoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, FishingSpotDetailDto, $Out> {
  FishingSpotDetailDtoCopyWith<$R, FishingSpotDetailDto, $Out>
      get $asFishingSpotDetailDto => $base.as(
          (v, t, t2) => _FishingSpotDetailDtoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class FishingSpotDetailDtoCopyWith<
    $R,
    $In extends FishingSpotDetailDto,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, FishTypeDto,
      FishTypeDtoCopyWith<$R, FishTypeDto, FishTypeDto>> get fishTypeList;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get images;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get certificationDocuments;
  ListCopyWith<
      $R,
      SpotPriceDtoFromApi,
      SpotPriceDtoFromApiCopyWith<$R, SpotPriceDtoFromApi,
          SpotPriceDtoFromApi>> get prices;
  ListCopyWith<
      $R,
      SpotFacilityDtoFromApi,
      SpotFacilityDtoFromApiCopyWith<$R, SpotFacilityDtoFromApi,
          SpotFacilityDtoFromApi>> get facilities;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get extraFishTypesList;
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get extraFacilitiesList;
  UserCopyWith<$R, User, User>? get creator;
  $R call(
      {int? id,
      String? name,
      String? address,
      double? latitude,
      double? longitude,
      String? province,
      String? city,
      String? county,
      bool? isOfficial,
      int? verificationLevel,
      String? description,
      int? visitorCount,
      double? rating,
      bool? hasFacilities,
      bool? isPaid,
      int? checkinCount,
      String? extraFishTypes,
      String? extraFacilities,
      int? createdBy,
      String? createdAt,
      String? updatedAt,
      int? status,
      String? source,
      String? visibility,
      List<FishTypeDto>? fishTypeList,
      List<String>? images,
      List<String>? certificationDocuments,
      List<SpotPriceDtoFromApi>? prices,
      List<SpotFacilityDtoFromApi>? facilities,
      double? distanceKm,
      int? recentMomentsCount,
      List<String>? extraFishTypesList,
      List<String>? extraFacilitiesList,
      User? creator});
  FishingSpotDetailDtoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _FishingSpotDetailDtoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, FishingSpotDetailDto, $Out>
    implements FishingSpotDetailDtoCopyWith<$R, FishingSpotDetailDto, $Out> {
  _FishingSpotDetailDtoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<FishingSpotDetailDto> $mapper =
      FishingSpotDetailDtoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, FishTypeDto,
          FishTypeDtoCopyWith<$R, FishTypeDto, FishTypeDto>>
      get fishTypeList => ListCopyWith($value.fishTypeList,
          (v, t) => v.copyWith.$chain(t), (v) => call(fishTypeList: v));
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get images =>
      ListCopyWith($value.images, (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(images: v));
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get certificationDocuments => ListCopyWith(
          $value.certificationDocuments,
          (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(certificationDocuments: v));
  @override
  ListCopyWith<
      $R,
      SpotPriceDtoFromApi,
      SpotPriceDtoFromApiCopyWith<$R, SpotPriceDtoFromApi,
          SpotPriceDtoFromApi>> get prices => ListCopyWith(
      $value.prices, (v, t) => v.copyWith.$chain(t), (v) => call(prices: v));
  @override
  ListCopyWith<
      $R,
      SpotFacilityDtoFromApi,
      SpotFacilityDtoFromApiCopyWith<$R, SpotFacilityDtoFromApi,
          SpotFacilityDtoFromApi>> get facilities => ListCopyWith(
      $value.facilities,
      (v, t) => v.copyWith.$chain(t),
      (v) => call(facilities: v));
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get extraFishTypesList => ListCopyWith(
          $value.extraFishTypesList,
          (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(extraFishTypesList: v));
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>>
      get extraFacilitiesList => ListCopyWith(
          $value.extraFacilitiesList,
          (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(extraFacilitiesList: v));
  @override
  UserCopyWith<$R, User, User>? get creator =>
      $value.creator?.copyWith.$chain((v) => call(creator: v));
  @override
  $R call(
          {int? id,
          String? name,
          String? address,
          double? latitude,
          double? longitude,
          String? province,
          String? city,
          String? county,
          bool? isOfficial,
          int? verificationLevel,
          Object? description = $none,
          int? visitorCount,
          double? rating,
          bool? hasFacilities,
          bool? isPaid,
          int? checkinCount,
          Object? extraFishTypes = $none,
          Object? extraFacilities = $none,
          Object? createdBy = $none,
          Object? createdAt = $none,
          Object? updatedAt = $none,
          Object? status = $none,
          Object? source = $none,
          Object? visibility = $none,
          List<FishTypeDto>? fishTypeList,
          List<String>? images,
          List<String>? certificationDocuments,
          List<SpotPriceDtoFromApi>? prices,
          List<SpotFacilityDtoFromApi>? facilities,
          Object? distanceKm = $none,
          Object? recentMomentsCount = $none,
          List<String>? extraFishTypesList,
          List<String>? extraFacilitiesList,
          Object? creator = $none}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (address != null) #address: address,
        if (latitude != null) #latitude: latitude,
        if (longitude != null) #longitude: longitude,
        if (province != null) #province: province,
        if (city != null) #city: city,
        if (county != null) #county: county,
        if (isOfficial != null) #isOfficial: isOfficial,
        if (verificationLevel != null) #verificationLevel: verificationLevel,
        if (description != $none) #description: description,
        if (visitorCount != null) #visitorCount: visitorCount,
        if (rating != null) #rating: rating,
        if (hasFacilities != null) #hasFacilities: hasFacilities,
        if (isPaid != null) #isPaid: isPaid,
        if (checkinCount != null) #checkinCount: checkinCount,
        if (extraFishTypes != $none) #extraFishTypes: extraFishTypes,
        if (extraFacilities != $none) #extraFacilities: extraFacilities,
        if (createdBy != $none) #createdBy: createdBy,
        if (createdAt != $none) #createdAt: createdAt,
        if (updatedAt != $none) #updatedAt: updatedAt,
        if (status != $none) #status: status,
        if (source != $none) #source: source,
        if (visibility != $none) #visibility: visibility,
        if (fishTypeList != null) #fishTypeList: fishTypeList,
        if (images != null) #images: images,
        if (certificationDocuments != null)
          #certificationDocuments: certificationDocuments,
        if (prices != null) #prices: prices,
        if (facilities != null) #facilities: facilities,
        if (distanceKm != $none) #distanceKm: distanceKm,
        if (recentMomentsCount != $none)
          #recentMomentsCount: recentMomentsCount,
        if (extraFishTypesList != null) #extraFishTypesList: extraFishTypesList,
        if (extraFacilitiesList != null)
          #extraFacilitiesList: extraFacilitiesList,
        if (creator != $none) #creator: creator
      }));
  @override
  FishingSpotDetailDto $make(CopyWithData data) => FishingSpotDetailDto(
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      address: data.get(#address, or: $value.address),
      latitude: data.get(#latitude, or: $value.latitude),
      longitude: data.get(#longitude, or: $value.longitude),
      province: data.get(#province, or: $value.province),
      city: data.get(#city, or: $value.city),
      county: data.get(#county, or: $value.county),
      isOfficial: data.get(#isOfficial, or: $value.isOfficial),
      verificationLevel:
          data.get(#verificationLevel, or: $value.verificationLevel),
      description: data.get(#description, or: $value.description),
      visitorCount: data.get(#visitorCount, or: $value.visitorCount),
      rating: data.get(#rating, or: $value.rating),
      hasFacilities: data.get(#hasFacilities, or: $value.hasFacilities),
      isPaid: data.get(#isPaid, or: $value.isPaid),
      checkinCount: data.get(#checkinCount, or: $value.checkinCount),
      extraFishTypes: data.get(#extraFishTypes, or: $value.extraFishTypes),
      extraFacilities: data.get(#extraFacilities, or: $value.extraFacilities),
      createdBy: data.get(#createdBy, or: $value.createdBy),
      createdAt: data.get(#createdAt, or: $value.createdAt),
      updatedAt: data.get(#updatedAt, or: $value.updatedAt),
      status: data.get(#status, or: $value.status),
      source: data.get(#source, or: $value.source),
      visibility: data.get(#visibility, or: $value.visibility),
      fishTypeList: data.get(#fishTypeList, or: $value.fishTypeList),
      images: data.get(#images, or: $value.images),
      certificationDocuments:
          data.get(#certificationDocuments, or: $value.certificationDocuments),
      prices: data.get(#prices, or: $value.prices),
      facilities: data.get(#facilities, or: $value.facilities),
      distanceKm: data.get(#distanceKm, or: $value.distanceKm),
      recentMomentsCount:
          data.get(#recentMomentsCount, or: $value.recentMomentsCount),
      extraFishTypesList:
          data.get(#extraFishTypesList, or: $value.extraFishTypesList),
      extraFacilitiesList:
          data.get(#extraFacilitiesList, or: $value.extraFacilitiesList),
      creator: data.get(#creator, or: $value.creator));

  @override
  FishingSpotDetailDtoCopyWith<$R2, FishingSpotDetailDto, $Out2>
      $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
          _FishingSpotDetailDtoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class FishTypeDtoMapper extends ClassMapperBase<FishTypeDto> {
  FishTypeDtoMapper._();

  static FishTypeDtoMapper? _instance;
  static FishTypeDtoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = FishTypeDtoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'FishTypeDto';

  static dynamic _$spots(FishTypeDto v) => v.spots;
  static const Field<FishTypeDto, dynamic> _f$spots =
      Field('spots', _$spots, opt: true);
  static dynamic _$season(FishTypeDto v) => v.season;
  static const Field<FishTypeDto, dynamic> _f$season =
      Field('season', _$season, opt: true);
  static int _$id(FishTypeDto v) => v.id;
  static const Field<FishTypeDto, int> _f$id = Field('id', _$id);
  static String _$name(FishTypeDto v) => v.name;
  static const Field<FishTypeDto, String> _f$name = Field('name', _$name);
  static String? _$description(FishTypeDto v) => v.description;
  static const Field<FishTypeDto, String> _f$description =
      Field('description', _$description, opt: true);
  static String? _$imageUrl(FishTypeDto v) => v.imageUrl;
  static const Field<FishTypeDto, String> _f$imageUrl =
      Field('imageUrl', _$imageUrl, key: r'image_url', opt: true);
  static bool _$seasonSpring(FishTypeDto v) => v.seasonSpring;
  static const Field<FishTypeDto, bool> _f$seasonSpring =
      Field('seasonSpring', _$seasonSpring, key: r'season_spring');
  static bool _$seasonSummer(FishTypeDto v) => v.seasonSummer;
  static const Field<FishTypeDto, bool> _f$seasonSummer =
      Field('seasonSummer', _$seasonSummer, key: r'season_summer');
  static bool _$seasonAutumn(FishTypeDto v) => v.seasonAutumn;
  static const Field<FishTypeDto, bool> _f$seasonAutumn =
      Field('seasonAutumn', _$seasonAutumn, key: r'season_autumn');
  static bool _$seasonWinter(FishTypeDto v) => v.seasonWinter;
  static const Field<FishTypeDto, bool> _f$seasonWinter =
      Field('seasonWinter', _$seasonWinter, key: r'season_winter');

  @override
  final MappableFields<FishTypeDto> fields = const {
    #spots: _f$spots,
    #season: _f$season,
    #id: _f$id,
    #name: _f$name,
    #description: _f$description,
    #imageUrl: _f$imageUrl,
    #seasonSpring: _f$seasonSpring,
    #seasonSummer: _f$seasonSummer,
    #seasonAutumn: _f$seasonAutumn,
    #seasonWinter: _f$seasonWinter,
  };

  static FishTypeDto _instantiate(DecodingData data) {
    return FishTypeDto(
        spots: data.dec(_f$spots),
        season: data.dec(_f$season),
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        description: data.dec(_f$description),
        imageUrl: data.dec(_f$imageUrl),
        seasonSpring: data.dec(_f$seasonSpring),
        seasonSummer: data.dec(_f$seasonSummer),
        seasonAutumn: data.dec(_f$seasonAutumn),
        seasonWinter: data.dec(_f$seasonWinter));
  }

  @override
  final Function instantiate = _instantiate;

  static FishTypeDto fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<FishTypeDto>(map);
  }

  static FishTypeDto fromJson(String json) {
    return ensureInitialized().decodeJson<FishTypeDto>(json);
  }
}

mixin FishTypeDtoMappable {
  String toJson() {
    return FishTypeDtoMapper.ensureInitialized()
        .encodeJson<FishTypeDto>(this as FishTypeDto);
  }

  Map<String, dynamic> toMap() {
    return FishTypeDtoMapper.ensureInitialized()
        .encodeMap<FishTypeDto>(this as FishTypeDto);
  }

  FishTypeDtoCopyWith<FishTypeDto, FishTypeDto, FishTypeDto> get copyWith =>
      _FishTypeDtoCopyWithImpl<FishTypeDto, FishTypeDto>(
          this as FishTypeDto, $identity, $identity);
  @override
  String toString() {
    return FishTypeDtoMapper.ensureInitialized()
        .stringifyValue(this as FishTypeDto);
  }

  @override
  bool operator ==(Object other) {
    return FishTypeDtoMapper.ensureInitialized()
        .equalsValue(this as FishTypeDto, other);
  }

  @override
  int get hashCode {
    return FishTypeDtoMapper.ensureInitialized().hashValue(this as FishTypeDto);
  }
}

extension FishTypeDtoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, FishTypeDto, $Out> {
  FishTypeDtoCopyWith<$R, FishTypeDto, $Out> get $asFishTypeDto =>
      $base.as((v, t, t2) => _FishTypeDtoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class FishTypeDtoCopyWith<$R, $In extends FishTypeDto, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {dynamic spots,
      dynamic season,
      int? id,
      String? name,
      String? description,
      String? imageUrl,
      bool? seasonSpring,
      bool? seasonSummer,
      bool? seasonAutumn,
      bool? seasonWinter});
  FishTypeDtoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _FishTypeDtoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, FishTypeDto, $Out>
    implements FishTypeDtoCopyWith<$R, FishTypeDto, $Out> {
  _FishTypeDtoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<FishTypeDto> $mapper =
      FishTypeDtoMapper.ensureInitialized();
  @override
  $R call(
          {Object? spots = $none,
          Object? season = $none,
          int? id,
          String? name,
          Object? description = $none,
          Object? imageUrl = $none,
          bool? seasonSpring,
          bool? seasonSummer,
          bool? seasonAutumn,
          bool? seasonWinter}) =>
      $apply(FieldCopyWithData({
        if (spots != $none) #spots: spots,
        if (season != $none) #season: season,
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (description != $none) #description: description,
        if (imageUrl != $none) #imageUrl: imageUrl,
        if (seasonSpring != null) #seasonSpring: seasonSpring,
        if (seasonSummer != null) #seasonSummer: seasonSummer,
        if (seasonAutumn != null) #seasonAutumn: seasonAutumn,
        if (seasonWinter != null) #seasonWinter: seasonWinter
      }));
  @override
  FishTypeDto $make(CopyWithData data) => FishTypeDto(
      spots: data.get(#spots, or: $value.spots),
      season: data.get(#season, or: $value.season),
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      description: data.get(#description, or: $value.description),
      imageUrl: data.get(#imageUrl, or: $value.imageUrl),
      seasonSpring: data.get(#seasonSpring, or: $value.seasonSpring),
      seasonSummer: data.get(#seasonSummer, or: $value.seasonSummer),
      seasonAutumn: data.get(#seasonAutumn, or: $value.seasonAutumn),
      seasonWinter: data.get(#seasonWinter, or: $value.seasonWinter));

  @override
  FishTypeDtoCopyWith<$R2, FishTypeDto, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _FishTypeDtoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class SpotPriceDtoFromApiMapper extends ClassMapperBase<SpotPriceDtoFromApi> {
  SpotPriceDtoFromApiMapper._();

  static SpotPriceDtoFromApiMapper? _instance;
  static SpotPriceDtoFromApiMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotPriceDtoFromApiMapper._());
      FishTypeDtoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'SpotPriceDtoFromApi';

  static int _$id(SpotPriceDtoFromApi v) => v.id;
  static const Field<SpotPriceDtoFromApi, int> _f$id = Field('id', _$id);
  static int? _$fishTypeId(SpotPriceDtoFromApi v) => v.fishTypeId;
  static const Field<SpotPriceDtoFromApi, int> _f$fishTypeId =
      Field('fishTypeId', _$fishTypeId, key: r'fish_type_id', opt: true);
  static FishTypeDto? _$fishType(SpotPriceDtoFromApi v) => v.fishType;
  static const Field<SpotPriceDtoFromApi, FishTypeDto> _f$fishType =
      Field('fishType', _$fishType, key: r'fish_type', opt: true);
  static int _$priceType(SpotPriceDtoFromApi v) => v.priceType;
  static const Field<SpotPriceDtoFromApi, int> _f$priceType =
      Field('priceType', _$priceType, key: r'price_type');
  static String _$priceTypeName(SpotPriceDtoFromApi v) => v.priceTypeName;
  static const Field<SpotPriceDtoFromApi, String> _f$priceTypeName =
      Field('priceTypeName', _$priceTypeName, key: r'price_type_name');
  static double _$price(SpotPriceDtoFromApi v) => v.price;
  static const Field<SpotPriceDtoFromApi, double> _f$price =
      Field('price', _$price);
  static int? _$hours(SpotPriceDtoFromApi v) => v.hours;
  static const Field<SpotPriceDtoFromApi, int> _f$hours =
      Field('hours', _$hours, opt: true);
  static String? _$description(SpotPriceDtoFromApi v) => v.description;
  static const Field<SpotPriceDtoFromApi, String> _f$description =
      Field('description', _$description, opt: true);

  @override
  final MappableFields<SpotPriceDtoFromApi> fields = const {
    #id: _f$id,
    #fishTypeId: _f$fishTypeId,
    #fishType: _f$fishType,
    #priceType: _f$priceType,
    #priceTypeName: _f$priceTypeName,
    #price: _f$price,
    #hours: _f$hours,
    #description: _f$description,
  };

  static SpotPriceDtoFromApi _instantiate(DecodingData data) {
    return SpotPriceDtoFromApi(
        id: data.dec(_f$id),
        fishTypeId: data.dec(_f$fishTypeId),
        fishType: data.dec(_f$fishType),
        priceType: data.dec(_f$priceType),
        priceTypeName: data.dec(_f$priceTypeName),
        price: data.dec(_f$price),
        hours: data.dec(_f$hours),
        description: data.dec(_f$description));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotPriceDtoFromApi fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotPriceDtoFromApi>(map);
  }

  static SpotPriceDtoFromApi fromJson(String json) {
    return ensureInitialized().decodeJson<SpotPriceDtoFromApi>(json);
  }
}

mixin SpotPriceDtoFromApiMappable {
  String toJson() {
    return SpotPriceDtoFromApiMapper.ensureInitialized()
        .encodeJson<SpotPriceDtoFromApi>(this as SpotPriceDtoFromApi);
  }

  Map<String, dynamic> toMap() {
    return SpotPriceDtoFromApiMapper.ensureInitialized()
        .encodeMap<SpotPriceDtoFromApi>(this as SpotPriceDtoFromApi);
  }

  SpotPriceDtoFromApiCopyWith<SpotPriceDtoFromApi, SpotPriceDtoFromApi,
      SpotPriceDtoFromApi> get copyWith => _SpotPriceDtoFromApiCopyWithImpl<
          SpotPriceDtoFromApi, SpotPriceDtoFromApi>(
      this as SpotPriceDtoFromApi, $identity, $identity);
  @override
  String toString() {
    return SpotPriceDtoFromApiMapper.ensureInitialized()
        .stringifyValue(this as SpotPriceDtoFromApi);
  }

  @override
  bool operator ==(Object other) {
    return SpotPriceDtoFromApiMapper.ensureInitialized()
        .equalsValue(this as SpotPriceDtoFromApi, other);
  }

  @override
  int get hashCode {
    return SpotPriceDtoFromApiMapper.ensureInitialized()
        .hashValue(this as SpotPriceDtoFromApi);
  }
}

extension SpotPriceDtoFromApiValueCopy<$R, $Out>
    on ObjectCopyWith<$R, SpotPriceDtoFromApi, $Out> {
  SpotPriceDtoFromApiCopyWith<$R, SpotPriceDtoFromApi, $Out>
      get $asSpotPriceDtoFromApi => $base.as(
          (v, t, t2) => _SpotPriceDtoFromApiCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotPriceDtoFromApiCopyWith<$R, $In extends SpotPriceDtoFromApi,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  FishTypeDtoCopyWith<$R, FishTypeDto, FishTypeDto>? get fishType;
  $R call(
      {int? id,
      int? fishTypeId,
      FishTypeDto? fishType,
      int? priceType,
      String? priceTypeName,
      double? price,
      int? hours,
      String? description});
  SpotPriceDtoFromApiCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _SpotPriceDtoFromApiCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotPriceDtoFromApi, $Out>
    implements SpotPriceDtoFromApiCopyWith<$R, SpotPriceDtoFromApi, $Out> {
  _SpotPriceDtoFromApiCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotPriceDtoFromApi> $mapper =
      SpotPriceDtoFromApiMapper.ensureInitialized();
  @override
  FishTypeDtoCopyWith<$R, FishTypeDto, FishTypeDto>? get fishType =>
      $value.fishType?.copyWith.$chain((v) => call(fishType: v));
  @override
  $R call(
          {int? id,
          Object? fishTypeId = $none,
          Object? fishType = $none,
          int? priceType,
          String? priceTypeName,
          double? price,
          Object? hours = $none,
          Object? description = $none}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (fishTypeId != $none) #fishTypeId: fishTypeId,
        if (fishType != $none) #fishType: fishType,
        if (priceType != null) #priceType: priceType,
        if (priceTypeName != null) #priceTypeName: priceTypeName,
        if (price != null) #price: price,
        if (hours != $none) #hours: hours,
        if (description != $none) #description: description
      }));
  @override
  SpotPriceDtoFromApi $make(CopyWithData data) => SpotPriceDtoFromApi(
      id: data.get(#id, or: $value.id),
      fishTypeId: data.get(#fishTypeId, or: $value.fishTypeId),
      fishType: data.get(#fishType, or: $value.fishType),
      priceType: data.get(#priceType, or: $value.priceType),
      priceTypeName: data.get(#priceTypeName, or: $value.priceTypeName),
      price: data.get(#price, or: $value.price),
      hours: data.get(#hours, or: $value.hours),
      description: data.get(#description, or: $value.description));

  @override
  SpotPriceDtoFromApiCopyWith<$R2, SpotPriceDtoFromApi, $Out2>
      $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
          _SpotPriceDtoFromApiCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class SpotFacilityDtoFromApiMapper
    extends ClassMapperBase<SpotFacilityDtoFromApi> {
  SpotFacilityDtoFromApiMapper._();

  static SpotFacilityDtoFromApiMapper? _instance;
  static SpotFacilityDtoFromApiMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotFacilityDtoFromApiMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'SpotFacilityDtoFromApi';

  static int _$id(SpotFacilityDtoFromApi v) => v.id;
  static const Field<SpotFacilityDtoFromApi, int> _f$id = Field('id', _$id);
  static String _$name(SpotFacilityDtoFromApi v) => v.name;
  static const Field<SpotFacilityDtoFromApi, String> _f$name =
      Field('name', _$name);
  static String? _$icon(SpotFacilityDtoFromApi v) => v.icon;
  static const Field<SpotFacilityDtoFromApi, String> _f$icon =
      Field('icon', _$icon, opt: true);
  static String? _$description(SpotFacilityDtoFromApi v) => v.description;
  static const Field<SpotFacilityDtoFromApi, String> _f$description =
      Field('description', _$description, opt: true);
  static String? _$details(SpotFacilityDtoFromApi v) => v.details;
  static const Field<SpotFacilityDtoFromApi, String> _f$details =
      Field('details', _$details, opt: true);

  @override
  final MappableFields<SpotFacilityDtoFromApi> fields = const {
    #id: _f$id,
    #name: _f$name,
    #icon: _f$icon,
    #description: _f$description,
    #details: _f$details,
  };

  static SpotFacilityDtoFromApi _instantiate(DecodingData data) {
    return SpotFacilityDtoFromApi(
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        icon: data.dec(_f$icon),
        description: data.dec(_f$description),
        details: data.dec(_f$details));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotFacilityDtoFromApi fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotFacilityDtoFromApi>(map);
  }

  static SpotFacilityDtoFromApi fromJson(String json) {
    return ensureInitialized().decodeJson<SpotFacilityDtoFromApi>(json);
  }
}

mixin SpotFacilityDtoFromApiMappable {
  String toJson() {
    return SpotFacilityDtoFromApiMapper.ensureInitialized()
        .encodeJson<SpotFacilityDtoFromApi>(this as SpotFacilityDtoFromApi);
  }

  Map<String, dynamic> toMap() {
    return SpotFacilityDtoFromApiMapper.ensureInitialized()
        .encodeMap<SpotFacilityDtoFromApi>(this as SpotFacilityDtoFromApi);
  }

  SpotFacilityDtoFromApiCopyWith<SpotFacilityDtoFromApi, SpotFacilityDtoFromApi,
          SpotFacilityDtoFromApi>
      get copyWith => _SpotFacilityDtoFromApiCopyWithImpl<
              SpotFacilityDtoFromApi, SpotFacilityDtoFromApi>(
          this as SpotFacilityDtoFromApi, $identity, $identity);
  @override
  String toString() {
    return SpotFacilityDtoFromApiMapper.ensureInitialized()
        .stringifyValue(this as SpotFacilityDtoFromApi);
  }

  @override
  bool operator ==(Object other) {
    return SpotFacilityDtoFromApiMapper.ensureInitialized()
        .equalsValue(this as SpotFacilityDtoFromApi, other);
  }

  @override
  int get hashCode {
    return SpotFacilityDtoFromApiMapper.ensureInitialized()
        .hashValue(this as SpotFacilityDtoFromApi);
  }
}

extension SpotFacilityDtoFromApiValueCopy<$R, $Out>
    on ObjectCopyWith<$R, SpotFacilityDtoFromApi, $Out> {
  SpotFacilityDtoFromApiCopyWith<$R, SpotFacilityDtoFromApi, $Out>
      get $asSpotFacilityDtoFromApi => $base.as((v, t, t2) =>
          _SpotFacilityDtoFromApiCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotFacilityDtoFromApiCopyWith<
    $R,
    $In extends SpotFacilityDtoFromApi,
    $Out> implements ClassCopyWith<$R, $In, $Out> {
  $R call(
      {int? id,
      String? name,
      String? icon,
      String? description,
      String? details});
  SpotFacilityDtoFromApiCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(
      Then<$Out2, $R2> t);
}

class _SpotFacilityDtoFromApiCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotFacilityDtoFromApi, $Out>
    implements
        SpotFacilityDtoFromApiCopyWith<$R, SpotFacilityDtoFromApi, $Out> {
  _SpotFacilityDtoFromApiCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotFacilityDtoFromApi> $mapper =
      SpotFacilityDtoFromApiMapper.ensureInitialized();
  @override
  $R call(
          {int? id,
          String? name,
          Object? icon = $none,
          Object? description = $none,
          Object? details = $none}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (icon != $none) #icon: icon,
        if (description != $none) #description: description,
        if (details != $none) #details: details
      }));
  @override
  SpotFacilityDtoFromApi $make(CopyWithData data) => SpotFacilityDtoFromApi(
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      icon: data.get(#icon, or: $value.icon),
      description: data.get(#description, or: $value.description),
      details: data.get(#details, or: $value.details));

  @override
  SpotFacilityDtoFromApiCopyWith<$R2, SpotFacilityDtoFromApi, $Out2>
      $chain<$R2, $Out2>(Then<$Out2, $R2> t) =>
          _SpotFacilityDtoFromApiCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
