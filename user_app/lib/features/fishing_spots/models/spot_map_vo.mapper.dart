// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'spot_map_vo.dart';

class SpotMapVoMapper extends ClassMapperBase<SpotMapVo> {
  SpotMapVoMapper._();

  static SpotMapVoMapper? _instance;
  static SpotMapVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotMapVoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'SpotMapVo';

  static int _$id(SpotMapVo v) => v.id;
  static const Field<SpotMapVo, int> _f$id = Field('id', _$id);
  static String _$name(SpotMapVo v) => v.name;
  static const Field<SpotMapVo, String> _f$name = Field('name', _$name);
  static String _$address(SpotMapVo v) => v.address;
  static const Field<SpotMapVo, String> _f$address =
      Field('address', _$address);
  static double _$latitude(SpotMapVo v) => v.latitude;
  static const Field<SpotMapVo, double> _f$latitude =
      Field('latitude', _$latitude);
  static double _$longitude(SpotMapVo v) => v.longitude;
  static const Field<SpotMapVo, double> _f$longitude =
      Field('longitude', _$longitude);
  static String _$type(SpotMapVo v) => v.type;
  static const Field<SpotMapVo, String> _f$type = Field('type', _$type);
  static double? _$rating(SpotMapVo v) => v.rating;
  static const Field<SpotMapVo, double> _f$rating =
      Field('rating', _$rating, opt: true);
  static int _$reviewCount(SpotMapVo v) => v.reviewCount;
  static const Field<SpotMapVo, int> _f$reviewCount =
      Field('reviewCount', _$reviewCount, key: r'review_count');
  static bool _$isPaid(SpotMapVo v) => v.isPaid;
  static const Field<SpotMapVo, bool> _f$isPaid =
      Field('isPaid', _$isPaid, key: r'is_paid');
  static bool _$hasFacilities(SpotMapVo v) => v.hasFacilities;
  static const Field<SpotMapVo, bool> _f$hasFacilities =
      Field('hasFacilities', _$hasFacilities, key: r'has_facilities');
  static List<String> _$fishTypes(SpotMapVo v) => v.fishTypes;
  static const Field<SpotMapVo, List<String>> _f$fishTypes =
      Field('fishTypes', _$fishTypes, key: r'fish_types');
  static String? _$imageUrl(SpotMapVo v) => v.imageUrl;
  static const Field<SpotMapVo, String> _f$imageUrl =
      Field('imageUrl', _$imageUrl, key: r'image_url', opt: true);
  static double? _$distance(SpotMapVo v) => v.distance;
  static const Field<SpotMapVo, double> _f$distance =
      Field('distance', _$distance, opt: true);
  static int _$checkinCount(SpotMapVo v) => v.checkinCount;
  static const Field<SpotMapVo, int> _f$checkinCount = Field(
      'checkinCount', _$checkinCount,
      key: r'checkin_count', opt: true, def: 0);

  @override
  final MappableFields<SpotMapVo> fields = const {
    #id: _f$id,
    #name: _f$name,
    #address: _f$address,
    #latitude: _f$latitude,
    #longitude: _f$longitude,
    #type: _f$type,
    #rating: _f$rating,
    #reviewCount: _f$reviewCount,
    #isPaid: _f$isPaid,
    #hasFacilities: _f$hasFacilities,
    #fishTypes: _f$fishTypes,
    #imageUrl: _f$imageUrl,
    #distance: _f$distance,
    #checkinCount: _f$checkinCount,
  };

  static SpotMapVo _instantiate(DecodingData data) {
    return SpotMapVo(
        id: data.dec(_f$id),
        name: data.dec(_f$name),
        address: data.dec(_f$address),
        latitude: data.dec(_f$latitude),
        longitude: data.dec(_f$longitude),
        type: data.dec(_f$type),
        rating: data.dec(_f$rating),
        reviewCount: data.dec(_f$reviewCount),
        isPaid: data.dec(_f$isPaid),
        hasFacilities: data.dec(_f$hasFacilities),
        fishTypes: data.dec(_f$fishTypes),
        imageUrl: data.dec(_f$imageUrl),
        distance: data.dec(_f$distance),
        checkinCount: data.dec(_f$checkinCount));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotMapVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotMapVo>(map);
  }

  static SpotMapVo fromJson(String json) {
    return ensureInitialized().decodeJson<SpotMapVo>(json);
  }
}

mixin SpotMapVoMappable {
  String toJson() {
    return SpotMapVoMapper.ensureInitialized()
        .encodeJson<SpotMapVo>(this as SpotMapVo);
  }

  Map<String, dynamic> toMap() {
    return SpotMapVoMapper.ensureInitialized()
        .encodeMap<SpotMapVo>(this as SpotMapVo);
  }

  SpotMapVoCopyWith<SpotMapVo, SpotMapVo, SpotMapVo> get copyWith =>
      _SpotMapVoCopyWithImpl<SpotMapVo, SpotMapVo>(
          this as SpotMapVo, $identity, $identity);
  @override
  String toString() {
    return SpotMapVoMapper.ensureInitialized()
        .stringifyValue(this as SpotMapVo);
  }

  @override
  bool operator ==(Object other) {
    return SpotMapVoMapper.ensureInitialized()
        .equalsValue(this as SpotMapVo, other);
  }

  @override
  int get hashCode {
    return SpotMapVoMapper.ensureInitialized().hashValue(this as SpotMapVo);
  }
}

extension SpotMapVoValueCopy<$R, $Out> on ObjectCopyWith<$R, SpotMapVo, $Out> {
  SpotMapVoCopyWith<$R, SpotMapVo, $Out> get $asSpotMapVo =>
      $base.as((v, t, t2) => _SpotMapVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotMapVoCopyWith<$R, $In extends SpotMapVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get fishTypes;
  $R call(
      {int? id,
      String? name,
      String? address,
      double? latitude,
      double? longitude,
      String? type,
      double? rating,
      int? reviewCount,
      bool? isPaid,
      bool? hasFacilities,
      List<String>? fishTypes,
      String? imageUrl,
      double? distance,
      int? checkinCount});
  SpotMapVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _SpotMapVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotMapVo, $Out>
    implements SpotMapVoCopyWith<$R, SpotMapVo, $Out> {
  _SpotMapVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotMapVo> $mapper =
      SpotMapVoMapper.ensureInitialized();
  @override
  ListCopyWith<$R, String, ObjectCopyWith<$R, String, String>> get fishTypes =>
      ListCopyWith($value.fishTypes, (v, t) => ObjectCopyWith(v, $identity, t),
          (v) => call(fishTypes: v));
  @override
  $R call(
          {int? id,
          String? name,
          String? address,
          double? latitude,
          double? longitude,
          String? type,
          Object? rating = $none,
          int? reviewCount,
          bool? isPaid,
          bool? hasFacilities,
          List<String>? fishTypes,
          Object? imageUrl = $none,
          Object? distance = $none,
          int? checkinCount}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (name != null) #name: name,
        if (address != null) #address: address,
        if (latitude != null) #latitude: latitude,
        if (longitude != null) #longitude: longitude,
        if (type != null) #type: type,
        if (rating != $none) #rating: rating,
        if (reviewCount != null) #reviewCount: reviewCount,
        if (isPaid != null) #isPaid: isPaid,
        if (hasFacilities != null) #hasFacilities: hasFacilities,
        if (fishTypes != null) #fishTypes: fishTypes,
        if (imageUrl != $none) #imageUrl: imageUrl,
        if (distance != $none) #distance: distance,
        if (checkinCount != null) #checkinCount: checkinCount
      }));
  @override
  SpotMapVo $make(CopyWithData data) => SpotMapVo(
      id: data.get(#id, or: $value.id),
      name: data.get(#name, or: $value.name),
      address: data.get(#address, or: $value.address),
      latitude: data.get(#latitude, or: $value.latitude),
      longitude: data.get(#longitude, or: $value.longitude),
      type: data.get(#type, or: $value.type),
      rating: data.get(#rating, or: $value.rating),
      reviewCount: data.get(#reviewCount, or: $value.reviewCount),
      isPaid: data.get(#isPaid, or: $value.isPaid),
      hasFacilities: data.get(#hasFacilities, or: $value.hasFacilities),
      fishTypes: data.get(#fishTypes, or: $value.fishTypes),
      imageUrl: data.get(#imageUrl, or: $value.imageUrl),
      distance: data.get(#distance, or: $value.distance),
      checkinCount: data.get(#checkinCount, or: $value.checkinCount));

  @override
  SpotMapVoCopyWith<$R2, SpotMapVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _SpotMapVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
