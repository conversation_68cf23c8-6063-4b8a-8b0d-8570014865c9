// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'spot_price_vo.dart';

class SpotPriceVoMapper extends ClassMapperBase<SpotPriceVo> {
  SpotPriceVoMapper._();

  static SpotPriceVoMapper? _instance;
  static SpotPriceVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = SpotPriceVoMapper._());
      FishTypeMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'SpotPriceVo';

  static num _$id(SpotPriceVo v) => v.id;
  static const Field<SpotPriceVo, num> _f$id = Field('id', _$id);
  static num? _$fishTypeId(SpotPriceVo v) => v.fishTypeId;
  static const Field<SpotPriceVo, num> _f$fishTypeId =
      Field('fishTypeId', _$fishTypeId, key: r'fish_type_id', opt: true);
  static FishType? _$fishType(SpotPriceVo v) => v.fishType;
  static const Field<SpotPriceVo, FishType> _f$fishType =
      Field('fishType', _$fishType, key: r'fish_type', opt: true);
  static int _$priceType(SpotPriceVo v) => v.priceType;
  static const Field<SpotPriceVo, int> _f$priceType =
      Field('priceType', _$priceType, key: r'price_type');
  static String _$priceTypeName(SpotPriceVo v) => v.priceTypeName;
  static const Field<SpotPriceVo, String> _f$priceTypeName =
      Field('priceTypeName', _$priceTypeName, key: r'price_type_name');
  static num _$price(SpotPriceVo v) => v.price;
  static const Field<SpotPriceVo, num> _f$price = Field('price', _$price);
  static int? _$hours(SpotPriceVo v) => v.hours;
  static const Field<SpotPriceVo, int> _f$hours =
      Field('hours', _$hours, opt: true);
  static String? _$description(SpotPriceVo v) => v.description;
  static const Field<SpotPriceVo, String> _f$description =
      Field('description', _$description, opt: true);

  @override
  final MappableFields<SpotPriceVo> fields = const {
    #id: _f$id,
    #fishTypeId: _f$fishTypeId,
    #fishType: _f$fishType,
    #priceType: _f$priceType,
    #priceTypeName: _f$priceTypeName,
    #price: _f$price,
    #hours: _f$hours,
    #description: _f$description,
  };

  static SpotPriceVo _instantiate(DecodingData data) {
    return SpotPriceVo(
        id: data.dec(_f$id),
        fishTypeId: data.dec(_f$fishTypeId),
        fishType: data.dec(_f$fishType),
        priceType: data.dec(_f$priceType),
        priceTypeName: data.dec(_f$priceTypeName),
        price: data.dec(_f$price),
        hours: data.dec(_f$hours),
        description: data.dec(_f$description));
  }

  @override
  final Function instantiate = _instantiate;

  static SpotPriceVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<SpotPriceVo>(map);
  }

  static SpotPriceVo fromJson(String json) {
    return ensureInitialized().decodeJson<SpotPriceVo>(json);
  }
}

mixin SpotPriceVoMappable {
  String toJson() {
    return SpotPriceVoMapper.ensureInitialized()
        .encodeJson<SpotPriceVo>(this as SpotPriceVo);
  }

  Map<String, dynamic> toMap() {
    return SpotPriceVoMapper.ensureInitialized()
        .encodeMap<SpotPriceVo>(this as SpotPriceVo);
  }

  SpotPriceVoCopyWith<SpotPriceVo, SpotPriceVo, SpotPriceVo> get copyWith =>
      _SpotPriceVoCopyWithImpl<SpotPriceVo, SpotPriceVo>(
          this as SpotPriceVo, $identity, $identity);
  @override
  String toString() {
    return SpotPriceVoMapper.ensureInitialized()
        .stringifyValue(this as SpotPriceVo);
  }

  @override
  bool operator ==(Object other) {
    return SpotPriceVoMapper.ensureInitialized()
        .equalsValue(this as SpotPriceVo, other);
  }

  @override
  int get hashCode {
    return SpotPriceVoMapper.ensureInitialized().hashValue(this as SpotPriceVo);
  }
}

extension SpotPriceVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, SpotPriceVo, $Out> {
  SpotPriceVoCopyWith<$R, SpotPriceVo, $Out> get $asSpotPriceVo =>
      $base.as((v, t, t2) => _SpotPriceVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class SpotPriceVoCopyWith<$R, $In extends SpotPriceVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  FishTypeCopyWith<$R, FishType, FishType>? get fishType;
  $R call(
      {num? id,
      num? fishTypeId,
      FishType? fishType,
      int? priceType,
      String? priceTypeName,
      num? price,
      int? hours,
      String? description});
  SpotPriceVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _SpotPriceVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, SpotPriceVo, $Out>
    implements SpotPriceVoCopyWith<$R, SpotPriceVo, $Out> {
  _SpotPriceVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<SpotPriceVo> $mapper =
      SpotPriceVoMapper.ensureInitialized();
  @override
  FishTypeCopyWith<$R, FishType, FishType>? get fishType =>
      $value.fishType?.copyWith.$chain((v) => call(fishType: v));
  @override
  $R call(
          {num? id,
          Object? fishTypeId = $none,
          Object? fishType = $none,
          int? priceType,
          String? priceTypeName,
          num? price,
          Object? hours = $none,
          Object? description = $none}) =>
      $apply(FieldCopyWithData({
        if (id != null) #id: id,
        if (fishTypeId != $none) #fishTypeId: fishTypeId,
        if (fishType != $none) #fishType: fishType,
        if (priceType != null) #priceType: priceType,
        if (priceTypeName != null) #priceTypeName: priceTypeName,
        if (price != null) #price: price,
        if (hours != $none) #hours: hours,
        if (description != $none) #description: description
      }));
  @override
  SpotPriceVo $make(CopyWithData data) => SpotPriceVo(
      id: data.get(#id, or: $value.id),
      fishTypeId: data.get(#fishTypeId, or: $value.fishTypeId),
      fishType: data.get(#fishType, or: $value.fishType),
      priceType: data.get(#priceType, or: $value.priceType),
      priceTypeName: data.get(#priceTypeName, or: $value.priceTypeName),
      price: data.get(#price, or: $value.price),
      hours: data.get(#hours, or: $value.hours),
      description: data.get(#description, or: $value.description));

  @override
  SpotPriceVoCopyWith<$R2, SpotPriceVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _SpotPriceVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
