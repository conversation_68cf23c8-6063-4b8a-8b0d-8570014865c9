import 'package:user_app/api/fishing_recommend_api.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/models/fishing_recommendation.dart';

class FishingRecommendService {
  final FishingRecommendApi _api;

  FishingRecommendService(this._api);

  /// Get fishing recommendations based on current weather conditions
  ///
  /// [weather] The current weather code
  /// [temperature] The current temperature in celsius
  Future<FishingRecommendation> getRecommendations({
    required String weather,
    required double temperature,
  }) async {
    try {
      return await _api.getFishingRecommendation(
        weather: weather,
        temperature: temperature,
      );
    } catch (e) {
      // Return a default recommendation instead of throwing
      return FishingRecommendation(
        fishingIndex: 3.5,
        recommendedFish: [
          FishType(
            id: 1,
            name: '草鱼',
            seasonSpring: true,
            seasonSummer: true,
            seasonAutumn: true,
            seasonWinter: false,
          ),
          FishType(
            id: 2,
            name: '鲫鱼',
            seasonSpring: true,
            seasonSummer: true,
            seasonAutumn: true,
            seasonWinter: true,
          ),
        ],
        weatherAnalysis: '天气适宜，建议钓鱼',
      );
    }
  }
}
