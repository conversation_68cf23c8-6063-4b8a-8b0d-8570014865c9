import 'package:algoliasearch/algoliasearch_lite.dart';
import 'package:flutter/foundation.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';

/// Specialized service for searching fishing spots using Algolia
/// This service connects directly to Algolia from the frontend to reduce backend load
class FishingSpotSearchService {
  static const String indexName = 'fishing_spots';
  final SearchClient _searchClient;

  FishingSpotSearchService(this._searchClient);

  /// Search for fishing spots directly using Algolia search
  ///
  /// [query] - Search query text
  /// [page] - Page number (0-based for pagination)
  /// [pageSize] - Number of items per page
  /// [latitude] - Optional latitude for geo search
  /// [longitude] - Optional longitude for geo search
  /// [radiusKm] - Optional search radius in kilometers
  Future<SearchResult> searchFishingSpots({
    required String query,
    int page = 0,
    int pageSize = 10,
    double? latitude,
    double? longitude,
    double? radiusKm,
  }) async {
    try {
      // Create search request with geo parameters if provided
      final searchRequest = SearchForHits(
        indexName: indexName,
        query: query,
        hitsPerPage: pageSize,
        page: page,
        // Add geo search parameters
        aroundLatLng: (latitude != null && longitude != null) 
            ? '$latitude,$longitude' 
            : null,
        aroundRadius: (latitude != null && longitude != null && radiusKm != null)
            ? (radiusKm * 1000).round() // Convert km to meters
            : null,
      );

      // Perform search using the search client
      final searchResponse =
          await _searchClient.searchIndex(request: searchRequest);

      // Debug: Print Algolia response
      debugPrint('🔍 [Algolia] Query: "$query"');
      debugPrint('🔍 [Algolia] Total hits: ${searchResponse.nbHits}');
      if (searchResponse.hits.isEmpty) {
        debugPrint('🔍 [Algolia] No results found');
      }

      // Convert hits to SpotSummaryVo objects
      final spots = <SpotSummaryVo>[];
      for (var hit in searchResponse.hits) {
        try {
          // Try to create SpotSummaryVo with safe defaults
          final spot = _createSpotFromHit(hit);
          if (spot != null) {
            debugPrint('🔍 [Algolia] Converted spot: ${spot.id} - ${spot.name}');
            spots.add(spot);
          }
        } catch (e) {
          debugPrint('❌ [Algolia] Error converting hit: $e');
        }
      }

      // Get pagination info
      final nbPages = searchResponse.nbPages ?? 1;
      final currentPage = searchResponse.page ?? 0;

      return SearchResult(
        spots: spots,
        nbPages: nbPages,
        currentPage: currentPage,
      );
    } catch (e) {
      debugPrint('❌ [Algolia] Search error: $e');
      return SearchResult(spots: [], nbPages: 0, currentPage: 0);
    }
  }

  /// Get nearby fishing spots using Algolia geo-search
  ///
  /// [latitude] - Current latitude
  /// [longitude] - Current longitude
  /// [radiusKm] - Search radius in kilometers
  /// [limit] - Maximum number of results to return
  Future<List<SpotSummaryVo>> getNearbySpots({
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
    int limit = 10,
  }) async {
    try {
      // For nearby spots, we use an empty query but specify location
      final searchResult = await searchFishingSpots(
        query: '', // Empty query to get all spots
        latitude: latitude,
        longitude: longitude,
        radiusKm: radiusKm,
        pageSize: limit,
      );

      return searchResult.spots;
    } catch (e) {
      debugPrint('❌ [Algolia] Nearby search error: $e');
      return [];
    }
  }

  /// Create SpotSummaryVo from Algolia hit data with safe defaults
  SpotSummaryVo? _createSpotFromHit(Map<String, dynamic> hit) {
    try {
      debugPrint('🔍 [Algolia] Processing hit: ${hit['name']} (${hit['id'] ?? hit['objectID']})');
      
      // Extract required fields with safe defaults
      final id = hit['id'] ?? hit['objectID'];
      if (id == null) {
        debugPrint('❌ [Algolia] No ID found in hit');
        return null;
      }

      // Parse numeric ID if it's a string
      final intId = id is int ? id : int.tryParse(id.toString());
      if (intId == null) {
        debugPrint('❌ [Algolia] Invalid ID format: $id');
        return null;
      }

      final name = hit['name']?.toString() ?? '未知钓点';
      final address = hit['address']?.toString() ?? '';
      final latitude = _parseDouble(hit['latitude']) ?? 0.0;
      final longitude = _parseDouble(hit['longitude']) ?? 0.0;
      final province = hit['province']?.toString() ?? '';
      final city = hit['city']?.toString() ?? '';
      final county = hit['county']?.toString() ?? '';
      final isOfficial = hit['isOfficial'] == true || hit['is_official'] == true;
      final verificationLevel = _parseInt(hit['verificationLevel']) ?? 0;
      final visitorCount = _parseInt(hit['visitorCount']) ?? 0;
      final rating = _parseDouble(hit['rating']) ?? 0.0;
      final hasFacilities = hit['hasFacilities'] == true || hit['has_facilities'] == true;
      final isPaid = hit['isPaid'] == true || hit['paid'] == true;
      final checkinCount = _parseInt(hit['checkinCount']) ?? 0;
      final recentMomentsCount = _parseInt(hit['recentMomentsCount']) ?? 0;

      // Extract simplified data for SpotSummaryVo
      final mainImage = hit['mainImage']?.toString() ?? hit['coverImage']?.toString();
      final priceText = hit['priceText']?.toString();
      
      // Parse fish type names
      List<String> fishTypeNames = [];
      if (hit['fishTypeNames'] is List) {
        fishTypeNames = (hit['fishTypeNames'] as List)
            .map((e) => e.toString())
            .where((name) => name.isNotEmpty)
            .toList();
      }

      final spot = SpotSummaryVo(
        id: intId,
        name: name,
        address: address,
        latitude: latitude,
        longitude: longitude,
        province: province,
        city: city,
        county: county,
        rating: rating,
        official: isOfficial,
        paid: isPaid,
        hasFacilities: hasFacilities,
        verificationLevel: verificationLevel,
        visitorCount: visitorCount,
        checkinCount: checkinCount,
        recentMomentsCount: recentMomentsCount,
        mainImage: mainImage,
        priceText: priceText,
        fishTypeNames: fishTypeNames,
      );
      
      debugPrint('✅ [Algolia] Successfully created SpotSummaryVo: ${spot.id} - ${spot.name}');
      return spot;
    } catch (e) {
      debugPrint('❌ [Algolia] Error in _createSpotFromHit: $e');
      debugPrint('❌ [Algolia] Hit data: ${hit.toString()}');
      return null;
    }
  }

  /// Safely parse double from dynamic value
  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  /// Safely parse int from dynamic value
  int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }
}

/// Class to hold search result information
class SearchResult {
  final List<SpotSummaryVo> spots;
  final int nbPages;
  final int currentPage;

  const SearchResult({
    required this.spots,
    required this.nbPages,
    required this.currentPage,
  });
}
