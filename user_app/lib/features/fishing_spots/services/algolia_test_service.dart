import 'package:algoliasearch/algoliasearch_lite.dart';
import 'package:user_app/config/global_config.dart';

/// Simple Algolia test service to see what data is returned
class AlgoliaTestService {
  static Future<void> testSearch(String query) async {
    try {
      final searchClient = SearchClient(
        appId: GlobalConfig.algoliaAppId,
        apiKey: GlobalConfig.algoliaApiKey,
      );

      final searchRequest = SearchForHits(
        indexName: 'fishing_spots',
        query: query,
        hitsPerPage: 5,
      );

      final searchResponse = await searchClient.searchIndex(request: searchRequest);

      print('🧪 [Test] Query: "$query"');
      print('🧪 [Test] Total hits: ${searchResponse.nbHits}');
      print('🧪 [Test] Number of results: ${searchResponse.hits.length}');
      
      for (int i = 0; i < searchResponse.hits.length; i++) {
        final hit = searchResponse.hits[i];
        print('🧪 [Test] Hit $i: $hit');
        print('🧪 [Test] Hit $i keys: ${hit.keys.toList()}');
        if (hit.containsKey('name')) {
          print('🧪 [Test] Hit $i name: ${hit['name']}');
        }
        if (hit.containsKey('objectID')) {
          print('🧪 [Test] Hit $i objectID: ${hit['objectID']}');
        }
        print('---');
      }
    } catch (e) {
      print('❌ [Test] Error: $e');
    }
  }
}