import 'package:user_app/api/fish_type_api.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';

class FishTypeService {
  final FishTypeApi _fishTypeApi;

  FishTypeService(this._fishTypeApi);

  Future<List<FishType>> getFishTypes() async {
    return await _fishTypeApi.getFishTypes();
  }

  Future<List<FishType>> getFishTypesForCurrentSeason(String season) async {
    final allFishTypes = await getFishTypes();

    return allFishTypes.where((fishType) {
      switch (season) {
        case '春':
          return fishType.seasonSpring;
        case '夏':
          return fishType.seasonSummer;
        case '秋':
          return fishType.seasonAutumn;
        case '冬':
          return fishType.seasonWinter;
        default:
          return true;
      }
    }).toList();
  }

  Future<FishType?> getFishTypeById(int id) async {
    final allFishTypes = await getFishTypes();
    try {
      return allFishTypes.firstWhere((fishType) => fishType.id == id);
    } catch (e) {
      return null;
    }
  }
}
