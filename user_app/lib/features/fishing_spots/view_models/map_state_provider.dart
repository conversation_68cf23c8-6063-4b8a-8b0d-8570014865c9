import 'package:flutter/material.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';

class MapStateViewModel extends ChangeNotifier {
  // 地图控制器
  AMap2DController? _controller;

  AMap2DController? get controller => _controller;

  // 初始化状态
  bool _isInitialized = false;

  bool get isInitialized => _isInitialized;

  // 最后的地图状态
  double? _lastLatitude;
  double? _lastLongitude;
  double? _lastZoomLevel;

  // 保存地图控制器并更新初始化状态
  void setController(AMap2DController controller) {
    _controller = controller;
    _isInitialized = true;
    notifyListeners();
  }

  // 保存当前视图状态
  void saveViewState(double lat, double lng, [double? zoom]) {
    _lastLatitude = lat;
    _lastLongitude = lng;
    if (zoom != null) {
      _lastZoomLevel = zoom;
    }
    notifyListeners();
  }

  // 检查是否有保存的位置
  bool get hasLastPosition => _lastLatitude != null && _lastLongitude != null;

  // 恢复地图位置
  Future<void> restoreMapPosition() async {
    if (_controller != null && hasLastPosition) {
      try {
        await _controller!
            .move(_lastLatitude.toString(), _lastLongitude.toString());

        if (_lastZoomLevel != null) {
          await _controller!.setZoom(zoomLevel: _lastZoomLevel!);
        }
        return Future.value(true);
      } catch (e) {
        print('Error restoring map position: $e');
      }
    }
    return Future.value(false);
  }

  // 清理资源
  void dispose() {
    _controller = null;
    super.dispose();
  }
}
