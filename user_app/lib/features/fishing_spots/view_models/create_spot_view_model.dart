import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/create_spot_dto.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/spot_facility.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/spot_price.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/models/image/uploaded_image.dart';
import 'package:user_app/services/oss_service.dart';

class CreateSpotViewModel extends BaseViewModel {
  final OssService _ossService;
  final FishingSpotViewModel _fishingSpotViewModel;
  final FishingSpotService _fishSpotService;
  final ImagePicker _imagePicker = ImagePicker();

  // Form controllers
  final nameController = TextEditingController();
  final addressController = TextEditingController();
  final descriptionController = TextEditingController();
  final priceController = TextEditingController();

  // Location data
  double latitude = 0.0;
  double longitude = 0.0;
  String province = '';
  String city = '';
  String county = '';
  String formattedAddress = '';
  bool useCurrentLocation = false;

  // Spot configuration
  bool isOfficial = false;
  bool hasFacilities = false;
  bool isPaid = false;
  String spotVisibility =
      'public'; // Default to public (public, followers, private)

  // Fish types
  List<String> selectedFishTypes = [];
  List<FishType> customFishTypes = [];
  List<int> selectedFishTypeIds = [];

  // Images
  List<UploadedImage> spotImages = [];

  // Facilities
  List<SpotFacility> facilities = [];

  // Prices
  List<SpotPrice> prices = [];

  // Certification documents
  List<UploadedImage> certificationDocuments = [];

  // State flags
  bool isLoadingFishTypes = true;
  bool isSubmitting = false;
  bool isUploadingDocuments = false;

  // Custom facilities
  List<String> customFacilities = [];

  // Constructor
  CreateSpotViewModel({
    required OssService ossService,
    required FishingSpotViewModel fishingSpotViewModel,
    required FishingSpotService fishSpotService,
  })  : _ossService = ossService,
        _fishingSpotViewModel = fishingSpotViewModel,
        _fishSpotService = fishSpotService;

  @override
  void dispose() {
    nameController.dispose();
    addressController.dispose();
    descriptionController.dispose();
    priceController.dispose();
    super.dispose();
  }

  // Initialize form data
  void initializeFormData() {
    latitude = 0.0;
    longitude = 0.0;
    province = '';
    city = '';
    county = '';
    formattedAddress = '';
    addressController.text = '';
    useCurrentLocation = false;
    facilities = [
      SpotFacility(
          name: '卫生间', icon: 'wc', description: '公共卫生间', isSelected: false),
      SpotFacility(
          name: '停车场',
          icon: 'local_parking',
          description: '免费停车场',
          isSelected: false),
      SpotFacility(
          name: '餐饮',
          icon: 'restaurant',
          description: '提供简餐和饮料',
          isSelected: false),
      SpotFacility(
          name: '住宿', icon: 'cabin', description: '提供住宿服务', isSelected: false),
      SpotFacility(
          name: '渔具店',
          icon: 'shopping_basket',
          description: '钓鱼用品和饵料',
          isSelected: false),
    ];
    prices = [];
    spotImages.clear();
    certificationDocuments.clear();
    selectedFishTypes = [];
    customFishTypes = [];
    customFacilities = [];
    isOfficial = false;
    hasFacilities = false;
    isPaid = false;
    spotVisibility = 'public';
    nameController.clear();
    descriptionController.clear();
    priceController.clear();
    isSubmitting = false;
    notifyListeners();
  }

  // Load fish types
  Future<void> loadFishTypes() async {
    isLoadingFishTypes = true;
    notifyListeners();

    try {
      await _fishingSpotViewModel.loadFishTypes();
      isLoadingFishTypes = false;
      notifyListeners();
    } catch (e) {
      isLoadingFishTypes = false;
      handleError('加载鱼类数据失败: $e');
      notifyListeners();
    }
  }

  // Method to get available fish types from fishingSpotViewModel
  List<FishType> getAvailableFishTypes() {
    return _fishingSpotViewModel.availableFishTypesObjects;
  }

  // Update location data
  void updateLocation({
    required double latitude,
    required double longitude,
    required String formattedAddress,
    required String province,
    required String city,
    required String county,
    required bool useCurrentLocation,
  }) {
    this.latitude = latitude;
    this.longitude = longitude;
    this.formattedAddress = formattedAddress;
    this.province = province;
    this.city = city;
    this.county = county;
    this.useCurrentLocation = useCurrentLocation;
    addressController.text = formattedAddress;
    notifyListeners();
  }

  // Update visibility setting
  void updateVisibility(String visibility) {
    if (['public', 'followers', 'private'].contains(visibility)) {
      spotVisibility = visibility;
      notifyListeners();
    }
  }

  // Image handling
  Future<void> pickImages() async {
    try {
      final List<XFile> images =
          await _imagePicker.pickMultiImage(imageQuality: 80);
      if (images.isEmpty) return;

      final currentCount = spotImages.length;
      final availableSlots = 9 - currentCount;

      if (images.length > availableSlots) {
        images.removeRange(availableSlots, images.length);
        handleError('最多只能上传9张图片');
      }

      if (images.isEmpty) return;

      final newImages = images
          .map((img) => UploadedImage(file: img, isUploading: true))
          .toList();

      spotImages.addAll(newImages);
      notifyListeners();

      List<Future> uploadFutures = [];
      for (int i = 0; i < newImages.length; i++) {
        final originalIndex = currentCount + i;
        uploadFutures.add(_uploadImage(originalIndex));
      }
      await Future.wait(uploadFutures);
    } catch (e) {
      handleError('选择或处理图片失败: $e');
    }
  }

  Future<void> _uploadImage(int index) async {
    if (index < 0 || index >= spotImages.length) return;
    final imageToUpload = spotImages[index];

    try {
      final url = await _ossService.saveFile(imageToUpload.file);

      if (index < spotImages.length &&
          spotImages[index].file.path == imageToUpload.file.path) {
        spotImages[index] = UploadedImage(
            file: imageToUpload.file, url: url, isUploading: false);
        notifyListeners();
      }
    } catch (e) {
      if (index < spotImages.length &&
          spotImages[index].file.path == imageToUpload.file.path) {
        spotImages[index] = UploadedImage(
            file: imageToUpload.file, isUploading: false, hasError: true);
        notifyListeners();
      }
      handleError('图片上传失败: ${imageToUpload.file.name} - $e');
    }
  }

  void removeImage(int index) {
    if (index < 0 || index >= spotImages.length) return;
    spotImages.removeAt(index);
    notifyListeners();
  }

  // New method to remove certification documents
  void removeDocument(int index) {
    if (index < 0 || index >= certificationDocuments.length) return;
    certificationDocuments.removeAt(index);
    notifyListeners();
  }

  // Facility toggle
  void toggleFacility(int index) {
    if (index < 0 || index >= facilities.length) return;
    facilities[index].isSelected = !facilities[index].isSelected;
    notifyListeners();
  }

  // Add new facility
  void addFacility({
    required String name,
    required String icon,
    String? description,
  }) {
    facilities.add(SpotFacility(
      name: name,
      icon: icon,
      description: description,
      isSelected: true,
    ));
    notifyListeners();
  }

  // Fish type management
  void toggleFishType(FishType fishType, bool isSelected) {
    final int fishTypeId = fishType.id.toInt();
    if (isSelected) {
      if (!selectedFishTypeIds.contains(fishTypeId)) {
        selectedFishTypeIds.add(fishTypeId);

        // Also update the legacy string-based list for backward compatibility
        if (!selectedFishTypes.contains(fishType.name)) {
          selectedFishTypes.add(fishType.name);
        }
      }
    } else {
      selectedFishTypeIds.remove(fishTypeId);
      // Also update the legacy string-based list
      selectedFishTypes.remove(fishType.name);
    }
    notifyListeners();
  }

  void addCustomFishType(FishType newFishType) {
    if (!customFishTypes.any((f) => f.name == newFishType.name)) {
      customFishTypes.add(newFishType);
      // 自动选中新添加的鱼种
      selectedFishTypeIds.add(newFishType.id.toInt());
      // Also update the legacy string-based list
      selectedFishTypes.add(newFishType.name);
      notifyListeners();
    }
  }

  // Price management
  void addPrice({
    required int priceTypeId,
    required String priceTypeName,
    double? price,
    int? hours,
    int? fishTypeId,
    String? fishTypeName,
    String? description,
  }) {
    if (price != null) {
      prices.add(SpotPrice(
        priceType: priceTypeId,
        priceTypeName: priceTypeName,
        price: price,
        hours: hours,
        fishTypeId: fishTypeId,
        fishTypeName: fishTypeName,
        description: description,
      ));
      notifyListeners();
    }
  }

  void removePrice(int index) {
    if (index >= 0 && index < prices.length) {
      prices.removeAt(index);
      notifyListeners();
    }
  }

  // Form validation
  bool validateBasicInfo() {
    return nameController.text.trim().isNotEmpty &&
        addressController.text.trim().isNotEmpty &&
        latitude != 0.0 &&
        longitude != 0.0;
  }

  bool validateImagesAndFishTypes() {
    return spotImages
            .where((img) => img.url != null && !img.hasError)
            .isNotEmpty &&
        selectedFishTypeIds.isNotEmpty;
  }

  bool validateFacilitiesAndPrices() {
    if (isPaid) {
      if (prices.isEmpty && priceController.text.trim().isEmpty) {
        return false;
      }
    }

    // Check if official certification has documents
    if (isOfficial && certificationDocuments.isEmpty) {
      return false;
    }

    return true;
  }

  bool validateAllSteps() {
    return validateBasicInfo() &&
        validateImagesAndFishTypes() &&
        validateFacilitiesAndPrices();
  }

  Future<int> submitSpot() async {
    if (!validateAllSteps()) {
      throw Exception('请完成所有必填项');
    }

    isSubmitting = true;
    notifyListeners();

    try {
      final CreateSpotDto createSpotDto = CreateSpotDto(
        name: nameController.text.trim(),
        description: descriptionController.text.trim(),
        latitude: latitude,
        longitude: longitude,
        address: addressController.text.trim(),
        province: province,
        city: city,
        county: county,
        imageUrls: spotImages
            .where(
                (img) => img.url != null && !img.hasError && !img.isUploading)
            .map((img) => img.url!)
            .toList(),
        fishTypeNames: selectedFishTypeIds
            .where((id) => _fishingSpotViewModel.availableFishTypesObjects
                .any((fishType) => fishType.id.toInt() == id))
            .map((id) => _fishingSpotViewModel.availableFishTypesObjects
                .firstWhere((t) => t.id.toInt() == id)
                .name)
            .toList(),
        extraFishTypes: selectedFishTypeIds
            .where((id) => !_fishingSpotViewModel.availableFishTypesObjects
                .any((fishType) => fishType.id.toInt() == id))
            .map((id) =>
                customFishTypes.firstWhere((t) => t.id.toInt() == id).name)
            .toList(),
        extraFacilities: facilities
            .where((facility) =>
                facility.isSelected && !_isDefaultFacility(facility.name))
            .map((facility) => facility.name)
            .toList(),
        hasFacilities: hasFacilities,
        facilities: facilities
            .where((facility) => facility.isSelected)
            .map((facility) => SpotFacility(
                  name: facility.name,
                  icon: facility.icon,
                  description: facility.description,
                  isSelected: true,
                ))
            .toList(),
        isPaid: isPaid,
        prices: prices,
        isOfficial: isOfficial,
        certificationDocuments: certificationDocuments,
        visibility: spotVisibility,
      );

      final int newSpotId =
          await _fishSpotService.createSpotFromDto(createSpotDto);

      isSubmitting = false;
      notifyListeners();

      return newSpotId;
    } catch (e) {
      isSubmitting = false;
      notifyListeners();
      handleError('创建失败: $e');
      rethrow;
    }
  }

  Future<void> pickDocumentImages() async {
    isUploadingDocuments = true;
    notifyListeners();

    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'jpg', 'jpeg', 'png', 'gif', // Images
          'pdf', 'doc', 'docx' // Documents
        ],
        allowMultiple: true,
        withData: true, // Ensure bytes are loaded, especially for web
      );

      if (result == null || result.files.isEmpty) {
        isUploadingDocuments = false;
        notifyListeners();
        return;
      }

      final pickedFiles = result.files;
      final currentCount = certificationDocuments.length;

      List<PlatformFile> filesToProcess = pickedFiles;

      if (filesToProcess.isEmpty) {
        isUploadingDocuments = false;
        notifyListeners();
        return;
      }

      // --- START FIX ---
      final List<UploadedImage?> potentialNewDocuments =
          filesToProcess.map((file) {
        XFile xFile;
        // On web, path is null, but bytes should be available.
        // On mobile/desktop, path is available.
        if (kIsWeb) {
          // Explicit check for web
          if (file.bytes != null) {
            xFile = XFile.fromData(
              file.bytes!,
              name: file.name,
              // Optional: You might want to get mimeType if needed by OSS
              // mimeType: lookupMimeType(file.name), // Requires mime package
            );
          } else {
            // Handle error: bytes are null on web for some reason
            print("Error: File bytes are null for ${file.name} on web.");
            handleError('无法读取文件: ${file.name}');
            return null; // Skip this file
          }
        } else {
          // Not Web (Mobile, Desktop, etc.)
          if (file.path != null) {
            xFile = XFile(
              file.path!,
              name: file.name,
            );
          } else {
            // Handle error: path is null on non-web platform (unexpected)
            print(
                "Error: File path is null for ${file.name} on non-web platform.");
            handleError('无法访问文件路径: ${file.name}');
            return null; // Skip this file
          }
        }
        return UploadedImage(file: xFile, isUploading: true);
      }).toList();

      // Filter out any files that couldn't be processed (returned null)
      final List<UploadedImage> newDocuments =
          potentialNewDocuments.whereType<UploadedImage>().toList();
      // --- END FIX ---

      if (newDocuments.isEmpty && filesToProcess.isNotEmpty) {
        // Handle case where all selected files failed processing
        print("Warning: None of the selected files could be processed.");
        // Optionally show a user-facing error message
      } else if (newDocuments.isNotEmpty) {
        certificationDocuments.addAll(newDocuments);
        notifyListeners(); // Update UI to show placeholders

        List<Future> uploadFutures = [];
        for (int i = 0; i < newDocuments.length; i++) {
          // Calculate index in the main list *after* adding
          final originalIndex = currentCount + i;
          uploadFutures.add(_uploadDocument(originalIndex));
        }
        await Future.wait(uploadFutures);
      }

      isUploadingDocuments = false;
      notifyListeners(); // Final update after uploads finish or if no files added
    } catch (e, s) {
      // Catch stack trace for better debugging
      isUploadingDocuments = false;
      notifyListeners();
      print("Error selecting/processing documents: $e\n$s"); // Log stack trace
      handleError('选择或处理证明文件失败: $e');
    }
  }

  Future<void> _uploadDocument(int index) async {
    if (index < 0 || index >= certificationDocuments.length) return;
    final docToUpload = certificationDocuments[index];

    // No changes needed here IF _ossService.saveFile(XFile file)
    // internally uses file.readAsBytes() which works for both
    // XFile(path) and XFile.fromData(bytes). This is usually the case.
    try {
      // Assuming _ossService.saveFile handles XFile regardless of its source
      final url = await _ossService.saveFile(docToUpload.file);

      // Check index validity again in case list changed during async operation
      if (index < certificationDocuments.length &&
          certificationDocuments[index].file.path ==
              docToUpload.file
                  .path && // Path comparison might fail on web, maybe compare name?
          certificationDocuments[index].file.name == docToUpload.file.name) {
        // Compare name as a fallback/alternative
        certificationDocuments[index] =
            UploadedImage(file: docToUpload.file, url: url, isUploading: false);
        notifyListeners();
      } else {
        print(
            "Warning: Document at index $index changed or removed during upload.");
      }
    } catch (e) {
      print("Error uploading document ${docToUpload.file.name}: $e");
      // Check index validity again
      if (index < certificationDocuments.length &&
          certificationDocuments[index].file.name == docToUpload.file.name) {
        certificationDocuments[index] = UploadedImage(
            file: docToUpload.file, isUploading: false, hasError: true);
        notifyListeners();
      }
      handleError('证明文件上传失败: ${docToUpload.file.name} - $e');
    }
  }

  // Handle errors
  void handleError(String errorMessage) {
    // Instead of setError which doesn't exist, just log the error
    // In a real app, this could be connected to a UI error display mechanism
    print('Error: $errorMessage');
  }

  // Custom facility management
  void addCustomFacility(String facilityName) {
    if (!customFacilities.contains(facilityName)) {
      customFacilities.add(facilityName);
      notifyListeners();
    }
  }

  void removeCustomFacility(String facilityName) {
    if (customFacilities.contains(facilityName)) {
      customFacilities.remove(facilityName);
      notifyListeners();
    }
  }

  // Helper method to check if a facility is one of the default facilities
  bool _isDefaultFacility(String name) {
    final defaultFacilityNames = ['卫生间', '停车场', '餐饮', '住宿', '渔具店'];
    return defaultFacilityNames.contains(name);
  }
}
