import 'dart:async';

import 'package:flutter/foundation.dart'; // Required for ChangeNotifier and debugPrint
import 'package:geolocator/geolocator.dart';
import 'package:user_app/core/models/paginated_state.dart'; // Ensure correct path
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';

/// Enum to represent the result of a location permission check and fetch attempt.
enum LocationStatus {
  initial,
  loading,
  serviceDisabled,
  permissionDenied,
  permissionDeniedForever,
  success,
  error,
}

class LocationSelectionViewModel extends ChangeNotifier {
  final FishingSpotService _fishingSpotService;

  LocationSelectionViewModel({
    required FishingSpotService fishingSpotService,
    // required SearchService searchService, // If separate
  }) : _fishingSpotService = fishingSpotService;

  // _searchService = searchService; // If separate

  // --- Paginated States for Different Sections ---
  PaginatedState<FishingSpotVo> _recentSpots = const PaginatedState();
  PaginatedState<FishingSpotVo> _favoriteSpots = const PaginatedState();
  PaginatedState<FishingSpotVo> _myCreatedSpots = const PaginatedState();
  PaginatedState<FishingSpotVo> _nearbySpotsState = const PaginatedState();
  PaginatedState<FishingSpotVo> _searchResults = const PaginatedState();

  // --- Location Related State ---
  Position? _currentPosition;
  LocationStatus _locationStatus = LocationStatus.initial;
  String? _locationError;

  // --- Getters for UI ---
  PaginatedState<FishingSpotVo> get recentSpots => _recentSpots;

  PaginatedState<FishingSpotVo> get favoriteSpots => _favoriteSpots;

  PaginatedState<FishingSpotVo> get myCreatedSpots => _myCreatedSpots;

  PaginatedState<FishingSpotVo> get nearbySpotsState => _nearbySpotsState;

  PaginatedState<FishingSpotVo> get searchResults => _searchResults;

  Position? get currentPosition => _currentPosition;

  LocationStatus get locationStatus => _locationStatus;

  String? get locationError => _locationError;

  bool get hasLocationPermissionIssue =>
      _locationStatus == LocationStatus.permissionDenied ||
      _locationStatus == LocationStatus.permissionDeniedForever ||
      _locationStatus == LocationStatus.serviceDisabled;

  // --- Loading Methods for Paginated Lists ---

  Future<void> loadRecentCheckins(
      {bool refresh = false, int pageSize = 10}) async {
    if (_recentSpots.isLoading || (!refresh && !_recentSpots.hasMore)) return;
    final pageToLoad = refresh ? 0 : _recentSpots.currentPage + 1;
    _recentSpots = _recentSpots.startLoading(isRefreshing: refresh);
    notifyListeners();
    try {
      final spots = await _fishingSpotService.getRecentCheckins(
          page: pageToLoad, pageSize: pageSize);
      _recentSpots = _recentSpots.loadSuccess(spots,
          pageLoaded: pageToLoad, hasMore: spots.length >= pageSize);
    } catch (e) {
      _recentSpots = _recentSpots.loadFailure('加载最近签到失败: ${e.toString()}');
    } finally {
      notifyListeners();
    }
  }

  Future<void> loadFavorites({bool refresh = false, int pageSize = 10}) async {
    if (_favoriteSpots.isLoading || (!refresh && !_favoriteSpots.hasMore))
      return;
    final pageToLoad = refresh ? 0 : _favoriteSpots.currentPage + 1;
    _favoriteSpots = _favoriteSpots.startLoading(isRefreshing: refresh);
    notifyListeners();
    try {
      final spots = await _fishingSpotService.getFavorites(
          page: pageToLoad, pageSize: pageSize);
      _favoriteSpots = _favoriteSpots.loadSuccess(spots,
          pageLoaded: pageToLoad, hasMore: spots.length >= pageSize);
    } catch (e) {
      _favoriteSpots = _favoriteSpots.loadFailure('加载收藏列表失败: ${e.toString()}');
    } finally {
      notifyListeners();
    }
  }

  Future<void> loadMyCreatedSpots(
      {bool refresh = false, int pageSize = 10}) async {
    if (_myCreatedSpots.isLoading || (!refresh && !_myCreatedSpots.hasMore)) {
      return;
    }

    final pageToLoad = refresh ? 0 : _myCreatedSpots.currentPage + 1;
    _myCreatedSpots = _myCreatedSpots.startLoading(isRefreshing: refresh);
    notifyListeners();

    try {
      final spots = await _fishingSpotService.getMyCreatedSpots(
          page: pageToLoad, pageSize: pageSize);
      _myCreatedSpots = _myCreatedSpots.loadSuccess(spots,
          pageLoaded: pageToLoad, hasMore: spots.length >= pageSize);
    } catch (e) {
      _myCreatedSpots =
          _myCreatedSpots.loadFailure('加载创建列表失败: ${e.toString()}');
    } finally {
      notifyListeners();
    }
  }

  // Renamed: Load nearby spots *after* position is known
  Future<void> _loadNearbySpotsInternal(
      {bool refresh = false, int pageSize = 10, double radiusKm = 50}) async {
    if (_currentPosition == null) {
      _nearbySpotsState = _nearbySpotsState.loadFailure('无法获取当前位置');
      notifyListeners();
      return;
    }
    // Check loading state
    if (_nearbySpotsState.isLoading ||
        (!refresh && !_nearbySpotsState.hasMore)) {
      return;
    }

    // Fix: Use 0-based pagination consistently like other methods
    final pageToLoad = refresh ? 0 : _nearbySpotsState.currentPage + 1;
    _nearbySpotsState = _nearbySpotsState.startLoading(isRefreshing: refresh);

    try {
      debugPrint(
          'Loading nearby spots: lat=${_currentPosition!.latitude}, lng=${_currentPosition!.longitude}, page=$pageToLoad, pageSize=$pageSize, radius=$radiusKm');

      // 添加超时处理
      final spots = await Future.any([
        _fishingSpotService.getNearbySpots(
          latitude: _currentPosition!.latitude,
          longitude: _currentPosition!.longitude,
          page: pageToLoad,
          pageSize: pageSize,
          radiusKm: radiusKm,
        ),
        // 10秒超时
        Future.delayed(const Duration(seconds: 10), () {
          throw TimeoutException('获取附近钓点超时');
        })
      ]);

      debugPrint('Loaded ${spots.length} nearby spots');

      // 即使返回空数组也要正确更新状态
      // 如果是第一页且结果为空，则表示没有更多数据
      bool hasMore = spots.length >= pageSize;
      if (pageToLoad == 0 && spots.isEmpty) {
        // 第一页为空，表示附近没有钓点
        hasMore = false;
        debugPrint('No nearby spots found, setting hasMore to false');
      }

      // 更新状态，确保即使为空数组也会结束loading状态
      _nearbySpotsState = _nearbySpotsState.loadSuccess(spots,
          pageLoaded: pageToLoad, hasMore: hasMore);
    } catch (e) {
      debugPrint('Failed to load nearby spots: $e');

      // 根据错误类型提供更友好的错误信息
      String errorMessage;
      if (e is TimeoutException) {
        errorMessage = '获取附近钓点超时，请检查网络连接';
      } else {
        errorMessage = '加载附近钓点失败: ${e.toString()}';
      }

      _nearbySpotsState = _nearbySpotsState.loadFailure(errorMessage);
    } finally {
      notifyListeners(); // Notify after load completes or fails
    }
  }

  // --- Search Methods ---

  Future<void> searchSpots(
      {required String query, bool refresh = false, int pageSize = 10}) async {
    if (query.isEmpty ||
        _searchResults.isLoading ||
        (!refresh && !_searchResults.hasMore)) {
      return;
    }
    final pageToLoad = refresh ? 0 : _searchResults.currentPage + 1;
    _searchResults = _searchResults.startLoading(isRefreshing: refresh);
    notifyListeners();
    try {
      final spots = await _fishingSpotService.searchFishingSpots(
          query: query, page: pageToLoad, pageSize: pageSize);
      _searchResults = _searchResults.loadSuccess(spots,
          pageLoaded: pageToLoad, hasMore: spots.length >= pageSize);
    } catch (e) {
      _searchResults = _searchResults.loadFailure('搜索失败: ${e.toString()}');
    } finally {
      notifyListeners();
    }
  }

  void clearSearchResults() {
    _searchResults = _searchResults.reset();
    notifyListeners();
  }

  // --- Location Handling Method ---

  /// Checks location permission, gets current position, and loads nearby spots.
  /// Returns the final LocationStatus. The Page should react to this status.
  Future<LocationStatus> checkAndLoadNearby({bool refresh = false}) async {
    _locationStatus = LocationStatus.loading;
    _nearbySpotsState = _nearbySpotsState.startLoading(
        isRefreshing: refresh); // Show loading for nearby
    notifyListeners();

    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _locationStatus = LocationStatus.serviceDisabled;
        _locationError = '请开启定位服务';
        _nearbySpotsState =
            _nearbySpotsState.loadFailure(_locationError!); // Update state
        notifyListeners();
        return _locationStatus;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        // Request permission (The Page should ideally handle showing rationale if needed)
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _locationStatus = LocationStatus.permissionDenied;
          _locationError = '获取位置权限被拒绝';
          _nearbySpotsState = _nearbySpotsState.loadFailure(_locationError!);
          notifyListeners();
          return _locationStatus;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _locationStatus = LocationStatus.permissionDeniedForever;
        _locationError = '位置权限已被永久拒绝，请在应用设置中开启';
        _nearbySpotsState = _nearbySpotsState.loadFailure(_locationError!);
        notifyListeners();
        return _locationStatus;
      }

      // 3. Permissions granted, get position
      try {
        LocationSettings locationSettings = const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
          timeLimit: Duration(seconds: 15),
        );

        debugPrint('Getting current position...');
        _currentPosition = await Geolocator.getCurrentPosition();
        debugPrint(
            'Got position: lat=${_currentPosition!.latitude}, lng=${_currentPosition!.longitude}');

        // 4. Load nearby spots using the obtained position
        await _loadNearbySpotsInternal(
            refresh:
                refresh); // This will handle its own state changes and notify

        _locationStatus = LocationStatus.success;
        _locationError = null;
        // No need to notifyListeners here, _loadNearbySpotsInternal does it
      } catch (positionError) {
        // 处理获取位置时的错误
        debugPrint('Error getting position: $positionError');
        _locationStatus = LocationStatus.error;
        _locationError = '获取位置失败: ${positionError.toString()}';

        // 即使获取位置失败，也要结束loading状态
        _nearbySpotsState = _nearbySpotsState.loadFailure(_locationError!);
        notifyListeners();
      }

      return _locationStatus;
    } catch (e) {
      debugPrint('Error checking location or loading nearby: $e');
      _locationStatus = LocationStatus.error;
      _locationError = '获取位置或加载附近钓点时出错: ${e.toString()}';
      _currentPosition = null; // Reset position on error

      // 确保结束loading状态
      _nearbySpotsState = _nearbySpotsState.loadFailure(_locationError!);
      notifyListeners();
      return _locationStatus;
    }
  }

  // --- Reset ---

  /// Resets all states managed by this ViewModel.
  void resetAll() {
    _recentSpots = const PaginatedState();
    _favoriteSpots = const PaginatedState();
    _myCreatedSpots = const PaginatedState();
    _nearbySpotsState = const PaginatedState();
    _searchResults = const PaginatedState();
    _currentPosition = null;
    _locationStatus = LocationStatus.initial;
    _locationError = null;
    notifyListeners();
  }

  // Override dispose to potentially cancel streams or timers if added later
  @override
  void dispose() {
    debugPrint("LocationSelectionViewModel disposed");
    // Cancel any active operations here if necessary
    super.dispose();
  }
}
