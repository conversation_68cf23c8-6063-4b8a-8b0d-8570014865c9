import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_map_vo.dart';
import 'package:user_app/features/fishing_spots/services/fish_type_service.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';

class FishingSpotViewModel extends BaseViewModel {
  final FishingSpotService _fishSportService;
  final FishTypeService _fishTypeService;

  List<FishingSpotVo> _fishingSpots = [];
  List<SpotSummaryVo> _fishingSpotSummaries = []; // 新增：用于列表显示的摘要数据
  List<SpotMapVo> _fishingSpotMaps = []; // 新增：用于地图显示的轻量级数据

  String? _errorMessage;

  // Pagination
  int _currentPage = 0;
  final int _pageSize = 10;
  int _totalPages = 1;
  bool _hasMore = true;

  // Filtering
  String? _filterType;
  List<String>? _selectedFishTypes;
  bool? _hasFacilities;
  bool? _hasParking;

  // Fish types reference list
  final List<FishType> _availableFishTypes = [];

  // Weather data (mock for now, would be from API in production)
  final Map<String, dynamic> _weatherData = {
    'temperature': 22,
    'condition': 'Partly Cloudy',
    'waterTemperature': 18,
    'windSpeed': 8,
    'humidity': 65,
    'pressure': 1013,
    'fishingIndex': 4.2,
    'recommendedFish': ['草鱼', '鲫鱼'],
  };

  List<FishType> _fishTypeObjects = []; // 实际的 FishType 对象列表

  // Getters
  List<FishingSpotVo> get fishingSpots => _fishingSpots;
  List<SpotSummaryVo> get fishingSpotSummaries =>
      _fishingSpotSummaries; // 新增：摘要列表
  List<SpotMapVo> get fishingSpotMaps => _fishingSpotMaps; // 新增：地图数据列表

  List<FishingSpotVo> get nearbySpots => _fishingSpots.take(3).toList();

  List<FishingSpotVo> get recentSpots => _fishingSpots.take(5).toList();

  List<FishingSpotVo> get favoriteSpots =>
      _fishingSpots.where((spot) => spot.rating >= 4).toList();

  String? get errorMessage => _errorMessage;

  bool get hasError => _errorMessage != null;

  bool get hasMore => _hasMore;

  Map<String, dynamic> get weatherData => _weatherData;

  List<FishType> get availableFishTypes => _availableFishTypes;
  List<FishType> _availableFishTypesObjects = [];
  List<FishType> _customFishTypes = [];
  bool _isLoadingFishTypes = false;
  String? _fishTypesError;

  // List<FishType> get availableFishTypesObjects {
  //   // Return a combined list to ensure custom fish types are included
  //   return [..._availableFishTypesObjects, ..._customFishTypes];
  // }

  // Inside FishingSpotViewModel:
  List<FishType> get availableFishTypesObjects {
    print(
        '>>> [ViewModel Getter] Combining base (${_availableFishTypesObjects.length}) and custom (${_customFishTypes.length})');
    // Ensure _customFishTypes is accessed *after* any potential update
    final List<FishType> currentCustomTypes =
        List.from(_customFishTypes); // Take a snapshot
    print(
        '>>> [ViewModel Getter] Snapshot of _customFishTypes: ${currentCustomTypes.map((f) => '${f.name}(${f.id})').join(', ')}');
    final combinedList = [
      ..._availableFishTypesObjects,
      ...currentCustomTypes
    ]; // Use the snapshot
    print('>>> [ViewModel Getter] Combined list size: ${combinedList.length}');
    // Check for duplicates in the combined list before returning (optional but good debug)
    final uniqueIds = Set<int>.from(combinedList.map((f) => f.id.toInt()));
    if (uniqueIds.length != combinedList.length) {
      print(
          ">>> [ViewModel Getter] WARNING: Duplicate IDs found in combined list!");
    }
    return combinedList;
  }

  bool get isLoadingFishTypes => _isLoadingFishTypes;

  String? get fishTypesError => _fishTypesError;

  FishingSpotViewModel({
    required FishingSpotService fishSpotService,
    required FishTypeService fishTypeService,
  })  : _fishSportService = fishSpotService,
        _fishTypeService = fishTypeService;

  Future<void> loadFishingSpots({bool refresh = false}) async {
    print(
        '🔄 [ViewModel] loadFishingSpots called with refresh=$refresh, isLoading=$isLoading');
    if (isLoading) {
      print('🔄 [ViewModel] Already loading, skipping...');
      return;
    }

    print('🔄 [ViewModel] Setting busy=true and starting load...');
    setBusy(true);
    if (refresh) {
      _resetPagination();
      print('🔄 [ViewModel] Pagination reset for refresh');
    }

    try {
      print('🔄 [ViewModel] Calling _fetchFishingSpots...');
      final spotsResult = await _fetchFishingSpots();
      print(
          '🔄 [ViewModel] _fetchFishingSpots returned ${spotsResult.content.length} spots');

      if (refresh) {
        _fishingSpots = spotsResult.content;
        print(
            '🔄 [ViewModel] Set _fishingSpots to ${_fishingSpots.length} spots (refresh)');
      } else {
        _fishingSpots = [..._fishingSpots, ...spotsResult.content];
        print(
            '🔄 [ViewModel] Added to _fishingSpots, now ${_fishingSpots.length} spots total');
      }

      _totalPages = spotsResult.totalPages;
      _currentPage = spotsResult.number;
      _hasMore = !spotsResult.last;

      print(
          '🔄 [ViewModel] Pagination: currentPage=$_currentPage, totalPages=$_totalPages, hasMore=$_hasMore');

      _errorMessage = null;

      print('🔄 [ViewModel] About to call notifyListeners()');
      notifyListeners();
      print('🔄 [ViewModel] notifyListeners() called successfully');
    } catch (e) {
      _errorMessage = 'Failed to load fishing spots: $e';
      print(_errorMessage);

      notifyListeners();
    } finally {
      setBusy(false);
    }
  }

  /// 新增：加载钓点摘要数据（用于列表显示）
  Future<void> loadFishingSpotSummaries({bool refresh = false}) async {
    print(
        '🔄 [ViewModel] loadFishingSpotSummaries called with refresh=$refresh, isLoading=$isLoading');
    if (isLoading) {
      print('🔄 [ViewModel] Already loading, skipping...');
      return;
    }

    print('🔄 [ViewModel] Setting busy=true and starting summary load...');
    setBusy(true);
    if (refresh) {
      _resetSummaryPagination();
      print('🔄 [ViewModel] Summary pagination reset for refresh');
    }

    try {
      print('🔄 [ViewModel] Calling _fetchFishingSpotSummaries...');
      final summariesResult = await _fetchFishingSpotSummaries();
      print(
          '🔄 [ViewModel] _fetchFishingSpotSummaries returned ${summariesResult.content.length} summaries');

      if (refresh) {
        _fishingSpotSummaries = summariesResult.content;
        print(
            '🔄 [ViewModel] Set _fishingSpotSummaries to ${_fishingSpotSummaries.length} summaries (refresh)');
      } else {
        _fishingSpotSummaries = [
          ..._fishingSpotSummaries,
          ...summariesResult.content
        ];
        print(
            '🔄 [ViewModel] Added to _fishingSpotSummaries, now ${_fishingSpotSummaries.length} summaries total');
      }

      _totalPages = summariesResult.totalPages;
      _currentPage = summariesResult.number;
      _hasMore = !summariesResult.last;

      print(
          '🔄 [ViewModel] Summary Pagination: currentPage=$_currentPage, totalPages=$_totalPages, hasMore=$_hasMore');

      _errorMessage = null;

      print('🔄 [ViewModel] About to call notifyListeners()');
      notifyListeners();
      print('🔄 [ViewModel] notifyListeners() called successfully');
    } catch (e) {
      _errorMessage = 'Failed to load fishing spot summaries: $e';
      print(_errorMessage);

      notifyListeners();
    } finally {
      setBusy(false);
    }
  }

  Future<void> loadMore() async {
    if (isLoading || !_hasMore) return;

    _currentPage++;
    await loadFishingSpots();
  }

  void applyFilters({
    String? filter,
    List<String>? fishTypes,
    bool? hasFacilities,
    bool? hasParking,
  }) {
    // 当选择"全部"时，不传递filterType参数
    _filterType = (filter == "全部") ? null : filter;

    // 当鱼类列表为空时，不传递fishTypes参数
    _selectedFishTypes = (fishTypes?.isEmpty ?? true) ? null : fishTypes;

    // 当设施筛选为false时，不传递hasFacilities参数
    _hasFacilities = (hasFacilities == false) ? null : hasFacilities;

    // hasParking目前后端API不支持，暂时不处理
    _hasParking = hasParking;
  }

  /// Reset filters to default values
  void resetFilters() {
    _filterType = null;
    _selectedFishTypes = null;
    _hasFacilities = null;
    _hasParking = null;
  }

  /// Get details for a specific fishing spot
  Future<FishingSpotVo> getFishingSpotDetail(int id) async {
    try {
      // First check if we already have this spot in our local data
      final localSpot = _fishingSpots.firstWhere(
        (spot) => spot.id == id,
        orElse: () => FishingSpotVo(
          id: 0,
          name: '',
          address: '',
          latitude: 0,
          longitude: 0,
          province: '',
          city: '',
          county: '',
          isOfficial: false,
          verificationLevel: 0,
          images: [],
          visitorCount: 0,
          rating: 0,
          hasFacilities: false,
          isPaid: false,
          checkinCount: 0,
          seasons: [],
          fishTypeList: [],
        ),
      );

      if (localSpot.id != 0) {
        return localSpot;
      }

      // If not found locally, fetch from API
      return await _fishSportService.getFishingSpotDetail(id);
    } catch (e) {
      _errorMessage = 'Failed to get spot details: $e';
      print(_errorMessage);
      rethrow;
    }
  }

  /// Get complete spot data for displaying in cards
  /// This method always fetches fresh data from API to ensure completeness
  Future<FishingSpotVo> getFreshSpotData(int id) async {
    try {
      print('🔄 [ViewModel] getFreshSpotData called for spot ID: $id');
      // Always fetch from API to get complete data (including full images array, latest moments, etc.)
      final result = await _fishSportService.getFishingSpotDetail(id);
      print('✅ [ViewModel] getFreshSpotData completed for spot ID: $id');
      print('🔍 [ViewModel] Fresh data details:');
      print('   - Name: ${result.name}');
      print('   - Images: ${result.images?.length ?? 0} images');
      if (result.images?.isNotEmpty == true) {
        print('   - First image: ${result.images!.first}');
      }
      print('   - Recent moments: ${result.recentMomentsCount}');
      print('   - Checkin count: ${result.checkinCount}');
      print('   - Visitor count: ${result.visitorCount}');
      return result;
    } catch (e) {
      _errorMessage = 'Failed to get fresh spot data: $e';
      print(
          '❌ [ViewModel] getFreshSpotData failed for spot ID: $id, error: $e');
      rethrow;
    }
  }

  /// Perform checkin at a fishing spot
  Future<bool> checkinFishingSpot(int spotId) async {
    try {
      final result = await _fishSportService.checkinFishingSpot(spotId);

      // If successful, update the local data
      if (result) {
        final spotIndex = _fishingSpots.indexWhere((spot) => spot.id == spotId);
        if (spotIndex >= 0) {
          final updatedSpot = _fishingSpots[spotIndex].copyWith(
            checkinCount: _fishingSpots[spotIndex].checkinCount + 1,
          );
          _fishingSpots[spotIndex] = updatedSpot;
          notifyListeners();
        }
      }

      return result;
    } catch (e) {
      _errorMessage = 'Failed to check in: $e';
      print(_errorMessage);
      return false;
    }
  }

  /// Update weather data
  Future<void> refreshWeatherData() async {
    // In a real app, this would fetch from a weather API
    // For now, we'll just simulate a refresh with mock data
    setBusy(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(milliseconds: 800));

      _weatherData['temperature'] = 20 + (DateTime.now().millisecond % 5);
      _weatherData['waterTemperature'] = 16 + (DateTime.now().millisecond % 5);
      _weatherData['fishingIndex'] =
          3.5 + (DateTime.now().millisecond % 15) / 10;

      // Update recommended fish based on current conditions
      final month = DateTime.now().month;
      if (month >= 3 && month <= 5) {
        // Spring
        _weatherData['recommendedFish'] = ['鲫鱼', '草鱼', '鲤鱼'];
      } else if (month >= 6 && month <= 8) {
        // Summer
        _weatherData['recommendedFish'] = ['草鱼', '鲢鱼', '鳙鱼'];
      } else if (month >= 9 && month <= 11) {
        // Fall
        _weatherData['recommendedFish'] = ['鲫鱼', '鲤鱼', '黑鱼'];
      } else {
        // Winter
        _weatherData['recommendedFish'] = ['鲫鱼', '鲢鱼'];
      }

      notifyListeners();
    } catch (e) {
      _errorMessage = 'Failed to update weather data: $e';
    } finally {
      setBusy(false);
    }
  }

  Future<void> loadFishTypes() async {
    _isLoadingFishTypes = true;
    _fishTypesError = null;

    try {
      final fishTypes = await _fishTypeService.getFishTypes();
      _availableFishTypesObjects = fishTypes;
      _isLoadingFishTypes = false;
    } catch (e) {
      _isLoadingFishTypes = false;
      _fishTypesError = 'Failed to load fish types: $e';
      notifyListeners();
    }
    notifyListeners();
  }

  void addCustomFishType(FishType customFishType) {
    // Check if this fish type already exists by ID (using the correct list)
    final existingIndex = _customFishTypes
        .indexWhere((type) => type.id.toInt() == customFishType.id.toInt());

    if (existingIndex >= 0) {
      // Update existing fish type (optional, good practice)
      _customFishTypes[existingIndex] = customFishType;
    } else {
      // --- ENSURE THIS IS AN APPEND OPERATION ---
      _customFishTypes.add(customFishType); // Use .add()
      // Or if you need a new list reference for state management:
      // _customFishTypes = [..._customFishTypes, customFishType];
      // --- END ENSURE APPEND ---
    }

    // Make sure to create a new list reference IF your state management requires it
    // If using standard ChangeNotifier, the .add() above might be enough if followed by notifyListeners()
    // If issues persist, explicitly create a new list:
    _customFishTypes = List.from(_customFishTypes);

    // --- MOVE LOGGING HERE ---
    print(
        '[ViewModel] Custom fish type added/updated: ${customFishType.name} (ID: ${customFishType.id})');
    print(
        '[ViewModel] Current custom fish types LIST: ${_customFishTypes.map((f) => '${f.name}(${f.id})').join(', ')}');
    // --- END MOVE LOGGING ---

    // --- REMOVE REDUNDANT/MISLEADING LOG ---
    // print(
    //     'Current custom fish types: ${customFishType.name}'); // REMOVE THIS
    // --- END REMOVE ---

    notifyListeners(); // Notify AFTER state modification is complete

    // This log is potentially misleading as it calls the getter, which might see
    // the state differently depending on timing. Keep if needed, but be aware.
    // print('Total available (getter): ${availableFishTypesObjects.length}');
  }

  // PRIVATE METHODS

  /// Fetch fishing spots from the service with current pagination and filters
  Future<dynamic> _fetchFishingSpots() async {
    print('🔄 [ViewModel] _fetchFishingSpots called with:');
    print('   - page: $_currentPage, size: $_pageSize');
    print('   - filterType: $_filterType');
    print('   - fishTypes: $_selectedFishTypes');
    print('   - hasFacilities: $_hasFacilities');

    // Call the service with all filter parameters
    // 只传递非null的参数，确保查询全部时不传递筛选条件
    return await _fishSportService.getFishingSpots(
      page: _currentPage,
      size: _pageSize,
      filterType: _filterType, // 当为"全部"时已在applyFilters中设为null
      fishTypes: _selectedFishTypes, // 当为空时已在applyFilters中设为null
      hasFacilities: _hasFacilities, // 当为false时已在applyFilters中设为null
      // hasParking参数目前后端API不支持，暂不传递
    );
  }

  /// Reset pagination state for a new data load
  void _resetPagination() {
    _currentPage = 0;
    _hasMore = true;
    _fishingSpots = [];
  }

  /// Reset pagination state for summary data
  void _resetSummaryPagination() {
    _currentPage = 0;
    _hasMore = true;
    _fishingSpotSummaries = [];
  }

  /// Fetch fishing spot summaries from the service
  Future<dynamic> _fetchFishingSpotSummaries() async {
    print('🔄 [ViewModel] _fetchFishingSpotSummaries called with:');
    print('   - page: $_currentPage, size: $_pageSize');
    print('   - filterType: $_filterType');
    print('   - fishTypes: $_selectedFishTypes');
    print('   - hasFacilities: $_hasFacilities');

    // Call the service with all filter parameters using the new summary API
    return await _fishSportService.getFishingSpotsAsSummary(
      page: _currentPage,
      size: _pageSize,
      filterType: _filterType,
      fishTypes: _selectedFishTypes,
      hasFacilities: _hasFacilities,
    );
  }

  bool get isBusy => busy;

  /// Update the check-in count for a specific spot in local data
  void updateSpotCheckinCount(int spotId, int newCount) {
    // Update in summary list
    final summaryIndex = _fishingSpotSummaries.indexWhere((spot) => spot.id == spotId);
    if (summaryIndex != -1) {
      final updatedSpot = _fishingSpotSummaries[summaryIndex].copyWith(
        checkinCount: newCount,
      );
      _fishingSpotSummaries[summaryIndex] = updatedSpot;
    }

    // Update in detailed list if exists
    final detailIndex = _fishingSpots.indexWhere((spot) => spot.id == spotId);
    if (detailIndex != -1) {
      _fishingSpots[detailIndex] = _fishingSpots[detailIndex].copyWith(
        checkinCount: newCount,
      );
    }

    notifyListeners();
  }

  // Getters for testing
  String? get filterType => _filterType;
  List<String>? get selectedFishTypes => _selectedFishTypes;
  bool? get hasFacilities => _hasFacilities;
}
