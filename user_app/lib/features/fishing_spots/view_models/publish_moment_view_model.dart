import 'dart:async';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:user_app/features/fishing_spots/constants/publish_moment_constants.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/view_models/moment_view_model.dart';
import 'package:user_app/models/image/uploaded_image.dart';
import 'package:user_app/services/oss_service.dart';

class PublishMomentViewModel extends ChangeNotifier {
  final MomentViewModel _momentViewModel;
  final OssService _ossService;
  final ImagePicker _imagePicker = ImagePicker();

  String _content = '';
  String _momentType = PublishMomentConstants.momentTypes[0];
  String _visibility = 'public';
  FishingSpotVo? _fishingSpot;
  List<UploadedImage> _images = [];
  bool _isLoading = false;
  String? _error;
  Map<String, dynamic> _typeSpecificData = {};

  // Types to visibility mappings
  final Map<String, List<String>> _typeToVisibilityOptions = {
    '钓获分享': ['public', 'followers', 'private'],
    '装备展示': ['public', 'followers', 'private'],
    '技巧分享': ['public', 'followers'], // 技巧分享通常是公开或关注者可见
    '问答求助': ['public'], // 问答通常是公开的，获取最多帮助
  };

  // Default visibility for each type
  final Map<String, String> _defaultVisibility = {
    '钓获分享': 'public',
    '装备展示': 'public',
    '技巧分享': 'public',
    '问答求助': 'public',
  };

  PublishMomentViewModel({
    required MomentViewModel momentViewModel,
    required OssService ossService,
  })  : _momentViewModel = momentViewModel,
        _ossService = ossService;

  // Getters
  String get content => _content;

  String get momentType => _momentType;

  String get visibility => _visibility;

  FishingSpotVo? get fishingSpot => _fishingSpot;

  List<UploadedImage> get images => _images;

  bool get isLoading => _isLoading;

  String? get error => _error;

  Map<String, dynamic> get typeSpecificData => _typeSpecificData;

  bool get canPublish =>
      _content.isNotEmpty &&
      _images.isNotEmpty &&
      !_images.any((img) => img.isUploading) &&
      !_images.any((img) => img.hasError) &&
      _isTypeSpecificDataValid();

  bool _isTypeSpecificDataValid() {
    switch (_momentType) {
      case '钓获分享':
        // 检查是否有鱼类记录
        return _typeSpecificData['caughtFishes'] != null &&
            (_typeSpecificData['caughtFishes'] as List<dynamic>).isNotEmpty;
      case '装备展示':
        // 检查装备名称是否已填写
        return _typeSpecificData['equipmentName'] != null &&
            _typeSpecificData['equipmentName'].toString().isNotEmpty;
      case '技巧分享':
        // 检查技巧名称是否已填写
        return _typeSpecificData['techniqueName'] != null &&
            _typeSpecificData['techniqueName'].toString().isNotEmpty;
      case '问答求助':
        // 检查问题标题是否已填写
        return _typeSpecificData['questionTitle'] != null &&
            _typeSpecificData['questionTitle'].toString().isNotEmpty;
      default:
        return true;
    }
  }

  // Helper to convert UI moment type to backend value
  String _getBackendMomentType() {
    switch (_momentType) {
      case '钓获分享':
        return 'fishing_catch';
      case '装备展示':
        return 'equipment';
      case '技巧分享':
        return 'technique';
      case '问答求助':
        return 'question';
      default:
        return 'fishing_catch';
    }
  }

  // Getters for visibility options
  List<String> getVisibilityOptionsForType(String type) {
    return _typeToVisibilityOptions[type] ?? ['public'];
  }

  // Setters
  void setContent(String value) {
    _content = value;
    notifyListeners();
  }

  void setMomentType(String type) {
    _momentType = type;
    // 切换类型时，自动设置该类型的默认可见范围
    _visibility = _defaultVisibility[type] ?? 'public';
    notifyListeners();
  }

  void setVisibility(String value) {
    _visibility = value;
    notifyListeners();
  }

  void setFishingSpot(FishingSpotVo? spot) {
    _fishingSpot = spot;
    notifyListeners();
  }

  void setTypeSpecificData(Map<String, dynamic> data) {
    _typeSpecificData = data;
    notifyListeners();
  }

  // Pick Images (moved from Page)
  Future<Map<String, dynamic>> pickImages() async {
    Map<String, dynamic> result = {'success': true, 'message': '', 'count': 0};

    if (_images.length >= PublishMomentConstants.maxImages) {
      result['success'] = false;
      result['message'] = '最多只能选择${PublishMomentConstants.maxImages}张图片';
      return result;
    }

    try {
      final pickedFiles = await _imagePicker.pickMultiImage(imageQuality: 85);

      if (pickedFiles.isEmpty) {
        result['success'] = true;
        result['count'] = 0;
        return result;
      }

      // Check how many more images we can add
      final int remainingSlots =
          PublishMomentConstants.maxImages - _images.length;
      final List<XFile> filesToProcess = pickedFiles.length > remainingSlots
          ? pickedFiles.sublist(0, remainingSlots)
          : pickedFiles;

      // Process each image through OSS service
      List<Future<void>> uploadFutures = [];
      for (final file in filesToProcess) {
        uploadFutures.add(uploadImage(file));
      }

      await Future.wait(uploadFutures);

      // Set result info
      result['success'] = true;
      result['count'] = filesToProcess.length;

      if (pickedFiles.length > remainingSlots) {
        result['message'] = '已选择$remainingSlots张图片，超出部分未添加';
      }

      return result;
    } catch (e) {
      result['success'] = false;
      result['message'] = '选择图片失败: $e';
      return result;
    }
  }

  // Image handling with OSS Service
  Future<void> uploadImage(XFile file) async {
    if (_images.length >= PublishMomentConstants.maxImages) {
      _error = '最多只能选择${PublishMomentConstants.maxImages}张图片';
      notifyListeners();
      return;
    }

    try {
      // Add the image with uploading state
      final newImage = UploadedImage(file: file, isUploading: true);
      final index = _images.length;
      _images.add(newImage);
      notifyListeners();

      // Upload using OssService
      try {
        // Upload file to OSS
        final String ossUrl = await _ossService.saveFile(file);

        // Update image with URL
        if (index < _images.length) {
          _images[index] = UploadedImage(
            file: file,
            url: ossUrl,
            isUploading: false,
            uploadProgress: 1.0,
          );
          notifyListeners();
        }
      } catch (e) {
        // Handle upload error
        if (index < _images.length) {
          _images[index] = UploadedImage(
            file: file,
            isUploading: false,
            hasError: true,
          );
        }
        _error = '上传图片失败: $e';
        notifyListeners();
      }
    } catch (e) {
      _error = '处理图片失败: $e';
      notifyListeners();
    }
  }

  void removeImage(int index) {
    if (index >= 0 && index < _images.length) {
      _images.removeAt(index);
      notifyListeners();
    }
  }

  void retryUpload(int index) async {
    if (index < 0 || index >= _images.length) return;

    final image = _images[index];
    if (!image.hasError) return;

    // Reset state to uploading
    _images[index] = UploadedImage(
      file: image.file,
      isUploading: true,
      hasError: false,
    );
    notifyListeners();

    try {
      // Retry upload
      final String ossUrl = await _ossService.saveFile(image.file);

      // Update image with URL
      _images[index] = UploadedImage(
        file: image.file,
        url: ossUrl,
        isUploading: false,
        uploadProgress: 1.0,
      );
      notifyListeners();
    } catch (e) {
      // Handle retry failure
      _images[index] = UploadedImage(
        file: image.file,
        isUploading: false,
        hasError: true,
      );
      _error = '重新上传失败: $e';
      notifyListeners();
    }
  }

  // Publish (moved from Page)
  Future<Map<String, dynamic>> publish() async {
    Map<String, dynamic> result = {
      'success': false,
      'message': '',
    };

    if (!canPublish) {
      // Generate detailed error message
      String errorMessage = '';
      if (_content.isEmpty) {
        errorMessage = '请输入内容';
      } else if (_images.isEmpty) {
        errorMessage = '请至少上传一张图片';
      } else if (_images.any((img) => img.isUploading)) {
        errorMessage = '图片正在上传中，请稍候';
      } else if (_images.any((img) => img.hasError)) {
        errorMessage = '部分图片上传失败，请重试或删除';
      }

      result['message'] = errorMessage.isEmpty ? '无法发布，请检查填写内容' : errorMessage;
      return result;
    }

    _isLoading = true;
    notifyListeners();

    try {
      final List<String> imageUrls = _images
          .where((img) => img.url != null)
          .map((img) => img.url!)
          .toList();

      await _momentViewModel.publishMoment(
        content: _content,
        momentType: _getBackendMomentType(),
        visibility: _visibility,
        fishingSpot: _fishingSpot,
        imageUrls: imageUrls,
        typeSpecificData: _buildTypeSpecificData(),
      );

      _isLoading = false;
      result['success'] = true;
      result['message'] = '发布成功！';
      notifyListeners();
      return result;
    } catch (e) {
      _error = '发布失败: $e';
      _isLoading = false;
      result['success'] = false;
      result['message'] = '发布失败: $e';
      notifyListeners();
      return result;
    }
  }

  void clear() {
    _content = '';
    _momentType = PublishMomentConstants.momentTypes[0];
    _visibility = 'public';
    _fishingSpot = null;
    _images = [];
    _isLoading = false;
    _error = null;
    notifyListeners();
  }

  void reorderImages(List<UploadedImage> list) {
    _images = list;
    notifyListeners();
  }

  void handleError(String errorMessage) {
    // Instead of setError which doesn't exist, just store the error
    // In a real app, this could be connected to a UI error display mechanism
    _error = errorMessage;
    notifyListeners();
  }

  // Helper to build type-specific data for backend
  Map<String, dynamic> _buildTypeSpecificData() {
    switch (_momentType) {
      case '钓获分享':
        return _buildFishingCatchData();
      case '装备展示':
        return _buildEquipmentData();
      case '技巧分享':
        return _buildTechniqueData();
      case '问答求助':
        return _buildQuestionData();
      default:
        return {};
    }
  }

  Map<String, dynamic> _buildFishingCatchData() {
    final data = Map<String, dynamic>.from(_typeSpecificData);

    // 转换钓获分享的数据结构
    return {
      'fishingMethod': data['fishingMethod'] ?? '',
      'weatherConditions': data['weatherConditions'] ?? '',
      'caughtFishes': data['caughtFishes'] ?? [],
      'totalWeight': data['totalWeight'] ?? 0.0,
      'catchImages': data['catchImages']
              ?.map((img) => {
                    'url': img.url,
                    'file': img.file?.path,
                    'isUploading': img.isUploading,
                    'hasError': img.hasError,
                  })
              ?.toList() ??
          [],
    };
  }

  Map<String, dynamic> _buildEquipmentData() {
    final data = Map<String, dynamic>.from(_typeSpecificData);

    // 转换装备展示的数据结构
    return {
      'equipmentName': data['equipmentName'] ?? '',
      'brand': data['brand'] ?? '',
      'model': data['model'] ?? '',
      'price': data['price'] ?? '',
      'category': data['category'] ?? '',
      'rating': data['rating'] ?? 0.0,
      'targetFish': data['targetFish'] ?? '',
      'targetFishTypes': data['targetFishTypes']
              ?.map((fishType) => {
                    'id': fishType.id,
                    'name': fishType.name,
                  })
              ?.toList() ??
          [],
    };
  }

  Map<String, dynamic> _buildTechniqueData() {
    final data = Map<String, dynamic>.from(_typeSpecificData);

    // 转换技巧分享的数据结构
    return {
      'techniqueName': data['techniqueName'] ?? '',
      'description': data['description'] ?? '',
      'targetFish': data['targetFish'] ?? '',
      'targetFishTypes': data['targetFishTypes']
              ?.map((fishType) => {
                    'id': fishType.id,
                    'name': fishType.name,
                  })
              ?.toList() ??
          [],
      'difficulty': data['difficulty'] ?? '',
      'environments': data['environments'] ?? [],
    };
  }

  Map<String, dynamic> _buildQuestionData() {
    final data = Map<String, dynamic>.from(_typeSpecificData);

    // 转换问答求助的数据结构
    return {
      'questionTitle': data['questionTitle'] ?? '',
      'detailedProblem': data['detailedProblem'] ?? '',
      'tags': data['tags'] ?? [],
    };
  }
}
