import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/features/fishing_spots/models/fishing_recommendation.dart';
import 'package:user_app/features/fishing_spots/models/weather_data_dto.dart';
import 'package:user_app/features/fishing_spots/services/fishing_recommend_service.dart';
import 'package:user_app/services/map_service.dart';

class WeatherCardViewModel extends BaseViewModel {
  final MapService _mapService;
  final FishingRecommendService _fishingRecommendService;

  WeatherDataDto? _weatherData;
  WeatherDataDto? _forecastData;
  FishingRecommendation? _fishingRecommendation;

  WeatherDataDto get weatherData => _weatherData ?? _getDefaultWeatherData();
  WeatherDataDto get forecastData => _forecastData ?? _getDefaultWeatherData();

  FishingRecommendation get fishingRecommendationData =>
      _fishingRecommendation ?? _getDefaultRecommendation();

  WeatherCardViewModel({
    required MapService mapService,
    required FishingRecommendService fishingRecommendService,
  })  : _mapService = mapService,
        _fishingRecommendService = fishingRecommendService;

  Future<void> fetchWeatherData() async {
    setBusy(true);

    try {
      // 获取当前天气
      _weatherData = await _mapService.getWeatherByIp();

      // 获取天气预报
      try {
        _forecastData = await _mapService.getWeatherForecast();
      } catch (e) {
        _forecastData = _getDefaultWeatherData();
      }

      double temp = double.tryParse(_weatherData!.temperatureFloat) ??
          double.tryParse(_weatherData!.temperature) ??
          20.0;
      String weather = _weatherData!.weather;

      try {
        _fishingRecommendation = await _fishingRecommendService
            .getRecommendations(weather: weather, temperature: temp);
      } catch (e) {
        _fishingRecommendation = _getDefaultRecommendation();
      }

      notifyListeners();
    } catch (e) {
      _weatherData = _getDefaultWeatherData();
      _forecastData = _getDefaultWeatherData();
      _fishingRecommendation = _getDefaultRecommendation();
      notifyListeners();
    } finally {
      setBusy(false);
    }
  }

  // Create default weather data to avoid null exceptions
  WeatherDataDto _getDefaultWeatherData() {
    return WeatherDataDto(
        province: '未知',
        city: '未知',
        adCode: '0',
        weather: '晴',
        temperature: '20',
        windDirection: '南风',
        windPower: '微风',
        humidity: '60',
        reportTime: DateTime.now().toString(),
        temperatureFloat: '20.0',
        humidityFloat: '60.0');
  }

  // Create default recommendation to avoid null exceptions
  FishingRecommendation _getDefaultRecommendation() {
    return FishingRecommendation(
        fishingIndex: 3.5, recommendedFish: [], weatherAnalysis: '天气适宜，建议钓鱼');
  }

  bool get isBusy => busy;

  // 为了兼容现有代码
  Future<void> loadWeatherData() async {
    await fetchWeatherData();
  }
}
