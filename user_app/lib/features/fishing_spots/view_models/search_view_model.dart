import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_search_service.dart';

class SearchViewModel extends ChangeNotifier {
  final FishingSpotSearchService _searchService;
  final SharedPreferences _prefs;

  SearchViewModel({
    required FishingSpotSearchService searchService,
    required SharedPreferences prefs,
  })  : _searchService = searchService,
        _prefs = prefs {
    _loadSearchHistory();
  }

  // Search results state
  List<SpotSummaryVo> _searchResults = [];
  List<SpotSummaryVo> get searchResults => _searchResults;

  bool _isSearching = false;
  bool get isSearching => _isSearching;

  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  // Search history
  List<String> _searchHistory = [];
  List<String> get searchHistory => _searchHistory;

  // Hot searches (can be loaded from backend later)
  final List<String> _hotSearches = [
    '草鱼塘',
    '路亚',
    '夜钓',
    '钓虾',
    '黑坑',
    '野钓',
    '免费钓场',
    '东湖'
  ];
  List<String> get hotSearches => _hotSearches;

  // Pagination
  int _currentPage = 0;
  bool _hasMore = true;
  bool get hasMore => _hasMore;

  static const String _historyKey = 'search_history';
  static const int _maxHistoryItems = 10;

  /// Search for fishing spots
  Future<void> searchSpots({
    required String query,
    bool refresh = false,
    double? latitude,
    double? longitude,
    double? radiusKm,
  }) async {
    if (query.trim().isEmpty) return;

    // Add to search history
    _addToSearchHistory(query);

    // Clear previous results when starting fresh search (refresh=true) or when it's a new query
    if (refresh) {
      _currentPage = 0;
      _searchResults.clear();
      _hasMore = true;
    }

    _isSearching = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final result = await _searchService.searchFishingSpots(
        query: query,
        page: _currentPage,
        pageSize: 20,
        latitude: latitude,
        longitude: longitude,
        radiusKm: radiusKm,
      );

      debugPrint('🔍 [SearchViewModel] Before update - Current results: ${_searchResults.length}');
      debugPrint('🔍 [SearchViewModel] Refresh flag: $refresh');
      debugPrint('🔍 [SearchViewModel] New spots from Algolia: ${result.spots.length}');
      
      if (refresh) {
        _searchResults = result.spots;
        debugPrint('🔍 [SearchViewModel] Replaced results with new spots');
      } else {
        _searchResults.addAll(result.spots);
        debugPrint('🔍 [SearchViewModel] Added spots to existing results');
      }

      _hasMore = _currentPage < result.nbPages - 1;
      _currentPage++;

      debugPrint('🔍 [SearchViewModel] After update - Final results: ${_searchResults.length}');
      debugPrint('🔍 [SearchViewModel] Final result names: ${_searchResults.map((r) => r.name).toList()}');
    } catch (e) {
      _errorMessage = '搜索失败，请稍后重试';
      debugPrint('❌ [SearchViewModel] Search error: $e');
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  /// Get nearby spots using current location
  Future<void> searchNearbySpots({
    required double latitude,
    required double longitude,
    double radiusKm = 50.0,
  }) async {
    _isSearching = true;
    _errorMessage = null;
    _searchResults.clear();
    notifyListeners();

    try {
      final spots = await _searchService.getNearbySpots(
        latitude: latitude,
        longitude: longitude,
        radiusKm: radiusKm,
        limit: 50,
      );

      _searchResults = spots;
      debugPrint('🔍 [SearchViewModel] Nearby search completed: ${_searchResults.length} results');
    } catch (e) {
      _errorMessage = '附近搜索失败，请稍后重试';
      debugPrint('❌ [SearchViewModel] Nearby search error: $e');
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }

  /// Add query to search history
  void _addToSearchHistory(String query) {
    if (query.trim().isEmpty) return;

    // Remove if already exists
    _searchHistory.remove(query);
    
    // Add to beginning
    _searchHistory.insert(0, query);
    
    // Limit history size
    if (_searchHistory.length > _maxHistoryItems) {
      _searchHistory = _searchHistory.take(_maxHistoryItems).toList();
    }
    
    _saveSearchHistory();
    notifyListeners();
  }

  /// Remove item from search history
  void removeFromHistory(String query) {
    _searchHistory.remove(query);
    _saveSearchHistory();
    notifyListeners();
  }

  /// Clear all search history
  void clearSearchHistory() {
    _searchHistory.clear();
    _saveSearchHistory();
    notifyListeners();
  }

  /// Clear current search results
  void clearSearchResults() {
    _searchResults.clear();
    _currentPage = 0;
    _hasMore = true;
    _errorMessage = null;
    notifyListeners();
  }

  /// Load search history from SharedPreferences
  void _loadSearchHistory() {
    final history = _prefs.getStringList(_historyKey) ?? [];
    _searchHistory = history.take(_maxHistoryItems).toList();
  }

  /// Save search history to SharedPreferences
  void _saveSearchHistory() {
    _prefs.setStringList(_historyKey, _searchHistory);
  }

  /// Retry search with last parameters
  void retry() {
    // Implementation depends on storing last search parameters
    notifyListeners();
  }
}