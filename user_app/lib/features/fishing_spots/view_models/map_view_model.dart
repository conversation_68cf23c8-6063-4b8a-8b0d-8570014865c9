import 'package:flutter/foundation.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/features/fishing_spots/models/spot_map_vo.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';

/// 地图视图专用的 ViewModel
/// 负责管理地图显示所需的轻量级钓点数据
class MapViewModel extends BaseViewModel {
  final FishingSpotService _fishingSpotService;

  // 地图数据
  List<SpotMapVo> _mapSpots = [];
  String? _errorMessage;
  bool _isBusy = false;

  // 分页信息
  int _currentPage = 0;
  int _totalPages = 1;
  bool _hasMore = true;
  final int _pageSize = 50; // 地图可以加载更多数据，因为数据量小

  // 筛选条件
  String? _filterType;
  List<String>? _selectedFishTypes;
  bool? _hasFacilities;
  bool? _hasParking;
  Map<String, double>? _mapBounds; // 地图边界

  // 位置和缩放级别
  double? _currentLatitude;
  double? _currentLongitude;
  double _currentZoomLevel = 12.0;
  double? _radiusKm; // 搜索半径

  // 数据加载策略
  bool _isLocationBasedLoading = false;
  bool _autoLoadOnRegionChange = true;
  Map<String, List<SpotMapVo>> _regionCache = {}; // 区域缓存

  // 状态管理
  bool _isInitializing = false; // 是否正在初始化
  String? _lastLoadedLocationKey; // 最后加载的位置key，防重复

  MapViewModel({required FishingSpotService fishingSpotService})
      : _fishingSpotService = fishingSpotService;

  // Getters
  List<SpotMapVo> get mapSpots => _mapSpots;
  String? get errorMessage => _errorMessage;
  bool get hasError => _errorMessage != null;
  bool get hasMore => _hasMore;
  int get totalSpots => _mapSpots.length;
  bool get isBusy => _isBusy;
  double get currentZoomLevel => _currentZoomLevel;
  bool get isLocationBasedLoading => _isLocationBasedLoading;
  double? get currentLatitude => _currentLatitude;
  double? get currentLongitude => _currentLongitude;
  bool get autoLoadOnRegionChange => _autoLoadOnRegionChange;

  // 状态管理方法
  @override
  void setBusy(bool value) {
    _isBusy = value;
    notifyListeners();
  }

  /// 加载地图钓点数据
  Future<void> loadMapSpots({bool refresh = false}) async {
    debugPrint('🗺️ [MapViewModel] loadMapSpots called with refresh=$refresh');

    if (isBusy) {
      debugPrint('🗺️ [MapViewModel] Already loading, skipping...');
      return;
    }

    if (refresh) {
      _resetPagination();
      debugPrint('🗺️ [MapViewModel] Pagination reset for refresh');
    }

    try {
      setBusy(true);
      _errorMessage = null;

      debugPrint('🗺️ [MapViewModel] Calling _fetchMapSpots...');
      final result = await _fetchMapSpots();
      debugPrint(
          '🗺️ [MapViewModel] _fetchMapSpots returned ${result.content.length} spots');

      if (refresh) {
        _mapSpots = result.content;
        debugPrint(
            '🗺️ [MapViewModel] Set _mapSpots to ${_mapSpots.length} spots (refresh)');
      } else {
        _mapSpots = [..._mapSpots, ...result.content];
        debugPrint(
            '🗺️ [MapViewModel] Added to _mapSpots, now ${_mapSpots.length} spots total');
      }

      _totalPages = result.totalPages;
      _currentPage = result.number;
      _hasMore = !result.last;

      debugPrint(
          '🗺️ [MapViewModel] Updated pagination: page=$_currentPage, totalPages=$_totalPages, hasMore=$_hasMore');

      notifyListeners();
    } catch (e) {
      debugPrint('❌ [MapViewModel] Error loading map spots: $e');
      _errorMessage = e.toString();

      if (refresh) {
        _mapSpots = [];
      }

      notifyListeners();
    } finally {
      setBusy(false);
    }
  }

  /// 加载更多地图数据
  Future<void> loadMore() async {
    if (isBusy || !_hasMore) return;

    _currentPage++;
    await loadMapSpots();
  }

  /// 应用筛选条件
  void applyFilters({
    String? filter,
    List<String>? fishTypes,
    bool? hasFacilities,
    bool? hasParking,
    Map<String, double>? bounds,
  }) {
    debugPrint('🗺️ [MapViewModel] Applying filters:');
    debugPrint('   - filter: $filter');
    debugPrint('   - fishTypes: $fishTypes');
    debugPrint('   - hasFacilities: $hasFacilities');
    debugPrint('   - hasParking: $hasParking');
    debugPrint('   - bounds: $bounds');

    _filterType = (filter == "全部") ? null : filter;
    _selectedFishTypes = (fishTypes?.isEmpty ?? true) ? null : fishTypes;
    _hasFacilities = hasFacilities;
    _hasParking = hasParking;
    _mapBounds = bounds;
  }

  /// 更新地图边界和缩放级别（当用户移动地图时调用）
  void updateMapRegion(Map<String, double> bounds, double zoomLevel) {
    _mapBounds = bounds;
    _currentZoomLevel = zoomLevel;

    // 计算中心点，但只有在边界有效且不是初始化时才更新坐标
    if (bounds.containsKey('north') &&
        bounds.containsKey('south') &&
        bounds.containsKey('east') &&
        bounds.containsKey('west')) {
      final newLat = (bounds['north']! + bounds['south']!) / 2;
      final newLng = (bounds['east']! + bounds['west']!) / 2;

      // 只有在新坐标有效（不是0,0）且与当前坐标有明显差异时才更新
      // 这避免了地图初始化时的错误坐标覆盖用户设置的坐标
      if (newLat != 0.0 && newLng != 0.0) {
        // 如果当前没有坐标，或者新坐标与当前坐标差异较大（用户真的移动了地图）
        if ((_currentLatitude == null || _currentLongitude == null) ||
            (_currentLatitude != null &&
                _currentLongitude != null &&
                ((_currentLatitude! - newLat).abs() > 0.001 ||
                    (_currentLongitude! - newLng).abs() > 0.001))) {
          debugPrint(
              '🗺️ [MapViewModel] Updating coordinates from map region:');
          debugPrint('   - Old: ($_currentLatitude, $_currentLongitude)');
          debugPrint('   - New: ($newLat, $newLng)');

          _currentLatitude = newLat;
          _currentLongitude = newLng;
        }
      } else {
        debugPrint(
            '🗺️ [MapViewModel] Ignoring invalid coordinates from map region: ($newLat, $newLng)');
      }
    }

    // 暂时禁用自动加载数据，避免在地图初始化时触发多次请求
    // 只有在用户主动移动地图时才应该重新加载数据
    // if (_autoLoadOnRegionChange && _shouldLoadDataForZoomLevel(zoomLevel)) {
    //   _loadDataForCurrentRegion();
    // }
  }

  /// 设置用户当前位置
  void setUserLocation(double latitude, double longitude) {
    _currentLatitude = latitude;
    _currentLongitude = longitude;
    notifyListeners();
  }

  /// 初始化地图视图 - 这是地图视图的主要业务入口
  ///
  /// 这个方法应该在用户切换到地图视图时调用，它会：
  /// 1. 获取用户位置（或使用默认位置）
  /// 2. 加载对应位置的钓点数据
  /// 3. 只在初始化时调用一次，避免重复请求
  Future<void> initializeMapView({
    double? defaultLatitude,
    double? defaultLongitude,
  }) async {
    if (_isInitializing) {
      debugPrint('🗺️ [MapViewModel] Already initializing, skipping...');
      return;
    }

    _isInitializing = true;

    try {
      debugPrint('🗺️ [MapViewModel] Starting map view initialization...');

      // 确定使用的坐标
      double lat = _currentLatitude ?? defaultLatitude ?? 39.9042; // 默认北京
      double lng = _currentLongitude ?? defaultLongitude ?? 116.4074;

      // 设置坐标（如果还没有设置的话）
      if (_currentLatitude == null || _currentLongitude == null) {
        setUserLocation(lat, lng);
      }

      // 启用基于位置的加载
      setLocationBasedLoading(true, radiusKm: 50.0);

      // 加载附近钓点数据
      await loadNearbySpots(
        latitude: lat,
        longitude: lng,
        refresh: true,
      );

      debugPrint('🗺️ [MapViewModel] Map view initialization completed');
    } catch (e) {
      debugPrint('❌ [MapViewModel] Map view initialization failed: $e');
      _errorMessage = '地图初始化失败: $e';
      notifyListeners();
    } finally {
      _isInitializing = false;
    }
  }

  /// 启用/禁用基于位置的数据加载
  void setLocationBasedLoading(bool enabled, {double? radiusKm}) {
    _isLocationBasedLoading = enabled;
    if (radiusKm != null) {
      _radiusKm = radiusKm;
    }
    notifyListeners();
  }

  /// 设置是否自动加载区域变化时的数据
  void setAutoLoadOnRegionChange(bool enabled) {
    _autoLoadOnRegionChange = enabled;
    notifyListeners();
  }

  /// 清除错误信息
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// 获取指定ID的钓点
  SpotMapVo? getSpotById(int id) {
    try {
      return _mapSpots.firstWhere((spot) => spot.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取指定区域内的钓点
  List<SpotMapVo> getSpotsInBounds(Map<String, double> bounds) {
    final north = bounds['north']!;
    final south = bounds['south']!;
    final east = bounds['east']!;
    final west = bounds['west']!;

    return _mapSpots.where((spot) {
      return spot.latitude >= south &&
          spot.latitude <= north &&
          spot.longitude >= west &&
          spot.longitude <= east;
    }).toList();
  }

  /// 基于当前位置加载附近钓点
  Future<void> loadNearbySpots({
    double? latitude,
    double? longitude,
    double? radiusKm,
    bool refresh = false,
  }) async {
    final lat = latitude ?? _currentLatitude;
    final lng = longitude ?? _currentLongitude;
    final radius = radiusKm ?? _radiusKm ?? 50.0;

    if (lat == null || lng == null) {
      debugPrint(
          '❌ [MapViewModel] Cannot load nearby spots: location not available');
      return;
    }

    // 防重复调用：如果正在加载中，直接返回
    if (isBusy) {
      debugPrint(
          '🗺️ [MapViewModel] Already loading nearby spots, skipping...');
      return;
    }

    // 防重复调用：检查是否是相同的位置和参数
    final locationKey =
        '${lat.toStringAsFixed(4)}_${lng.toStringAsFixed(4)}_$radius';
    if (!refresh && _lastLoadedLocationKey == locationKey) {
      debugPrint(
          '🗺️ [MapViewModel] Same location already loaded, skipping...');
      return;
    }

    if (refresh) {
      _resetPagination();
    }

    try {
      setBusy(true);
      _errorMessage = null;

      debugPrint(
          '🗺️ [MapViewModel] Loading nearby spots at ($lat, $lng) within ${radius}km');

      // 使用专门的地图API，直接返回SpotMapPageVo格式
      final mapPage = await _fishingSpotService.getNearbyMapSpots(
        latitude: lat,
        longitude: lng,
        page: _currentPage,
        pageSize: _pageSize,
        radiusKm: radius,
      );

      final mapSpots = mapPage.content;

      if (refresh) {
        _mapSpots = mapSpots;
      } else {
        _mapSpots = [..._mapSpots, ...mapSpots];
      }

      // 更新分页信息
      _totalPages = mapPage.totalPages;
      _currentPage = mapPage.number;
      _hasMore = !mapPage.last;

      // 记录最后加载的位置，防止重复调用
      _lastLoadedLocationKey = locationKey;

      debugPrint(
          '🗺️ [MapViewModel] Loaded ${mapSpots.length} nearby spots, total: ${_mapSpots.length}');

      notifyListeners();
    } catch (e) {
      debugPrint('❌ [MapViewModel] Error loading nearby spots: $e');
      _errorMessage = e.toString();
      if (refresh) {
        _mapSpots = [];
      }
      notifyListeners();
    } finally {
      setBusy(false);
    }
  }

  // PRIVATE METHODS

  /// 从服务获取地图钓点数据
  Future<dynamic> _fetchMapSpots() async {
    debugPrint('🗺️ [MapViewModel] _fetchMapSpots called with:');
    debugPrint('   - page: $_currentPage, size: $_pageSize');
    debugPrint('   - filterType: $_filterType');
    debugPrint('   - fishTypes: $_selectedFishTypes');
    debugPrint('   - hasFacilities: $_hasFacilities');
    debugPrint('   - hasParking: $_hasParking');
    debugPrint('   - bounds: $_mapBounds');
    debugPrint('   - _currentLatitude: $_currentLatitude');
    debugPrint('   - _currentLongitude: $_currentLongitude');
    debugPrint('   - _radiusKm: $_radiusKm');

    // 确定使用的坐标，如果坐标为null或者为0（无效坐标），使用默认坐标
    double lat = (_currentLatitude == null || _currentLatitude == 0.0)
        ? 39.9042
        : _currentLatitude!; // 默认北京坐标
    double lng = (_currentLongitude == null || _currentLongitude == 0.0)
        ? 116.4074
        : _currentLongitude!;
    double radius = _radiusKm ?? 50.0;

    debugPrint(
        '🗺️ [MapViewModel] Final coordinates to use: lat=$lat, lng=$lng, radius=$radius');

    return await _fishingSpotService.getFishingSpotsForMap(
      latitude: lat,
      longitude: lng,
      radius: radius,
      page: _currentPage,
      size: _pageSize,
      filterType: _filterType,
      fishTypes: _selectedFishTypes,
      hasFacilities: _hasFacilities,
      hasParking: _hasParking,
      bounds: _mapBounds,
    );
  }

  /// 重置分页状态
  void _resetPagination() {
    _currentPage = 0;
    _hasMore = true;
    _mapSpots = [];
  }

  /// 判断是否应该为当前缩放级别加载数据
  bool _shouldLoadDataForZoomLevel(double zoomLevel) {
    // 缩放级别大于10时才加载详细数据，避免过度加载
    return zoomLevel >= 10.0;
  }

  /// 为当前地图区域加载数据
  Future<void> _loadDataForCurrentRegion() async {
    if (_mapBounds == null) return;

    final cacheKey = _generateCacheKey(_mapBounds!, _currentZoomLevel);

    // 检查缓存
    if (_regionCache.containsKey(cacheKey)) {
      debugPrint('🗺️ [MapViewModel] Using cached data for region');
      _mapSpots = _regionCache[cacheKey]!;
      notifyListeners();
      return;
    }

    // 根据数据加载策略选择加载方式
    if (_isLocationBasedLoading &&
        _currentLatitude != null &&
        _currentLongitude != null) {
      await loadNearbySpots(refresh: true);
    } else {
      await loadMapSpots(refresh: true);
    }

    // 缓存结果
    _regionCache[cacheKey] = List.from(_mapSpots);

    // 限制缓存大小
    if (_regionCache.length > 10) {
      final oldestKey = _regionCache.keys.first;
      _regionCache.remove(oldestKey);
    }
  }

  /// 生成区域缓存键
  String _generateCacheKey(Map<String, double> bounds, double zoomLevel) {
    final rounded = bounds
        .map((key, value) => MapEntry(key, (value * 1000).round() / 1000));
    final zoomRounded = (zoomLevel * 10).round() / 10;
    return '${rounded.toString()}_$zoomRounded';
  }

  /// 清除区域缓存
  void clearRegionCache() {
    _regionCache.clear();
    debugPrint('🗺️ [MapViewModel] Region cache cleared');
  }

  /// Update the check-in count for a specific spot in map data
  void updateSpotCheckinCount(int spotId, int newCount) {
    final index = _mapSpots.indexWhere((spot) => spot.id == spotId);
    if (index != -1) {
      _mapSpots[index] = _mapSpots[index].copyWith(
        checkinCount: newCount,
      );
      notifyListeners();
      debugPrint('🗺️ [MapViewModel] Updated spot $spotId checkin count to $newCount');
    }
  }
}
