import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/services/moment_service.dart';

class MomentViewModel extends ChangeNotifier {
  final MomentService _momentService;

  bool _isLoading = false;
  String? _error;

  MomentViewModel(this._momentService);

  bool get isLoading => _isLoading;

  String? get error => _error;

  Future<String> uploadImage(List<int> bytes) async {
    // TODO: Implement actual image upload logic
    await Future.delayed(const Duration(seconds: 1));
    return 'https://example.com/image.jpg';
  }

  Future<void> publishMoment({
    required String content,
    required String momentType,
    required String visibility,
    FishingSpotVo? fishingSpot,
    required List<String> imageUrls,
    Map<String, dynamic>? typeSpecificData,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Use the MomentService to create the moment from the data
      await _momentService.createMomentFromData({
        'content': content,
        'momentType': momentType,
        'visibility': visibility,
        'imageUrls': imageUrls,
        'fishingSpotId': fishingSpot?.id,
        'typeSpecificData': typeSpecificData,
        'longitude': fishingSpot?.longitude.toString(),
        'latitude': fishingSpot?.latitude.toString(),
        'province': fishingSpot?.province,
        'city': fishingSpot?.city,
        'county': fishingSpot?.county,
        'addressDetail': fishingSpot?.address ?? '',
      });

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      rethrow;
    }
  }
}
