import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/models/spot_map_vo.dart';

/// 钓点信息气泡组件 - 在地图上显示钓点的简要信息
class SpotInfoBubble extends StatelessWidget {
  final SpotMapVo spot;
  final VoidCallback? onViewDetails;
  final VoidCallback? onNavigation;
  final VoidCallback? onClose;
  final bool triangleAtBottom; // 控制三角形位置

  const SpotInfoBubble({
    super.key,
    required this.spot,
    this.onViewDetails,
    this.onNavigation,
    this.onClose,
    this.triangleAtBottom = true, // 默认三角形在底部（气泡在标记上方）
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final bubbleWidget = Container(
          constraints: const BoxConstraints(
            maxWidth: 280,
            minWidth: 240,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.15),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with close button
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 12, 8, 0),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        spot.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    IconButton(
                      onPressed: onClose,
                      icon: const Icon(Icons.close, size: 18),
                      color: Colors.grey[600],
                      padding: const EdgeInsets.all(4),
                      constraints: const BoxConstraints(
                        minWidth: 24,
                        minHeight: 24,
                      ),
                    ),
                  ],
                ),
              ),

              // Address
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
                child: Text(
                  spot.address,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // Rating and tags row
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
                child: Row(
                  children: [
                    // Rating
                    if (spot.rating != null && spot.rating! > 0) ...[
                      Icon(
                        Icons.star,
                        size: 14,
                        color: Colors.amber[600],
                      ),
                      const SizedBox(width: 2),
                      Text(
                        spot.rating!.toStringAsFixed(1),
                        style: const TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],

                    // Payment status tag
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: spot.isPaid
                            ? Colors.orange.withValues(alpha: 0.1)
                            : Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        spot.isPaid ? '付费' : '免费',
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                          color: spot.isPaid
                              ? Colors.orange[700]
                              : Colors.green[700],
                        ),
                      ),
                    ),

                    const SizedBox(width: 6),

                    // Facilities tag
                    if (spot.hasFacilities)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          '有设施',
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w500,
                            color: Colors.blue[700],
                          ),
                        ),
                      ),

                    const Spacer(),

                    // Distance
                    if (spot.distance != null)
                      Text(
                        '${spot.distance!.toStringAsFixed(1)}km',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ),
              ),

              // Fish types
              if (spot.fishTypes.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '主要鱼类',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Wrap(
                        spacing: 4,
                        runSpacing: 4,
                        children: spot.fishTypes.take(3).map((fishType) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 3),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              fishType,
                              style: const TextStyle(
                                fontSize: 11,
                                color: Colors.black87,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),

              // Action buttons
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: onNavigation,
                        icon: const Icon(Icons.directions, size: 16),
                        label: const Text('导航'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: theme.primaryColor,
                          side: BorderSide(
                              color: theme.primaryColor.withValues(alpha: 0.5)),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          textStyle: const TextStyle(fontSize: 13),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: onViewDetails,
                        icon: const Icon(Icons.info_outline, size: 16),
                        label: const Text('详情'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.primaryColor,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          textStyle: const TextStyle(fontSize: 13),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );

    final triangleWidget = CustomPaint(
      painter: _BubbleTrianglePainter(pointingDown: triangleAtBottom),
      size: const Size(16, 8),
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: triangleAtBottom
          ? [bubbleWidget, triangleWidget] // 气泡在上，三角形在下（指向下方）
          : [triangleWidget, bubbleWidget], // 三角形在上（指向上方），气泡在下
    );
  }
}

/// 绘制气泡的小三角形指示器
class _BubbleTrianglePainter extends CustomPainter {
  final bool pointingDown;

  const _BubbleTrianglePainter({this.pointingDown = true});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final path = Path();
    
    if (pointingDown) {
      // 三角形指向下方（气泡在标记上方）
      path.moveTo(size.width / 2 - 8, 0); // 左上角
      path.lineTo(size.width / 2, size.height); // 底部中心点
      path.lineTo(size.width / 2 + 8, 0); // 右上角
    } else {
      // 三角形指向上方（气泡在标记下方）
      path.moveTo(size.width / 2 - 8, size.height); // 左下角
      path.lineTo(size.width / 2, 0); // 顶部中心点
      path.lineTo(size.width / 2 + 8, size.height); // 右下角
    }
    path.close();

    // 添加阴影
    canvas.drawShadow(path, Colors.black.withValues(alpha: 0.1), 4, false);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is _BubbleTrianglePainter && 
           oldDelegate.pointingDown != pointingDown;
  }
}
