import 'package:flutter/material.dart';
import 'package:user_app/utils/ui_helpers.dart';

class BasicInfoLocationStep extends StatelessWidget {
  final GlobalKey<FormState> formKey; // Pass if validation happens per step
  final TextEditingController nameController;
  final TextEditingController addressController;
  final TextEditingController descriptionController;
  final double latitude;
  final double longitude;
  final String formattedAddress;
  final bool useCurrentLocation;
  final bool isLoadingLocation; // For UI feedback if needed
  final VoidCallback onShowMapModal;
  final String Function() getSuggestedName; // Function to get suggestion

  const BasicInfoLocationStep({
    super.key,
    required this.formKey,
    required this.nameController,
    required this.addressController,
    required this.descriptionController,
    required this.latitude,
    required this.longitude,
    required this.formattedAddress,
    required this.useCurrentLocation,
    required this.isLoadingLocation,
    required this.onShowMapModal,
    required this.getSuggestedName,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildSectionHeader(
            context, '基本信息和位置', Icons.info_outline, '选择钓点位置并填写基本信息'),
        const SizedBox(height: 24),

        const Text('位置信息',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
        const SizedBox(height: 16),

        // Location Selection Card
        Container(
          decoration: BoxDecoration(
            /* Card styling */
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2))
            ],
          ),
          child: latitude != 0 && longitude != 0
              ? Column(
                  /* Display selected location info and Edit button */
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            /* Title + Edit Button */
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(children: [
                                Icon(Icons.location_on,
                                    size: 18,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondary),
                                const SizedBox(width: 8),
                                Text(useCurrentLocation ? '已选择当前位置' : '已选择位置',
                                    style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 15,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .secondary)),
                              ]),
                              OutlinedButton.icon(
                                onPressed: onShowMapModal,
                                // Use callback
                                icon: const Icon(Icons.edit_location_alt,
                                    size: 16),
                                label: const Text('修改位置'),
                                style: OutlinedButton.styleFrom(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    visualDensity: VisualDensity.compact),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(formattedAddress,
                              style: TextStyle(
                                  color: Colors.grey.shade700, fontSize: 14),
                              maxLines: 2),
                          const SizedBox(height: 4),
                          Text(
                              '经度: ${longitude.toStringAsFixed(6)}, 纬度: ${latitude.toStringAsFixed(6)}',
                              style: TextStyle(
                                  fontSize: 12, color: Colors.grey.shade500)),
                        ],
                      ),
                    ),
                  ],
                )
              : Padding(
                  /* Prompt to select location */
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.location_on_outlined,
                          size: 40,
                          color: Theme.of(context)
                              .colorScheme
                              .primary
                              .withOpacity(0.7)),
                      const SizedBox(height: 12),
                      const Text("请选择钓点位置",
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      const Text("标记位置可以帮助您记录和找到钓点，也可以选择是否与其他钓友分享",
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey, fontSize: 13)),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: onShowMapModal,
                        // Use callback
                        icon: const Icon(Icons.map),
                        label: const Text("打开地图选择位置"),
                        style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12)),
                      ),
                    ],
                  ),
                ),
        ),

        const SizedBox(height: 24),

        // Conditional Fields (Address, Name, Description)
        if (latitude != 0 && longitude != 0) ...[
          // Detailed Address TextFormField using addressController
          buildTextField(
            context: context,
            controller: addressController,
            label: '详细地址',
            hintText: '省市区详细地址',
            prefixIcon: Icons.location_on,
            maxLines: 2,
            mandatory: true,
            validator: (value) {
              if (value == null || value.isEmpty) return '请输入详细地址';
              return null;
            },
          ),

          const SizedBox(height: 24),

          // Suggested Name Box
          if (formattedAddress.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: Container(
                /* Suggestion box styling */
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                    color: Colors.amber.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.amber.withOpacity(0.3))),
                child: Row(
                  children: [
                    Icon(Icons.lightbulb_outline,
                        color: Colors.amber.shade700, size: 24),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        /* Suggestion text */
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('钓点名称建议',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.amber.shade700)),
                          const SizedBox(height: 4),
                          Text(getSuggestedName(),
                              style: const TextStyle(
                                  fontSize: 14)), // Use callback
                        ],
                      ),
                    ),
                    TextButton(
                      // Use suggestion button
                      onPressed: () {
                        nameController.text = getSuggestedName();
                        // Force a rebuild of the widget to update any state
                        (context as Element).markNeedsBuild();
                      },
                      style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          minimumSize: const Size(50, 36)),
                      // Use callback and controller
                      child: const Text('使用'),
                    ),
                  ],
                ),
              ),
            ),

          // Spot Name TextFormField using nameController
          buildTextField(
            context: context,
            controller: nameController,
            label: '钓点名称',
            hintText: '例如: 青城湖水库',
            prefixIcon: Icons.place,
            mandatory: true,
            validator: (value) {
              if (value == null || value.isEmpty) return '请输入钓点名称';
              return null;
            },
          ),

          const SizedBox(height: 24),

          // Spot Description TextFormField using descriptionController
          buildTextField(
            context: context,
            controller: descriptionController,
            label: '钓点描述',
            hintText: '描述钓点环境、水域特点、周边配套等... (可选)',
            prefixIcon: Icons.description,
            // Changed icon for consistency
            maxLines: 4,
            // validator: null, // Optional field
          ),
        ],
      ],
    );
  }
}
