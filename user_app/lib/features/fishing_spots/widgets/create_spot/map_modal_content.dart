import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart'; // Ensure this import is correct

class LocationResult {
  final double latitude;
  final double longitude;
  final String formattedAddress;
  final String province;
  final String city;
  final String county;

  final bool useCurrentLocation;

  LocationResult({
    required this.latitude,
    required this.longitude,
    required this.formattedAddress,
    required this.province,
    required this.city,
    required this.county,
    required this.useCurrentLocation,
  });
}

class MapModalContent extends StatefulWidget {
  final double initialLatitude;

  final double initialLongitude;

  /// An optional initial address to display. If empty and initial lat/lon are provided,
  /// it will attempt to reverse geocode.
  final String initialFormattedAddress;

  const MapModalContent({
    super.key,
    required this.initialLatitude,
    required this.initialLongitude,
    this.initialFormattedAddress = '', // Default to empty
  });

  @override
  State<MapModalContent> createState() => _MapModalContentState();
}

class _MapModalContentState extends State<MapModalContent> {
  AMap2DController? _mapController;
  final _searchController = TextEditingController();
  List<PoiSearch> _searchResults = [];
  bool _isSearching = false; // Indicates if a POI search is in progress
  bool _isLoadingLocation =
      false; // Indicates general map/location operations (geocoding, initial load, marker add)
  bool _isMapInitialized =
      false; // Tracks if the map controller has been created

  // State for the currently selected location within the modal
  double _modalLatitude = 0.0;
  double _modalLongitude = 0.0;
  String _modalFormattedAddress = '';
  String _modalProvince = '';
  String _modalCity = '';
  String _modalCounty = '';
  bool _modalUseCurrentLocation = false;

  // Flags to manage initial state and prevent race conditions
  bool _hasProcessedInitialLocation = false;
  bool _searchAttempted =
      false; // Tracks if a search was performed, even if no results

  @override
  void initState() {
    super.initState();
    _modalLatitude = widget.initialLatitude;
    _modalLongitude = widget.initialLongitude;
    _modalFormattedAddress = widget.initialFormattedAddress;
  }

  @override
  void dispose() {
    _searchController.dispose(); // Dispose the text controller first

    // --- Safely handle map controller disposal ---
    if (_mapController != null) {
      try {
        // Attempt to clear markers, but expect it might fail
        _mapController!.clearMarkers();
        print("AMap markers cleared during dispose."); // Optional log
      } catch (e) {
        // Catch potential errors if the underlying map/JS is already gone
        print("Caught error clearing markers during dispose: $e");
        // Don't rethrow; we are disposing anyway.
      } finally {
        // *Always* nullify the controller reference *after* attempting cleanup
        _mapController = null;
        print("AMap controller reference nullified."); // Optional log
      }
    }
    // --- End safe handling ---

    super.dispose(); // Call the framework's dispose last
  }

  Future<void> _onMapCreated(AMap2DController controller) async {
    if (!mounted) return;

    _mapController = controller;

    // CRITICAL: Set _isMapInitialized synchronously *before* any async gaps.
    // Also trigger a rebuild to potentially remove the initial loading overlay.
    setState(() {
      _isMapInitialized = true;
      _isLoadingLocation = true; // Start loading indicator for initial setup
    });

    await Future.delayed(const Duration(milliseconds: 150));

    if (!mounted || _mapController == null) {
      // If disposed, ensure loading stops if it was still true
      if (mounted && _isLoadingLocation) {
        setState(() => _isLoadingLocation = false);
      }
      return;
    }

    try {
      // Basic map setup
      await _mapController!.setZoom(zoomLevel: 14.0);

      // --- Handle Initial Location ---
      if (_modalLatitude != 0 && _modalLongitude != 0) {
        _hasProcessedInitialLocation = true;

        if (!mounted || _mapController == null) return;

        await _mapController!.move(
          _modalLatitude.toString(),
          _modalLongitude.toString(),
        );
        await _mapController!.addMarkers([
          {
            'latitude': _modalLatitude.toString(),
            'longitude': _modalLongitude.toString(),
          }
        ]);

        if (_modalFormattedAddress.isEmpty) {
          if (mounted && _mapController != null) {
            await _mapController!.reGeocode(_modalLatitude, _modalLongitude);
          } else if (mounted) {
            setState(() => _isLoadingLocation = false);
          }
        } else {
          if (mounted) {
            setState(() => _isLoadingLocation = false);
          }
        }
      } else {
        _hasProcessedInitialLocation = false;

        if (!mounted || _mapController == null) {
          if (mounted && _isLoadingLocation) {
            setState(() => _isLoadingLocation = false);
          }
          return;
        }

        bool locationTimedOut = false;
        Timer locationTimer = Timer(const Duration(seconds: 10), () {
          locationTimedOut = true;
          // Check if still mounted, still loading, and haven't received a location yet
          if (mounted && _isLoadingLocation && !_hasProcessedInitialLocation) {
            setState(() {
              _isLoadingLocation = false; // Stop loading on timeout
            });
            _showErrorSnackBar('获取位置超时，请手动选择位置或重试');
            // Default to a fallback location (e.g., Beijing)
            if (mounted && _mapController != null) {
              _mapController!.move('39.909187', '116.397451');
              // Attempt to add marker, might fail if controller issues persist
              try {
                _mapController!.addMarkers([
                  {
                    'latitude': '39.909187',
                    'longitude': '116.397451',
                  }
                ]);
              } catch (e) {
                print("Error adding default marker after timeout: $e");
              }
            }
          }
        });

        // Ensure map controller is still valid before calling location()
        if (mounted && _mapController != null) {
          await _mapController!.location().catchError((e) {
            locationTimer.cancel(); // Cancel timer on error
            if (mounted && !locationTimedOut) {
              // Avoid acting if timeout already happened
              setState(() => _isLoadingLocation = false);
              // Provide more specific feedback based on common AMap errors
              if (e.toString().contains('timeout') ||
                  e.toString().contains('Get geolocation timeout')) {
                _showErrorSnackBar('获取位置超时，请检查定位权限或网络连接');
              } else if (e.toString().contains('Get ipLocation failed')) {
                _showErrorSnackBar('IP定位失败，请手动选择位置');
              } else if (e.toString().contains('permission')) {
                _showErrorSnackBar('定位权限不足，请检查应用设置');
              } else {
                _showErrorSnackBar(
                    '获取位置失败: ${e.toString().substring(0, 100)}...'); // Limit error length
              }

              // Move to fallback location on error
              if (mounted && _mapController != null) {
                _mapController!.move('39.909187', '116.397451');
                try {
                  _mapController!.addMarkers([
                    {
                      'latitude': '39.909187',
                      'longitude': '116.397451',
                    }
                  ]);
                } catch (addMarkerError) {
                  print(
                      "Error adding default marker after location error: $addMarkerError");
                }
              }
            }
          });

          if (!locationTimedOut) {
            locationTimer.cancel();
          }
        } else {
          locationTimer.cancel();
          if (mounted) {
            setState(() => _isLoadingLocation = false);
            _showErrorSnackBar('地图控制器无效，无法获取位置');
          }
        }
      }
    } catch (e) {
      print('Error during map initialization: $e');
      if (mounted) {
        setState(() => _isLoadingLocation = false);
        _showErrorSnackBar('地图初始化失败: ${e.toString().substring(0, 100)}...');
      }
    }
  }

  /// Callback when the device's current location is obtained via `controller.location()`.
  void _onGetLocation(LngLat location) async {
    if (!mounted ||
        _mapController == null ||
        !_isMapInitialized ||
        _hasProcessedInitialLocation) {
      // Ensure loading stops if this callback fires unexpectedly after initial setup was done
      if (mounted && _isLoadingLocation && !_hasProcessedInitialLocation) {
        setState(() => _isLoadingLocation = false);
      } else if (!mounted) {}
      return;
    }

    // Even if we are already loading, ensure it stays true
    if (mounted && !_isLoadingLocation) {
      setState(() {
        _isLoadingLocation = true;
      });
    }

    _hasProcessedInitialLocation =
        true; // Mark that we now have *a* location (either initial or current)

    try {
      final newLat = location.latitude.toDouble(); // Use null-aware access
      final newLon = location.longitude.toDouble();

      // Validate coordinates (AMap might return null or 0,0 on failure)
      if ((newLat == 0 && newLon == 0)) {
        throw Exception('获取到的当前位置坐标无效');
      }

      // Clear previous markers and add one for the current location
      await _mapController!.clearMarkers();
      await _mapController!.addMarkers([
        {
          'latitude': newLat.toString(),
          'longitude': newLon.toString(),
        }
      ]);

      // Update modal state *before* async gap of reGeocode
      if (mounted) {
        setState(() {
          _modalLatitude = newLat;
          _modalLongitude = newLon;
          _modalUseCurrentLocation = true; // Mark as current location
          _modalFormattedAddress =
              ''; // Clear old address while fetching new one
          _searchResults = []; // Clear any previous search results
          _searchController.clear(); // Clear search input
          _searchAttempted = false;
        });
      }

      // Move map view to the current location
      await _mapController!.move(newLat.toString(), newLon.toString());

      // Trigger reverse geocoding to get the address
      // Ensure controller is still valid
      if (mounted && _mapController != null) {
        await _mapController!.reGeocode(newLat, newLon);
        // _isLoadingLocation will be set to false in _onReGeocode
      } else if (mounted) {
        // If controller became invalid, stop loading here
        setState(() => _isLoadingLocation = false);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoadingLocation = false); // Stop loading on error
        _showErrorSnackBar('设置当前位置失败: $e');
        // Optionally move to a default location here as well
      }
    }
    // Note: _isLoadingLocation is primarily turned off by _onReGeocode upon success
  }

  /// Callback when the user clicks on the map.
  void _onMapClick(num? lat, num? lon) async {
    // Handle nullable inputs
    // Prevent interaction if map not ready or already processing something
    if (!mounted ||
        _mapController == null ||
        !_isMapInitialized ||
        _isLoadingLocation) return;

    final clickedLat = lat?.toDouble();
    final clickedLon = lon?.toDouble();

    // Validate click coordinates
    if (clickedLat == null ||
        clickedLon == null ||
        (clickedLat == 0 && clickedLon == 0)) {
      _showErrorSnackBar('无效的点击坐标');
      return;
    }

    setState(() {
      _isLoadingLocation =
          true; // Show loading for marker and geocode operations
    });

    try {
      // Clear previous markers and add one at the clicked location
      await _mapController!.clearMarkers();
      await _mapController!.addMarkers([
        {
          'latitude': clickedLat.toString(),
          'longitude': clickedLon.toString(),
        }
      ]);

      // Update modal state *before* async gap of reGeocode
      if (mounted) {
        setState(() {
          _modalLatitude = clickedLat;
          _modalLongitude = clickedLon;
          _modalUseCurrentLocation = false; // Mark as manually selected
          _searchResults = []; // Clear search results
          _searchController.clear(); // Clear search input
          _searchAttempted = false;
          _modalFormattedAddress =
              ''; // Clear old address while fetching new one
        });
      }

      // Trigger reverse geocoding for the clicked coordinates
      // Ensure controller is still valid
      if (mounted && _mapController != null) {
        await _mapController!.reGeocode(clickedLat, clickedLon);
        // _isLoadingLocation will be set to false in _onReGeocode
      } else if (mounted) {
        setState(() => _isLoadingLocation = false);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoadingLocation = false); // Stop loading on error
        _showErrorSnackBar('获取点击位置信息失败: $e');
      }
    }
  }

  /// Callback when reverse geocoding results are available.
  void _onReGeocode(ReGeocodeResult? result) {
    // Handle nullable result
    if (!mounted) return;

    // Ensure loading stops regardless of result validity
    if (_isLoadingLocation) {
      setState(() => _isLoadingLocation = false);
    }

    if (result == null ||
        result.regeocode.formattedAddress == null ||
        result.regeocode.formattedAddress!.isEmpty) {
      // Keep lat/lon, but show address error state
      setState(() {
        _modalFormattedAddress = '无法获取地址信息';
        _modalProvince = '';
        _modalCity = '';
        _modalCounty = '';
      });
      _showErrorSnackBar('无法获取该位置的地址信息');
      return;
    }

    // Successfully received geocode results
    setState(() {
      // Extract address components safely
      final addressComponent = result.regeocode.addressComponent;
      _modalProvince = addressComponent?.province ?? '';
      _modalCity = addressComponent?.city ?? '';
      // AMap sometimes returns city in district field for municipalities (like Shanghai, Beijing)
      // Use province if city is empty but province is not
      if (_modalCity.isEmpty && _modalProvince.isNotEmpty) {
        // Check if province looks like a municipality name
        if (['北京市', '上海市', '天津市', '重庆市'].contains(_modalProvince)) {
          _modalCity = _modalProvince;
        } else {
          // Otherwise, maybe it's just a province-level entity without a city field?
          // Use citycode if available for city name? AMap behavior can vary.
          // For now, we fallback to province if city is truly empty.
          _modalCity =
              _modalProvince; // Or keep it empty if that's more accurate
        }
      }
      _modalCounty = addressComponent?.district ?? '';

      _modalFormattedAddress = result
          .regeocode.formattedAddress; // Use ! since we checked for null/empty

      // Ensure loading indicator is off (double check, belt-and-suspenders)
      _isLoadingLocation = false;
    });
  }

  /// Initiates a POI search based on the input query.
  void _performSearch(String query) {
    final trimmedQuery = query.trim();

    // Check map readiness *before* proceeding
    if (!mounted || _mapController == null || !_isMapInitialized) {
      _showErrorSnackBar('地图尚未准备好，请稍候再试');
      return;
    }

    // If query is empty, just clear results and state
    if (trimmedQuery.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
        _searchAttempted = false; // Reset search attempt state
      });
      return;
    }

    FocusScope.of(context).unfocus(); // Hide keyboard

    setState(() {
      _isSearching = true; // Show search loading indicator
      _searchResults = []; // Clear previous results immediately
      _searchAttempted = true; // Mark that a search was attempted
    });

    try {
      // Call the map controller's search function
      _mapController!.search(trimmedQuery);
      // Results will be handled by _onPoiSearched
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSearching = false; // Turn off loading on error
          // _searchAttempted remains true
        });
        _showErrorSnackBar('搜索功能调用失败: $e');
      }
    }
  }

  /// Callback when POI search results are received.
  void _onPoiSearched(List<PoiSearch>? poiList) {
    // Handle nullable list
    if (!mounted) return;

    // Always stop searching state, even if list is null/empty
    setState(() {
      _isSearching = false;
      _searchResults = poiList ?? []; // Use empty list if null
      // _searchAttempted is already true from _performSearch
    });

    if (poiList == null || poiList.isEmpty) {
      // The build method will show the "no results" message based on flags
    }
  }

  /// Handles selection of a POI from the search results list.
  void _selectSearchResult(PoiSearch poi) async {
    // Check readiness
    if (!mounted ||
        _mapController == null ||
        !_isMapInitialized ||
        _isLoadingLocation) {
      return;
    }

    // Validate POI coordinates before proceeding
    final lat = double.tryParse(poi.latitude ?? "0");
    final lon = double.tryParse(poi.longitude ?? "0");

    if (lat == null || lon == null || lat == 0 || lon == 0) {
      _showErrorSnackBar('选择的位置坐标无效');
      return;
    }

    FocusScope.of(context).unfocus(); // Hide keyboard

    // Show loading indicator for map operations and potential re-geocode
    setState(() {
      _isLoadingLocation = true;
      _searchResults = []; // Hide results list immediately on selection
      _searchAttempted = false; // Reset search attempt state
      _modalFormattedAddress = ''; // Clear old address temporarily
    });

    try {
      // Clear existing markers and add one for the selected POI
      await _mapController!.clearMarkers();
      await _mapController!.addMarkers([
        {
          'latitude': lat.toString(),
          'longitude': lon.toString(),
        }
      ]);
      // Move map view to the POI location
      await _mapController!.move(lat.toString(), lon.toString());

      // ---- Update modal state with POI details ----
      final province = poi.provinceName ?? '';
      final city = poi.cityName ??
          (province.isNotEmpty &&
                  ['北京市', '上海市', '天津市', '重庆市'].contains(province)
              ? province
              : ''); // Handle municipalities better
      final county = poi.adName ?? '';
      final addressDetail =
          poi.address ?? ''; // Snippet or specific address part
      // Construct a reasonable formatted address from POI data if title/address exist
      String constructedFormattedAddress = '';
      if ((poi.title ?? '').isNotEmpty &&
          addressDetail.isNotEmpty &&
          !addressDetail.contains(poi.title!)) {
        // If address doesn't already seem to contain the title, prepend province/city/county
        constructedFormattedAddress =
            '$province$city$county$addressDetail'.replaceAll(' ', '');
      } else if (addressDetail.isNotEmpty) {
        // If address seems complete or title is missing, use address primarily
        constructedFormattedAddress =
            '$province$city$county$addressDetail'.replaceAll(' ', '');
      } else {
        // Fallback if address is missing
        constructedFormattedAddress =
            '$province$city$county${poi.title ?? ''}'.replaceAll(' ', '');
      }

      // Use POI title if constructed address is still empty
      final displayAddress = constructedFormattedAddress.isNotEmpty
          ? constructedFormattedAddress
          : (poi.title ?? '选定位置');
      final displayTitle = poi.title ?? '选定位置';

      if (mounted) {
        setState(() {
          _modalLatitude = lat;
          _modalLongitude = lon;
          _modalUseCurrentLocation = false; // Selected from search

          // Update address fields directly from POI
          _modalProvince = province;
          _modalCity = city.isNotEmpty
              ? city
              : (province.isNotEmpty
                  ? province
                  : ''); // Ensure city has a value if possible
          _modalCounty = county;
          _modalFormattedAddress = displayAddress; // Use the determined address

          // Update search bar text to reflect selection, move cursor to end
          _searchController.text = displayTitle;
          _searchController.selection = TextSelection.fromPosition(
            TextPosition(offset: _searchController.text.length),
          );
        });
      }
      // ---- End Update modal state ----

      // Optionally show a brief confirmation
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已定位到: $displayTitle'),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(milliseconds: 1500), // Shorter duration
            margin: const EdgeInsets.only(
                bottom: 100, left: 20, right: 20), // Position above button
          ),
        );
      }

      // Re-Geocode Condition: Trigger if the address we got from the POI seems basic
      // (e.g., just the title, or missing details like province/city/county)
      // Or if the constructed address is identical to just the title (less likely to be full)
      bool shouldReGeocode = displayAddress == displayTitle ||
          province.isEmpty ||
          city.isEmpty ||
          county.isEmpty;

      if (shouldReGeocode) {
        if (mounted && _mapController != null) {
          // Re-check mounted and controller before async call
          await _mapController!.reGeocode(lat, lon);
          // _isLoadingLocation will be set to false in _onReGeocode
        } else if (mounted) {
          // Controller became null or unmounted before regeocode call
          setState(() => _isLoadingLocation = false);
        }
      } else {
        if (mounted) {
          setState(() => _isLoadingLocation = false);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingLocation = false; // Turn off loading on error
        });
        _showErrorSnackBar('设置搜索结果位置失败: $e');
      }
    } finally {
      // Ensure loading always stops, even if reGeocode wasn't called or an intermediate error occurred
      if (mounted && _isLoadingLocation) {
        setState(() => _isLoadingLocation = false);
      }
    }
  }

  /// Confirms the currently selected location and returns it via Navigator.pop.
  void _confirmLocation() {
    // Validate that a location has been selected
    if (_modalLatitude == 0 && _modalLongitude == 0) {
      _showErrorSnackBar('请先在地图上选择一个位置');
      return;
    }

    // Validate that address is available and not the error message
    if (_modalFormattedAddress.isEmpty ||
        _modalFormattedAddress == '无法获取地址信息') {
      _showErrorSnackBar('正在获取地址信息或地址无效，请稍候或重新选择');

      // Optionally trigger reGeocode again if needed and map is ready and not already loading
      if (!_isLoadingLocation && _mapController != null && _isMapInitialized) {
        setState(() => _isLoadingLocation = true);
        _mapController!
            .reGeocode(_modalLatitude, _modalLongitude)
            .catchError((e) {
          if (mounted) {
            setState(() => _isLoadingLocation = false);
            if (e.toString().contains('timeout')) {
              _showErrorSnackBar('获取地址信息超时，请检查网络连接');
            } else {
              _showErrorSnackBar('尝试获取地址失败: $e');
            }
          }
        }).whenComplete(() {
          // Ensure loading stops even if reGeocode completes but _onReGeocode doesn't fire correctly
          if (mounted && _isLoadingLocation) {
            // A small delay allows _onReGeocode to potentially update the state first
            Future.delayed(Duration(milliseconds: 100), () {
              if (mounted && _isLoadingLocation) {
                setState(() => _isLoadingLocation = false);
              }
            });
          }
        });
      }
      return; // Don't pop yet
    }

    // All checks passed, create the result object
    final result = LocationResult(
      latitude: _modalLatitude,
      longitude: _modalLongitude,
      formattedAddress: _modalFormattedAddress,
      province: _modalProvince,
      city: _modalCity,
      county: _modalCounty,
      useCurrentLocation: _modalUseCurrentLocation,
    );

    Navigator.pop(context, result);
  }

  /// Helper to show a standardized error SnackBar.
  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context)
        .removeCurrentSnackBar(); // Remove previous snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.red.shade700,
        // Position slightly higher to avoid confirm button
        margin: const EdgeInsets.fromLTRB(20, 0, 20, 100),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // --- Build Method ---
  @override
  Widget build(BuildContext context) {
    // Determine if search results should be shown
    final bool showSearchResults = !_isSearching && _searchResults.isNotEmpty;
    // Determine if "no results" message should be shown
    final bool showNoResults = !_isSearching &&
        _searchAttempted &&
        _searchResults.isEmpty &&
        _searchController.text.isNotEmpty;

    // Check if confirmation should be disabled
    final bool isConfirmDisabled = _isLoadingLocation || !_isMapInitialized;

    return DraggableScrollableSheet(
      initialChildSize: 0.9,
      // Start slightly larger
      minChildSize: 0.4,
      maxChildSize: 0.95,
      // Allow nearly full screen
      expand: false,
      // MUST be false for modal behavior
      builder: (context, scrollController) {
        // Using a Column directly within the sheet's builder
        return Container(
          // Add decoration to the container returned by builder
          decoration: BoxDecoration(
            color: Theme.of(context).canvasColor, // Use theme background
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -2))
            ],
          ),
          child: Column(
            children: [
              // --- Draggable Handle ---
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Center(
                  child: Container(
                    width: 40,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade400,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ),

              // --- Header (Title & Close) ---
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 8, 4),
                // Reduced padding
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.initialLatitude != 0 &&
                              widget.initialLongitude != 0
                          ? '修改位置'
                          : '选择位置',
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontWeight: FontWeight.w600),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close_rounded),
                      padding: EdgeInsets.zero,
                      visualDensity: VisualDensity.compact,
                      tooltip: '关闭',
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),

              // --- Search Bar ---
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: '搜索地点、小区、地标...',
                    hintStyle: TextStyle(color: Colors.grey.shade500),
                    filled: true,
                    fillColor: Colors.grey.shade100,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          BorderSide.none, // No border needed when filled
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                          color: Theme.of(context).primaryColor,
                          width: 1.5), // Highlight when focused
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 10),
                    // Adjust vertical padding
                    // Show loading indicator OR search icon
                    prefixIcon: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: _isSearching
                          ? const SizedBox(
                              width: 18,
                              height: 18,
                              child:
                                  CircularProgressIndicator(strokeWidth: 2.5))
                          : Icon(Icons.search_rounded,
                              size: 22, color: Colors.grey.shade600),
                    ),
                    // Clear button
                    suffixIcon:
                        _searchController.text.isNotEmpty && !_isSearching
                            ? IconButton(
                                icon: const Icon(Icons.cancel_rounded,
                                    size: 20, color: Colors.grey),
                                tooltip: '清除',
                                onPressed: () {
                                  _searchController.clear();
                                  // Hide keyboard and clear results
                                  FocusScope.of(context).unfocus();
                                  setState(() {
                                    _searchResults = [];
                                    _searchAttempted = false;
                                    _isSearching = false;
                                  });
                                },
                              )
                            : null, // No suffix icon otherwise
                  ),
                  onChanged: (value) {
                    // Clear results immediately if user clears text
                    if (value.isEmpty &&
                        (_searchResults.isNotEmpty || _searchAttempted)) {
                      setState(() {
                        _searchResults = [];
                        _searchAttempted = false;
                      });
                    } else if (mounted) {
                      // Just trigger rebuild to potentially show/hide clear button
                      setState(() {});
                    }
                  },
                  onSubmitted: _performSearch,
                  // Trigger search on submit
                  textInputAction: TextInputAction.search,
                ),
              ),

              // --- Search Results / No Results ---
              // Use AnimatedSize for smoother transitions
              AnimatedSize(
                duration: const Duration(milliseconds: 250),
                curve: Curves.easeInOutCubic,
                child: Column(
                    // Required for AnimatedSize target
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Note: _isSearching indicator is now inside the TextField prefix
                      if (showNoResults)
                        Container(
                          width: double.infinity,
                          margin: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                                color: Colors.orange.shade200, width: 0.5),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.warning_amber_rounded,
                                  color: Colors.orange.shade700, size: 18),
                              const SizedBox(width: 10),
                              Flexible(
                                child: Text(
                                  '未找到与"${_searchController.text}"相关的地点',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      color: Colors.orange.shade800,
                                      fontSize: 13),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        )
                      else if (showSearchResults)
                        Container(
                          constraints: BoxConstraints(
                            // Limit height, e.g., max 25% of screen height
                            maxHeight:
                                MediaQuery.of(context).size.height * 0.25,
                          ),
                          margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
                          decoration: BoxDecoration(
                            color: Theme.of(context).cardColor,
                            // Use card color for list background
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                                color: Colors.grey.shade200, width: 0.5),
                          ),
                          clipBehavior: Clip.antiAlias,
                          // Clip list items to rounded corners
                          // Use ListView directly, scrolls independently
                          child: ListView.separated(
                            // controller: scrollController, // DO NOT USE SHEET CONTROLLER HERE
                            shrinkWrap: true,
                            // Important for constraints
                            padding: EdgeInsets.zero,
                            itemCount: _searchResults.length,
                            itemBuilder: (context, index) {
                              final poi = _searchResults[index];
                              final title = poi.title ?? '未知位置';
                              // Construct subtitle more reliably
                              final province = poi.provinceName ?? '';
                              final city = poi.cityName ?? '';
                              final district = poi.adName ?? '';
                              final address = poi.address ?? '';
                              final subtitleParts = [
                                city,
                                district,
                                address
                              ] // Often province is redundant here
                                  .where((s) => s.isNotEmpty)
                                  .join(' ')
                                  .trim();
                              final displaySubtitle = subtitleParts.isNotEmpty
                                  ? subtitleParts
                                  : '详细地址未知';

                              return ListTile(
                                dense: true,
                                title: Text(title,
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 14.5)),
                                subtitle: Text(
                                  displaySubtitle,
                                  style: TextStyle(
                                      fontSize: 12.5,
                                      color: Colors.grey.shade600),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 16, vertical: 4),
                                // Adjust padding
                                leading: CircleAvatar(
                                    radius: 18,
                                    backgroundColor: Theme.of(context)
                                        .primaryColor
                                        .withOpacity(0.1),
                                    child: Icon(Icons.location_on_outlined,
                                        size: 18,
                                        color: Theme.of(context).primaryColor)),
                                trailing: Icon(Icons.chevron_right_rounded,
                                    size: 20, color: Colors.grey.shade400),
                                onTap: () => _selectSearchResult(poi),
                              );
                            },
                            separatorBuilder: (context, index) => Divider(
                                height: 0.5,
                                thickness: 0.5,
                                color: Colors.grey.shade200,
                                indent: 60,
                                endIndent: 16), // Add dividers
                          ),
                        )
                      // else: Render nothing if neither applies (initial state or empty search box)
                    ]),
              ),

              // --- Map View Area ---
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border:
                          Border.all(color: Colors.grey.shade300, width: 0.5),
                    ),
                    clipBehavior: Clip.antiAlias, // Clip map to rounded corners
                    child: Stack(
                      alignment: Alignment.center, // Center overlays by default
                      children: [
                        // --- Map View ---
                        // Always keep AMap2DView in the tree for controller lifecycle
                        GestureDetector(
                          // Prevent vertical map drags interfering with sheet dragging
                          onVerticalDragStart: (_) {},
                          onVerticalDragUpdate: (_) {},
                          onVerticalDragEnd: (_) {},
                          child: AMap2DView(
                            onAMap2DViewCreated: _onMapCreated,
                            onGetLocation: _onGetLocation,
                            onReGeocode: _onReGeocode,
                            onClick: _onMapClick,
                            onPoiSearched: _onPoiSearched,
                          ),
                        ),

                        // --- Initial Loading (Before map is ready) ---
                        if (!_isMapInitialized)
                          Positioned.fill(
                            child: Container(
                              color: Colors.grey.shade200.withOpacity(0.8),
                              child: const Center(
                                  child: CircularProgressIndicator()),
                            ),
                          ),

                        // --- General Loading Overlay (After map is ready, during operations) ---
                        if (_isLoadingLocation && _isMapInitialized)
                          Positioned.fill(
                            child: Container(
                              color: Colors.black.withOpacity(0.35),
                              // Darker overlay
                              child: const Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                  strokeWidth: 3.0,
                                ),
                              ),
                            ),
                          ),

                        // --- Retry Button for Location (If initial fetch failed) ---
                        // Show if map is ready, not loading, and no coords selected yet
                        if (_isMapInitialized &&
                            !_isLoadingLocation &&
                            _modalLatitude == 0 &&
                            _modalLongitude == 0 &&
                            !_hasProcessedInitialLocation)
                          Positioned(
                            bottom: 16,
                            // Center horizontally
                            left: 0, right: 0,
                            child: Center(
                              child: ElevatedButton.icon(
                                icon:
                                    const Icon(Icons.refresh_rounded, size: 18),
                                label: const Text('重新获取当前位置'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      Theme.of(context).colorScheme.primary,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 10),
                                  textStyle: const TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.w500),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20)),
                                  elevation: 2,
                                ),
                                onPressed: () {
                                  if (_mapController != null) {
                                    print("Retrying location fetch...");
                                    setState(() => _isLoadingLocation = true);
                                    _hasProcessedInitialLocation =
                                        false; // Allow _onGetLocation again
                                    // Re-use the location logic with error handling
                                    _mapController!.location().catchError((e) {
                                      print("Error retrying location: $e");
                                      if (mounted) {
                                        setState(
                                            () => _isLoadingLocation = false);
                                        // Provide feedback based on error
                                        if (e.toString().contains('timeout') ||
                                            e.toString().contains(
                                                'Get geolocation timeout')) {
                                          _showErrorSnackBar(
                                              '获取位置超时，请检查定位权限或网络连接');
                                        } else if (e.toString().contains(
                                            'Get ipLocation failed')) {
                                          _showErrorSnackBar('IP定位失败，请手动选择位置');
                                        } else if (e
                                            .toString()
                                            .contains('permission')) {
                                          _showErrorSnackBar('定位权限不足，请检查应用设置');
                                        } else {
                                          _showErrorSnackBar(
                                              '获取位置失败: ${e.toString().substring(0, 100)}...');
                                        }
                                      }
                                    });
                                  }
                                },
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),

              // --- Selected Location Info Display ---
              // Animate its appearance/disappearance
              AnimatedSize(
                duration: const Duration(milliseconds: 200),
                curve: Curves.fastOutSlowIn,
                child: (_modalFormattedAddress.isNotEmpty &&
                        _modalFormattedAddress != '无法获取地址信息' &&
                        !_isLoadingLocation)
                    ? Container(
                        width: double.infinity,
                        margin: const EdgeInsets.fromLTRB(16, 12, 16, 0),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor.withOpacity(
                                0.05), // Subtle theme color background
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.2),
                                width: 0.5)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(children: [
                              Icon(
                                  _modalUseCurrentLocation
                                      ? Icons.my_location_rounded
                                      : Icons.location_on_rounded,
                                  size: 18,
                                  color: Theme.of(context).primaryColor),
                              const SizedBox(width: 8),
                              Text(_modalUseCurrentLocation ? '当前定位' : '已选位置',
                                  style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                      color: Theme.of(context).primaryColor)),
                            ]),
                            const SizedBox(height: 6),
                            Text(
                              _modalFormattedAddress,
                              style: TextStyle(
                                  fontSize: 14.5,
                                  color: Colors.grey.shade800,
                                  height: 1.4), // Increased line height
                              softWrap: true,
                            ),
                            const SizedBox(height: 4),
                            Text(
                                '坐标: ${_modalLatitude.toStringAsField(6)}, ${_modalLongitude.toStringAsField(6)}',
                                style: TextStyle(
                                    fontSize: 11.5,
                                    color: Colors.grey.shade600)),
                          ],
                        ),
                      )
                    : (_isLoadingLocation &&
                            _isMapInitialized &&
                            _modalLatitude !=
                                0) // Show loading address text only if a point is selected
                        ? Padding(
                            padding: const EdgeInsets.fromLTRB(16, 12, 16, 0),
                            child: Row(
                              children: [
                                const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2)),
                                const SizedBox(width: 12),
                                Text('正在获取地址信息...',
                                    style: TextStyle(
                                        color: Colors.grey.shade600,
                                        fontSize: 14)),
                              ],
                            ))
                        : const SizedBox(
                            height:
                                12), // Placeholder height when no info is shown to reduce layout jump
              ),

              // --- Confirm Button ---
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 16),
                // Consistent padding
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.check_circle_outline_rounded,
                        size: 20),
                    label: const Text('确认此位置'),
                    onPressed: isConfirmDisabled ? null : _confirmLocation,
                    style: ButtonStyle(
                      padding: WidgetStateProperty.all<EdgeInsets>(
                          const EdgeInsets.symmetric(vertical: 14)),
                      shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12))),
                      // Consistent rounding
                      textStyle: WidgetStateProperty.all<TextStyle>(
                          const TextStyle(
                              fontSize: 16, fontWeight: FontWeight.bold)),
                      // Use resolveWith for dynamic background/foreground based on disabled state
                      backgroundColor: WidgetStateProperty.resolveWith<Color>(
                        (Set<WidgetState> states) {
                          if (states.contains(WidgetState.disabled)) {
                            return Colors.grey.shade300; // Disabled background
                          }
                          return Theme.of(context)
                              .primaryColor; // Enabled background (theme color)
                        },
                      ),
                      foregroundColor: WidgetStateProperty.resolveWith<Color>(
                        (Set<WidgetState> states) {
                          if (states.contains(WidgetState.disabled)) {
                            return Colors
                                .grey.shade500; // Disabled text/icon color
                          }
                          return Colors.white; // Enabled text/icon color
                        },
                      ),
                      elevation: WidgetStateProperty.resolveWith<double>(
                          (Set<WidgetState> states) {
                        if (states.contains(WidgetState.disabled)) return 0;
                        return 2; // Elevation when enabled
                      }),
                    ),
                  ),
                ),
              ),
              // Add padding at the bottom only if there's no system bottom inset (like iPhone home bar)
              SizedBox(
                  height: MediaQuery.of(context).padding.bottom > 0 ? 0 : 8),
            ],
          ),
        );
      },
    );
  }
}

// Helper extension for coordinate formatting
extension DoubleFormatExt on double {
  /// Formats double to fixed fraction digits, handling 0.0 case.
  String toStringAsField(int fractionDigits) {
    // Check for exactly 0.0 to avoid potential "-0.000000"
    if (this == 0.0) {
      // Create a string of '0's with the correct length
      return '0.${'0' * fractionDigits}';
    }
    return toStringAsFixed(fractionDigits);
  }
}
