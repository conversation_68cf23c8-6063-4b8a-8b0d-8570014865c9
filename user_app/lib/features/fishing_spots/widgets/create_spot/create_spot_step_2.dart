import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/spot_facility.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/spot_price.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/widgets/fish_type_picker.dart';
import 'package:user_app/models/image/uploaded_image.dart';
import 'package:user_app/utils/ui_helpers.dart';

class FacilitiesPriceStep extends StatelessWidget {
  final bool hasFacilities;
  final bool isPaid;
  final bool isOfficial;
  final List<SpotFacility> facilities;
  final List<SpotPrice> prices;
  final TextEditingController priceController;
  final List<FishType> availableFishTypes;
  final String spotVisibility;
  final List<UploadedImage> certificationDocuments;
  final bool isUploadingDocuments;

  final ValueChanged<bool> onHasFacilitiesChanged;
  final void Function(int index) onToggleFacility;
  final VoidCallback onAddNewFacility;
  final ValueChanged<bool> onIsPaidChanged;
  final void Function({
    required int priceTypeId,
    required String priceTypeName,
    double? price,
    int? hours,
    int? fishTypeId,
    String? fishTypeName,
    String? description,
  }) onAddPrice;
  final void Function(int index) onRemovePrice;
  final ValueChanged<bool> onIsOfficialChanged;
  final VoidCallback onUploadDocuments;
  final ValueChanged<String> onVisibilityChanged;
  final void Function(int index) onRemoveDocument;

  const FacilitiesPriceStep({
    super.key,
    required this.hasFacilities,
    required this.isPaid,
    required this.isOfficial,
    required this.facilities,
    required this.prices,
    required this.priceController,
    required this.onHasFacilitiesChanged,
    required this.onToggleFacility,
    required this.onAddNewFacility,
    required this.onIsPaidChanged,
    required this.onAddPrice,
    required this.onRemovePrice,
    required this.onIsOfficialChanged,
    required this.onUploadDocuments,
    required this.spotVisibility,
    required this.onVisibilityChanged,
    required this.certificationDocuments,
    required this.isUploadingDocuments,
    required this.onRemoveDocument,
    this.availableFishTypes = const [],
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildSectionHeader(
            context, '设施和价格', Icons.shopping_cart, '设置场地设施和收费信息'),
        const SizedBox(height: 20),

        // Facilities Section
        _buildFacilitiesSection(context),

        const SizedBox(height: 24),

        // Pricing Section
        _buildPricingSection(context),

        const SizedBox(height: 24),

        // Official Certification Section
        _buildOfficialSection(context),

        const SizedBox(height: 24),

        // Visibility Section
        _buildVisibilitySection(context),
      ],
    );
  }

  // FACILITIES SECTION
  Widget _buildFacilitiesSection(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.restaurant, color: Colors.green),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    '场地设施',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                Switch(
                  value: hasFacilities,
                  onChanged: onHasFacilitiesChanged,
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
            if (hasFacilities) ...[
              const SizedBox(height: 16),
              const Text(
                '选择提供的设施',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 12),
              _buildFacilitiesGrid(context),
            ] else ...[
              const SizedBox(height: 8),
              Text(
                '此钓点不提供任何额外设施',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFacilitiesGrid(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.85,
      ),
      itemCount: facilities.length + 1,
      itemBuilder: (context, index) {
        // Add New Facility button
        if (index == facilities.length) {
          return _buildAddFacilityButton(context);
        }

        // Regular facility item
        return _buildFacilityCard(context, index);
      },
    );
  }

  Widget _buildFacilityCard(BuildContext context, int index) {
    final facility = facilities[index];
    final isSelected = facility.isSelected;
    final colorScheme = Theme.of(context).colorScheme;

    return InkWell(
      onTap: () => onToggleFacility(index),
      borderRadius: BorderRadius.circular(16),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isSelected
              ? colorScheme.primary.withOpacity(0.1)
              : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? colorScheme.primary : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: colorScheme.primary.withOpacity(0.15),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  )
                ]
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isSelected
                    ? colorScheme.primary.withOpacity(0.15)
                    : Colors.grey.shade100,
                shape: BoxShape.circle,
              ),
              child: Icon(
                getIconData(facility.icon),
                size: 28,
                color: isSelected ? colorScheme.primary : Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              facility.name,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? colorScheme.primary : Colors.grey.shade800,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            if (facility.description != null && isSelected) ...[
              const SizedBox(height: 4),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 6),
                child: Text(
                  facility.description!,
                  style: TextStyle(
                    fontSize: 11,
                    color: colorScheme.primary.withOpacity(0.8),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAddFacilityButton(BuildContext context) {
    return InkWell(
      onTap: onAddNewFacility,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.5),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.add,
                size: 28,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '添加设施',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // PRICING SECTION
  Widget _buildPricingSection(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.paid, color: Colors.blue),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    '价格设置',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                Switch(
                  value: isPaid,
                  onChanged: onIsPaidChanged,
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
            if (isPaid) ...[
              const SizedBox(height: 16),
              _buildPricesList(context),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _showAddPriceDialog(context),
                  icon: const Icon(Icons.add),
                  label: const Text('添加价格设置'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ] else ...[
              const SizedBox(height: 8),
              Text(
                '此钓点免费开放',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPricesList(BuildContext context) {
    if (prices.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.blue.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.blue.withOpacity(0.2)),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade700, size: 20),
                const SizedBox(width: 10),
                Text(
                  '价格提示',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• 您可以设置按天或按小时的价格\n'
              '• 可以针对不同鱼类设置专属价格\n'
              '• 添加详细说明帮助钓友了解价格政策',
              style: TextStyle(fontSize: 14, height: 1.5),
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: prices.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        return _buildPriceCard(context, prices[index], index);
      },
    );
  }

  Widget _buildPriceCard(BuildContext context, SpotPrice price, int index) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Price indicator
            Container(
              width: 12,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
              ),
            ),
            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Price badge
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 10),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '¥${price.price.toStringAsFixed(price.price.truncateToDouble() == price.price ? 0 : 1)}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                          fontSize: 18,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Price details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                price.priceType == SpotPrice.PRICE_TYPE_DAY
                                    ? Icons.calendar_today_rounded
                                    : Icons.schedule,
                                size: 16,
                                color: Colors.grey.shade600,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                price.priceTypeName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 15,
                                ),
                              ),
                              if (price.hours != null) ...[
                                const SizedBox(width: 4),
                                Text(
                                  '(${price.hours}小时)',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 8),

                          // Fish type chip if available
                          if (price.fishTypeName != null) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.teal.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.water,
                                    size: 14,
                                    color: Colors.teal.shade700,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    price.fishTypeName!,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.teal.shade700,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 8),
                          ],

                          // Description if available
                          if (price.description != null &&
                              price.description!.isNotEmpty)
                            Text(
                              price.description!,
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.grey.shade600,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),

                    // Delete button
                    IconButton(
                      icon: Icon(
                        Icons.delete_outline,
                        color: Colors.red.shade400,
                      ),
                      onPressed: () => onRemovePrice(index),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      visualDensity: VisualDensity.compact,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // IMPROVED PRICE SETTING WITH FISHTYPEPICKER
  void _showAddPriceDialog(BuildContext context) {
    // 使用StatefulBuilder创建一个状态可变的对话框
    final TextEditingController priceController = TextEditingController();
    final TextEditingController hoursController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();

    int selectedPriceTypeId = SpotPrice.PRICE_TYPE_DAY;
    String selectedPriceTypeName = "按天计费";
    FishType? selectedFishType;

    // 为所有鱼类设置价格的标志
    bool applyToAllFish = true;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
                left: 16,
                right: 16,
                top: 16,
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 顶部把手指示器
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),

                    // 标题和关闭按钮
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.blue.withOpacity(0.1),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(Icons.paid, color: Colors.blue),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              '添加价格设置',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // 价格适用范围选择
                    const Text(
                      '价格适用范围',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // 选择是否为所有鱼类设置通用价格
                    Row(
                      children: [
                        _buildSelectionOption(
                          context,
                          title: '所有鱼类',
                          description: '设置通用价格',
                          icon: Icons.all_inclusive,
                          isSelected: applyToAllFish,
                          onTap: () {
                            setState(() {
                              applyToAllFish = true;
                              selectedFishType = null;
                            });
                          },
                        ),
                        const SizedBox(width: 12),
                        _buildSelectionOption(
                          context,
                          title: '指定鱼类',
                          description: '设置特定价格',
                          icon: Icons.water,
                          isSelected: !applyToAllFish,
                          onTap: () {
                            setState(() {
                              applyToAllFish = false;
                            });
                          },
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // 如果选择了特定鱼类，显示鱼类选择器
                    if (!applyToAllFish) ...[
                      const Text(
                        '选择鱼类',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 12),
                      FishTypePicker(
                        availableFishTypes: availableFishTypes,
                        allowMultipleSelection: false,
                        selectedFishTypeId: selectedFishType?.id.toInt(),
                        // maxHeight: 200,
                        // 使用新添加的maxHeight参数，而不是外层包裹SizedBox
                        onFishSelected: (fishType) {
                          setState(() {
                            selectedFishType = fishType;
                          });
                        },
                        onCustomFishTypeAdded: (newFishType) {
                          setState(() {
                            // 确保选中新添加的鱼类
                            selectedFishType = newFishType;
                          });
                        },
                        showAllFishOption: false, // 不显示"所有鱼类"选项，因为已经有单独的切换了
                      ),
                      const SizedBox(height: 20),
                    ],

                    // 价格类型选择
                    const Text(
                      '计费类型',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        _buildPriceTypeOption(
                          context,
                          title: '按天计费',
                          description: '每天固定价格',
                          icon: Icons.calendar_today_rounded,
                          isSelected:
                              selectedPriceTypeId == SpotPrice.PRICE_TYPE_DAY,
                          onTap: () {
                            setState(() {
                              selectedPriceTypeId = SpotPrice.PRICE_TYPE_DAY;
                              selectedPriceTypeName = "按天计费";
                              hoursController.clear();
                            });
                          },
                        ),
                        const SizedBox(width: 12),
                        _buildPriceTypeOption(
                          context,
                          title: '按小时计费',
                          description: '每小时固定价格',
                          icon: Icons.schedule,
                          isSelected:
                              selectedPriceTypeId == SpotPrice.PRICE_TYPE_HOUR,
                          onTap: () {
                            setState(() {
                              selectedPriceTypeId = SpotPrice.PRICE_TYPE_HOUR;
                              selectedPriceTypeName = "按小时计费";
                            });
                          },
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // 价格输入
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 价格字段
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                '价格 (元) *',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                controller: priceController,
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                        decimal: true),
                                decoration: InputDecoration(
                                  hintText: '输入价格',
                                  prefixIcon: const Icon(Icons.currency_yen),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        // 小时数字段（按小时计费时显示）
                        if (selectedPriceTypeId ==
                            SpotPrice.PRICE_TYPE_HOUR) ...[
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  '小时数 *',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                TextField(
                                  controller: hoursController,
                                  keyboardType: TextInputType.number,
                                  decoration: InputDecoration(
                                    hintText: '输入小时数',
                                    prefixIcon: const Icon(Icons.timer),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),

                    const SizedBox(height: 24),

                    // 价格说明输入
                    const Text(
                      '价格说明 (可选)',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '可添加额外说明，如特殊政策等',
                      style: TextStyle(fontSize: 13, color: Colors.grey),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: descriptionController,
                      maxLines: 3,
                      decoration: InputDecoration(
                        hintText: '例如：节假日价格上浮10%，儿童半价...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),

                    const SizedBox(height: 32),

                    // 操作按钮
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('取消'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: FilledButton(
                            onPressed: () {
                              // 验证价格
                              final priceText = priceController.text.trim();
                              if (priceText.isEmpty) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text("请输入价格"),
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                                return;
                              }

                              final price = double.tryParse(priceText);
                              if (price == null || price <= 0) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text("请输入有效的价格"),
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                                return;
                              }

                              // 验证小时数（针对按小时计费）
                              int? hours;
                              if (selectedPriceTypeId ==
                                  SpotPrice.PRICE_TYPE_HOUR) {
                                final hoursText = hoursController.text.trim();
                                if (hoursText.isNotEmpty) {
                                  hours = int.tryParse(hoursText);
                                  if (hours == null || hours <= 0) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text("请输入有效的小时数"),
                                        behavior: SnackBarBehavior.floating,
                                      ),
                                    );
                                    return;
                                  }
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text("请输入小时数"),
                                      behavior: SnackBarBehavior.floating,
                                    ),
                                  );
                                  return;
                                }
                              }

                              // 如果选择了特定鱼类但没有选择具体鱼类
                              if (!applyToAllFish && selectedFishType == null) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text("请先选择鱼类"),
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                                return;
                              }

                              // 添加价格
                              onAddPrice(
                                priceTypeId: selectedPriceTypeId,
                                priceTypeName: selectedPriceTypeName,
                                price: price,
                                hours: hours,
                                fishTypeId: !applyToAllFish
                                    ? selectedFishType?.id.toInt()
                                    : null,
                                fishTypeName: !applyToAllFish
                                    ? selectedFishType?.name
                                    : null,
                                description:
                                    descriptionController.text.trim().isEmpty
                                        ? null
                                        : descriptionController.text.trim(),
                              );

                              Navigator.pop(context);

                              // 显示成功消息
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    !applyToAllFish && selectedFishType != null
                                        ? "已添加 ${selectedFishType?.name} 的价格设置"
                                        : "已添加通用价格设置",
                                  ),
                                  behavior: SnackBarBehavior.floating,
                                  backgroundColor: Colors.green,
                                ),
                              );
                            },
                            style: FilledButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('添加价格'),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // VISIBILITY SECTION
  Widget _buildVisibilitySection(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.purple.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.visibility, color: Colors.purple),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    '可见范围',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              '选择钓点信息的可见范围',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildSelectionOption(
                  context,
                  title: '公开',
                  description: '所有人可见',
                  icon: Icons.public,
                  isSelected: spotVisibility == 'public',
                  onTap: () => onVisibilityChanged('public'),
                ),
                const SizedBox(width: 12),
                _buildSelectionOption(
                  context,
                  title: '关注者',
                  description: '仅关注者可见',
                  icon: Icons.people,
                  isSelected: spotVisibility == 'followers',
                  onTap: () => onVisibilityChanged('followers'),
                ),
                const SizedBox(width: 12),
                _buildSelectionOption(
                  context,
                  title: '私密',
                  description: '仅自己可见',
                  icon: Icons.lock,
                  isSelected: spotVisibility == 'private',
                  onTap: () => onVisibilityChanged('private'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // OFFICIAL CERTIFICATION SECTION
  Widget _buildOfficialSection(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.amber.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.verified, color: Colors.amber),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    '官方认证',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                Switch(
                  value: isOfficial,
                  onChanged: onIsOfficialChanged,
                  activeColor: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
            if (isOfficial) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.amber.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.amber.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.amber),
                        SizedBox(width: 8),
                        Text(
                          '认证须知',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '• 需要提供营业执照、场地使用权证明材料\n'
                      '• 认证后将获得官方标识，提高曝光率\n'
                      '• 认证申请将在7个工作日内审核完成',
                      style: TextStyle(fontSize: 14, height: 1.5),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.info_outline,
                                  size: 18, color: Colors.blue.shade700),
                              const SizedBox(width: 8),
                              Text(
                                '支持的文件类型',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 15,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Wrap(
                            spacing: 12,
                            runSpacing: 8,
                            children: [
                              _buildFileTypeChip(
                                  context, '图片', Icons.image, Colors.blue),
                              _buildFileTypeChip(context, 'PDF',
                                  Icons.picture_as_pdf, Colors.red),
                              _buildFileTypeChip(context, 'Word',
                                  Icons.description, Colors.blue.shade800),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // 已上传文件预览区域
                    if (certificationDocuments.isNotEmpty) ...[
                      const Text(
                        '已上传的证明材料',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildUploadedDocumentsGrid(context),
                      const SizedBox(height: 16),
                    ],
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed:
                            isUploadingDocuments ? null : onUploadDocuments,
                        icon: isUploadingDocuments
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : const Icon(Icons.upload_file),
                        label:
                            Text(isUploadingDocuments ? '正在上传...' : '上传证明材料'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.amber,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ] else ...[
              const SizedBox(height: 8),
              Text(
                '可选择官方认证以提高钓点可信度',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // HELPER WIDGETS
  Widget _buildFileTypeChip(
      BuildContext context, String label, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadedDocumentsGrid(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: certificationDocuments.length +
          (certificationDocuments.length < 5 ? 1 : 0),
      itemBuilder: (context, index) {
        // 如果未满5个文件，添加"添加更多"按钮
        if (index == certificationDocuments.length) {
          return _buildAddMoreDocumentButton(context);
        }

        // 显示已上传的文件
        return _buildDocumentPreviewCard(context, index);
      },
    );
  }

  Widget _buildAddMoreDocumentButton(BuildContext context) {
    return InkWell(
      onTap: isUploadingDocuments ? null : onUploadDocuments,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.amber.withOpacity(0.5),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.add,
                size: 24,
                color: Colors.amber.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '添加更多文件',
              style: TextStyle(
                color: Colors.amber.shade700,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentPreviewCard(BuildContext context, int index) {
    final document = certificationDocuments[index];

    return AspectRatio(
      aspectRatio: 1,
      child: Stack(
        children: [
          // 文件预览区域
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: document.hasError
                    ? Colors.red.shade300
                    : Colors.grey.shade300,
                width: document.hasError ? 2 : 1,
              ),
            ),
            clipBehavior: Clip.antiAlias,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // 文件预览占据主要空间
                Expanded(
                  child: _buildDocumentPreview(document),
                ),

                // 文件名称底部栏
                Container(
                  color: Colors.grey.shade100,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: Text(
                    _getShortFileName(document.file.name),
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey.shade800,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // 删除按钮
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap:
                  document.isUploading ? null : () => onRemoveDocument(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  size: 14,
                  color: Colors.white,
                ),
              ),
            ),
          ),

          // 上传状态指示
          if (document.isUploading)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                ),
              ),
            ),

          // 上传错误指示
          if (document.hasError)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        '上传失败',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDocumentPreview(UploadedImage document) {
    // If document is still uploading, show loading state
    if (document.isUploading) {
      return Container(
        color: Colors.grey.shade200,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // If document had an error during upload
    if (document.hasError) {
      return Container(
        color: Colors.red.shade50,
        child: Center(
          child:
              Icon(Icons.error_outline, color: Colors.red.shade300, size: 32),
        ),
      );
    }

    // If we have a URL (successfully uploaded to OSS)
    if (document.url != null) {
      // Check if this is an image file type
      final fileName = document.file.name.toLowerCase();
      if (fileName.endsWith('.jpg') ||
          fileName.endsWith('.jpeg') ||
          fileName.endsWith('.png')) {
        return Image.network(
          document.url!,
          fit: BoxFit.cover,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              color: Colors.grey.shade100,
              child: Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return _buildDefaultFileIcon('image');
          },
        );
      }
    }

    // For files without URL or non-image files, show icon based on file type
    final fileName = document.file.name.toLowerCase();
    if (fileName.endsWith('.pdf')) {
      return _buildDefaultFileIcon('pdf');
    } else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
      return _buildDefaultFileIcon('doc');
    } else if (fileName.endsWith('.jpg') ||
        fileName.endsWith('.jpeg') ||
        fileName.endsWith('.png')) {
      return _buildDefaultFileIcon('image');
    } else {
      return _buildDefaultFileIcon('file');
    }
  }

  Widget _buildDefaultFileIcon(String fileType) {
    IconData iconData;
    Color iconColor;

    switch (fileType) {
      case 'image':
        iconData = Icons.image;
        iconColor = Colors.blue;
        break;
      case 'pdf':
        iconData = Icons.picture_as_pdf;
        iconColor = Colors.red;
        break;
      case 'doc':
        iconData = Icons.description;
        iconColor = Colors.blue.shade800;
        break;
      case 'file':
      default:
        iconData = Icons.insert_drive_file;
        iconColor = Colors.amber;
        break;
    }

    return Container(
      color: Colors.grey.shade100,
      child: Center(
        child: Icon(
          iconData,
          size: 36,
          color: iconColor,
        ),
      ),
    );
  }

  String _getShortFileName(String fullName) {
    if (fullName.length <= 15) return fullName;

    final extension = fullName.contains('.')
        ? fullName.substring(fullName.lastIndexOf('.'))
        : '';

    final name = fullName.contains('.')
        ? fullName.substring(0, fullName.lastIndexOf('.'))
        : fullName;

    if (name.length <= 10) return fullName;

    return '${name.substring(0, 6)}...${extension}';
  }

  // 通用选择选项控件
  Widget _buildSelectionOption(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
          decoration: BoxDecoration(
            color: isSelected
                ? colorScheme.primary.withOpacity(0.1)
                : Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? colorScheme.primary : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected ? colorScheme.primary : Colors.grey.shade700,
                size: 28,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: isSelected ? colorScheme.primary : Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 价格类型选项
  Widget _buildPriceTypeOption(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return _buildSelectionOption(
      context,
      title: title,
      description: description,
      icon: icon,
      isSelected: isSelected,
      onTap: onTap,
    );
  }
}
