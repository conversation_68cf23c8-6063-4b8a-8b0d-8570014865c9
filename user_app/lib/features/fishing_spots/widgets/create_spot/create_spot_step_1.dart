import 'dart:io';

import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/widgets/fish_type_picker.dart';
import 'package:user_app/models/image/uploaded_image.dart';
import 'package:user_app/utils/ui_helpers.dart';

class ImagesFishTypesStep extends StatelessWidget {
  final List<UploadedImage> spotImages;
  final List<FishType> availableFishTypes;
  final List<FishType> customFishTypes; // 改为 FishType 类型列表
  final List<int> selectedFishTypeIds; // 改为存储 ID 而不是名称
  final bool isLoadingFishTypes; // 添加加载状态
  final Future<void> Function() onPickImages;
  final void Function(int index) onRemoveImage;
  final void Function(FishType fishType, bool isSelected)
      onFishTypeToggle; // 更新回调签名
  final void Function(FishType fishType) onCustomFishTypeAdded; // 添加自定义鱼种回调

  const ImagesFishTypesStep({
    super.key,
    required this.spotImages,
    required this.availableFishTypes,
    required this.customFishTypes,
    required this.selectedFishTypeIds,
    this.isLoadingFishTypes = false, // 默认为false
    required this.onPickImages,
    required this.onRemoveImage,
    required this.onFishTypeToggle,
    required this.onCustomFishTypeAdded,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildSectionHeader(
            context, '钓点图片和鱼类', Icons.photo_library_outlined, '添加钓点照片和可钓鱼类信息'),
        const SizedBox(height: 24),

        // Image Upload Area
        const Text('钓点图片',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
        const SizedBox(height: 12),

        // Image upload widget
        if (spotImages.isEmpty)
          _buildAddImageButton(context)
        else
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildImageHeaderRow(context),
              const SizedBox(height: 16),
              _buildImagesGrid(context),
            ],
          ),

        const SizedBox(height: 32),

        // 鱼类选择区域
        _buildFishTypeSelectionArea(context),
      ],
    );
  }

  // 构建鱼类选择区域
  Widget _buildFishTypeSelectionArea(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(Icons.set_meal, size: 20),
            const SizedBox(width: 8),
            const Text(
              '可钓鱼类',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const Spacer(),
            // 在直接嵌入模式下，不需要添加鱼类按钮，使用组件内部的添加功能
          ],
        ),
        const SizedBox(height: 8),
        const Text(
          '选择此钓点有哪些鱼种',
          style: TextStyle(fontSize: 13, color: Colors.grey),
        ),
        const SizedBox(height: 16),

        // 显示加载状态或鱼类选择器
        if (isLoadingFishTypes)
          _buildFishTypesLoadingState()
        else
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: FishTypePicker(
              availableFishTypes: [...availableFishTypes, ...customFishTypes],
              allowMultipleSelection: true,
              selectedFishTypeIds: selectedFishTypeIds,
              onMultipleFishesSelected: (selectedFishes) {
                for (final fishType in [
                  ...availableFishTypes,
                  ...customFishTypes
                ]) {
                  final isSelected =
                      selectedFishes.any((f) => f.id == fishType.id);
                  onFishTypeToggle(
                      fishType, isSelected); // Propagate toggle event
                }
              },
              onCustomFishTypeAdded:
                  availableFishTypes.isEmpty ? null : onCustomFishTypeAdded,
              helperText: '选择此钓点可以钓到的鱼类，可多选',
              displaySeasons: true,
              requireSeasonsForCustomFish: true,
              showSelectionIndicator: false, // 不显示左上角的复选框指示器
            ),
          ),

        // 如果没有选择鱼类，显示提示
        if (selectedFishTypeIds.isEmpty && !isLoadingFishTypes)
          Padding(
            padding: const EdgeInsets.only(top: 12.0),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.amber.shade800),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      '建议添加鱼类信息，这将帮助钓友找到适合的钓点',
                      style: TextStyle(color: Colors.amber),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  // Add Image Button with enhanced styling
  Widget _buildAddImageButton(BuildContext context) {
    return GestureDetector(
      onTap: onPickImages,
      child: Container(
        height: 150,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
            width: 1.5,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.add_photo_alternate,
                size: 36,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '点击添加图片',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '最多可上传9张图片',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Header row with count and add more button
  Widget _buildImageHeaderRow(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '已选择 ${spotImages.length}/9 张图片',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        if (spotImages.length < 9)
          TextButton.icon(
            onPressed: onPickImages,
            icon: const Icon(Icons.add_photo_alternate, size: 18),
            label: const Text('添加图片'),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
      ],
    );
  }

  // 修复后的图片网格实现，确保固定比例
  Widget _buildImagesGrid(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        mainAxisSpacing: 8,
        crossAxisSpacing: 8,
        // 确保固定的长宽比为 1:1
        childAspectRatio: 1,
      ),
      itemCount: spotImages.length,
      itemBuilder: (context, index) {
        final image = spotImages[index];
        return AspectRatio(
          aspectRatio: 1, // 强制 1:1 比例
          child: Stack(
            children: [
              // 图片容器，固定比例
              Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: image.hasError
                        ? Colors.red.shade300
                        : Colors.grey.shade300,
                    width: image.hasError ? 2 : 1,
                  ),
                ),
                clipBehavior: Clip.antiAlias,
                child: _buildImageContent(image),
              ),

              // 删除按钮
              Positioned(
                top: 4,
                right: 4,
                child: GestureDetector(
                  onTap: image.isUploading ? null : () => onRemoveImage(index),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 14,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),

              // 上传中指示器
              if (image.isUploading)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 3,
                      ),
                    ),
                  ),
                ),

              // 错误指示器
              if (image.hasError)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.white,
                            size: 24,
                          ),
                          const SizedBox(height: 2),
                          const Text(
                            '上传失败',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

// 专门用于图片内容的方法，确保一致的裁剪方式
  Widget _buildImageContent(UploadedImage image) {
    // 上传中显示加载指示器
    if (image.isUploading) {
      return Container(
        color: Colors.grey.shade200,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // 图片加载错误时显示错误状态
    if (image.hasError) {
      return Container(
        color: Colors.red.shade50,
        child: Center(
          child:
              Icon(Icons.error_outline, color: Colors.red.shade300, size: 32),
        ),
      );
    }

    // 图片已上传且有URL时，显示网络图片
    if (image.url != null) {
      return Image.network(
        image.url!,
        fit: BoxFit.cover,
        // 确保填充整个区域，裁剪保持比例
        width: double.infinity,
        height: double.infinity,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            color: Colors.grey.shade100,
            child: Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey.shade200,
            child: const Center(
              child: Icon(Icons.broken_image, color: Colors.grey),
            ),
          );
        },
      );
    }

    return Image.file(
      File(image.file.path),
      fit: BoxFit.cover, // 确保填充整个区域，裁剪保持比例
      width: double.infinity,
      height: double.infinity,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          color: Colors.grey.shade200,
          child: const Center(
            child: Icon(Icons.image_not_supported, color: Colors.grey),
          ),
        );
      },
    );
  }

  // Loading state for fish types
  Widget _buildFishTypesLoadingState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 32.0),
        child: Column(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            const Text(
              '正在加载鱼类数据...',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            Text(
              '这可能需要一点时间',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
            ),
          ],
        ),
      ),
    );
  }

  // Image preview builder function
  Widget buildImagePreview(UploadedImage image) {
    // If image is still uploading, show a loading indicator
    if (image.isUploading) {
      return Container(
        color: Colors.grey.shade200,
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // If image has an error, show an error indicator
    if (image.hasError) {
      return Container(
        color: Colors.red.shade100,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, color: Colors.red, size: 32),
              const SizedBox(height: 4),
              const Text(
                '上传失败',
                style: TextStyle(color: Colors.red, fontSize: 12),
              ),
            ],
          ),
        ),
      );
    }

    // If image has URL, display it
    if (image.url != null) {
      return Image.network(
        image.url!,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            color: Colors.grey.shade100,
            child: Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
                strokeWidth: 2,
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: Colors.grey.shade200,
            child: const Center(
              child: Icon(Icons.broken_image, color: Colors.grey),
            ),
          );
        },
      );
    }

    // Display the local file as fallback
    return Image.file(
      File(image.file.path),
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          color: Colors.grey.shade200,
          child: const Center(
            child: Icon(Icons.image_not_supported, color: Colors.grey),
          ),
        );
      },
    );
  }
}
