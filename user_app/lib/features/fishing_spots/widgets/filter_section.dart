import 'package:flutter/material.dart';

class FilterSection extends StatelessWidget {
  final String selectedFilter;
  final List<String> filterOptions;
  final bool hasActiveAdvancedFilters;
  final int activeAdvancedFiltersCount;
  final Function(String) onFilterChanged;
  final VoidCallback onAdvancedFilterTapped;

  const FilterSection({
    super.key,
    required this.selectedFilter,
    required this.filterOptions,
    required this.hasActiveAdvancedFilters,
    required this.activeAdvancedFiltersCount,
    required this.onFilterChanged,
    required this.onAdvancedFilterTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      margin: const EdgeInsets.only(top: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          ...filterOptions.map((filter) => _FilterPill(
                label: filter,
                isSelected: selectedFilter == filter,
                onTap: () => onFilterChanged(filter),
              )),
          _AdvancedFilterPill(
            hasActiveFilters: hasActiveAdvancedFilters,
            activeFiltersCount: activeAdvancedFiltersCount,
            onTap: onAdvancedFilterTapped,
          ),
        ],
      ),
    );
  }
}

class _FilterPill extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const _FilterPill({
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(right: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: isSelected ? Theme.of(context).primaryColor : Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade200,
                width: 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: Theme.of(context).primaryColor.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
            child: Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey.shade700,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                fontSize: 14,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _AdvancedFilterPill extends StatelessWidget {
  final bool hasActiveFilters;
  final int activeFiltersCount;
  final VoidCallback onTap;

  const _AdvancedFilterPill({
    required this.hasActiveFilters,
    required this.activeFiltersCount,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: hasActiveFilters
                  ? Theme.of(context).primaryColor.withOpacity(0.1)
                  : Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: hasActiveFilters
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade200,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.tune,
                  size: 16,
                  color: hasActiveFilters
                      ? Theme.of(context).primaryColor
                      : Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  '高级筛选',
                  style: TextStyle(
                    color: hasActiveFilters
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade700,
                    fontWeight:
                        hasActiveFilters ? FontWeight.w600 : FontWeight.normal,
                    fontSize: 14,
                  ),
                ),
                if (hasActiveFilters) ...[
                  const SizedBox(width: 4),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      '$activeFiltersCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
