import 'package:flutter/material.dart';

class FloatingActionSection extends StatelessWidget {
  final AnimationController fabAnimationController;
  final bool isMapView;
  final bool isVisible;
  final VoidCallback onAddSpot;
  final VoidCallback onToggleView;

  const FloatingActionSection({
    super.key,
    required this.fabAnimationController,
    required this.isMapView,
    required this.isVisible,
    required this.onAddSpot,
    required this.onToggleView,
  });

  @override
  Widget build(BuildContext context) {
    final fabScaleAnimation = CurvedAnimation(
      parent: fabAnimationController,
      curve: Curves.elasticOut,
    );

    return Positioned(
      right: 16,
      bottom: 100,
      child: AnimatedSlide(
        duration: const Duration(milliseconds: 200),
        offset: isVisible ? Offset.zero : const Offset(2.0, 0),
        curve: Curves.easeInOut,
        child: AnimatedOpacity(
          duration: const Duration(milliseconds: 200),
          opacity: isVisible ? 1.0 : 0.0,
          child: Column(
            children: [
              // Add Spot Button
              ScaleTransition(
                scale: fabScaleAnimation,
                child: FloatingActionButton(
                  onPressed: onAddSpot,
                  heroTag: "add_spot",
                  backgroundColor: Theme.of(context).primaryColor,
                  child: const Icon(Icons.add_location_alt, size: 28),
                ),
              ),
              const SizedBox(height: 12),
              // View Switch Button
              ScaleTransition(
                scale: fabScaleAnimation,
                child: FloatingActionButton(
                  onPressed: onToggleView,
                  heroTag: "toggle_view",
                  backgroundColor: Colors.white,
                  elevation: 4,
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      isMapView ? Icons.list : Icons.map,
                      key: ValueKey(isMapView),
                      color: Theme.of(context).primaryColor,
                      size: 28,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
