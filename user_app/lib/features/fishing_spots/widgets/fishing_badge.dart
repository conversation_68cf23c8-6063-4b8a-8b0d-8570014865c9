import 'package:flutter/material.dart';

class FishingBadge extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final Color? backgroundColor;
  final Color? textColor;
  final double fontSize;
  final EdgeInsetsGeometry padding;
  final BorderRadius? borderRadius;
  final Border? border;

  const FishingBadge({
    super.key,
    required this.icon,
    required this.label,
    required this.color,
    this.backgroundColor,
    this.textColor,
    this.fontSize = 12,
    this.padding = const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
    this.borderRadius,
    this.border,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = backgroundColor ?? color.withOpacity(0.1);
    final effectiveTextColor = textColor ?? color;
    final effectiveBorderRadius = borderRadius ?? BorderRadius.circular(30);
    final effectiveBorder =
        border ?? Border.all(color: color.withOpacity(0.3), width: 1);

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: effectiveBorderRadius,
        border: effectiveBorder,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: fontSize + 2,
            color: effectiveTextColor,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w600,
              color: effectiveTextColor,
            ),
          ),
        ],
      ),
    );
  }

  /// Factory constructor for creating a filled badge with inverted colors
  factory FishingBadge.filled({
    required IconData icon,
    required String label,
    required Color color,
    double fontSize = 12,
    EdgeInsetsGeometry padding =
        const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
    BorderRadius? borderRadius,
  }) {
    return FishingBadge(
      icon: icon,
      label: label,
      color: color,
      backgroundColor: color,
      textColor: Colors.white,
      fontSize: fontSize,
      padding: padding,
      borderRadius: borderRadius,
      border: null,
    );
  }

  /// Factory constructor for creating a dark badge suitable for overlays
  factory FishingBadge.dark({
    required IconData icon,
    required String label,
    Color color = Colors.white,
    double fontSize = 12,
    EdgeInsetsGeometry padding =
        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    BorderRadius? borderRadius,
  }) {
    return FishingBadge(
      icon: icon,
      label: label,
      color: color,
      backgroundColor: Colors.black.withOpacity(0.7),
      textColor: color,
      fontSize: fontSize,
      padding: padding,
      borderRadius: borderRadius ?? BorderRadius.circular(8),
      border: null,
    );
  }
}
