import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/features/fishing_spots/widgets/moments/spot_moments_section.dart';

class SpotCard extends StatefulWidget {
  final SpotSummaryVo spot;
  final VoidCallback onTap;

  const SpotCard({
    super.key,
    required this.spot,
    required this.onTap,
  });

  @override
  State<SpotCard> createState() => _SpotCardState();
}

class _SpotCardState extends State<SpotCard> {
  bool _isFavorite = false;
  bool _isLoadingFavorite = false;

  // 获取钓点ID
  int get spotId => widget.spot.id;

  // 获取钓点名称
  String get spotName => widget.spot.name;

  // 获取图片列表
  List<String> get images {
    return widget.spot.mainImage != null ? [widget.spot.mainImage!] : [];
  }

  // 获取评分
  double get rating => widget.spot.rating;

  // 获取最近动态数量
  int get recentMomentsCount => widget.spot.recentMomentsCount;

  @override
  void initState() {
    super.initState();
    _checkFavoriteStatus();
  }

  Future<void> _checkFavoriteStatus() async {
    // 可以在这里检查收藏状态
    // 由于 FishingSpotVo 没有 isFavorite 字段，需要单独查询
  }

  Future<void> _toggleFavorite() async {
    if (_isLoadingFavorite) return;

    setState(() {
      _isLoadingFavorite = true;
    });

    try {
      final fishingSpotService = getIt<FishingSpotService>();

      if (_isFavorite) {
        await fishingSpotService.unfavoriteFishingSpot(spotId);
        setState(() {
          _isFavorite = false;
        });
      } else {
        await fishingSpotService.favoriteFishingSpot(spotId);
        setState(() {
          _isFavorite = true;
        });
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isFavorite ? '已添加收藏' : '已取消收藏'),
            duration: const Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('操作失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingFavorite = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 修改条件：如果有最近动态就显示"新动态"徽章
    final hasNewMoments = recentMomentsCount > 0;

    // 调试信息
    print('🔍 [SpotCard] Spot: ${widget.spot.name}');
    print('🔍 [SpotCard] recentMomentsCount: $recentMomentsCount');
    print('🔍 [SpotCard] hasNewMoments: $hasNewMoments');

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.06),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image Section
                _SpotImageSection(
                  images: images,
                  spotName: spotName,
                  spotId: widget.spot.id,
                  hasNewMoments: hasNewMoments,
                  rating: rating,
                  recentMomentsCount: recentMomentsCount,
                ),

                // Content Section
                _SpotContentSection(
                  spot: widget.spot,
                  isFavorite: _isFavorite,
                  isLoadingFavorite: _isLoadingFavorite,
                  onFavoriteTap: _toggleFavorite,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _SpotImageSection extends StatelessWidget {
  final List<String> images;
  final String spotName;
  final int spotId;
  final bool hasNewMoments;
  final double rating;
  final int recentMomentsCount;

  const _SpotImageSection({
    required this.images,
    required this.spotName,
    required this.spotId,
    required this.hasNewMoments,
    required this.rating,
    required this.recentMomentsCount,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Image
        Container(
          height: 180,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            color: Colors.grey.shade200,
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            child: images.isNotEmpty
                ? CachedNetworkImage(
                    imageUrl: images.first,
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey.shade200,
                      child: Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.grey.shade400,
                          ),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey.shade200,
                      child: Icon(
                        Icons.image_not_supported,
                        size: 48,
                        color: Colors.grey.shade400,
                      ),
                    ),
                  )
                : Container(
                    color: Colors.grey.shade200,
                    child: Icon(
                      Icons.water,
                      size: 48,
                      color: Colors.grey.shade400,
                    ),
                  ),
          ),
        ),

        // Gradient Overlay
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: 80,
          child: Container(
            decoration: BoxDecoration(
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(20)),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.5),
                ],
              ),
            ),
          ),
        ),

        // Badges
        Positioned(
          top: 12,
          left: 12,
          right: 12,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // New Moments Indicator - 强制显示用于调试
              if (hasNewMoments || recentMomentsCount > 0)
                GestureDetector(
                  onTap: () =>
                      _navigateToSpotMoments(context, spotId, spotName),
                  child: _Badge(
                    icon: Icons.whatshot,
                    label: hasNewMoments ? '新动态' : '调试:${recentMomentsCount}',
                    gradient: LinearGradient(
                      colors: [
                        hasNewMoments
                            ? Colors.orange.shade400
                            : Colors.red.shade400,
                        hasNewMoments
                            ? Colors.orange.shade600
                            : Colors.red.shade600,
                      ],
                    ),
                  ),
                ),

              // Recent Moments Count - 修改条件：如果有最新动态就显示数量（至少显示1）
              if (hasNewMoments || recentMomentsCount > 0)
                GestureDetector(
                  onTap: () =>
                      _navigateToSpotMoments(context, spotId, spotName),
                  child: _Badge(
                    icon: Icons.forum,
                    label: '${recentMomentsCount > 0 ? recentMomentsCount : 1}',
                    backgroundColor: Colors.white.withOpacity(0.9),
                    textColor: Colors.black87,
                  ),
                ),
            ],
          ),
        ),

        // Rating
        if (rating > 0)
          Positioned(
            bottom: 12,
            left: 12,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.star,
                    size: 16,
                    color: Colors.amber.shade400,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    rating.toStringAsFixed(1),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}

class _SpotContentSection extends StatelessWidget {
  final SpotSummaryVo spot;
  final bool isFavorite;
  final bool isLoadingFavorite;
  final VoidCallback onFavoriteTap;

  const _SpotContentSection({
    required this.spot,
    required this.isFavorite,
    required this.isLoadingFavorite,
    required this.onFavoriteTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and Favorite
          Row(
            children: [
              Expanded(
                child: Text(
                  spot.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    height: 1.2,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              // Favorite Button
              GestureDetector(
                onTap: isLoadingFavorite ? null : onFavoriteTap,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color:
                        isFavorite ? Colors.red.shade50 : Colors.grey.shade100,
                    shape: BoxShape.circle,
                  ),
                  child: isLoadingFavorite
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              isFavorite ? Colors.red : Colors.grey.shade600,
                            ),
                          ),
                        )
                      : Icon(
                          isFavorite ? Icons.favorite : Icons.favorite_border,
                          size: 20,
                          color: isFavorite ? Colors.red : Colors.grey.shade600,
                        ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Address
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: 16,
                color: Colors.grey.shade500,
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  spot.address,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                    height: 1.3,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Tags
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              if (spot.official) _Tag('官方认证', isHighlight: true),
              if (spot.paid) _Tag('收费') else _Tag('免费'),
              if (spot.fishTypeNames.isNotEmpty)
                _Tag('${spot.fishTypeNames.length}种鱼'),
              if (spot.hasFacilities) _Tag('设施齐全'),
            ],
          ),
        ],
      ),
    );
  }
}

class _Badge extends StatelessWidget {
  final IconData icon;
  final String label;
  final Gradient? gradient;
  final Color? backgroundColor;
  final Color? textColor;

  const _Badge({
    required this.icon,
    required this.label,
    this.gradient,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        gradient: gradient,
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: textColor ?? Colors.white,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: textColor ?? Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

class _Tag extends StatelessWidget {
  final String text;
  final bool isHighlight;

  const _Tag(this.text, {this.isHighlight = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: isHighlight
            ? Theme.of(context).primaryColor.withOpacity(0.1)
            : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: isHighlight
              ? Theme.of(context).primaryColor
              : Colors.grey.shade700,
          fontWeight: isHighlight ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }
}

// Helper function to navigate to spot moments
void _navigateToSpotMoments(BuildContext context, int spotId, String spotName) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    enableDrag: true,
    isDismissible: true,
    builder: (context) => DraggableScrollableSheet(
      initialChildSize: 0.9,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) => SpotMomentsSection(
        fishingSpotId: spotId,
        fishingSpotName: spotName,
      ),
    ),
  );
}
