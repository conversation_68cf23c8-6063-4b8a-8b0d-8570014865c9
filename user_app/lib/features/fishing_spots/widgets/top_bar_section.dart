import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/fishing_spots/models/weather_data_dto.dart';
import 'package:user_app/features/fishing_spots/view_models/weather_card_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/weather_details_sheet.dart';

class TopBarSection extends StatelessWidget {
  final VoidCallback onSearchTapped;
  final List<Widget> children;

  const TopBarSection({
    super.key,
    required this.onSearchTapped,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.white,
              Colors.white.withOpacity(0.95),
              Colors.white.withOpacity(0),
            ],
            stops: const [0, 0.8, 1],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          bottom: false,
          child: Column(
            children: [
              // Compact Header
              _CompactWeatherHeader(onSearchTapped: onSearchTapped),
              // Additional children (filter section, etc.)
              ...children,
            ],
          ),
        ),
      ),
    );
  }
}

class _CompactWeatherHeader extends StatefulWidget {
  final VoidCallback onSearchTapped;

  const _CompactWeatherHeader({required this.onSearchTapped});

  @override
  State<_CompactWeatherHeader> createState() => _CompactWeatherHeaderState();
}

class _CompactWeatherHeaderState extends State<_CompactWeatherHeader> {
  @override
  void initState() {
    super.initState();
    // 触发天气数据加载
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final weatherViewModel = context.read<WeatherCardViewModel>();
      if (!weatherViewModel.isBusy &&
          weatherViewModel.weatherData.city == '未知') {
        weatherViewModel.fetchWeatherData();
      }
    });
  }

  void _showFullWeatherInfo() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const WeatherDetailsSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Row(
        children: [
          // Weather Info (Compact)
          Expanded(
            child: Consumer<WeatherCardViewModel>(
              builder: (context, weatherViewModel, _) {
                return _WeatherCompactCard(
                  isLoading: weatherViewModel.isBusy,
                  weatherData: weatherViewModel.weatherData,
                  onTap: _showFullWeatherInfo,
                );
              },
            ),
          ),
          const SizedBox(width: 12),
          // Search Button
          _SearchButton(onTap: widget.onSearchTapped),
        ],
      ),
    );
  }
}

class _WeatherCompactCard extends StatelessWidget {
  final bool isLoading;
  final WeatherDataDto weatherData;
  final VoidCallback onTap;

  const _WeatherCompactCard({
    required this.isLoading,
    required this.weatherData,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child:
            isLoading ? _buildLoadingSkeleton() : _buildWeatherContent(context),
      ),
    );
  }

  Widget _buildLoadingSkeleton() {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 120,
                height: 16,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: 4),
              Container(
                width: 80,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildWeatherContent(BuildContext context) {
    final temperature = double.tryParse(weatherData.temperatureFloat) ??
        double.tryParse(weatherData.temperature) ??
        20.0;

    return Row(
      children: [
        // Weather Icon
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: _getWeatherGradient(weatherData.weather),
            ),
            shape: BoxShape.circle,
          ),
          child: Icon(
            _getWeatherIcon(weatherData.weather),
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        // Weather Info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${temperature.toStringAsFixed(0)}°C ${weatherData.weather}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '${weatherData.windPower} ${weatherData.humidity}%湿度',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
        Icon(
          Icons.chevron_right,
          color: Colors.grey.shade400,
        ),
      ],
    );
  }

  List<Color> _getWeatherGradient(String weather) {
    if (weather.contains('晴')) {
      return [Colors.orange.shade300, Colors.orange.shade500];
    } else if (weather.contains('云') || weather.contains('阴')) {
      return [Colors.blueGrey.shade300, Colors.blueGrey.shade500];
    } else if (weather.contains('雨')) {
      return [Colors.blue.shade300, Colors.blue.shade500];
    } else if (weather.contains('雪')) {
      return [Colors.lightBlue.shade200, Colors.lightBlue.shade400];
    } else {
      return [Colors.blue.shade300, Colors.blue.shade500];
    }
  }

  IconData _getWeatherIcon(String weather) {
    if (weather.contains('晴')) {
      return Icons.wb_sunny;
    } else if (weather.contains('云')) {
      return Icons.cloud;
    } else if (weather.contains('阴')) {
      return Icons.cloud_queue;
    } else if (weather.contains('雨')) {
      return Icons.umbrella;
    } else if (weather.contains('雪')) {
      return Icons.ac_unit;
    } else if (weather.contains('风')) {
      return Icons.air;
    } else {
      return Icons.wb_sunny;
    }
  }
}

class _SearchButton extends StatelessWidget {
  final VoidCallback onTap;

  const _SearchButton({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(12),
            child: Icon(
              Icons.search,
              color: Colors.grey.shade700,
            ),
          ),
        ),
      ),
    );
  }
}
