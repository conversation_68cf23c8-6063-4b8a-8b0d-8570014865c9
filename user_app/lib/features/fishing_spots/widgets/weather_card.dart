import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/models/fishing_recommendation.dart';
import 'package:user_app/features/fishing_spots/models/weather_data_dto.dart';
import 'package:weather_icons/weather_icons.dart';

class WeatherCard extends StatelessWidget {
  final bool isLoading;
  final WeatherDataDto weatherData;
  final FishingRecommendation fishingRecommendation;

  const WeatherCard({
    super.key,
    required this.isLoading,
    required this.weatherData,
    required this.fishingRecommendation,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 2,
      shadowColor: Colors.black26,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: isLoading
          ? const SizedBox(
              height: 120,
              child: Center(child: CircularProgressIndicator()),
            )
          : Container(
              padding: const EdgeInsets.all(16),
              height: 120,
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '今日钓况预报',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: colorScheme.primary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        FittedBox(
                          fit: BoxFit.scaleDown,
                          alignment: Alignment.centerLeft,
                          child: Row(
                            children: [
                              Icon(
                                _getWeatherIcon(weatherData.weather),
                                size: 18,
                                color: colorScheme.onSurface.withOpacity(0.7),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${weatherData.temperature}°C | ${weatherData.weather}',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: colorScheme.onSurface.withOpacity(0.8),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.waves,
                              size: 18,
                              color: colorScheme.onSurface.withOpacity(0.7),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '水温: ${(double.tryParse(weatherData.temperatureFloat) ?? double.tryParse(weatherData.temperature) ?? 0.0) - 3}°C',
                              style: TextStyle(
                                fontSize: 14,
                                color: colorScheme.onSurface.withOpacity(0.8),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Divider
                  Container(
                    height: 80,
                    width: 1,
                    color: Colors.grey.withOpacity(0.3),
                  ),

                  // Fishing recommendation section
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.catching_pokemon,
                                size: 16,
                                color: colorScheme.secondary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '推荐鱼种',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: colorScheme.secondary,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getRecommendedFishText(
                                fishingRecommendation.recommendedFish),
                            style: TextStyle(
                              fontSize: 14,
                              color: colorScheme.onSurface.withOpacity(0.8),
                            ),
                          ),
                          const SizedBox(height: 6),
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: colorScheme.primary.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.thumb_up,
                                      size: 12,
                                      color: colorScheme.primary,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      '出钓指数: ${fishingRecommendation.fishingIndex ?? '3.0'}',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                        color: colorScheme.primary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  IconData _getWeatherIcon(String condition) {
    // Sunny / Clear
    if (condition.contains('晴') &&
        !condition.contains('多云') &&
        !condition.contains('间')) {
      return WeatherIcons.day_sunny;
    }

    // Cloudy conditions
    else if (condition.contains('少云')) {
      return WeatherIcons.day_sunny_overcast;
    } else if (condition.contains('晴间多云') || condition.contains('晴转多云')) {
      return WeatherIcons.day_cloudy;
    } else if (condition.contains('多云')) {
      return WeatherIcons.cloud;
    } else if (condition.contains('阴')) {
      return WeatherIcons.cloudy;
    }

    // Rain conditions
    else if (condition.contains('雷阵雨') && condition.contains('冰雹')) {
      return WeatherIcons.day_thunderstorm;
    } else if (condition.contains('雷阵雨') || condition.contains('雷')) {
      return condition.contains('强')
          ? WeatherIcons.thunderstorm
          : WeatherIcons.day_lightning;
    } else if (condition.contains('阵雨')) {
      return condition.contains('强')
          ? WeatherIcons.day_rain_wind
          : WeatherIcons.day_showers;
    } else if (condition.contains('小雨') ||
        condition.contains('毛毛雨') ||
        condition.contains('细雨')) {
      return WeatherIcons.day_sprinkle;
    } else if (condition.contains('中雨')) {
      return WeatherIcons.day_rain;
    } else if (condition.contains('大雨')) {
      return WeatherIcons.rain;
    } else if (condition.contains('暴雨') ||
        condition.contains('特大暴雨') ||
        condition.contains('极端降雨')) {
      return WeatherIcons.rain_wind;
    } else if (condition.contains('雨')) {
      return WeatherIcons.day_rain;
    }

    // Snow & Mixed precipitation
    else if (condition.contains('雨夹雪') ||
        condition.contains('雨雪') ||
        condition.contains('冻雨')) {
      return WeatherIcons.day_rain_mix;
    } else if (condition.contains('阵雪')) {
      return WeatherIcons.day_snow;
    } else if (condition.contains('小雪')) {
      return WeatherIcons.day_snow;
    } else if (condition.contains('中雪')) {
      return WeatherIcons.snow;
    } else if (condition.contains('大雪') || condition.contains('暴雪')) {
      return WeatherIcons.snow_wind;
    } else if (condition.contains('雪')) {
      return WeatherIcons.day_snow;
    }

    // Wind conditions
    else if (condition.contains('飓风') || condition.contains('龙卷风')) {
      return WeatherIcons.hurricane;
    } else if (condition.contains('风暴') ||
        condition.contains('狂爆风') ||
        condition.contains('热带风暴')) {
      return WeatherIcons.storm_warning;
    } else if (condition.contains('风')) {
      return condition.contains('大') ||
              condition.contains('强') ||
              condition.contains('烈') ||
              condition.contains('疾')
          ? WeatherIcons.strong_wind
          : WeatherIcons.windy;
    }

    // Fog & Haze
    else if (condition.contains('霾')) {
      return WeatherIcons.day_haze;
    } else if (condition.contains('雾')) {
      return WeatherIcons.fog;
    }

    // Dust & Sand
    else if (condition.contains('沙尘暴') ||
        condition.contains('扬沙') ||
        condition.contains('浮尘')) {
      return WeatherIcons.sandstorm;
    }

    // Hot & Cold
    else if (condition.contains('热')) {
      return WeatherIcons.hot;
    } else if (condition.contains('冷')) {
      return WeatherIcons.snowflake_cold;
    }

    // Default
    else {
      return WeatherIcons
          .day_sunny; // Default icon when condition is unrecognized or '未知'
    }
  }

  String _getRecommendedFishText(List<FishType> recommendedFish) {
    List<String> fishNames = recommendedFish
        .map((fish) => fish.name)
        .where((name) => name.isNotEmpty)
        .toList();
    if (fishNames.isEmpty) {
      return '暂无推荐鱼种';
    }
    if (fishNames.length > 3) {
      fishNames = fishNames.sublist(0, 3);
    }
    return fishNames.join(', ');
  }
}
