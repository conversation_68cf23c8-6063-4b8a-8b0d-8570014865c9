import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/features/fishing_spots/widgets/fishing_spot_details_content.dart';
import 'package:user_app/models/user.dart';

/// 钓点详情模态弹窗，会调用API获取完整的钓点详情数据
class FishingSpotDetailsModal extends StatefulWidget {
  final int spotId;
  final FishingSpotVo? initialSpot;
  final ScrollController scrollController;
  final VoidCallback? onNavigationPressed;
  final VoidCallback? onCheckinPressed;
  final VoidCallback? onViewDynamicPressed;

  const FishingSpotDetailsModal({
    super.key,
    required this.spotId,
    this.initialSpot,
    required this.scrollController,
    this.onNavigationPressed,
    this.onCheckinPressed,
    this.onViewDynamicPressed,
  });

  @override
  State<FishingSpotDetailsModal> createState() =>
      _FishingSpotDetailsModalState();
}

class _FishingSpotDetailsModalState extends State<FishingSpotDetailsModal> {
  FishingSpotVo? spot;
  bool isLoading = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    // 如果有初始数据，先显示初始数据，然后加载详细数据
    if (widget.initialSpot != null) {
      spot = widget.initialSpot;
    }
    _loadSpotDetail();
  }

  Future<void> _loadSpotDetail() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      final fishingSpotService = getIt<FishingSpotService>();
      final detailSpot =
          await fishingSpotService.getFishingSpotDetail(widget.spotId);

      if (mounted) {
        setState(() {
          spot = detailSpot;
          isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ 加载钓点详情失败: $e');
      if (mounted) {
        setState(() {
          isLoading = false;
          errorMessage = e is ApiError ? e.message : '加载钓点详情失败';
        });
      }
    }
  }

  void _navigateToUserProfile(User user) {
    final route = '${AppRoutes.profile}/${user.id}';
    debugPrint('🚀 [FishingSpotDetailsModal] 导航到用户主页: $route');
    debugPrint('🚀 [FishingSpotDetailsModal] 用户信息: 姓名=${user.name}, ID=${user.id}');
    context.push(route);
  }

  @override
  Widget build(BuildContext context) {
    // 如果没有钓点数据且不在加载中，显示错误
    if (spot == null && !isLoading) {
      return Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                errorMessage ?? '钓点不存在',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadSpotDetail,
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    // 显示钓点详情内容
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Stack(
        children: [
          if (spot != null)
            FishingSpotDetailsContent(
              spot: spot!,
              scrollController: widget.scrollController,
              onCreatorTap: _navigateToUserProfile,
            ),

          // 加载指示器
          if (isLoading)
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
