import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/constants/publish_moment_constants.dart';
import 'package:user_app/models/image/uploaded_image.dart';

class ImageGrid extends StatelessWidget {
  final List<UploadedImage> images;
  final bool isUploading;
  final Function(int) onRemoveImage;
  final Function(int) onRetryUpload;
  final Function(int, int) onReorder;

  const ImageGrid({
    super.key,
    required this.images,
    required this.isUploading,
    required this.onRemoveImage,
    required this.onRetryUpload,
    required this.onReorder,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Info Row: Count and Add Button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('已选择 ${images.length}/${PublishMomentConstants.maxImages} 张',
                style: TextStyle(fontSize: 14, color: Colors.grey.shade700)),
          ],
        ),
        const SizedBox(height: 12),

        ReorderableListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: (images.length / 3).ceil(),
          itemBuilder: (context, rowIndex) {
            return Container(
              key: ValueKey('row_$rowIndex'),
              margin: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: List.generate(3, (colIndex) {
                  final index = rowIndex * 3 + colIndex;
                  if (index >= images.length) {
                    return const Expanded(child: SizedBox());
                  }
                  return Expanded(
                    child: Container(
                      margin: EdgeInsets.only(right: colIndex < 2 ? 8 : 0),
                      child: _buildImageItem(context, images[index], index),
                    ),
                  );
                }),
              ),
            );
          },
          onReorder: (int oldIndex, int newIndex) {
            if (isUploading) {
              ScaffoldMessenger.of(context)
                  .showSnackBar(const SnackBar(content: Text('请等待图片上传完成再排序')));
              return;
            }
            onReorder(oldIndex, newIndex);
          },
        ),

        // Help text for reordering
        if (images.length > 1)
          Padding(
            padding: const EdgeInsets.only(top: 12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.info_outline, size: 14, color: Colors.grey.shade500),
                const SizedBox(width: 4),
                Text(
                  '长按拖动可调整图片顺序',
                  style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade500,
                      fontStyle: FontStyle.italic),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildImageItem(BuildContext context, UploadedImage image, int index) {
    return Stack(
      key: ValueKey(image.file?.path),
      children: [
        // Image container
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: image.hasError ? Colors.red : Colors.grey.shade300,
              width: 1,
            ),
            color: Colors.white,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: AspectRatio(
              aspectRatio: 1,
              child: Stack(
                children: [
                  // Image
                  if (image.url != null && !image.hasError)
                    Image.network(
                      image.url!,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey.shade200,
                          child: const Center(
                            child: Icon(Icons.broken_image, color: Colors.grey),
                          ),
                        );
                      },
                    )
                  else
                    FutureBuilder<Uint8List>(
                      future: image.file?.readAsBytes(),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.done &&
                            snapshot.hasData) {
                          return Image.memory(
                            snapshot.data!,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                          );
                        }
                        return Container(
                          color: Colors.grey.shade200,
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      },
                    ),

                  // Upload indicator
                  if (image.isUploading)
                    Container(
                      color: Colors.black.withOpacity(0.5),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(
                              value: image.uploadProgress > 0
                                  ? image.uploadProgress
                                  : null,
                              color: Colors.white,
                              strokeWidth: 3,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '${(image.uploadProgress * 100).toStringAsFixed(0)}%',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Error indicator
                  if (image.hasError)
                    Container(
                      color: Colors.black.withOpacity(0.5),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.error_outline,
                              color: Colors.white,
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            ElevatedButton.icon(
                              onPressed: () => onRetryUpload(index),
                              icon: const Icon(
                                Icons.refresh,
                                size: 16,
                              ),
                              label: const Text('重试'),
                              style: ElevatedButton.styleFrom(
                                visualDensity: VisualDensity.compact,
                                backgroundColor: Colors.white,
                                foregroundColor: Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),

        // Remove button
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => onRemoveImage(index),
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),

        // Drag handle indicator
        Positioned(
          bottom: 4,
          right: 4,
          child: Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Icon(
              Icons.drag_handle,
              size: 16,
              color: Colors.white,
            ),
          ),
        ),

        // Index badge
        Positioned(
          top: 4,
          left: 4,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              '${index + 1}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
