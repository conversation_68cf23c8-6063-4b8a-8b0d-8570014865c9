import 'package:flutter/material.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';
import 'package:user_app/features/fishing_spots/models/spot_map_vo.dart';
import 'package:user_app/features/fishing_spots/widgets/spot_info_bubble.dart';
import 'package:permission_handler/permission_handler.dart';

class SpotMapView extends StatefulWidget {
  final List<SpotMapVo> spots;
  final Function(AMap2DController) onMapCreated;
  final Function(SpotMapVo) onSpotTapped;
  final Function(SpotMapVo)? onSpotDetailsRequested;
  final Function(SpotMapVo)? onSpotNavigationRequested;
  final Function(Map<String, double> bounds, double zoom)? onRegionChange;
  final bool enableLocationButton;
  final bool enableZoomControls;
  final bool enableClustering;

  const SpotMapView({
    super.key,
    required this.spots,
    required this.onMapCreated,
    required this.onSpotTapped,
    this.onSpotDetailsRequested,
    this.onSpotNavigationRequested,
    this.onRegionChange,
    this.enableLocationButton = true,
    this.enableZoomControls = true,
    this.enableClustering = false,
  });

  @override
  State<SpotMapView> createState() => _SpotMapViewState();
}

class _SpotMapViewState extends State<SpotMapView>
    with TickerProviderStateMixin {
  AMap2DController? _mapController;
  bool _isMapReady = false;
  final Map<String, SpotMapVo> _markerSpotMap = {}; // 标记ID到钓点的映射
  double _currentZoomLevel = 12.0;
  Map<String, double>? _currentBounds;

  // 信息气泡相关状态
  SpotMapVo? _selectedSpot;
  Offset? _bubblePosition;
  late AnimationController _bubbleAnimationController;
  late Animation<double> _bubbleAnimation;

  @override
  void initState() {
    super.initState();
    _bubbleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _bubbleAnimation = CurvedAnimation(
      parent: _bubbleAnimationController,
      curve: Curves.easeOutBack,
    );
  }

  @override
  void dispose() {
    _bubbleAnimationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(SpotMapView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当spots数据更新时，重新设置标记
    if (oldWidget.spots != widget.spots) {
      _setupMarkers();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Map
        GestureDetector(
          onTap: _hideBubble,
          child: AMap2DView(
            onAMap2DViewCreated: (controller) {
              setState(() {
                _mapController = controller;
                _isMapReady = true;
              });
              widget.onMapCreated(controller);
              _setupMapCallbacks();
              _setupMarkers();
            },
          ),
        ),

        // Map controls
        if (_isMapReady) _buildMapControls(),

        // Information bubble
        if (_selectedSpot != null && _bubblePosition != null)
          _buildInfoBubble(),
      ],
    );
  }

  Widget _buildMapControls() {
    return Positioned(
      left: 16,
      bottom: 100,
      child: Column(
        children: [
          // My Location Button
          if (widget.enableLocationButton)
            _MapControlButton(
              icon: Icons.my_location,
              onTap: _centerOnMyLocation,
            ),
          if (widget.enableLocationButton) const SizedBox(height: 12),
          // Zoom In
          if (widget.enableZoomControls)
            _MapControlButton(
              icon: Icons.add,
              onTap: _zoomIn,
            ),
          if (widget.enableZoomControls) const SizedBox(height: 12),
          // Zoom Out
          if (widget.enableZoomControls)
            _MapControlButton(
              icon: Icons.remove,
              onTap: _zoomOut,
            ),
        ],
      ),
    );
  }

  void _setupMapCallbacks() {
    if (_mapController == null) return;

    // 设置标记点击回调
    _mapController!.setMarkerClickCallback((markerId) async {
      debugPrint('🗺️ [SpotMapView] Marker clicked: $markerId');
      final spot = _markerSpotMap[markerId];
      if (spot != null) {
        debugPrint('🗺️ [SpotMapView] Found spot: ${spot.name}');
        // 使用屏幕中心位置作为气泡位置的近似值
        final screenSize = MediaQuery.of(context).size;
        final position =
            Offset(screenSize.width / 2, screenSize.height / 2 - 100);
        debugPrint('🗺️ [SpotMapView] Showing bubble at position: $position');
        _showBubble(spot, position);

        // 同时调用原有的回调以保持兼容性
        widget.onSpotTapped(spot);
      } else {
        debugPrint('🗺️ [SpotMapView] No spot found for marker: $markerId');
      }
    });

    // 设置地图区域变化回调
    if (widget.onRegionChange != null) {
      _mapController!.setMapRegionChangeCallback((bounds, zoom) {
        _currentBounds = bounds;
        _currentZoomLevel = zoom;
        widget.onRegionChange!(bounds, zoom);
        // 地图移动时隐藏气泡
        _hideBubble();
      });
    }
  }

  void _setupMarkers() {
    if (!_isMapReady || _mapController == null) return;

    debugPrint(
        '🗺️ [SpotMapView] Setting up markers for ${widget.spots.length} spots');

    // Clear existing markers and mapping
    _mapController!.clearMarkers();
    _markerSpotMap.clear();

    // Filter valid spots
    final validSpots = widget.spots
        .where((spot) => spot.latitude != 0 && spot.longitude != 0)
        .toList();

    debugPrint('🗺️ [SpotMapView] Valid spots: ${validSpots.length}');
    for (final spot in validSpots) {
      debugPrint(
          '🗺️ [SpotMapView] Spot: ${spot.name} at (${spot.latitude}, ${spot.longitude})');
    }

    if (validSpots.isEmpty) return;

    // 使用聚类或直接添加标记
    List<SpotMapVo> spotsToShow;
    if (widget.enableClustering && validSpots.length > 20) {
      spotsToShow = _clusterSpots(validSpots);
    } else {
      spotsToShow = validSpots;
    }

    // Add markers with spot ID for identification
    final markers = spotsToShow.map((spot) {
      final markerId = 'spot_${spot.id}';
      _markerSpotMap[markerId] = spot;

      return {
        'id': markerId,
        'latitude': spot.latitude.toString(),
        'longitude': spot.longitude.toString(),
        'title': spot.name,
        'snippet': _buildMarkerSnippet(spot),
      };
    }).toList();

    _mapController!.addMarkers(markers).then((_) {
      // Zoom to fit all markers after a delay
      Future.delayed(const Duration(milliseconds: 500), () {
        if (_isMapReady && _mapController != null && spotsToShow.isNotEmpty) {
          _mapController!.zoomToFitMarkers();
        }
      });
    }).catchError((error) {
      debugPrint("Error adding markers: $error");
    });
  }

  /// 构建标记提示信息
  String _buildMarkerSnippet(SpotMapVo spot) {
    final parts = <String>[];

    if (spot.rating != null && spot.rating! > 0) {
      parts.add('★ ${spot.rating!.toStringAsFixed(1)}');
    }

    if (spot.isPaid) {
      parts.add('付费');
    } else {
      parts.add('免费');
    }

    if (spot.distance != null) {
      parts.add('${spot.distance!.toStringAsFixed(1)}km');
    }

    return parts.join(' • ');
  }

  /// 简单的钓点聚类算法
  List<SpotMapVo> _clusterSpots(List<SpotMapVo> spots) {
    // 按照距离进行简单聚类
    final clustered = <SpotMapVo>[];
    final processed = <bool>[];

    for (int i = 0; i < spots.length; i++) {
      processed.add(false);
    }

    for (int i = 0; i < spots.length; i++) {
      if (processed[i]) continue;

      final mainSpot = spots[i];
      processed[i] = true;

      // 查找附近的钓点（简单的距离判断）
      final nearby = <SpotMapVo>[mainSpot];

      for (int j = i + 1; j < spots.length; j++) {
        if (processed[j]) continue;

        final distance = _calculateDistance(
          mainSpot.latitude,
          mainSpot.longitude,
          spots[j].latitude,
          spots[j].longitude,
        );

        // 如果距离小于500米，则认为是同一个集群
        if (distance < 0.5) {
          nearby.add(spots[j]);
          processed[j] = true;
        }
      }

      // 如果只有一个钓点，直接添加
      if (nearby.length == 1) {
        clustered.add(mainSpot);
      } else {
        // 创建聚类标记（使用第一个钓点作为代表）
        final clusterSpot = SpotMapVo(
          id: mainSpot.id,
          name: '${nearby.length}个钓点',
          address: mainSpot.address,
          latitude: mainSpot.latitude,
          longitude: mainSpot.longitude,
          type: mainSpot.type,
          rating: nearby.map((s) => s.rating ?? 0.0).reduce((a, b) => a + b) /
              nearby.length,
          reviewCount: nearby.map((s) => s.reviewCount).reduce((a, b) => a + b),
          isPaid: nearby.any((s) => s.isPaid),
          hasFacilities: nearby.any((s) => s.hasFacilities),
          fishTypes: nearby.expand((s) => s.fishTypes).toSet().toList(),
          imageUrl: mainSpot.imageUrl,
          distance: mainSpot.distance,
        );
        clustered.add(clusterSpot);
      }
    }

    return clustered;
  }

  /// 计算两点间距离（单位：公里）
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371.0; // 地球半径（公里）

    final double dLat = (lat2 - lat1) * (3.14159265359 / 180.0);
    final double dLon = (lon2 - lon1) * (3.14159265359 / 180.0);

    final double a = (dLat / 2).abs() * (dLat / 2).abs() +
        (lat1 * (3.14159265359 / 180.0)).abs() *
            (lat2 * (3.14159265359 / 180.0)).abs() *
            (dLon / 2).abs() *
            (dLon / 2).abs();

    final double c = 2 * (a.abs()).abs();

    return earthRadius * c;
  }

  /// 显示信息气泡
  void _showBubble(SpotMapVo spot, Offset position) {
    debugPrint('🗺️ [SpotMapView] _showBubble called for spot: ${spot.name}');
    debugPrint('🗺️ [SpotMapView] Current _selectedSpot: $_selectedSpot');

    // 如果已经有选中的钓点，先隐藏当前气泡
    if (_selectedSpot != null) {
      debugPrint('🗺️ [SpotMapView] Hiding existing bubble first');
      _hideBubble();
      // 延迟显示新气泡，确保动画流畅
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          debugPrint('🗺️ [SpotMapView] Setting new bubble state (delayed)');
          setState(() {
            _selectedSpot = spot;
            _bubblePosition = position;
          });
          _bubbleAnimationController.forward();
        }
      });
    } else {
      debugPrint('🗺️ [SpotMapView] Setting bubble state immediately');
      setState(() {
        _selectedSpot = spot;
        _bubblePosition = position;
      });
      _bubbleAnimationController.forward();
    }
  }

  /// 隐藏信息气泡
  void _hideBubble() {
    if (_selectedSpot != null) {
      _bubbleAnimationController.reverse().then((_) {
        if (mounted) {
          setState(() {
            _selectedSpot = null;
            _bubblePosition = null;
          });
        }
      });
    }
  }

  /// 构建信息气泡
  Widget _buildInfoBubble() {
    debugPrint('🗺️ [SpotMapView] _buildInfoBubble called');
    debugPrint('🗺️ [SpotMapView] _selectedSpot: $_selectedSpot');
    debugPrint('🗺️ [SpotMapView] _bubblePosition: $_bubblePosition');

    if (_selectedSpot == null || _bubblePosition == null) {
      debugPrint(
          '🗺️ [SpotMapView] Returning empty widget - no spot or position');
      return const SizedBox.shrink();
    }

    // 计算气泡的最佳位置，避免超出屏幕边界
    final screenSize = MediaQuery.of(context).size;
    final safeArea = MediaQuery.of(context).padding;
    const bubbleWidth = 280.0;
    const bubbleHeight = 200.0; // 估算的气泡高度
    const padding = 16.0;
    
    // 考虑顶部的安全区域和头部栏高度（天气栏 + 筛选栏约180px）
    const topBarHeight = 180.0;
    final effectiveTopPadding = safeArea.top + topBarHeight + padding;

    double left = _bubblePosition!.dx - (bubbleWidth / 2);
    double top = _bubblePosition!.dy - bubbleHeight - 50; // 在标记上方显示，考虑三角形指示器的高度
    bool triangleAtBottom = true; // 默认三角形在底部（气泡在上方）

    // 确保气泡不超出左右边界
    if (left < padding) {
      left = padding;
    } else if (left + bubbleWidth > screenSize.width - padding) {
      left = screenSize.width - bubbleWidth - padding;
    }

    // 确保气泡不超出上下边界，考虑顶部栏的高度
    if (top < effectiveTopPadding) {
      top = _bubblePosition!.dy + 50; // 如果上方空间不够，显示在标记下方
      triangleAtBottom = false; // 气泡在下方时，三角形在顶部
    }

    // 如果下方空间也不够，强制显示在可视区域内
    if (top + bubbleHeight > screenSize.height - safeArea.bottom - padding) {
      top = screenSize.height - bubbleHeight - safeArea.bottom - padding;
    }
    
    // 最后确保不会被顶部遮挡
    if (top < effectiveTopPadding) {
      top = effectiveTopPadding;
      triangleAtBottom = false; // 如果被迫显示在顶部区域，三角形应该在顶部
    }

    return Positioned(
      left: left,
      top: top,
      child: AnimatedBuilder(
        animation: _bubbleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _bubbleAnimation.value,
            alignment: Alignment.bottomCenter,
            child: Opacity(
              opacity: _bubbleAnimation.value.clamp(0.0, 1.0),
              child: SpotInfoBubble(
                spot: _selectedSpot!,
                triangleAtBottom: triangleAtBottom,
                onViewDetails: () {
                  _hideBubble();
                  widget.onSpotDetailsRequested?.call(_selectedSpot!);
                },
                onNavigation: () {
                  _hideBubble();
                  widget.onSpotNavigationRequested?.call(_selectedSpot!);
                },
                onClose: _hideBubble,
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _centerOnMyLocation() async {
    if (_mapController == null) return;

    try {
      // 检查位置权限
      final permission = await Permission.location.request();
      if (!permission.isGranted) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('需要位置权限才能定位')),
          );
        }
        return;
      }

      // 获取当前位置
      await _mapController!.location();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('已定位到当前位置'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (error) {
      debugPrint('Error getting location: $error');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('无法获取当前位置')),
        );
      }
    }
  }

  Future<void> _zoomIn() async {
    if (_mapController != null) {
      try {
        await _mapController!.zoomIn();
        _currentZoomLevel += 1;
      } catch (e) {
        debugPrint('Error zooming in: $e');
      }
    }
  }

  Future<void> _zoomOut() async {
    if (_mapController != null) {
      try {
        await _mapController!.zoomOut();
        _currentZoomLevel -= 1;
      } catch (e) {
        debugPrint('Error zooming out: $e');
      }
    }
  }

  /// 更新标记（在spots数据变化时调用）
  void updateMarkers() {
    _setupMarkers();
  }

  /// 获取当前地图边界
  Future<Map<String, double>?> getCurrentMapBounds() async {
    if (_mapController == null) return null;

    try {
      return await _mapController!.getMapBounds();
    } catch (e) {
      debugPrint('Error getting map bounds: $e');
      return null;
    }
  }

  /// 获取当前缩放级别
  Future<double?> getCurrentZoomLevel() async {
    if (_mapController == null) return null;

    try {
      return await _mapController!.getZoomLevel();
    } catch (e) {
      debugPrint('Error getting zoom level: $e');
      return null;
    }
  }
}

class _MapControlButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;

  const _MapControlButton({
    required this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(12),
            child: Icon(
              icon,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      ),
    );
  }
}
