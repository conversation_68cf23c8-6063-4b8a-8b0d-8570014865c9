import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/constants/publish_moment_constants.dart';

class VisibilityCard extends StatelessWidget {
  final String value;
  final bool isSelected;
  final VoidCallback onTap;

  const VisibilityCard({
    Key? key,
    required this.value,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final color = _getColorForValue(value);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: 1.5,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: color.withOpacity(0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: isSelected
                    ? color.withOpacity(0.2)
                    : color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                PublishMomentConstants.visibilityIcons[value],
                size: 24,
                color: color,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    PublishMomentConstants.visibilityOptions[value] ?? '',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? color : Colors.grey.shade800,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getDescriptionForValue(value),
                    style: TextStyle(
                      fontSize: 14,
                      color: isSelected
                          ? color.withOpacity(0.8)
                          : Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  size: 16,
                  color: Colors.white,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getColorForValue(String value) {
    switch (value) {
      case 'public':
        return const Color(0xFF4CAF50);
      case 'followers':
        return const Color(0xFF2196F3);
      case 'private':
        return const Color(0xFF9E9E9E);
      default:
        return const Color(0xFF4CAF50);
    }
  }

  String _getDescriptionForValue(String value) {
    switch (value) {
      case 'public':
        return '所有人可见';
      case 'followers':
        return '仅关注者可见';
      case 'private':
        return '仅自己可见';
      default:
        return '';
    }
  }
}
