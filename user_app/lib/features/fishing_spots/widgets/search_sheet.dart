import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get_it/get_it.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/view_models/search_view_model.dart';

class SearchSheet extends StatefulWidget {
  final Function(String) onSearch;
  final Function(List<SpotSummaryVo> results, {String? query})? onSearchResults;

  const SearchSheet({
    super.key,
    required this.onSearch,
    this.onSearchResults,
  });

  @override
  State<SearchSheet> createState() => _SearchSheetState();
}

class _SearchSheetState extends State<SearchSheet> {
  final TextEditingController _searchController = TextEditingController();
  late SearchViewModel _searchViewModel;
  Position? _currentPosition;
  bool _hasSearched = false;

  @override
  void initState() {
    super.initState();
    _searchViewModel = GetIt.instance<SearchViewModel>();
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    try {
      final permission = await Permission.location.request();
      if (permission.isGranted) {
        _currentPosition = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high,
          timeLimit: const Duration(seconds: 10),
        );
      }
    } catch (e) {
      debugPrint('获取位置失败: $e');
    }
  }

  void _performSearch(String query) async {
    if (query.trim().isEmpty) return;
    
    debugPrint('🔍 [SearchSheet] Starting search for: "$query"');
    
    setState(() {
      _hasSearched = true;
    });
    
    // 执行Algolia搜索
    await _searchViewModel.searchSpots(
      query: query,
      refresh: true,
      latitude: _currentPosition?.latitude,
      longitude: _currentPosition?.longitude,
      radiusKm: _currentPosition != null ? 100.0 : null,
    );
    
    debugPrint('🔍 [SearchSheet] Search completed. Results: ${_searchViewModel.searchResults.length}');
    debugPrint('🔍 [SearchSheet] Error message: ${_searchViewModel.errorMessage}');
    
    // Debug: Print actual results being passed
    debugPrint('🔍 [SearchSheet] Passing results to callback: ${_searchViewModel.searchResults.map((r) => r.name).toList()}');
    
    // 搜索完成后总是跳转到结果页面（无论有无结果）
    if (widget.onSearchResults != null) {
      widget.onSearchResults!(_searchViewModel.searchResults, query: query);
    } else {
      // 如果没有搜索结果回调，则调用原有回调关闭界面
      widget.onSearch(query);
    }
  }

  void _searchNearby() async {
    if (_currentPosition == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('无法获取当前位置')),
      );
      return;
    }
    
    setState(() {
      _hasSearched = true;
    });
    
    await _searchViewModel.searchNearbySpots(
      latitude: _currentPosition!.latitude,
      longitude: _currentPosition!.longitude,
      radiusKm: 50.0,
    );
    
    // 搜索完成后总是跳转到结果页面（无论有无结果）
    if (widget.onSearchResults != null) {
      widget.onSearchResults!(_searchViewModel.searchResults, query: '附近钓点');
    } else {
      // 如果没有搜索结果回调，则调用原有回调关闭界面
      widget.onSearch('附近钓点');
    }
  }

  Widget _buildMainContent(SearchViewModel searchViewModel) {
    // Show loading state
    if (searchViewModel.isSearching) {
      return Container(
        padding: const EdgeInsets.all(32),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在搜索...'),
            ],
          ),
        ),
      );
    }

    // Show error state
    if (searchViewModel.errorMessage != null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                searchViewModel.errorMessage!,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  // Clear error and return to default state
                  _searchViewModel.clearSearchResults();
                },
                child: const Text('重新搜索'),
              ),
            ],
          ),
        ),
      );
    }

    // Show search results if any
    if (searchViewModel.searchResults.isNotEmpty) {
      return SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '搜索结果 (${searchViewModel.searchResults.length})',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 12),
            ...searchViewModel.searchResults.take(5).map(
              (spot) => _SearchResultItem(
                spot: spot,
                onTap: () {
                  Navigator.pop(context);
                  // Handle spot selection
                },
              ),
            ),
            if (searchViewModel.searchResults.length > 5)
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  // Show full results
                },
                child: Text('查看全部${searchViewModel.searchResults.length}个结果'),
              ),
          ],
        ),
      );
    }

    // Check if we just finished a search with no results
    if (_hasSearched && searchViewModel.searchResults.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 64,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                '没有找到相关钓点',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '试试其他关键词或使用筛选功能',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade500,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  _searchController.clear();
                  _searchViewModel.clearSearchResults();
                  setState(() {
                    _hasSearched = false;
                  });
                },
                child: const Text('重新搜索'),
              ),
            ],
          ),
        ),
      );
    }

    // Show default content (search history, hot searches, etc.)
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Nearby Search Button
          Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 24),
            child: ElevatedButton.icon(
              onPressed: _searchNearby,
              icon: const Icon(Icons.near_me),
              label: const Text('搜索附近钓点'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),

          // Search History
          if (searchViewModel.searchHistory.isNotEmpty) ...[
            _SectionHeader(
              title: '搜索历史',
              action: TextButton(
                onPressed: () {
                  searchViewModel.clearSearchHistory();
                },
                child: const Text('清空'),
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: searchViewModel.searchHistory
                  .map((item) => _SearchChip(
                        label: item,
                        onTap: () => _performSearch(item),
                        onDelete: () {
                          searchViewModel.removeFromHistory(item);
                        },
                      ))
                  .toList(),
            ),
            const SizedBox(height: 24),
          ],

          // Hot Searches
          _SectionHeader(title: '热门搜索'),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: searchViewModel.hotSearches
                .map((item) => _SearchChip(
                      label: item,
                      onTap: () => _performSearch(item),
                      isHot: true,
                    ))
                .toList(),
          ),

          const SizedBox(height: 24),

          // Search Tips
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      size: 20,
                      color: Colors.blue.shade700,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '搜索小贴士',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '• 可以搜索钓点名称、地址或特色\n• 搜索"免费"查找免费钓场\n• 搜索鱼种名称查找相关钓点',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.blue.shade600,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _searchViewModel,
      child: Consumer<SearchViewModel>(
        builder: (context, searchViewModel, child) {
          return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12, bottom: 16),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Search Bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              controller: _searchController,
              autofocus: true,
              textInputAction: TextInputAction.search,
              decoration: InputDecoration(
                hintText: '搜索钓点名称、地址...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {});
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
              ),
              onChanged: (value) {
                setState(() {});
              },
              onSubmitted: _performSearch,
            ),
          ),

          // Main Content Area
          Expanded(
            child: _buildMainContent(searchViewModel),
          ),
        ],
      ),
        );
        },
      ),
    );
  }
}

class _SectionHeader extends StatelessWidget {
  final String title;
  final Widget? action;

  const _SectionHeader({
    required this.title,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade800,
          ),
        ),
        if (action != null) action!,
      ],
    );
  }
}

class _SearchChip extends StatelessWidget {
  final String label;
  final VoidCallback onTap;
  final VoidCallback? onDelete;
  final bool isHot;

  const _SearchChip({
    required this.label,
    required this.onTap,
    this.onDelete,
    this.isHot = false,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isHot ? Colors.orange.shade50 : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isHot ? Colors.orange.shade200 : Colors.grey.shade200,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isHot)
                Icon(
                  Icons.local_fire_department,
                  size: 16,
                  color: Colors.orange.shade600,
                ),
              if (isHot) const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: isHot ? Colors.orange.shade700 : Colors.grey.shade700,
                ),
              ),
              if (onDelete != null) ...[
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: onDelete,
                  child: Icon(
                    Icons.close,
                    size: 16,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class _SearchResultItem extends StatelessWidget {
  final SpotSummaryVo spot;
  final VoidCallback onTap;

  const _SearchResultItem({
    required this.spot,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                // Spot Icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.place,
                    color: Colors.blue.shade600,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                
                // Spot Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        spot.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        spot.address,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                
                // Rating
                if (spot.rating > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.star,
                          size: 14,
                          color: Colors.orange.shade600,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          spot.rating.toStringAsFixed(1),
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
