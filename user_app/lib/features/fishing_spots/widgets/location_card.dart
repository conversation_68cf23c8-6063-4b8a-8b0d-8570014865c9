import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';

class LocationCard extends StatelessWidget {
  final FishingSpotVo? spot;
  final VoidCallback onSelectLocation;
  final VoidCallback onRemoveLocation;

  const LocationCard({
    Key? key,
    required this.spot,
    required this.onSelectLocation,
    required this.onRemoveLocation,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (spot == null) {
      return _buildLocationPrompt(context);
    }
    return _buildSelectedLocationCard(context);
  }

  Widget _buildSelectedLocationCard(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.location_on,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    spot!.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    spot!.address,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: onSelectLocation,
                icon: const Icon(Icons.edit_location_alt),
                label: const Text('更换'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: onRemoveLocation,
                icon: const Icon(Icons.delete_outline),
                label: const Text('移除'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red,
                  side: const BorderSide(color: Colors.red),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),

        // Location badges
        if (spot!.isOfficial || spot!.hasFacilities || spot!.isPaid)
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                if (spot!.isOfficial)
                  Chip(
                    avatar: const Icon(Icons.verified, size: 16),
                    label: const Text('官方认证'),
                    backgroundColor: Colors.green.shade100,
                    side: BorderSide.none,
                    labelStyle: TextStyle(
                      color: Colors.green.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                if (spot!.hasFacilities)
                  Chip(
                    avatar: const Icon(Icons.restaurant, size: 16),
                    label: const Text('设施齐全'),
                    backgroundColor: Colors.blue.shade100,
                    side: BorderSide.none,
                    labelStyle: TextStyle(
                      color: Colors.blue.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                if (spot!.isPaid)
                  Chip(
                    avatar: const Icon(Icons.payments, size: 16),
                    label: const Text('收费钓场'),
                    backgroundColor: Colors.orange.shade100,
                    side: BorderSide.none,
                    labelStyle: TextStyle(
                      color: Colors.orange.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildLocationPrompt(BuildContext context) {
    return Column(
      children: [
        Icon(
          Icons.location_on_outlined,
          size: 48,
          color: Theme.of(context).colorScheme.primary.withOpacity(0.8),
        ),
        const SizedBox(height: 16),
        const Text(
          '选择钓点位置',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '添加位置帮助其他钓友找到好钓点',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 24),
        Row(
          children: [
            Expanded(
              child: FilledButton.icon(
                onPressed: onSelectLocation,
                icon: const Icon(Icons.search),
                label: const Text('搜索钓点'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: onSelectLocation,
                icon: const Icon(Icons.my_location),
                label: const Text('使用当前位置'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
