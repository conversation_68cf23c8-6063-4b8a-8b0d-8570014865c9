import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/fishing_spots/view_models/weather_card_view_model.dart';
import 'package:user_app/features/fishing_spots/models/weather_data_dto.dart';

class WeatherDetailsSheet extends StatelessWidget {
  const WeatherDetailsSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Consumer<WeatherCardViewModel>(
        builder: (context, viewModel, _) {
          if (viewModel.isLoading) {
            return _buildLoadingState();
          }

          final weather = viewModel.weatherData;
          final forecast = viewModel.forecastData;

          return Column(
            children: [
              // Handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(top: 12, bottom: 20),
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header with gradient background
              Container(
                padding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: _getWeatherGradient(weather.weather),
                  ),
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(24)),
                ),
                child: Column(
                  children: [
                    // Location
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.location_on,
                          size: 20,
                          color: Colors.white70,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          weather.city,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Main weather info
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Temperature
                        Text(
                          weather.temperature,
                          style: const TextStyle(
                            fontSize: 72,
                            color: Colors.white,
                            fontWeight: FontWeight.w300,
                            height: 1,
                          ),
                        ),
                        const Padding(
                          padding: EdgeInsets.only(top: 16),
                          child: Text(
                            '°C',
                            style: TextStyle(
                              fontSize: 32,
                              color: Colors.white,
                              fontWeight: FontWeight.w300,
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // Weather description
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        weather.weather,
                        style: const TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Feels like
                    Text(
                      '湿度 ${weather.humidity}%',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),

              // Weather details grid
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Today's details
                      _buildSectionTitle('今日详情'),
                      const SizedBox(height: 16),
                      _buildDetailsGrid(weather),

                      const SizedBox(height: 32),

                      // Fishing conditions
                      _buildSectionTitle('钓鱼条件'),
                      const SizedBox(height: 16),
                      _buildFishingConditions(weather),

                      const SizedBox(height: 32),

                      // 7-day forecast
                      _buildSectionTitle('7天预报'),
                      const SizedBox(height: 16),
                      _buildWeeklyForecast(forecast),

                      const SizedBox(height: 32),

                      // Tips
                      _buildSectionTitle('钓鱼建议'),
                      const SizedBox(height: 16),
                      _buildFishingTips(weather),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            strokeWidth: 3,
            valueColor: AlwaysStoppedAnimation<Color>(
              Colors.blue.shade400,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            '加载天气信息中...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w700,
      ),
    );
  }

  Widget _buildDetailsGrid(weather) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                  child: _buildDetailItem(
                      '当前温度', '${weather.temperature}°C', Icons.thermostat)),
              const SizedBox(width: 16),
              Expanded(
                  child: _buildDetailItem(
                      '精确温度',
                      '${weather.temperatureFloat}°C',
                      Icons.thermostat_outlined)),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                  child: _buildDetailItem(
                      '湿度', '${weather.humidity}%', Icons.water_drop)),
              const SizedBox(width: 16),
              Expanded(
                  child: _buildDetailItem('精确湿度', '${weather.humidityFloat}%',
                      Icons.water_drop_outlined)),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                  child:
                      _buildDetailItem('风向', weather.windDirection, Icons.air)),
              const SizedBox(width: 16),
              Expanded(
                  child: _buildDetailItem(
                      '风力', weather.windPower, Icons.wind_power)),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                  child: _buildDetailItem(
                      '省份', weather.province, Icons.location_on)),
              const SizedBox(width: 16),
              Expanded(
                  child: _buildDetailItem(
                      '更新时间',
                      _formatReportTime(weather.reportTime),
                      Icons.access_time)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 18,
              color: Colors.blue.shade600,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFishingConditions(weather) {
    final conditions = _analyzeFishingConditions(weather);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            conditions['rating'] >= 4
                ? Colors.green.shade50
                : conditions['rating'] >= 2
                    ? Colors.orange.shade50
                    : Colors.red.shade50,
            Colors.white,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: conditions['rating'] >= 4
              ? Colors.green.shade200
              : conditions['rating'] >= 2
                  ? Colors.orange.shade200
                  : Colors.red.shade200,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.grade,
                color: conditions['rating'] >= 4
                    ? Colors.green.shade600
                    : conditions['rating'] >= 2
                        ? Colors.orange.shade600
                        : Colors.red.shade600,
                size: 32,
              ),
              const SizedBox(width: 8),
              Text(
                conditions['label'],
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w700,
                  color: conditions['rating'] >= 4
                      ? Colors.green.shade700
                      : conditions['rating'] >= 2
                          ? Colors.orange.shade700
                          : Colors.red.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...conditions['factors']
              .map<Widget>(
                (factor) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Icon(
                        factor['good'] ? Icons.check_circle : Icons.cancel,
                        size: 20,
                        color: factor['good'] ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          factor['text'],
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
        ],
      ),
    );
  }

  Widget _buildWeeklyForecast(WeatherDataDto forecastData) {
    final forecasts = forecastData.foreCast ?? [];

    if (forecasts.isEmpty) {
      return SizedBox(
        height: 120,
        child: Center(
          child: Text(
            '暂无预报数据',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
        ),
      );
    }

    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: forecasts.length,
        itemBuilder: (context, index) {
          final forecast = forecasts[index];
          return Container(
            width: 80,
            margin: const EdgeInsets.only(right: 12),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: index == 0 ? Colors.blue.shade50 : Colors.grey.shade50,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: index == 0 ? Colors.blue.shade200 : Colors.grey.shade200,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatWeekDay(forecast.week),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight:
                        index == 0 ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
                Icon(
                  _getWeatherIcon(forecast.dayWeather),
                  size: 28,
                  color:
                      index == 0 ? Colors.blue.shade600 : Colors.grey.shade600,
                ),
                Text(
                  '${forecast.dayTemp}°/${forecast.nightTemp}°',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  forecast.dayWeather,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFishingTips(weather) {
    final tips = _generateFishingTips(weather);

    return Column(
      children: tips
          .map((tip) => Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Colors.blue.shade100,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        tip['icon'],
                        size: 18,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            tip['title'],
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            tip['content'],
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey.shade700,
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ))
          .toList(),
    );
  }

  List<Color> _getWeatherGradient(String description) {
    if (description.contains('晴')) {
      return [Colors.blue.shade400, Colors.blue.shade600];
    } else if (description.contains('云') || description.contains('阴')) {
      return [Colors.blueGrey.shade400, Colors.blueGrey.shade600];
    } else if (description.contains('雨')) {
      return [Colors.indigo.shade400, Colors.indigo.shade600];
    } else {
      return [Colors.blue.shade400, Colors.blue.shade600];
    }
  }

  Map<String, dynamic> _analyzeFishingConditions(weather) {
    int rating = 5;
    List<Map<String, dynamic>> factors = [];

    // Parse temperature from string
    final temperature = double.tryParse(weather.temperatureFloat) ??
        double.tryParse(weather.temperature) ??
        20.0;

    // Temperature analysis
    if (temperature >= 15 && temperature <= 25) {
      factors.add({'text': '温度适宜 (${temperature.toInt()}°C)', 'good': true});
    } else {
      factors.add({
        'text':
            '温度${temperature > 25 ? "偏高" : "偏低"} (${temperature.toInt()}°C)',
        'good': false
      });
      rating--;
    }

    // Wind analysis - using wind power description since windSpeed is not available
    final windPower = weather.windPower ?? '微风';
    if (windPower.contains('微风') ||
        windPower.contains('1级') ||
        windPower.contains('2级')) {
      factors.add({'text': '风力适宜 ($windPower)', 'good': true});
    } else {
      factors.add({'text': '风力偏大 ($windPower)', 'good': false});
      rating--;
    }

    // Humidity analysis - using available humidity data
    final humidity = double.tryParse(weather.humidityFloat) ??
        double.tryParse(weather.humidity) ??
        60.0;
    if (humidity >= 50 && humidity <= 80) {
      factors.add({'text': '湿度适宜 (${humidity.toInt()}%)', 'good': true});
    } else {
      factors.add({
        'text': '湿度${humidity > 80 ? "偏高" : "偏低"} (${humidity.toInt()}%)',
        'good': false
      });
      rating--;
    }

    // Weather condition - using available weather field
    final weatherCondition = weather.weather ?? '晴';
    if (weatherCondition.contains('晴') || weatherCondition.contains('多云')) {
      factors.add({'text': '天气状况良好 ($weatherCondition)', 'good': true});
    } else {
      factors.add({'text': '天气状况不佳 ($weatherCondition)', 'good': false});
      rating--;
    }

    String label;
    if (rating >= 4) {
      label = '绝佳';
    } else if (rating >= 3) {
      label = '良好';
    } else if (rating >= 2) {
      label = '一般';
    } else {
      label = '较差';
    }

    return {
      'rating': rating,
      'label': label,
      'factors': factors,
    };
  }

  List<Map<String, dynamic>> _generateFishingTips(weather) {
    List<Map<String, dynamic>> tips = [];

    // Temperature tips
    final temp = double.tryParse(weather.temperature) ?? 20.0;
    if (temp < 15) {
      tips.add({
        'icon': Icons.ac_unit,
        'title': '低温垂钓',
        'content': '水温较低，鱼类活动减少。建议使用小钩细线，选择深水区域，耐心等待。',
      });
    } else if (temp > 25) {
      tips.add({
        'icon': Icons.wb_sunny,
        'title': '高温垂钓',
        'content': '避开中午时段，选择早晚垂钓。注意防晒，多补充水分。',
      });
    }

    // Wind tips - 根据风力等级判断
    if (weather.windPower.contains('大风') || weather.windPower.contains('强风')) {
      tips.add({
        'icon': Icons.air,
        'title': '风天垂钓',
        'content': '选择背风位置，使用较重的铅坠。风浪大时鱼儿易在下风口觅食。',
      });
    }

    // Weather condition tips
    if (weather.weather.contains('雨')) {
      tips.add({
        'icon': Icons.umbrella,
        'title': '雨天垂钓',
        'content': '小雨天气鱼儿活跃，是垂钓好时机。注意防雨装备，确保安全。',
      });
    }

    // General tip
    tips.add({
      'icon': Icons.lightbulb,
      'title': '通用建议',
      'content': '根据目标鱼种选择合适的饵料和钓法。保持安静，避免惊扰鱼群。',
    });

    return tips;
  }

  String _formatReportTime(String reportTime) {
    try {
      final dateTime = DateTime.parse(reportTime);
      return '${dateTime.month}/${dateTime.day} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return reportTime;
    }
  }

  String _formatWeekDay(String week) {
    final weekMap = {
      '1': '周一',
      '2': '周二',
      '3': '周三',
      '4': '周四',
      '5': '周五',
      '6': '周六',
      '7': '周日',
    };
    return weekMap[week] ?? '周$week';
  }

  IconData _getWeatherIcon(String weather) {
    if (weather.contains('晴')) {
      return Icons.wb_sunny;
    } else if (weather.contains('云')) {
      return Icons.cloud;
    } else if (weather.contains('雨')) {
      return Icons.umbrella;
    } else if (weather.contains('雪')) {
      return Icons.ac_unit;
    } else if (weather.contains('雾')) {
      return Icons.blur_on;
    } else {
      return Icons.wb_cloudy;
    }
  }
}
