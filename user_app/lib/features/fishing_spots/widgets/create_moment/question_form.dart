import 'package:flutter/material.dart';

class QuestionForm extends StatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;
  final Map<String, dynamic>? initialData;

  const QuestionForm({
    Key? key,
    required this.onDataChanged,
    this.initialData,
  }) : super(key: key);

  @override
  State<QuestionForm> createState() => _QuestionFormState();
}

class _QuestionFormState extends State<QuestionForm> {
  final TextEditingController _questionTitleController =
      TextEditingController();
  final TextEditingController _detailedProblemController =
      TextEditingController();
  List<String> _selectedTags = [];

  // Question tags
  final List<String> _tags = [
    '入门指导',
    '装备选购',
    '技巧问题',
    '鱼种识别',
    '钓场推荐',
    '鱼饵选择',
    '疑难杂症',
    '装备维护',
    '钓法探讨',
    '渔获处理',
    '天气影响',
    '场地选择'
  ];

  @override
  void initState() {
    super.initState();

    // Initialize with data if available
    if (widget.initialData != null) {
      _questionTitleController.text =
          widget.initialData!['questionTitle'] ?? '';
      _detailedProblemController.text =
          widget.initialData!['detailedProblem'] ?? '';
      _selectedTags = List<String>.from(widget.initialData!['tags'] ?? []);
    }

    // Add listeners to update data
    _questionTitleController.addListener(_updateData);
    _detailedProblemController.addListener(_updateData);
  }

  void _updateData() {
    widget.onDataChanged({
      'questionTitle': _questionTitleController.text,
      'detailedProblem': _detailedProblemController.text,
      'tags': _selectedTags,
    });
  }

  @override
  void dispose() {
    _questionTitleController.dispose();
    _detailedProblemController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          children: [
            Icon(
              Icons.help_outline,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            const Text(
              '问题详情',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Question title field
        TextField(
          controller: _questionTitleController,
          decoration: const InputDecoration(
            labelText: '问题标题 *',
            hintText: '简明扼要地描述您的问题',
            border: OutlineInputBorder(),
          ),
        ),

        const SizedBox(height: 16),

        // Tags multi-select
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '问题标签 (最多选择3个)',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _tags.map((tag) {
                final isSelected = _selectedTags.contains(tag);
                return FilterChip(
                  label: Text(tag),
                  selected: isSelected,
                  onSelected: isSelected || _selectedTags.length < 3
                      ? (selected) {
                          setState(() {
                            if (selected && _selectedTags.length < 3) {
                              _selectedTags.add(tag);
                            } else if (!selected) {
                              _selectedTags.remove(tag);
                            }
                            _updateData();
                          });
                        }
                      : null,
                  selectedColor:
                      Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  checkmarkColor: Theme.of(context).colorScheme.primary,
                  backgroundColor: _selectedTags.length >= 3 && !isSelected
                      ? Colors.grey.shade200
                      : null,
                );
              }).toList(),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Detailed problem field
        TextField(
          controller: _detailedProblemController,
          maxLines: 5,
          decoration: const InputDecoration(
            labelText: '问题详情',
            hintText: '详细描述您遇到的问题，越具体越容易得到准确回答',
            border: OutlineInputBorder(),
            alignLabelWithHint: true,
          ),
        ),

        const SizedBox(height: 16),

        // Help tips card
        Card(
          color: Colors.blue.shade50,
          margin: EdgeInsets.zero,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.tips_and_updates,
                      size: 18,
                      color: Colors.blue.shade700,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '如何提出好问题',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  '• 使用清晰具体的标题\n• 详细描述您的具体情况\n• 添加相关照片辅助说明\n• 说明您已经尝试过的解决方法',
                  style: TextStyle(
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
