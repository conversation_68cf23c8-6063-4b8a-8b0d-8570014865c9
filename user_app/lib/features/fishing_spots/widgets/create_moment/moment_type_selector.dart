import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/constants/publish_moment_constants.dart';

class MomentTypeSelector extends StatelessWidget {
  final String selectedType;
  final Function(String) onTypeSelected;

  const MomentTypeSelector({
    super.key,
    required this.selectedType,
    required this.onTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Row(
          children: [
            Icon(
              Icons.category_outlined,
              size: 20,
              color: colorScheme.primary,
            ),
            const SizedBox(width: 8),
            const Text(
              '动态类型',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        // 水平滚动的类型选择器
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          child: Row(
            children: PublishMomentConstants.momentTypes.map((type) {
              final isSelected = type == selectedType;
              final icon = PublishMomentConstants.momentTypeIcons[type] ??
                  Icons.article_outlined;
              final Color typeColor = _getColorForType(type, colorScheme);

              return Padding(
                padding: const EdgeInsets.only(right: 10),
                child: _buildTypeChip(
                  context: context,
                  type: type,
                  icon: icon,
                  isSelected: isSelected,
                  color: typeColor,
                  onTap: () => onTypeSelected(type),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildTypeChip({
    required BuildContext context,
    required String type,
    required IconData icon,
    required bool isSelected,
    required Color color,
    required VoidCallback onTap,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        color: isSelected ? color.withOpacity(0.1) : Colors.white,
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: isSelected ? color : Colors.grey.shade200,
          width: isSelected ? 2 : 1,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: color.withOpacity(0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(30),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon
                Icon(
                  icon,
                  size: 22,
                  color: isSelected ? color : Colors.grey.shade500,
                ),
                const SizedBox(width: 8),
                // Label
                Text(
                  type,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected ? color : Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getColorForType(String type, ColorScheme colorScheme) {
    switch (type) {
      case '钓获分享':
        return Colors.green;
      case '装备展示':
        return Colors.blue;
      case '技巧分享':
        return Colors.orange;
      case '问答求助':
        return Colors.purple;
      default:
        return colorScheme.primary;
    }
  }
}
