import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';

class FishTypeSelector extends StatefulWidget {
  final List<FishType> availableFishTypes;
  final List<Map<String, dynamic>> caughtFishes;
  final Function(FishType fishType) onFishSelected;
  final Function(FishType) onCustomFishTypeAdded;
  final bool isLoading;

  const FishTypeSelector({
    super.key,
    required this.availableFishTypes,
    required this.caughtFishes,
    required this.onFishSelected,
    required this.onCustomFishTypeAdded,
    this.isLoading = false,
  });

  @override
  State<FishTypeSelector> createState() => _FishTypeSelectorState();
}

class _FishTypeSelectorState extends State<FishTypeSelector>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  // 对不同鱼类分配不同颜色，便于视觉区分
  final List<Color> _fishColors = [
    Colors.blue,
    Colors.green,
    Colors.purple,
    Colors.orange,
    Colors.teal,
    Colors.deepPurple,
    Colors.amber,
    Colors.indigo,
    Colors.cyan,
    Colors.brown,
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  bool _isAlreadyAdded(FishType fishType) {
    return widget.caughtFishes.any(
      (fish) => fish['fishTypeId'] == fishType.id,
    );
  }

  Color _getFishColor(FishType fishType) {
    // 根据ID分配稳定的颜色
    int colorIndex = fishType.id.toInt() % _fishColors.length;
    return _fishColors[colorIndex];
  }

  void _showAddCustomFishDialog() {
    final nameController = TextEditingController();
    bool hasError = false;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20)),
              title: const Text('添加自定义鱼类'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    autofocus: true,
                    decoration: InputDecoration(
                      labelText: '鱼类名称',
                      hintText: '例如：黄鳝、泥鳅等',
                      errorText: hasError ? '请输入鱼类名称' : null,
                      prefixIcon: const Icon(Icons.water),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    onChanged: (_) {
                      if (hasError && nameController.text.isNotEmpty) {
                        setState(() {
                          hasError = false;
                        });
                      }
                    },
                    textInputAction: TextInputAction.done,
                    onSubmitted: (_) {
                      final name = nameController.text.trim();
                      if (name.isEmpty) {
                        setState(() {
                          hasError = true;
                        });
                        return;
                      }

                      // 创建自定义鱼类
                      final customId =
                          10000 + DateTime.now().millisecondsSinceEpoch % 90000;
                      final newFishType = FishType(
                        id: customId,
                        name: name,
                        seasonSpring: true,
                        seasonSummer: true,
                        seasonAutumn: true,
                        seasonWinter: true,
                      );

                      Navigator.of(context).pop();
                      widget.onCustomFishTypeAdded(newFishType);
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                FilledButton(
                  onPressed: () {
                    final name = nameController.text.trim();
                    if (name.isEmpty) {
                      setState(() {
                        hasError = true;
                      });
                      return;
                    }

                    // 创建自定义鱼类
                    final customId =
                        10000 + DateTime.now().millisecondsSinceEpoch % 90000;
                    final newFishType = FishType(
                      id: customId,
                      name: name,
                      seasonSpring: true,
                      seasonSummer: true,
                      seasonAutumn: true,
                      seasonWinter: true,
                    );

                    Navigator.of(context).pop();
                    widget.onCustomFishTypeAdded(newFishType);
                  },
                  child: const Text('添加'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // 整合可用的鱼类（包括系统和自定义）
    List<FishType> combinedFishTypes = widget.availableFishTypes;

    // 添加已捕获但不在可用列表中的自定义鱼类
    for (var fish in widget.caughtFishes) {
      final fishTypeId = fish['fishTypeId'] as int;
      final fishName = fish['fishTypeName'] as String? ?? '未知鱼类';

      // 检查是否已经在可用列表中
      bool exists = combinedFishTypes.any((ft) => ft.id.toInt() == fishTypeId);

      // 如果不存在，添加到可用列表中（重建FishType对象）
      if (!exists) {
        combinedFishTypes = List.from(combinedFishTypes)
          ..add(FishType(
            id: fishTypeId,
            name: fishName,
            seasonSpring: true,
            seasonSummer: true,
            seasonAutumn: true,
            seasonWinter: true,
          ));
      }
    }

    return FadeTransition(
      opacity: _animation,
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 鱼类类型提示标签
            AnimatedOpacity(
              opacity: widget.isLoading ? 0.0 : 1.0,
              duration: const Duration(milliseconds: 300),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    const Icon(Icons.catching_pokemon, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      '可选鱼类 (${widget.availableFishTypes.length})',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // 添加自定义鱼类按钮
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: OutlinedButton.icon(
                onPressed: _showAddCustomFishDialog,
                icon: const Icon(Icons.add),
                label: const Text('添加自定义鱼类'),
                style: OutlinedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12)),
                ),
              ),
            ),

            // 主体鱼类列表
            Flexible(
              child: widget.isLoading
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('正在加载鱼类数据...'),
                        ],
                      ),
                    )
                  : widget.availableFishTypes.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.water_drop_outlined,
                                  size: 48, color: Colors.grey.shade400),
                              const SizedBox(height: 16),
                              Text(
                                '没有可用的鱼类数据',
                                style: TextStyle(color: Colors.grey.shade600),
                              ),
                            ],
                          ),
                        )
                      : GridView.builder(
                          padding: const EdgeInsets.all(16),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 1.5,
                            crossAxisSpacing: 12,
                            mainAxisSpacing: 12,
                          ),
                          itemCount: combinedFishTypes.length,
                          itemBuilder: (context, index) {
                            final fishType = combinedFishTypes[index];
                            final isAlreadyAdded = _isAlreadyAdded(fishType);
                            final fishColor = _getFishColor(fishType);
                            final isCustom = fishType.id.toInt() >=
                                10000; // 自定义鱼类ID通常大于10000

                            return GestureDetector(
                              onTap: isAlreadyAdded
                                  ? () {
                                      // 提示已添加
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text('${fishType.name}已添加'),
                                          behavior: SnackBarBehavior.floating,
                                          duration: const Duration(seconds: 1),
                                        ),
                                      );
                                    }
                                  : () {
                                      // 触觉反馈
                                      HapticFeedback.mediumImpact();
                                      // 选择鱼类
                                      widget.onFishSelected(fishType);
                                    },
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                decoration: BoxDecoration(
                                  color: isAlreadyAdded
                                      ? Colors.grey.shade200
                                      : fishColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(16),
                                  border: Border.all(
                                    color: isAlreadyAdded
                                        ? Colors.grey.shade400
                                        : fishColor.withOpacity(0.5),
                                    width: 1.5,
                                  ),
                                  boxShadow: isAlreadyAdded
                                      ? null
                                      : [
                                          BoxShadow(
                                            color: fishColor.withOpacity(0.1),
                                            blurRadius: 4,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                ),
                                child: Stack(
                                  children: [
                                    // 主要内容
                                    Padding(
                                      padding: const EdgeInsets.all(12),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Row(
                                            children: [
                                              Icon(
                                                isCustom
                                                    ? Icons.create
                                                    : Icons.water,
                                                size: 20,
                                                color: isAlreadyAdded
                                                    ? Colors.grey.shade500
                                                    : fishColor,
                                              ),
                                              const SizedBox(width: 8),
                                              Expanded(
                                                child: Text(
                                                  fishType.name,
                                                  style: TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.bold,
                                                    color: isAlreadyAdded
                                                        ? Colors.grey.shade600
                                                        : colorScheme.onSurface,
                                                  ),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                          if (!isAlreadyAdded) ...[
                                            const SizedBox(height: 8),
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons.add_circle_outline,
                                                  size: 16,
                                                  color: fishColor
                                                      .withOpacity(0.8),
                                                ),
                                                const SizedBox(width: 4),
                                                Text(
                                                  '点击添加',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: fishColor
                                                        .withOpacity(0.8),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),

                                    // 已添加标记
                                    if (isAlreadyAdded)
                                      Positioned(
                                        top: 6,
                                        right: 6,
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: Colors.grey.shade500,
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              const Icon(
                                                Icons.check,
                                                size: 12,
                                                color: Colors.white,
                                              ),
                                              const SizedBox(width: 2),
                                              const Text(
                                                '已添加',
                                                style: TextStyle(
                                                  fontSize: 10,
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
            ),
          ],
        ),
      ),
    );
  }
}
