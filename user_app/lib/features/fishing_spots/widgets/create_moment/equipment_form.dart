import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/fish_type_bottom_sheet.dart';

class EquipmentForm extends StatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;
  final Map<String, dynamic>? initialData;
  final FishingSpotViewModel? fishingSpotViewModel;

  const EquipmentForm({
    super.key,
    required this.onDataChanged,
    this.initialData,
    this.fishingSpotViewModel,
  });

  @override
  State<EquipmentForm> createState() => _EquipmentFormState();
}

class _EquipmentFormState extends State<EquipmentForm> {
  final TextEditingController _equipmentNameController =
      TextEditingController();
  final TextEditingController _brandController = TextEditingController();
  final TextEditingController _modelController = TextEditingController();
  final TextEditingController _priceController = TextEditingController();
  String? _selectedCategory;
  double _rating = 0;

  // Fish type selection
  List<FishType> _selectedFishTypes = [];
  String _selectedFishNames = '';

  // Equipment categories
  final List<String> _categories = [
    '鱼竿',
    '鱼线',
    '鱼钩',
    '浮漂',
    '鱼饵',
    '渔轮',
    '钓箱',
    '支架',
    '抄网',
    '配件',
    '套装',
    '其他'
  ];

  // Category icons for better visual recognition
  final Map<String, IconData> _categoryIcons = {
    '鱼竿': Icons.architecture,
    '鱼线': Icons.linear_scale,
    '鱼钩': Icons.catching_pokemon,
    '浮漂': Icons.water,
    '鱼饵': Icons.emoji_food_beverage,
    '渔轮': Icons.loop,
    '钓箱': Icons.inventory_2,
    '支架': Icons.table_restaurant,
    '抄网': Icons.grid_on,
    '配件': Icons.build,
    '套装': Icons.inventory,
    '其他': Icons.devices_other,
  };

  @override
  void initState() {
    super.initState();

    // Initialize with data if available
    if (widget.initialData != null) {
      _equipmentNameController.text =
          widget.initialData!['equipmentName'] ?? '';
      _brandController.text = widget.initialData!['brand'] ?? '';
      _modelController.text = widget.initialData!['model'] ?? '';
      _priceController.text = widget.initialData!['price'] ?? '';
      _selectedCategory = widget.initialData!['category'];
      _rating = widget.initialData!['rating'] ?? 0.0;

      // Initialize fish type selection if available
      if (widget.initialData!['targetFishTypes'] != null) {
        _selectedFishTypes =
            List<FishType>.from(widget.initialData!['targetFishTypes'] ?? []);
        _updateSelectedFishNames();
      } else if (widget.initialData!['targetFish'] != null) {
        // Backward compatibility
        _selectedFishNames = widget.initialData!['targetFish'] ?? '';
      }
    }

    // Add listeners to update data
    _equipmentNameController.addListener(_updateData);
    _brandController.addListener(_updateData);
    _modelController.addListener(_updateData);
    _priceController.addListener(_updateData);

    // Ensure fish types data is loaded
    _ensureFishTypesLoaded();
  }

  // Ensure fish types data is loaded
  void _ensureFishTypesLoaded() {
    if (widget.fishingSpotViewModel != null &&
        widget.fishingSpotViewModel!.availableFishTypesObjects.isEmpty) {
      widget.fishingSpotViewModel!.loadFishTypes();
    }
  }

  void _updateData() {
    widget.onDataChanged({
      'equipmentName': _equipmentNameController.text,
      'brand': _brandController.text,
      'model': _modelController.text,
      'price': _priceController.text,
      'category': _selectedCategory,
      'rating': _rating,
      'targetFish': _selectedFishNames, // For backward compatibility
      'targetFishTypes': _selectedFishTypes, // New fish type data structure
    });
  }

  void _updateSelectedFishNames() {
    if (_selectedFishTypes.isEmpty) {
      _selectedFishNames = '';
    } else {
      _selectedFishNames =
          _selectedFishTypes.map((fish) => fish.name).join('、');
    }
  }

  void _showFishTypeSelector() {
    // Use view model's fish type data
    List<FishType> availableFishTypes = [];

    if (widget.fishingSpotViewModel != null) {
      availableFishTypes =
          widget.fishingSpotViewModel!.availableFishTypesObjects;
    }

    // If data is not loaded or empty, show a prompt
    if (availableFishTypes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('正在加载鱼种数据，请稍后再试...'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      // Try to load data again
      _ensureFishTypesLoaded();
      return;
    }

    FocusScope.of(context).unfocus();
    FishTypeBottomSheet.show(
      context: context,
      availableFishTypes: availableFishTypes,
      allowMultipleSelection: true,
      selectedFishTypeIds:
          _selectedFishTypes.map((fish) => fish.id.toInt()).toList(),
      title: '选择适用鱼种',
      onMultipleFishSelected: (selectedFishes) {
        setState(() {
          _selectedFishTypes = selectedFishes;
          _updateSelectedFishNames();
          _updateData();
        });
      },
      allowCustomFishType: true,
      onCustomFishTypeAdded: (newFish) {
        // Handle custom fish type addition
        if (widget.fishingSpotViewModel != null) {
          widget.fishingSpotViewModel!.addCustomFishType(newFish);

          // Show notification
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已添加自定义鱼种: ${newFish.name}'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
    );
  }

  @override
  void dispose() {
    _equipmentNameController.dispose();
    _brandController.dispose();
    _modelController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildSectionHeader(
              context, '装备详情', Icons.backpack, '添加有关您钓鱼装备的详细信息'),
        ),

        // Equipment name - required field
        _buildInputField(
          context,
          controller: _equipmentNameController,
          label: '装备名称',
          hint: '输入装备名称',
          icon: Icons.inventory_2,
          isRequired: true,
        ),

        const SizedBox(height: 20),

        // Category selector
        _buildCategorySelector(context),

        const SizedBox(height: 20),

        // Target fish field - NEW ADDITION
        InkWell(
          onTap: _showFishTypeSelector,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(
                color: colorScheme.outline.withOpacity(0.5),
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.eco,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '适用鱼种',
                        style: TextStyle(
                          fontSize: 12,
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _selectedFishNames.isEmpty
                            ? '选择适用鱼种'
                            : _selectedFishNames,
                        style: TextStyle(
                          color: _selectedFishNames.isEmpty
                              ? colorScheme.onSurfaceVariant
                              : colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),

        const SizedBox(height: 20),

        // Brand and model in responsive layout
        LayoutBuilder(
          builder: (context, constraints) {
            // For narrow screens, stack vertically
            if (constraints.maxWidth < 500) {
              return Column(
                children: [
                  _buildInputField(
                    context,
                    controller: _brandController,
                    label: '品牌',
                    hint: '如: 达亿瓦、SHIMANO等',
                    icon: Icons.branding_watermark,
                  ),
                  const SizedBox(height: 20),
                  _buildInputField(
                    context,
                    controller: _modelController,
                    label: '型号',
                    hint: '产品具体型号',
                    icon: Icons.style,
                  ),
                ],
              );
            }
            // For wider screens, place side by side
            else {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: _buildInputField(
                      context,
                      controller: _brandController,
                      label: '品牌',
                      hint: '如: 达亿瓦、SHIMANO等',
                      icon: Icons.branding_watermark,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildInputField(
                      context,
                      controller: _modelController,
                      label: '型号',
                      hint: '产品具体型号',
                      icon: Icons.style,
                    ),
                  ),
                ],
              );
            }
          },
        ),

        const SizedBox(height: 20),

        // Price field
        _buildInputField(
          context,
          controller: _priceController,
          label: '价格',
          hint: '装备价格',
          icon: Icons.price_change,
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          prefix: '¥',
        ),

        const SizedBox(height: 24),

        // Rating section
        _buildRatingSelector(context),

        const SizedBox(height: 24),

        // Tip card
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: colorScheme.primaryContainer.withOpacity(0.5),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: colorScheme.primaryContainer),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.lightbulb_rounded,
                color: colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '发布技巧',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '提供详细的装备信息和您的使用体验，可以帮助其他钓友做出更好的装备选择。如果有实际使用照片，效果会更好。',
                      style: TextStyle(
                        color: colorScheme.onPrimaryContainer,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Section header with icon and optional subtitle
  Widget _buildSectionHeader(BuildContext context, String title, IconData icon,
      [String? subtitle]) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: colorScheme.primary),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        if (subtitle != null) ...[
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.only(left: 36),
            child: Text(
              subtitle,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ),
        ],
      ],
    );
  }

  // Standard input field with consistent styling
  Widget _buildInputField(
    BuildContext context, {
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool isRequired = false,
    TextInputType keyboardType = TextInputType.text,
    String? prefix,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with icon
        Row(
          children: [
            Icon(icon, size: 18, color: colorScheme.onSurfaceVariant),
            const SizedBox(width: 8),
            Text(
              isRequired ? '$label *' : label,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // Text field with consistent styling
        TextField(
          controller: controller,
          keyboardType: keyboardType,
          style: const TextStyle(fontSize: 16),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.grey.shade400),
            filled: true,
            fillColor: theme.colorScheme.surface,
            prefixText: prefix,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: colorScheme.primary, width: 2),
            ),
          ),
        ),
      ],
    );
  }

  // Category selector with grid of options
  Widget _buildCategorySelector(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Icon(Icons.category, size: 18, color: colorScheme.onSurfaceVariant),
            const SizedBox(width: 8),
            Text(
              '装备类别',
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Grid of category options
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            childAspectRatio: 0.9,
            crossAxisSpacing: 10,
            mainAxisSpacing: 10,
          ),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            final isSelected = _selectedCategory == category;

            return InkWell(
              onTap: () {
                setState(() {
                  _selectedCategory = category;
                  _updateData();
                });
              },
              borderRadius: BorderRadius.circular(12),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color: isSelected
                      ? colorScheme.primaryContainer
                      : theme.cardColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        isSelected ? colorScheme.primary : Colors.grey.shade300,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _categoryIcons[category] ?? Icons.category,
                      size: 28,
                      color: isSelected
                          ? colorScheme.primary
                          : Colors.grey.shade600,
                    ),
                    const SizedBox(height: 6),
                    Text(
                      category,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected
                            ? colorScheme.primary
                            : colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  // Rating selector with stars and slider
  Widget _buildRatingSelector(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        Row(
          children: [
            Icon(Icons.star_rate,
                size: 18, color: colorScheme.onSurfaceVariant),
            const SizedBox(width: 8),
            Text(
              '个人评分',
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
                color: colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Rating card with stars and slider
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(
            children: [
              // Star display
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return IconButton(
                    onPressed: () {
                      setState(() {
                        _rating = index + 1.0;
                        _updateData();
                      });
                    },
                    icon: Icon(
                      index < _rating.floor()
                          ? Icons.star_rounded
                          : (index < _rating
                              ? Icons.star_half_rounded
                              : Icons.star_outline_rounded),
                      size: 32,
                      color: Colors.amber,
                    ),
                    padding: const EdgeInsets.all(4),
                    splashRadius: 24,
                  );
                }),
              ),

              // Rating value display
              Text(
                '$_rating/5',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.amber.shade700,
                ),
              ),

              const SizedBox(height: 16),

              // Slider for more precise control
              SliderTheme(
                data: SliderThemeData(
                  thumbShape: const RoundSliderThumbShape(
                    enabledThumbRadius: 10,
                  ),
                  overlayShape: const RoundSliderOverlayShape(
                    overlayRadius: 20,
                  ),
                  thumbColor: Colors.amber,
                  activeTrackColor: Colors.amber,
                  inactiveTrackColor: Colors.grey.shade300,
                  trackHeight: 6,
                ),
                child: Slider(
                  value: _rating,
                  min: 0,
                  max: 5,
                  divisions: 10,
                  onChanged: (value) {
                    setState(() {
                      _rating = value;
                      _updateData();
                    });
                  },
                ),
              ),

              // Rating descriptions
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('较差',
                        style: TextStyle(
                            fontSize: 12, color: Colors.grey.shade600)),
                    Text('一般',
                        style: TextStyle(
                            fontSize: 12, color: Colors.grey.shade600)),
                    Text('很好',
                        style: TextStyle(
                            fontSize: 12, color: Colors.grey.shade600)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
