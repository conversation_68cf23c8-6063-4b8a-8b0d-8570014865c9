import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/create_moment/fish_catches_list.dart';
import 'package:user_app/features/fishing_spots/widgets/create_moment/fish_type_selector.dart';
import 'package:user_app/features/fishing_spots/widgets/create_moment/weight_and_count_dialog.dart';
import 'package:user_app/models/image/uploaded_image.dart';
import 'package:user_app/services/oss_service.dart';

class EnhancedFishingCatchForm extends StatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;
  final Map<String, dynamic>? initialData;
  final FishingSpotViewModel fishingSpotViewModel;

  const EnhancedFishingCatchForm({
    super.key,
    required this.onDataChanged,
    required this.fishingSpotViewModel,
    this.initialData,
  });

  @override
  State<EnhancedFishingCatchForm> createState() =>
      _EnhancedFishingCatchFormState();
}

class _EnhancedFishingCatchFormState extends State<EnhancedFishingCatchForm> {
  final TextEditingController _fishingMethodController =
      TextEditingController();
  final TextEditingController _weatherConditionsController =
      TextEditingController();

  final List<Map<String, dynamic>> _caughtFishes = [];
  String _lastEnteredWeight = '';
  double _totalWeight = 0.0;

  // Image related fields
  final ImagePicker _imagePicker = ImagePicker();
  List<UploadedImage> _catchImages = [];
  bool _isUploadingImages = false;
  late OssService _ossService;

  @override
  void initState() {
    super.initState();
    _ossService = getIt<OssService>();

    // 使用初始数据（如果有）
    if (widget.initialData != null) {
      _fishingMethodController.text =
          widget.initialData!['fishingMethod'] ?? '';
      _weatherConditionsController.text =
          widget.initialData!['weatherConditions'] ?? '';

      if (widget.initialData!['caughtFishes'] != null) {
        final initialCatches = List<Map<String, dynamic>>.from(
            widget.initialData!['caughtFishes']);
        _caughtFishes.addAll(initialCatches.map((fish) => {...fish}));

        if (_caughtFishes.isNotEmpty) {
          _lastEnteredWeight = _caughtFishes.last['weight']?.toString() ?? '';
        }
      }

      // Initialize catch images if available
      if (widget.initialData!['catchImages'] != null &&
          widget.initialData!['catchImages'] is List<UploadedImage>) {
        _catchImages =
            List<UploadedImage>.from(widget.initialData!['catchImages']);
      }
    }

    // 添加监听器更新数据
    _fishingMethodController.addListener(_updateData);
    _weatherConditionsController.addListener(_updateData);

    // 确保鱼类数据已加载或正在加载
    if (widget.fishingSpotViewModel.availableFishTypesObjects.isEmpty &&
        !widget.fishingSpotViewModel.isLoadingFishTypes) {
      widget.fishingSpotViewModel.loadFishTypes();
    }

    // 初始数据加载回调
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _calculateTotalWeight();
        _updateData();
      }
    });
  }

  @override
  void dispose() {
    _fishingMethodController.removeListener(_updateData);
    _weatherConditionsController.removeListener(_updateData);
    _fishingMethodController.dispose();
    _weatherConditionsController.dispose();
    super.dispose();
  }

  // 计算总重量
  void _calculateTotalWeight() {
    double total = 0.0;
    for (var fish in _caughtFishes) {
      if (fish['weight'] != null) {
        total += (fish['weight'] as num).toDouble();
      }
    }
    setState(() {
      _totalWeight = total;
    });
  }

  void _updateData() {
    if (mounted) {
      widget.onDataChanged({
        'fishingMethod': _fishingMethodController.text,
        'weatherConditions': _weatherConditionsController.text,
        'caughtFishes': List<Map<String, dynamic>>.from(_caughtFishes),
        'totalWeight': _totalWeight,
        'catchImages': _catchImages,
      });
    }
  }

  // 显示鱼类选择器
  void _showFishSelectorBottomSheet() {
    if (widget.fishingSpotViewModel.isLoadingFishTypes) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('正在加载鱼类列表，请稍候...'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    if (widget.fishingSpotViewModel.fishTypesError != null &&
        widget.fishingSpotViewModel.availableFishTypesObjects.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Text(widget.fishingSpotViewModel.fishTypesError!),
              TextButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                  widget.fishingSpotViewModel.loadFishTypes();
                },
                child: const Text('重试', style: TextStyle(color: Colors.white)),
              ),
            ],
          ),
          duration: const Duration(seconds: 4),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final currentAvailableFishTypes =
        widget.fishingSpotViewModel.availableFishTypesObjects;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.7,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 顶部把手和标题
                Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      Icon(
                        Icons.set_meal,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        '选择鱼类',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
                const Divider(),

                // 简化版鱼类选择器
                Expanded(
                  child: FishTypeSelector(
                    availableFishTypes: currentAvailableFishTypes,
                    caughtFishes: _caughtFishes,
                    isLoading: widget.fishingSpotViewModel.isLoadingFishTypes,
                    onFishSelected: (selectedFish) {
                      Navigator.pop(context);
                      _showWeightAndCountDialog(selectedFish);
                    },
                    onCustomFishTypeAdded: (FishType newFish) {
                      widget.fishingSpotViewModel.addCustomFishType(newFish);
                      Navigator.pop(context);
                      _showWeightAndCountDialog(newFish);
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 显示重量和数量输入对话框
  Future<void> _showWeightAndCountDialog(FishType fishType) async {
    showDialog<void>(
      context: context,
      builder: (dialogContext) {
        return WeightAndCountDialog(
          fishName: fishType.name,
          initialWeight: _lastEnteredWeight,
          onSubmit: (String validatedWeightText, int count) {
            if (!mounted) return;

            final weight = double.parse(validatedWeightText);
            _addOrUpdateFish(fishType, weight, validatedWeightText, count);
            Navigator.of(dialogContext).pop();
          },
        );
      },
    );
  }

  // 添加或更新鱼
  void _addOrUpdateFish(
      FishType fishType, double weight, String weightText, int count) {
    setState(() {
      _lastEnteredWeight = weightText;

      final existingIndex = _caughtFishes.indexWhere(
        (f) => f['fishTypeId'] == fishType.id,
      );

      if (existingIndex >= 0) {
        // 如果已存在，更新重量和数量
        double oldWeight =
            (_caughtFishes[existingIndex]['weight'] as num).toDouble();
        _caughtFishes[existingIndex]['weight'] = weight;
        _caughtFishes[existingIndex]['fishTypeName'] = fishType.name;
        _caughtFishes[existingIndex]['count'] = count;

        // 更新总重量 (减去旧重量，加上新重量)
        _totalWeight = _totalWeight - oldWeight + weight;
      } else {
        // 如果是新鱼，添加到列表
        _caughtFishes.add({
          'id': DateTime.now().millisecondsSinceEpoch.toString(),
          'fishTypeId': fishType.id.toInt(),
          'fishTypeName': fishType.name,
          'weight': weight,
          'count': count,
        });

        // 更新总重量 (只需加上新重量)
        _totalWeight += weight;
      }

      _updateData();
    });
  }

  // 编辑现有的鱼
  void _editFish(int index) {
    if (index < 0 || index >= _caughtFishes.length) return;

    final fish = _caughtFishes[index];
    final fishTypeId = fish['fishTypeId'];

    // 查找相应的鱼类
    final fishType =
        widget.fishingSpotViewModel.availableFishTypesObjects.firstWhere(
      (f) => f.id.toInt() == fishTypeId,
      orElse: () => FishType(
        id: fishTypeId,
        name: fish['fishTypeName'] ?? '未知鱼类',
        seasonSpring: true,
        seasonSummer: true,
        seasonAutumn: true,
        seasonWinter: true,
      ),
    );

    // 显示编辑对话框
    showDialog<void>(
      context: context,
      builder: (dialogContext) {
        return WeightAndCountDialog(
          fishName: fishType.name,
          initialWeight: fish['weight'].toString(),
          initialCount: fish['count'] ?? 1,
          onSubmit: (String validatedWeightText, int count) {
            if (!mounted) return;

            setState(() {
              // 计算重量变化
              final oldWeight = (fish['weight'] as num).toDouble();
              final newWeight = double.parse(validatedWeightText);
              final weightDiff = newWeight - oldWeight;

              // 更新数据
              _caughtFishes[index]['weight'] = newWeight;
              _caughtFishes[index]['count'] = count;

              // 更新总重量
              _totalWeight += weightDiff;

              _updateData();
            });

            Navigator.of(dialogContext).pop();
          },
        );
      },
    );
  }

  // 移除鱼
  void _removeFish(int index) {
    if (index < 0 || index >= _caughtFishes.length) return;

    setState(() {
      // 减去被移除鱼的重量
      if (_caughtFishes[index]['weight'] != null) {
        _totalWeight -= (_caughtFishes[index]['weight'] as num).toDouble();
      }

      _caughtFishes.removeAt(index);
      _updateData();

      if (_caughtFishes.isNotEmpty) {
        _lastEnteredWeight = _caughtFishes.last['weight']?.toString() ?? '';
      } else {
        _lastEnteredWeight = '';
      }
    });
  }

  // Image picking functionality
  Future<void> _pickCatchImages() async {
    if (_catchImages.length >= 5) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('最多只能添加5张钓获照片'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    setState(() {
      _isUploadingImages = true;
    });

    try {
      final List<XFile> pickedFiles =
          await _imagePicker.pickMultiImage(imageQuality: 80);

      if (pickedFiles.isEmpty) {
        setState(() {
          _isUploadingImages = false;
        });
        return;
      }

      // Calculate how many more images we can add
      final int remainingSlots = 5 - _catchImages.length;
      final List<XFile> filesToProcess = pickedFiles.length > remainingSlots
          ? pickedFiles.sublist(0, remainingSlots)
          : pickedFiles;

      if (pickedFiles.length > remainingSlots) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('只能再添加$remainingSlots张照片，超出部分已忽略'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      // Add images with uploading state
      List<UploadedImage> newImages = filesToProcess
          .map((file) => UploadedImage(file: file, isUploading: true))
          .toList();

      setState(() {
        _catchImages.addAll(newImages);
      });

      // Upload each image
      for (int i = 0; i < newImages.length; i++) {
        final int imageIndex = _catchImages.length - newImages.length + i;

        try {
          // Upload using OssService
          final String ossUrl = await _ossService.saveFile(newImages[i].file);

          // Only update if the widget is still mounted
          if (!mounted) continue;

          // Update image with URL
          setState(() {
            _catchImages[imageIndex] = UploadedImage(
              file: newImages[i].file,
              url: ossUrl,
              isUploading: false,
              uploadProgress: 1.0,
            );
          });
        } catch (e) {
          // Handle upload error
          if (!mounted) continue;

          setState(() {
            _catchImages[imageIndex] = UploadedImage(
              file: newImages[i].file,
              isUploading: false,
              hasError: true,
            );
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('图片上传失败: ${e.toString()}'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('选择图片失败: ${e.toString()}'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isUploadingImages = false;
        });
        _updateData();
      }
    }
  }

  // Remove an image
  void _removeImage(int index) {
    if (index >= 0 && index < _catchImages.length) {
      setState(() {
        _catchImages.removeAt(index);
        _updateData();
      });
    }
  }

  // Retry uploading a failed image
  void _retryImageUpload(int index) async {
    if (index < 0 ||
        index >= _catchImages.length ||
        !_catchImages[index].hasError) {
      return;
    }

    final imageToRetry = _catchImages[index];

    setState(() {
      _catchImages[index] = UploadedImage(
        file: imageToRetry.file,
        isUploading: true,
        hasError: false,
      );
    });

    try {
      // Retry upload
      final String ossUrl = await _ossService.saveFile(imageToRetry.file);

      if (!mounted) return;

      setState(() {
        _catchImages[index] = UploadedImage(
          file: imageToRetry.file,
          url: ossUrl,
          isUploading: false,
          uploadProgress: 1.0,
        );
        _updateData();
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _catchImages[index] = UploadedImage(
          file: imageToRetry.file,
          isUploading: false,
          hasError: true,
        );
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('重新上传失败: ${e.toString()}'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Consumer<FishingSpotViewModel>(
      builder: (context, viewModel, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 渔获记录标题和添加按钮
            _buildSectionHeader(
              context: context,
              title: '渔获记录',
              icon: Icons.set_meal,
              trailing: TextButton.icon(
                onPressed: _showFishSelectorBottomSheet,
                icon: const Icon(Icons.add, size: 18),
                label: const Text('添加渔获'),
                style: TextButton.styleFrom(
                  foregroundColor: colorScheme.primary,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 渔获列表
            FishCatchesList(
              caughtFishes: _caughtFishes,
              viewModel: viewModel,
              onEdit: _editFish,
              onRemove: _removeFish,
              totalWeight: _totalWeight,
            ),

            const SizedBox(height: 24),

            // 钓法和天气
            _buildFlatTextField(
              context: context,
              controller: _fishingMethodController,
              label: '钓法',
              hint: '如: 台钓、路亚等',
              icon: Icons.sports_handball,
            ),

            const SizedBox(height: 16),

            _buildFlatTextField(
              context: context,
              controller: _weatherConditionsController,
              label: '天气情况',
              hint: '如: 晴天、阴天、小雨等',
              icon: Icons.wb_sunny,
            ),

            const SizedBox(height: 20),

            // 提示信息
            if (_caughtFishes.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade100),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.tips_and_updates,
                      color: Colors.blue.shade700,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '提示',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade800,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '点击"添加渔获"按钮记录您的钓获，这将帮助其他钓友了解该水域的鱼情',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.blue.shade800,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
          ],
        );
      },
    );
  }

  // 构建标题和标签
  Widget _buildSectionHeader({
    required BuildContext context,
    required String title,
    required IconData icon,
    Widget? trailing,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const Spacer(),
        if (trailing != null) trailing,
      ],
    );
  }

  // 构建文本输入框
  Widget _buildFlatTextField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool isRequired = false,
    TextInputType keyboardType = TextInputType.text,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标签
        Row(
          children: [
            Icon(icon, size: 18, color: colorScheme.onSurfaceVariant),
            const SizedBox(width: 8),
            Text(
              isRequired ? '$label *' : label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: colorScheme.onSurface,
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // 输入框
        TextField(
          controller: controller,
          keyboardType: keyboardType,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.grey.shade500),
            filled: true,
            fillColor: Colors.grey.shade50,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: colorScheme.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 14,
            ),
          ),
          style: const TextStyle(fontSize: 15),
        ),
      ],
    );
  }
}
