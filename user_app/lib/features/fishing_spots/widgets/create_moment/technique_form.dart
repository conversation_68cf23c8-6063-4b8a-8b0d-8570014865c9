import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/fish_type_bottom_sheet.dart';

class TechniqueForm extends StatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;
  final Map<String, dynamic>? initialData;
  final FishingSpotViewModel? fishingSpotViewModel;

  const TechniqueForm({
    super.key,
    required this.onDataChanged,
    this.initialData,
    this.fishingSpotViewModel,
  });

  @override
  State<TechniqueForm> createState() => _TechniqueFormState();
}

class _TechniqueFormState extends State<TechniqueForm> {
  final TextEditingController _techniqueNameController =
      TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  String? _selectedDifficulty;
  List<String> _selectedEnvironments = [];
  final _formKey = GlobalKey<FormState>();
  bool _techniqueNameHasError = false;

  // 添加鱼种相关字段
  List<FishType> _selectedFishTypes = [];
  String _selectedFishNames = '';

  // Technique difficulties
  final List<String> _difficulties = ['入门级', '初级', '中级', '高级', '专业级'];

  // Fishing environments
  final List<String> _environments = [
    '湖泊',
    '河流',
    '水库',
    '池塘',
    '海边',
    '江河',
    '沼泽',
    '溪流'
  ];

  @override
  void initState() {
    super.initState();

    // Initialize with data if available
    if (widget.initialData != null) {
      _techniqueNameController.text =
          widget.initialData!['techniqueName'] ?? '';
      _descriptionController.text = widget.initialData!['description'] ?? '';
      _selectedDifficulty = widget.initialData!['difficulty'];
      _selectedEnvironments =
          List<String>.from(widget.initialData!['environments'] ?? []);

      // 初始化已选择的鱼种
      if (widget.initialData!['targetFishTypes'] != null) {
        _selectedFishTypes =
            List<FishType>.from(widget.initialData!['targetFishTypes'] ?? []);
        _updateSelectedFishNames();
      } else if (widget.initialData!['targetFish'] != null) {
        // 兼容旧数据格式
        _selectedFishNames = widget.initialData!['targetFish'] ?? '';
      }
    }

    // Add listeners to update data
    _techniqueNameController.addListener(_updateData);
    _descriptionController.addListener(_updateData);

    // 确保鱼种数据已加载
    _ensureFishTypesLoaded();
  }

  // 确保鱼种数据已加载
  void _ensureFishTypesLoaded() {
    if (widget.fishingSpotViewModel != null &&
        widget.fishingSpotViewModel!.availableFishTypesObjects.isEmpty) {
      widget.fishingSpotViewModel!.loadFishTypes();
    }
  }

  void _updateData() {
    final isValid = _formKey.currentState?.validate() ?? false;

    widget.onDataChanged({
      'techniqueName': _techniqueNameController.text,
      'description': _descriptionController.text,
      'targetFish': _selectedFishNames, // 用于向后兼容
      'targetFishTypes': _selectedFishTypes, // 新的鱼种数据结构
      'difficulty': _selectedDifficulty,
      'environments': _selectedEnvironments,
      'isValid': isValid && _techniqueNameController.text.isNotEmpty,
    });

    setState(() {
      _techniqueNameHasError = _techniqueNameController.text.isEmpty;
    });
  }

  void _updateSelectedFishNames() {
    if (_selectedFishTypes.isEmpty) {
      _selectedFishNames = '';
    } else {
      _selectedFishNames =
          _selectedFishTypes.map((fish) => fish.name).join('、');
    }
  }

  void _showDifficultyBottomSheet() {
    FocusScope.of(context).unfocus();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '选择难度级别',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),
              ListView.builder(
                shrinkWrap: true,
                itemCount: _difficulties.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    title: Text(_difficulties[index]),
                    leading: Radio<String>(
                      value: _difficulties[index],
                      groupValue: _selectedDifficulty,
                      onChanged: (value) {
                        setState(() {
                          _selectedDifficulty = value;
                          _updateData();
                        });
                        Navigator.pop(context);
                      },
                    ),
                    onTap: () {
                      setState(() {
                        _selectedDifficulty = _difficulties[index];
                        _updateData();
                      });
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showFishTypeSelector() {
    // 使用view model中的鱼种数据
    List<FishType> availableFishTypes = [];

    if (widget.fishingSpotViewModel != null) {
      // 确保已加载鱼种数据
      if (widget.fishingSpotViewModel!.availableFishTypesObjects.isEmpty) {
        widget.fishingSpotViewModel!.loadFishTypes();
      }
      availableFishTypes =
          widget.fishingSpotViewModel!.availableFishTypesObjects;
    }

    // 如果数据未加载或为空，使用一些默认提示
    if (availableFishTypes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('正在加载鱼种数据，请稍后再试...'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      // 再次尝试加载数据
      _ensureFishTypesLoaded();
      return;
    }

    FocusScope.of(context).unfocus();
    FishTypeBottomSheet.show(
      context: context,
      availableFishTypes: availableFishTypes,
      allowMultipleSelection: true,
      // 允许多选鱼种
      selectedFishTypeIds:
          _selectedFishTypes.map((fish) => fish.id.toInt()).toList(),
      title: '选择适用鱼种',
      onMultipleFishSelected: (selectedFishes) {
        setState(() {
          _selectedFishTypes = selectedFishes;
          _updateSelectedFishNames();
          _updateData();
        });
      },
      allowCustomFishType: true,
      // 允许添加自定义鱼种
      onCustomFishTypeAdded: (newFish) {
        // 处理自定义鱼种的添加逻辑
        if (widget.fishingSpotViewModel != null) {
          widget.fishingSpotViewModel!.addCustomFishType(newFish);

          // 更新选择状态，包含新添加的鱼种
          setState(() {
            _selectedFishTypes.add(newFish);
            _updateSelectedFishNames();
            _updateData();
          });

          // 显示提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已添加自定义鱼种: ${newFish.name}'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      },
    );
  }

  @override
  void dispose() {
    _techniqueNameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Row(
              children: [
                Icon(
                  Icons.auto_awesome,
                  size: 20,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  '技巧详情',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Technique name field
          TextFormField(
            controller: _techniqueNameController,
            decoration: InputDecoration(
              labelText: '技巧名称',
              hintText: '输入钓鱼技巧名称',
              helperText: '必填项',
              helperStyle: TextStyle(
                color: _techniqueNameHasError
                    ? Theme.of(context).colorScheme.error
                    : null,
              ),
              errorText: _techniqueNameHasError ? '请输入技巧名称' : null,
              prefixIcon: const Icon(Icons.title),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(
                vertical: 16,
                horizontal: 16,
              ),
            ),
            textInputAction: TextInputAction.next,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入技巧名称';
              }
              return null;
            },
            onChanged: (value) {
              setState(() {
                _techniqueNameHasError = value.isEmpty;
              });
              _updateData();
            },
          ),

          const SizedBox(height: 20),

          // Difficulty selector - changed to InkWell with bottom sheet
          InkWell(
            onTap: _showDifficultyBottomSheet,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.leaderboard,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '难度级别',
                          style: TextStyle(
                            fontSize: 12,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _selectedDifficulty ?? '选择难度级别',
                          style: TextStyle(
                            color: _selectedDifficulty == null
                                ? Theme.of(context).colorScheme.onSurfaceVariant
                                : Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(Icons.arrow_drop_down),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Target fish field
          InkWell(
            onTap: _showFishTypeSelector,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.eco,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '适用鱼种',
                          style: TextStyle(
                            fontSize: 12,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _selectedFishNames.isEmpty
                              ? '选择适用鱼种'
                              : _selectedFishNames,
                          style: TextStyle(
                            color: _selectedFishNames.isEmpty
                                ? Theme.of(context).colorScheme.onSurfaceVariant
                                : Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(Icons.arrow_drop_down),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Environments multi-select
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.landscape,
                    size: 20,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    '适用环境',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .surfaceVariant
                      .withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Wrap(
                  spacing: 8,
                  runSpacing: 12,
                  children: _environments.map((environment) {
                    final isSelected =
                        _selectedEnvironments.contains(environment);
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          if (isSelected) {
                            _selectedEnvironments.remove(environment);
                          } else {
                            _selectedEnvironments.add(environment);
                          }
                          _updateData();
                        });
                        // Provide haptic feedback on tap
                        HapticFeedback.lightImpact();
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 14,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.2)
                              : Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(24),
                          border: Border.all(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context)
                                    .colorScheme
                                    .outline
                                    .withOpacity(0.5),
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (isSelected) ...[
                              Icon(
                                Icons.check,
                                size: 16,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                              const SizedBox(width: 4),
                            ],
                            Text(
                              environment,
                              style: TextStyle(
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(context).colorScheme.onSurface,
                                fontWeight: isSelected
                                    ? FontWeight.w500
                                    : FontWeight.normal,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Description field
          TextFormField(
            controller: _descriptionController,
            maxLines: 4,
            decoration: InputDecoration(
              labelText: '技巧描述',
              hintText: '详细描述这个钓鱼技巧和使用方法',
              alignLabelWithHint: true,
              prefixIcon: Padding(
                padding: const EdgeInsets.only(bottom: 60),
                child: Icon(Icons.description),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(
                vertical: 16,
                horizontal: 16,
              ),
            ),
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.sentences,
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
