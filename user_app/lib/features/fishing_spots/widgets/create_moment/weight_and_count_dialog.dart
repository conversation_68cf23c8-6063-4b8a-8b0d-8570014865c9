import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class WeightAndCountDialog extends StatefulWidget {
  final String fishName;
  final String initialWeight;
  final int initialCount;
  final Function(String weightText, int count) onSubmit;

  const WeightAndCountDialog({
    super.key,
    required this.fishName,
    required this.initialWeight,
    this.initialCount = 1,
    required this.onSubmit,
  });

  @override
  State<WeightAndCountDialog> createState() => _WeightAndCountDialogState();
}

class _WeightAndCountDialogState extends State<WeightAndCountDialog> {
  late final TextEditingController _weightController;
  late final TextEditingController _countController;
  String? _weightErrorText;
  String? _countErrorText;
  double _sliderWeight = 0.5; // 默认起始重量
  int _selectedCount = 1; // 默认数量

  // 常用重量预设
  final List<double> _commonWeights = [0.5, 1.0, 2.0, 5.0, 10.0, 20.0, 30.0];

  @override
  void initState() {
    super.initState();
    _weightController = TextEditingController();
    _countController =
        TextEditingController(text: widget.initialCount.toString());
    _selectedCount = widget.initialCount;

    // 如果有初始重量，尝试解析
    if (widget.initialWeight.isNotEmpty) {
      try {
        final initialWeight = double.parse(widget.initialWeight);
        _sliderWeight = initialWeight;
        _weightController.text = initialWeight.toStringAsFixed(2);
      } catch (e) {
        _weightController.text = "0.50";
      }
    } else {
      _weightController.text = "0.50";
    }

    // 添加文本控制器监听器
    _weightController.addListener(_onWeightTextChanged);
    _countController.addListener(_onCountTextChanged);
  }

  void _onWeightTextChanged() {
    try {
      final newWeight = double.parse(_weightController.text);
      if (newWeight != _sliderWeight) {
        setState(() {
          _sliderWeight = newWeight.clamp(0.01, 50.0);
        });
      }
    } catch (e) {
      // 文本不是有效数字时不更新滑块
    }
  }

  void _onCountTextChanged() {
    try {
      final newCount = int.parse(_countController.text);
      if (newCount != _selectedCount && newCount >= 1 && newCount <= 100) {
        setState(() {
          _selectedCount = newCount;
        });
      }
    } catch (e) {
      // 文本不是有效数字时不更新选中状态
    }
  }

  void _updateWeightFromSlider(double value) {
    setState(() {
      _sliderWeight = value;
      _weightController.text = value.toStringAsFixed(2);
    });
    // 清除错误提示
    if (_weightErrorText != null) {
      setState(() {
        _weightErrorText = null;
      });
    }
  }

  void _selectCount(int count) {
    // 触觉反馈
    HapticFeedback.lightImpact();

    setState(() {
      _selectedCount = count;
      _countController.text = count.toString();
    });
    // 清除错误提示
    if (_countErrorText != null) {
      setState(() {
        _countErrorText = null;
      });
    }
  }

  bool _validateInputs() {
    bool isValid = true;

    // 验证重量
    try {
      final weight = double.parse(_weightController.text);
      if (weight <= 0) {
        setState(() {
          _weightErrorText = '请输入大于0的重量';
        });
        isValid = false;
      } else {
        setState(() {
          _weightErrorText = null;
        });
      }
    } catch (e) {
      setState(() {
        _weightErrorText = '请输入有效的数字';
      });
      isValid = false;
    }

    // 验证数量
    try {
      final count = int.parse(_countController.text);
      if (count <= 0) {
        setState(() {
          _countErrorText = '请输入大于0的数量';
        });
        isValid = false;
      } else {
        setState(() {
          _countErrorText = null;
        });
      }
    } catch (e) {
      setState(() {
        _countErrorText = '请输入有效的整数';
      });
      isValid = false;
    }

    return isValid;
  }

  void _submitData() {
    if (_validateInputs()) {
      widget.onSubmit(_weightController.text, int.parse(_countController.text));
    }
  }

  @override
  void dispose() {
    _weightController.removeListener(_onWeightTextChanged);
    _countController.removeListener(_onCountTextChanged);
    _weightController.dispose();
    _countController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '添加${widget.fishName}',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Divider(color: colorScheme.outline.withOpacity(0.5)),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 重量部分
            Text(
              '重量',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            // 重量数值显示
            Center(
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeOutQuart,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: colorScheme.primary.withOpacity(0.3),
                    width: 2,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.scale,
                      size: 28,
                      color: colorScheme.primary,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      _sliderWeight.toStringAsFixed(2),
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'kg',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: colorScheme.primary.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // 滑块控件
            SliderTheme(
              data: SliderThemeData(
                thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
                overlayShape: const RoundSliderOverlayShape(overlayRadius: 24),
                thumbColor: colorScheme.primary,
                activeTrackColor: colorScheme.primary,
                inactiveTrackColor: colorScheme.primary.withOpacity(0.2),
                valueIndicatorColor: colorScheme.primaryContainer,
                valueIndicatorTextStyle: TextStyle(
                  color: colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.bold,
                ),
                showValueIndicator: ShowValueIndicator.always,
              ),
              child: Slider(
                min: 0.01,
                max: 50.0,
                divisions: 100,
                // 0.5kg 刻度
                value: _sliderWeight,
                label: "${_sliderWeight.toStringAsFixed(2)} kg",
                onChanged: _updateWeightFromSlider,
              ),
            ),

            // 快速选择重量按钮
            Wrap(
              spacing: 8,
              runSpacing: 8,
              alignment: WrapAlignment.center,
              children: _commonWeights.map((weight) {
                final isSelected = (_sliderWeight - weight).abs() < 0.01;
                return ElevatedButton(
                  onPressed: () => _updateWeightFromSlider(weight),
                  style: ElevatedButton.styleFrom(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    backgroundColor: isSelected
                        ? colorScheme.primary
                        : colorScheme.surfaceVariant,
                    foregroundColor: isSelected
                        ? colorScheme.onPrimary
                        : colorScheme.onSurfaceVariant,
                    elevation: isSelected ? 2 : 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: Text("${weight.toStringAsFixed(1)}kg"),
                );
              }).toList(),
            ),

            const SizedBox(height: 16),

            // 精确输入框
            TextField(
              controller: _weightController,
              keyboardType:
                  const TextInputType.numberWithOptions(decimal: true),
              decoration: InputDecoration(
                labelText: '精确重量 (kg)',
                errorText: _weightErrorText,
                suffixText: 'kg',
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // 鱼的数量
            Text(
              '数量',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            // 常用数量快速选择
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(5, (index) {
                final count = index + 1;
                final isSelected = _selectedCount == count;
                return GestureDetector(
                  onTap: () => _selectCount(count),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? colorScheme.primary
                          : colorScheme.surfaceVariant,
                      shape: BoxShape.circle,
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: colorScheme.primary.withOpacity(0.4),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              )
                            ]
                          : null,
                    ),
                    child: Center(
                      child: Text(
                        count.toString(),
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: isSelected
                              ? colorScheme.onPrimary
                              : colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ),
                );
              }),
            ),

            const SizedBox(height: 16),

            // 精确数量输入框
            TextField(
              controller: _countController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: '精确数量',
                errorText: _countErrorText,
                helperText: '捕获的鱼数量',
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        OutlinedButton(
          onPressed: () => Navigator.pop(context),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: const Text('取消'),
        ),
        FilledButton.icon(
          onPressed: _submitData,
          icon: const Icon(Icons.add),
          label: const Text('添加'),
          style: FilledButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ],
      actionsPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }
}
