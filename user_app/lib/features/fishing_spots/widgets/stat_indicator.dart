import 'package:flutter/material.dart';

class StatIndicator extends StatelessWidget {
  final IconData? icon;
  final String value;
  final String label;
  final Color color;
  final Color? valueColor;
  final Color? labelColor;
  final double? iconSize;
  final double valueFontSize;
  final double labelFontSize;
  final FontWeight valueFontWeight;
  final FontWeight labelFontWeight;
  final bool showIconBackground;

  const StatIndicator({
    super.key,
    this.icon,
    required this.value,
    required this.label,
    required this.color,
    this.valueColor,
    this.labelColor,
    this.iconSize = 22,
    this.valueFontSize = 18,
    this.labelFontSize = 14,
    this.valueFontWeight = FontWeight.bold,
    this.labelFontWeight = FontWeight.normal,
    this.showIconBackground = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (icon != null)
          if (showIconBackground)
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: iconSize,
                color: color,
              ),
            )
          else
            Icon(
              icon,
              size: iconSize,
              color: color,
            ),
        if (icon != null) const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: valueFontSize,
            fontWeight: valueFontWeight,
            color: valueColor ?? color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: labelFontSize,
            fontWeight: labelFontWeight,
            color: labelColor ?? Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  /// Factory constructor for a compact stat indicator (no icon background)
  factory StatIndicator.compact({
    IconData? icon,
    required String value,
    required String label,
    required Color color,
    Color? valueColor,
    Color? labelColor,
  }) {
    return StatIndicator(
      icon: icon,
      value: value,
      label: label,
      color: color,
      valueColor: valueColor,
      labelColor: labelColor,
      iconSize: 16,
      valueFontSize: 14,
      labelFontSize: 12,
      showIconBackground: false,
    );
  }
}
