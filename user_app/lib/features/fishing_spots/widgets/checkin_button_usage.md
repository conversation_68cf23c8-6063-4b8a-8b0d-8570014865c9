# CheckinButton 签到组件使用指南

`CheckinButton` 是一个可复用的钓点签到组件，可以在任何需要展示签到功能的地方使用。

## 基本用法

### 1. 导入组件
```dart
import 'package:user_app/features/fishing_spots/widgets/checkin_button.dart';
```

### 2. 基本使用
```dart
CheckinButton(
  spotId: spot.id,
  spotName: spot.name,
)
```

## 详细配置

### 参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `spotId` | `int` | ✅ | - | 钓点ID |
| `spotName` | `String` | ✅ | - | 钓点名称（用于日志和提示） |
| `onCheckinSuccess` | `Function(int)?` | ❌ | `null` | 签到成功回调，参数为新增的签到次数 |
| `style` | `CheckinButtonStyle` | ❌ | `outlined` | 按钮样式 |
| `buttonText` | `String?` | ❌ | `'签到打卡'` | 按钮文本 |
| `fullWidth` | `bool` | ❌ | `false` | 是否全宽按钮 |
| `icon` | `IconData?` | ❌ | `Icons.check_circle_outline_rounded` | 自定义图标 |

### 按钮样式

```dart
enum CheckinButtonStyle {
  outlined,  // 轮廓按钮（默认）
  elevated,  // 填充按钮  
  text,      // 文本按钮
}
```

## 使用场景示例

### 1. 钓点列表中的签到按钮
```dart
// 在钓点列表项中使用小型签到按钮
Card(
  child: ListTile(
    title: Text(spot.name),
    subtitle: Text(spot.address),
    trailing: CheckinButton(
      spotId: spot.id,
      spotName: spot.name,
      style: CheckinButtonStyle.text,
      buttonText: '签到',
      onCheckinSuccess: (count) {
        // 更新列表项的签到次数
        setState(() {
          spot.checkinCount += count;
        });
      },
    ),
  ),
)
```

### 2. 钓点卡片中的签到按钮
```dart
// 在钓点卡片中使用填充式签到按钮
Card(
  child: Column(
    children: [
      // 钓点信息...
      Padding(
        padding: EdgeInsets.all(16),
        child: CheckinButton(
          spotId: spot.id,
          spotName: spot.name,
          style: CheckinButtonStyle.elevated,
          fullWidth: true,
          onCheckinSuccess: (count) {
            // 显示成功提示或更新数据
            _showSuccessMessage();
            _refreshSpotData();
          },
        ),
      ),
    ],
  ),
)
```

### 3. 地图标记中的签到按钮
```dart
// 在地图弹窗中使用简洁的签到按钮
Widget _buildMapPopup(FishingSpotVo spot) {
  return Container(
    padding: EdgeInsets.all(12),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(spot.name, style: TextStyle(fontWeight: FontWeight.bold)),
        Text(spot.address),
        SizedBox(height: 8),
        CheckinButton(
          spotId: spot.id,
          spotName: spot.name,
          style: CheckinButtonStyle.outlined,
          buttonText: '快速签到',
          icon: Icons.location_on,
        ),
      ],
    ),
  );
}
```

### 4. 钓点详情页面中的签到按钮
```dart
// 在详情页面中使用全宽签到按钮
CheckinButton(
  spotId: spot.id,
  spotName: spot.name,
  style: CheckinButtonStyle.outlined,
  fullWidth: true,
  onCheckinSuccess: (newCheckinCount) {
    // 更新页面数据
    setState(() {
      spot = spot.copyWith(checkinCount: spot.checkinCount + newCheckinCount);
    });
  },
)
```

## 功能特性

### 1. 自动登录状态检查
- 组件会自动检查用户登录状态
- 未登录时显示友好提示并提供登录按钮

### 2. 智能错误处理
- 今日已签到：橙色提示
- 登录过期：橙色提示 + 登录按钮
- 网络错误：灰色提示
- 其他错误：红色提示

### 3. 加载状态显示
- 签到过程中按钮显示加载动画
- 防止重复点击

### 4. 多种视觉样式
- 支持轮廓、填充、文本三种按钮样式
- 支持自定义图标和文本
- 支持全宽布局

### 5. 详细调试日志
- 完整的调试信息输出
- 便于问题排查和性能监控

## 注意事项

1. **权限要求**：签到功能需要用户登录
2. **频率限制**：每天每个钓点只能签到一次
3. **网络依赖**：需要网络连接才能完成签到
4. **状态同步**：使用 `onCheckinSuccess` 回调更新页面状态

## 最佳实践

1. **合理使用样式**：
   - 列表项中使用 `text` 样式
   - 卡片中使用 `outlined` 样式  
   - 主要操作使用 `elevated` 样式

2. **处理成功回调**：
   ```dart
   onCheckinSuccess: (count) {
     // 更新本地数据
     _updateSpotData(count);
     // 可选：刷新相关列表
     _refreshRelatedData();
   }
   ```

3. **提供用户反馈**：
   组件已内置消息提示，无需额外处理

4. **性能优化**：
   - 避免在列表中频繁重建组件
   - 合理使用 `const` 构造函数