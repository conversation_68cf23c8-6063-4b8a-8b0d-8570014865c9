import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/view_models/auth_view_model.dart';

/// 钓点签到按钮组件
/// 
/// 可以在任何需要展示签到功能的地方复用
/// 支持不同的按钮样式和回调
class CheckinButton extends StatefulWidget {
  /// 钓点ID
  final int spotId;
  
  /// 钓点名称（用于日志和提示）
  final String spotName;
  
  /// 签到成功回调 - 传入更新后的签到次数
  final Function(int newCheckinCount)? onCheckinSuccess;
  
  /// 按钮样式类型
  final CheckinButtonStyle style;
  
  /// 按钮文本
  final String? buttonText;
  
  /// 是否全宽按钮
  final bool fullWidth;
  
  /// 自定义图标
  final IconData? icon;

  const CheckinButton({
    super.key,
    required this.spotId,
    required this.spotName,
    this.onCheckinSuccess,
    this.style = CheckinButtonStyle.outlined,
    this.buttonText,
    this.fullWidth = false,
    this.icon,
  });

  @override
  State<CheckinButton> createState() => _CheckinButtonState();
}

class _CheckinButtonState extends State<CheckinButton> {
  bool _isLoading = false;
  bool _hasCheckedInToday = false;

  @override
  void initState() {
    super.initState();
    _checkTodayCheckinStatus();
  }

  /// 检查今天是否已经签到过这个钓点
  Future<void> _checkTodayCheckinStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0]; // yyyy-MM-dd
      final key = 'checkin_${widget.spotId}_$today';
      final hasCheckedIn = prefs.getBool(key) ?? false;
      
      if (mounted) {
        setState(() {
          _hasCheckedInToday = hasCheckedIn;
        });
      }
    } catch (e) {
      debugPrint('检查签到状态失败: $e');
    }
  }

  /// 记录今天已经签到过这个钓点
  Future<void> _markCheckedInToday() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now().toIso8601String().split('T')[0]; // yyyy-MM-dd
      final key = 'checkin_${widget.spotId}_$today';
      await prefs.setBool(key, true);
      
      if (mounted) {
        setState(() {
          _hasCheckedInToday = true;
        });
      }
    } catch (e) {
      debugPrint('记录签到状态失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // 根据签到状态决定按钮文本和图标
    final buttonText = _hasCheckedInToday 
        ? (widget.buttonText?.contains('再次') == true ? widget.buttonText! : '已签到')
        : (widget.buttonText ?? '签到打卡');
    final icon = _hasCheckedInToday 
        ? Icons.check_circle_rounded // 已签到用实心图标
        : (widget.icon ?? Icons.check_circle_outline_rounded); // 未签到用空心图标

    switch (widget.style) {
      case CheckinButtonStyle.elevated:
        return _buildElevatedButton(buttonText, icon);
      case CheckinButtonStyle.text:
        return _buildTextButton(buttonText, icon);
      case CheckinButtonStyle.outlined:
        return _buildOutlinedButton(buttonText, icon);
    }
  }

  Widget _buildOutlinedButton(String text, IconData iconData) {
    final isDisabled = _isLoading || _hasCheckedInToday;
    
    final button = OutlinedButton.icon(
      onPressed: isDisabled ? null : _handleCheckin,
      icon: _isLoading
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Icon(iconData, size: 18),
      label: Text(text),
      style: OutlinedButton.styleFrom(
        foregroundColor: _isLoading 
            ? Colors.grey 
            : (_hasCheckedInToday ? Colors.grey : Colors.green),
        side: BorderSide(
          color: _isLoading
              ? Colors.grey.withValues(alpha: 0.3)
              : (_hasCheckedInToday 
                  ? Colors.grey.withValues(alpha: 0.5)
                  : Colors.green.withValues(alpha: 0.5)),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      ),
    );

    return widget.fullWidth
        ? SizedBox(width: double.infinity, child: button)
        : button;
  }

  Widget _buildElevatedButton(String text, IconData iconData) {
    final isDisabled = _isLoading || _hasCheckedInToday;
    
    final button = ElevatedButton.icon(
      onPressed: isDisabled ? null : _handleCheckin,
      icon: _isLoading
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
            )
          : Icon(iconData, size: 18),
      label: Text(text),
      style: ElevatedButton.styleFrom(
        backgroundColor: _isLoading 
            ? Colors.grey 
            : (_hasCheckedInToday ? Colors.grey : Colors.green),
        foregroundColor: Colors.white,
        disabledBackgroundColor: Colors.grey,
        disabledForegroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        elevation: _hasCheckedInToday ? 0 : 2,
      ),
    );

    return widget.fullWidth
        ? SizedBox(width: double.infinity, child: button)
        : button;
  }

  Widget _buildTextButton(String text, IconData iconData) {
    final isDisabled = _isLoading || _hasCheckedInToday;
    
    return TextButton.icon(
      onPressed: isDisabled ? null : _handleCheckin,
      icon: _isLoading
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2, color: Colors.green),
            )
          : Icon(iconData, size: 18),
      label: Text(text),
      style: TextButton.styleFrom(
        foregroundColor: _isLoading 
            ? Colors.grey 
            : (_hasCheckedInToday ? Colors.grey : Colors.green),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      ),
    );
  }

  Future<void> _handleCheckin() async {
    debugPrint('🔄 [CheckinButton] 开始签到检查，钓点: ${widget.spotName}(${widget.spotId})');

    // 检查用户登录状态
    final authViewModel = context.read<AuthViewModel>();
    final currentUser = authViewModel.currentUser;
    
    // 同时检查SharedPreferences中的token
    final sharedPreferences = getIt<SharedPreferences>();
    final token = sharedPreferences.getString('token');
    
    debugPrint('🔍 [CheckinButton] 当前用户状态: ${currentUser != null ? "已登录(${currentUser.id})" : "未登录"}');
    debugPrint('🔍 [CheckinButton] Token状态: ${token != null ? "存在(长度${token.length})" : "不存在"}');
    
    if (currentUser == null || token == null) {
      debugPrint('⚠️ [CheckinButton] 用户未登录，显示登录提示');
      _showLoginPrompt();
      return;
    }

    debugPrint('✅ [CheckinButton] 用户已登录，开始执行签到');

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('🔄 [CheckinButton] 调用签到服务，钓点ID: ${widget.spotId}');
      
      final fishingSpotService = context.read<FishingSpotService>();
      debugPrint('🔄 [CheckinButton] FishingSpotService实例获取成功');
      
      final success = await fishingSpotService.checkinFishingSpot(widget.spotId);
      debugPrint('🔄 [CheckinButton] 签到API调用完成，结果: $success');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        if (success) {
          debugPrint('✅ [CheckinButton] 签到成功');
          
          // 记录今天已签到的状态
          await _markCheckedInToday();
          
          _showSuccessMessage();
          
          // 调用成功回调，传递更新后的签到次数（这里简单+1，实际应该从API返回）
          widget.onCheckinSuccess?.call(1); // 可以根据需要调整
        } else {
          debugPrint('❌ [CheckinButton] 签到返回false');
          _showErrorMessage('签到失败，请稍后重试');
        }
      }
    } catch (e) {
      debugPrint('❌ [CheckinButton] 签到异常: $e');
      debugPrint('❌ [CheckinButton] 异常类型: ${e.runtimeType}');
      
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        String errorMessage = '签到失败';
        bool shouldShowLoginButton = false;
        
        if (e.toString().contains('今日已签到') || e.toString().contains('已签到')) {
          errorMessage = '今日已签到过了';
          // 如果服务器返回已签到，也标记本地状态
          await _markCheckedInToday();
        } else if (e.toString().contains('用户不存在') || e.toString().contains('401')) {
          errorMessage = '登录已过期，请重新登录';
          shouldShowLoginButton = true;
        } else if (e.toString().contains('网络') || e.toString().contains('Network')) {
          errorMessage = '网络连接失败，请检查网络';
        }

        _showErrorMessage(errorMessage, showLoginButton: shouldShowLoginButton);
      }
    }
  }

  void _showLoginPrompt() {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.person_off, color: Colors.white),
            SizedBox(width: 8),
            Expanded(child: Text('请先登录再进行签到')),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: '去登录',
          textColor: Colors.white,
          onPressed: () {
            // 导航到登录页面
            context.push(AppRoutes.login);
          },
        ),
      ),
    );
  }

  void _showSuccessMessage() {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            SizedBox(width: 8),
            Text('签到成功！'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _showErrorMessage(String message, {bool showLoginButton = false}) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              showLoginButton ? Icons.person_off : Icons.error,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: showLoginButton ? Colors.orange : Colors.red,
        duration: const Duration(seconds: 3),
        action: showLoginButton ? SnackBarAction(
          label: '去登录',
          textColor: Colors.white,
          onPressed: () {
            context.push(AppRoutes.login);
          },
        ) : null,
      ),
    );
  }
}

/// 签到按钮样式枚举
enum CheckinButtonStyle {
  /// 轮廓按钮（默认）
  outlined,
  /// 填充按钮
  elevated,
  /// 文本按钮
  text,
}