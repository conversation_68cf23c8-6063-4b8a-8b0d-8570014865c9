import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/view_models/search_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/fishing_spot_details_modal.dart';

class SearchResultsPage extends StatefulWidget {
  final List<SpotSummaryVo> initialResults;
  final String searchQuery;
  final SearchViewModel searchViewModel;

  const SearchResultsPage({
    super.key,
    required this.initialResults,
    required this.searchQuery,
    required this.searchViewModel,
  });

  @override
  State<SearchResultsPage> createState() => _SearchResultsPageState();
}

class _SearchResultsPageState extends State<SearchResultsPage> {
  final ScrollController _scrollController = ScrollController();
  late List<SpotSummaryVo> _currentResults;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
    
    // Initialize with the results passed to this page instance
    _currentResults = List.from(widget.initialResults);
    
    // Debug: Check SearchViewModel state on init
    debugPrint('🔍 [SearchResultsPage] InitState - ViewModel results: ${widget.searchViewModel.searchResults.length}');
    debugPrint('🔍 [SearchResultsPage] InitState - Initial results: ${widget.initialResults.length}');
    debugPrint('🔍 [SearchResultsPage] InitState - Current results: ${_currentResults.length}');
    debugPrint('🔍 [SearchResultsPage] InitState - Query: "${widget.searchQuery}"');
    debugPrint('🔍 [SearchResultsPage] InitState - Initial results names: ${widget.initialResults.map((r) => r.name).toList()}');
    debugPrint('🔍 [SearchResultsPage] InitState - ViewModel results names: ${widget.searchViewModel.searchResults.map((r) => r.name).toList()}');
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!widget.searchViewModel.isSearching && widget.searchViewModel.hasMore) {
        // Load more results
        widget.searchViewModel.searchSpots(
          query: widget.searchQuery,
          refresh: false,
        );
      }
    }
  }

  void _showSpotDetails(SpotSummaryVo spot) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => FishingSpotDetailsModal(
          spotId: spot.id,
          scrollController: scrollController,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: widget.searchViewModel,
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F7FA),
        appBar: AppBar(
          title: Text('搜索结果'),
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Consumer<SearchViewModel>(
          builder: (context, searchViewModel, child) {
            // For now, let's completely isolate this page instance's data
            // and not be affected by other searches
                
            debugPrint('🔍 [SearchResultsPage] Current results: ${_currentResults.length}');
            debugPrint('🔍 [SearchResultsPage] ViewModel results: ${searchViewModel.searchResults.length}');
            debugPrint('🔍 [SearchResultsPage] Initial results: ${widget.initialResults.length}');
            debugPrint('🔍 [SearchResultsPage] Is searching: ${searchViewModel.isSearching}');
            debugPrint('🔍 [SearchResultsPage] Error: ${searchViewModel.errorMessage}');
            debugPrint('🔍 [SearchResultsPage] Search query: "${widget.searchQuery}"');
            debugPrint('🔍 [SearchResultsPage] Current result names: ${_currentResults.map((r) => r.name).toList()}');
                
            if (_currentResults.isEmpty && !searchViewModel.isSearching) {
              return _buildEmptyState();
            }

            return RefreshIndicator(
              onRefresh: () => searchViewModel.searchSpots(
                query: widget.searchQuery,
                refresh: true,
              ),
              child: ListView.separated(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                itemCount: _currentResults.length + 1,
                separatorBuilder: (context, index) => const SizedBox(height: 12),
                itemBuilder: (context, index) {
                  if (index == 0) {
                    return _buildSearchHeader(searchViewModel, _currentResults);
                  }
                  
                  final spotIndex = index - 1;
                  if (spotIndex < _currentResults.length) {
                    final spot = _currentResults[spotIndex];
                    return _SearchResultCard(
                      spot: spot,
                      onTap: () => _showSpotDetails(spot),
                    );
                  }
                  
                  // Loading indicator at bottom
                  if (searchViewModel.isSearching) {
                    return Container(
                      padding: const EdgeInsets.all(32),
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  }
                  
                  return const SizedBox.shrink();
                },
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSearchHeader(SearchViewModel searchViewModel, List<SpotSummaryVo> results) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.search,
                color: Colors.grey.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '"${widget.searchQuery}"',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '找到 ${results.length} 个相关钓点',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          if (searchViewModel.errorMessage != null) ...[
            const SizedBox(height: 8),
            Text(
              searchViewModel.errorMessage!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.red.shade600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '没有找到相关钓点',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '试试其他关键词或使用筛选功能',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('返回搜索'),
          ),
        ],
      ),
    );
  }
}

class _SearchResultCard extends StatelessWidget {
  final SpotSummaryVo spot;
  final VoidCallback onTap;

  const _SearchResultCard({
    required this.spot,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Spot Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          spot.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.place_outlined,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                spot.address,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // Rating and Price
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if (spot.rating > 0)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.orange.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.star,
                                size: 14,
                                color: Colors.orange.shade600,
                              ),
                              const SizedBox(width: 2),
                              Text(
                                spot.rating.toStringAsFixed(1),
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.orange.shade700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: spot.paid ? Colors.red.shade100 : Colors.green.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          spot.paid ? '付费' : '免费',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: spot.paid ? Colors.red.shade700 : Colors.green.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Features
              if (spot.fishTypeNames.isNotEmpty)
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: spot.fishTypeNames.take(3).map((fishTypeName) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Text(
                        fishTypeName,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              
            ],
          ),
        ),
      ),
    );
  }
}