import 'package:flutter/material.dart';

class FilterBar extends StatelessWidget {
  final bool isMapView;
  final String selectedFilter;
  final List<String> filterOptions;
  final List<String> selectedFishTypes;
  final bool filterHasFacilities;
  final Function(bool) onViewToggle;
  final Function(String) onFilterSelected;
  final VoidCallback onShowFilterDialog;
  final VoidCallback onAddSpotPressed;

  const FilterBar({
    super.key,
    required this.isMapView,
    required this.selectedFilter,
    required this.filterOptions,
    required this.selectedFishTypes,
    required this.filterHasFacilities,
    required this.onViewToggle,
    required this.onFilterSelected,
    required this.onShowFilterDialog,
    required this.onAddSpotPressed,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // View toggle and filter button
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            children: [
              // Simple view toggle buttons
              Container(
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // List view button
                    _buildToggleButton(
                      context: context,
                      icon: Icons.list,
                      label: '列表',
                      isSelected: !isMapView,
                      onTap: () => onViewToggle(false),
                    ),
                    // Map view button
                    _buildToggleButton(
                      context: context,
                      icon: Icons.map,
                      label: '地图',
                      isSelected: isMapView,
                      onTap: () => onViewToggle(true),
                    ),
                  ],
                ),
              ),

              const Spacer(),

              // Filter button with badge
              Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Badge(
                  isLabelVisible: selectedFilter != "全部" ||
                      selectedFishTypes.isNotEmpty ||
                      filterHasFacilities,
                  label: Text((selectedFishTypes.length +
                          (filterHasFacilities ? 1 : 0) +
                          (selectedFilter != "全部" ? 1 : 0))
                      .toString()),
                  child: FilledButton.icon(
                    onPressed: onShowFilterDialog,
                    icon: const Icon(Icons.filter_list, size: 18),
                    label: const Text('筛选', style: TextStyle(fontSize: 13)),
                    style: FilledButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      visualDensity: VisualDensity.compact,
                    ),
                  ),
                ),
              ),

              // Add spot button
              OutlinedButton.icon(
                onPressed: onAddSpotPressed,
                icon: const Icon(Icons.add_location_alt, size: 18),
                label: const Text('添加钓点', style: TextStyle(fontSize: 13)),
                style: OutlinedButton.styleFrom(
                  foregroundColor: colorScheme.primary,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  visualDensity: VisualDensity.compact,
                ),
              ),
            ],
          ),
        ),

        // Elegant filter options with proper Chinese support
        SizedBox(
          height: 34,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: filterOptions.length,
            itemBuilder: (context, index) {
              final filter = filterOptions[index];
              final isSelected = selectedFilter == filter;

              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => onFilterSelected(filter),
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 6),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? colorScheme.primaryContainer
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: isSelected
                              ? colorScheme.primary
                              : Colors.grey.shade300,
                          width: 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          filter,
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: isSelected
                                ? FontWeight.w500
                                : FontWeight.normal,
                            color: isSelected
                                ? colorScheme.primary
                                : Colors.grey.shade700,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  // Helper method to build each toggle button
  Widget _buildToggleButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? colorScheme.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : Colors.grey.shade700,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                color: isSelected ? Colors.white : Colors.grey.shade700,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
