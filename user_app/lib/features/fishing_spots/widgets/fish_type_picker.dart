import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';

/// Modern flat-style fish type picker that allows selecting fish types and adding custom ones
/// Optimized for mobile interactions with clear visual indicators and proper dialog management
class FishTypePicker extends StatefulWidget {
  /// Available fish types to select from
  final List<FishType> availableFishTypes;

  /// Whether multiple fish types can be selected
  final bool allowMultipleSelection;

  /// Currently selected fish type ID (for single selection mode)
  final int? selectedFishTypeId;

  /// Currently selected fish type IDs (for multiple selection mode)
  final List<int> selectedFishTypeIds;

  /// Callback when a fish type is selected (single selection mode)
  final Function(FishType selectedFish)? onFishSelected;

  /// Callback when multiple fish types are selected (multiple selection mode)
  final Function(List<FishType> selectedFishes)? onMultipleFishesSelected;

  /// Callback when a custom fish type is added
  final Function(FishType)? onCustomFishTypeAdded;

  /// Whether to show the "All fish" option (for single selection mode)
  final bool showAllFishOption;

  /// Whether custom fish types can be added
  final bool allowCustomFishType;

  /// Helper text to display above the grid
  final String? helperText;

  /// Whether to show selection indicators on fish type cards
  final bool showSelectionIndicator;

  /// Maximum height of the grid, if exceeded it will scroll
  final double? maxHeight;

  /// Whether to display season indicators on the cards
  final bool displaySeasons;

  /// Whether seasons must be selected when adding a custom fish type
  final bool requireSeasonsForCustomFish;

  const FishTypePicker({
    super.key,
    required this.availableFishTypes,
    this.allowMultipleSelection = false,
    this.selectedFishTypeId,
    this.selectedFishTypeIds = const [],
    this.onFishSelected,
    this.onMultipleFishesSelected,
    this.onCustomFishTypeAdded,
    this.showAllFishOption = false,
    this.allowCustomFishType = true,
    this.helperText,
    this.showSelectionIndicator = true,
    this.maxHeight,
    this.displaySeasons = false, // Default to false (like in FishingCatchForm)
    this.requireSeasonsForCustomFish = false, // Default to false
  });

  @override
  State<FishTypePicker> createState() => _FishTypePickerState();
}

class _FishTypePickerState extends State<FishTypePicker> {
  // Current selected fish type ID for single selection mode
  int? _selectedFishTypeId;

  // Current selected fish type IDs for multiple selection mode
  List<int> _selectedFishTypeIds = [];

  // --- Alpha constants for clarity ---
  static const int alpha10 = 26; // (0.1 * 255).round()
  static const int alpha15 = 38; // (0.15 * 255).round()
  static const int alpha20 = 51; // (0.2 * 255).round()
  static const int alpha30 = 77; // (0.3 * 255).round()
  static const int alpha40 = 102; // (0.4 * 255).round()
  static const int alpha50 = 128; // (0.5 * 255).round()
  static const int alpha70 = 179; // (0.7 * 255).round()
  static const int alpha08 = 20; // (0.08 * 255).round()
  static const int alpha05 = 13; // (0.05 * 255).round()
  // --- End Alpha constants ---

  @override
  void initState() {
    super.initState();
    if (widget.allowMultipleSelection) {
      _selectedFishTypeIds = List.from(widget.selectedFishTypeIds);
    } else {
      _selectedFishTypeId = widget.selectedFishTypeId;
    }
  }

  @override
  void didUpdateWidget(FishTypePicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedFishTypeId != oldWidget.selectedFishTypeId) {
      _selectedFishTypeId = widget.selectedFishTypeId;
    }
    if (widget.selectedFishTypeIds != oldWidget.selectedFishTypeIds) {
      _selectedFishTypeIds = List.from(widget.selectedFishTypeIds);
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: 50,
              maxHeight: widget.maxHeight ?? constraints.maxHeight,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (widget.helperText != null) ...[
                  Text(
                    widget.helperText!,
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                if (!widget.allowMultipleSelection && widget.showAllFishOption)
                  _buildAllFishOption(context),
                Flexible(
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount:
                          _calculateCrossAxisCount(constraints.maxWidth),
                      childAspectRatio: 0.95,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                    ),
                    itemCount: widget.availableFishTypes.length +
                        (widget.allowCustomFishType ? 1 : 0),
                    itemBuilder: (context, index) {
                      if (widget.allowCustomFishType &&
                          index == widget.availableFishTypes.length) {
                        return _buildAddCustomButton(context);
                      }
                      final fishType = widget.availableFishTypes[index];
                      final fishTypeId = fishType.id.toInt();
                      final bool isSelected = widget.allowMultipleSelection
                          ? _selectedFishTypeIds.contains(fishTypeId)
                          : _selectedFishTypeId == fishTypeId;
                      final bool isCustom = fishTypeId > 10000;
                      return _buildFishTypeCard(
                        context,
                        fishType,
                        isSelected,
                        isCustom: isCustom,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  int _calculateCrossAxisCount(double width) {
    if (width < 300) return 2;
    if (width < 500) return 3;
    if (width < 700) return 4;
    return 5;
  }

  Widget _buildAllFishOption(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final bool isSelected = _selectedFishTypeId == null;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedFishTypeId = null;
          });
          if (widget.onFishSelected != null) {
            widget.onFishSelected!(FishType(
              id: -1,
              name: "所有鱼类",
              seasonSpring: true,
              seasonSummer: true,
              seasonAutumn: true,
              seasonWinter: true,
            ));
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: isSelected
                ? colorScheme.primary.withAlpha(alpha10)
                : Colors.white, // FIXED
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? colorScheme.primary
                  : colorScheme.outline.withAlpha(alpha50), // FIXED
              width: isSelected ? 2 : 1,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                        color: colorScheme.primary.withAlpha(alpha15), // FIXED
                        blurRadius: 4,
                        spreadRadius: 0,
                        offset: const Offset(0, 2))
                  ]
                : null,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: isSelected
                      ? colorScheme.primary.withAlpha(alpha20) // FIXED
                      : colorScheme.surfaceContainerHighest.withAlpha(alpha50),
                  // FIXED
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(Icons.all_inclusive,
                    color: isSelected
                        ? colorScheme.primary
                        : colorScheme.onSurfaceVariant,
                    size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('所有鱼类',
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w500,
                          color: isSelected
                              ? colorScheme.primary
                              : colorScheme.onSurface,
                        )),
                    const SizedBox(height: 4),
                    Text('价格适用于所有鱼类',
                        style: TextStyle(
                            fontSize: 12, color: colorScheme.onSurfaceVariant)),
                  ],
                ),
              ),
              if (isSelected)
                Icon(Icons.check_circle, color: colorScheme.primary, size: 22),
            ],
          ),
        ),
      ),
    );
  }

  // Modify the _buildFishTypeCard method in the FishTypePicker class
// Find this method in lib/features/fishing_spots/widgets/fish_type_picker.dart

  Widget _buildFishTypeCard(
      BuildContext context, FishType fishType, bool isSelected,
      {bool isCustom = false}) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final fishTypeId = fishType.id.toInt();

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          setState(() {
            if (widget.allowMultipleSelection) {
              if (_selectedFishTypeIds.contains(fishTypeId)) {
                _selectedFishTypeIds.remove(fishTypeId);
              } else {
                _selectedFishTypeIds.add(fishTypeId);
              }
              if (widget.onMultipleFishesSelected != null) {
                final selectedFishes = _selectedFishTypeIds.map((id) {
                  try {
                    return widget.availableFishTypes
                        .firstWhere((type) => type.id.toInt() == id);
                  } catch (e) {
                    debugPrint(
                        'Fish type with ID $id not found in available fish types');
                    if (widget.availableFishTypes.isNotEmpty) {
                      return widget.availableFishTypes.first;
                    } else {
                      throw Exception(
                          'No fish types available and fish type $id not found');
                    }
                  }
                }).toList();
                widget.onMultipleFishesSelected!(selectedFishes);
              }
            } else {
              _selectedFishTypeId = fishTypeId;
              if (widget.onFishSelected != null) {
                widget.onFishSelected!(fishType);
              }
            }
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Ink(
          decoration: BoxDecoration(
            color: isSelected
                ? colorScheme.primary.withAlpha(alpha08)
                : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? colorScheme.primary
                  : colorScheme.outline.withAlpha(alpha50),
              width: isSelected ? 2 : 1,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                        color: colorScheme.primary.withAlpha(alpha15),
                        blurRadius: 4,
                        spreadRadius: 0,
                        offset: const Offset(0, 2))
                  ]
                : null,
          ),
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Remove or comment out the checkbox code for multi-selection
                    // if (widget.allowMultipleSelection &&
                    //     widget.showSelectionIndicator) ...[
                    //   Container(
                    //     width: 22,
                    //     height: 22,
                    //     margin: const EdgeInsets.only(bottom: 8),
                    //     decoration: BoxDecoration(
                    //       color: isSelected
                    //           ? colorScheme.primary
                    //           : Colors.transparent,
                    //       border: Border.all(
                    //         color: isSelected
                    //             ? colorScheme.primary
                    //             : colorScheme.outline,
                    //         width: 2,
                    //       ),
                    //       borderRadius: BorderRadius.circular(4),
                    //     ),
                    //     child: isSelected
                    //         ? Icon(Icons.check,
                    //             size: 16, color: colorScheme.onPrimary)
                    //         : null,
                    //   ),
                    // ],
                    Flexible(
                      child: Text(
                        fishType.name,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w500,
                          color: isSelected
                              ? colorScheme.primary
                              : colorScheme.onSurface,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (widget.displaySeasons) ...[
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (fishType.seasonSpring)
                            _buildSeasonDot(Colors.green),
                          if (fishType.seasonSummer)
                            _buildSeasonDot(Colors.orange),
                          if (fishType.seasonAutumn)
                            _buildSeasonDot(Colors.amber),
                          if (fishType.seasonWinter)
                            _buildSeasonDot(Colors.blue),
                        ],
                      ),
                    ],
                    // Modify this to show in both single and multi selection
                    if (isSelected) ...[
                      const SizedBox(height: 8),
                      Icon(
                        Icons.check_circle,
                        color: colorScheme.primary,
                        size: 16,
                      ),
                    ],
                  ],
                ),
              ),
              if (isCustom)
                Positioned(
                  top: 4,
                  right: 4,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.tertiary,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Icon(Icons.edit,
                        size: 10, color: theme.colorScheme.onTertiary),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSeasonDot(Color color) {
    return Container(
      width: 8,
      height: 8,
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildAddCustomButton(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final bool canAddCustom = widget.onCustomFishTypeAdded != null;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: canAddCustom
            ? () => _showAddCustomFishDialog(context, widget.displaySeasons,
                widget.requireSeasonsForCustomFish)
            : null,
        borderRadius: BorderRadius.circular(12),
        child: Ink(
          decoration: BoxDecoration(
            color: canAddCustom
                ? colorScheme.primaryContainer.withAlpha(alpha30) // FIXED
                : colorScheme.surfaceContainerHighest.withAlpha(alpha30),
            // FIXED
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: canAddCustom
                  ? colorScheme.primary.withAlpha(alpha50) // FIXED
                  : colorScheme.outline.withAlpha(alpha30), // FIXED
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add_circle_outline,
                size: 28,
                color: canAddCustom
                    ? colorScheme.primary
                    : colorScheme.onSurfaceVariant.withAlpha(alpha40),
              ), // FIXED
              const SizedBox(height: 8),
              Text(
                canAddCustom ? '自定义鱼类' : '请先加载鱼类数据',
                style: TextStyle(
                  fontSize: 13,
                  color: canAddCustom
                      ? colorScheme.primary
                      : colorScheme.onSurfaceVariant
                          .withAlpha(alpha70), // FIXED
                  fontWeight:
                      canAddCustom ? FontWeight.w500 : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddCustomFishDialog(
      BuildContext context, bool showSeasonSelector, bool requireSeasons) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    bool spring = true, summer = true, autumn = true, winter = false;
    bool hasNameError = false;
    String? seasonErrorText;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(alpha20), // FIXED
                    blurRadius: 10, offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(20, 8, 20, 16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Center(
                        child: Container(
                          width: 40,
                          height: 4,
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade400,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: colorScheme.primary.withAlpha(alpha10),
                              shape: BoxShape.circle,
                            ), // FIXED
                            child: Icon(
                              Icons.add_circle_outline,
                              color: colorScheme.primary,
                              size: 22,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '添加自定义鱼类',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.onSurface,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '自定义鱼类将添加到可选择的鱼种列表中',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: () => Navigator.pop(dialogContext),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Flexible(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  /* ... name label ... */
                                ],
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                controller: nameController,
                                autofocus: true,
                                decoration: InputDecoration(
                                  hintText: '例如：黄鳝、泥鳅等',
                                  helperText: '输入鱼类的名称，必填',
                                  errorText: hasNameError ? '请输入鱼类名称' : null,
                                  prefixIcon: const Icon(Icons.water),
                                  filled: true,
                                  fillColor: Colors.grey.shade50,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                onChanged: (_) {
                                  if (hasNameError &&
                                      nameController.text.isNotEmpty) {
                                    setState(() {
                                      hasNameError = false;
                                    });
                                  }
                                },
                              ),
                              const SizedBox(height: 20),
                              if (showSeasonSelector)
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withAlpha(alpha05),
                                    // FIXED
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                        color: Colors.blue
                                            .withAlpha(alpha20)), // FIXED
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          /* ... season label ... */
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      const Text(
                                        '选择这种鱼最适合钓鱼的季节',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.black87,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      Wrap(
                                        spacing: 12,
                                        runSpacing: 12,
                                        children: [
                                          _buildSeasonChip(
                                            context: context,
                                            label: '春季',
                                            color: Colors.green,
                                            icon: Icons.eco,
                                            isSelected: spring,
                                            onToggle: () => setState(
                                                () => spring = !spring),
                                          ),
                                          _buildSeasonChip(
                                            context: context,
                                            label: '夏季',
                                            color: Colors.orange,
                                            icon: Icons.wb_sunny,
                                            isSelected: summer,
                                            onToggle: () => setState(
                                                () => summer = !summer),
                                          ),
                                          _buildSeasonChip(
                                            context: context,
                                            label: '秋季',
                                            color: Colors.amber,
                                            icon: Icons.landscape,
                                            isSelected: autumn,
                                            onToggle: () => setState(
                                                () => autumn = !autumn),
                                          ),
                                          _buildSeasonChip(
                                            context: context,
                                            label: '冬季',
                                            color: Colors.blue,
                                            icon: Icons.ac_unit,
                                            isSelected: winter,
                                            onToggle: () => setState(
                                                () => winter = !winter),
                                          ),
                                        ],
                                      ),
                                      if (seasonErrorText != null)
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 12),
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.error_outline,
                                                size: 16,
                                                color: colorScheme.error,
                                              ),
                                              const SizedBox(width: 8),
                                              Text(
                                                seasonErrorText!,
                                                style: TextStyle(
                                                  fontSize: 14,
                                                  color: colorScheme.error,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () => Navigator.pop(dialogContext),
                              style: OutlinedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 14),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: const Text('取消'),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: FilledButton(
                              onPressed: () {
                                final customName = nameController.text.trim();
                                bool seasonValid = true;
                                setState(() {
                                  hasNameError = customName.isEmpty;
                                  seasonErrorText = null;
                                });
                                if (hasNameError) return;
                                if (requireSeasons) {
                                  if (!spring &&
                                      !summer &&
                                      !autumn &&
                                      !winter) {
                                    seasonValid = false;
                                    setState(() {
                                      seasonErrorText = '请至少选择一个适钓季节';
                                    });
                                  }
                                }
                                if (!seasonValid) return;
                                final customId = 10000 +
                                    DateTime.now().millisecondsSinceEpoch %
                                        90000;
                                final newFishType = FishType(
                                  id: customId,
                                  name: customName,
                                  description: descriptionController.text
                                          .trim()
                                          .isNotEmpty
                                      ? descriptionController.text.trim()
                                      : null,
                                  seasonSpring:
                                      showSeasonSelector ? spring : true,
                                  seasonSummer:
                                      showSeasonSelector ? summer : true,
                                  seasonAutumn:
                                      showSeasonSelector ? autumn : true,
                                  seasonWinter:
                                      showSeasonSelector ? winter : true,
                                );
                                Navigator.pop(dialogContext);
                                if (widget.onCustomFishTypeAdded != null) {
                                  // Call the callback
                                  widget.onCustomFishTypeAdded!(newFishType);
                                }
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('已添加自定义鱼类：$customName'),
                                    behavior: SnackBarBehavior.floating,
                                    backgroundColor: Colors.green,
                                  ),
                                );
                              },
                              style: FilledButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 14),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: const Text('添加'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSeasonChip({
    required BuildContext context,
    required String label,
    required Color color,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onToggle,
  }) {
    return InkWell(
      onTap: onToggle,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? color.withAlpha(alpha15) : Colors.white, // FIXED
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey.shade600,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                color: isSelected ? color : Colors.grey.shade800,
              ),
            ),
            if (isSelected) ...[
              const SizedBox(width: 8),
              Icon(
                Icons.check_circle,
                color: color,
                size: 16,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
