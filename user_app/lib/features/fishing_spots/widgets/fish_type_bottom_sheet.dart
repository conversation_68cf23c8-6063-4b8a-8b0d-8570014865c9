import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/widgets/fish_type_picker.dart';

/// 通用鱼类选择底部弹出Sheet
/// 可用于各种需要选择鱼类的场景，支持单选和多选，支持添加鱼类重量
class FishTypeBottomSheet extends StatefulWidget {
  /// 可用的鱼类列表
  final List<FishType> availableFishTypes;

  /// 是否允许多选
  final bool allowMultipleSelection;

  /// 是否需要输入重量
  final bool requireWeight;

  /// 已选择的鱼类ID（单选模式）
  final int? selectedFishTypeId;

  /// 已选择的鱼类ID列表（多选模式）
  final List<int> selectedFishTypeIds;

  /// 单选模式下选择回调
  final Function(FishType selectedFish, String? weight)? onFishSelected;

  /// 多选模式下选择回调
  final Function(List<FishType> selectedFishes)? onMultipleFishSelected;

  /// 标题文本
  final String title;

  /// 是否支持添加自定义鱼类
  final bool allowCustomFishType;

  /// 自定义鱼类添加回调
  final Function(FishType)? onCustomFishTypeAdded;

  /// 初始重量值（用于预填充重量字段）
  final String? initialWeight;
  final bool displaySeasons;
  final bool requireSeasonsForCustomFish;

  const FishTypeBottomSheet({
    super.key,
    required this.availableFishTypes,
    this.allowMultipleSelection = false,
    this.requireWeight = false,
    this.selectedFishTypeId,
    this.selectedFishTypeIds = const [],
    this.onFishSelected,
    this.onMultipleFishSelected,
    this.title = '选择鱼类',
    this.allowCustomFishType = true,
    this.onCustomFishTypeAdded,
    this.initialWeight,
    this.displaySeasons = false,
    this.requireSeasonsForCustomFish = false,
  });

  /// 显示鱼类选择底部弹出Sheet的静态方法
  static Future<void> show({
    required BuildContext context,
    required List<FishType> availableFishTypes,
    bool allowMultipleSelection = false,
    bool requireWeight = false,
    int? selectedFishTypeId,
    List<int> selectedFishTypeIds = const [],
    Function(FishType selectedFish, String? weight)? onFishSelected,
    Function(List<FishType> selectedFishes)? onMultipleFishSelected,
    String title = '选择鱼类',
    bool allowCustomFishType = true,
    Function(FishType)? onCustomFishTypeAdded,
    String? initialWeight,
    bool displaySeasons = false,
    bool requireSeasonsForCustomFish = false,
  }) async {
    // Print debug info
    print(
        '[FishTypeBottomSheet] Opening with ${availableFishTypes.length} fish types');

    // Create a mutable copy of the fish types list that can be updated during the bottomsheet lifetime
    final List<FishType> fishTypesList =
        List<FishType>.from(availableFishTypes);

    // Create a custom fish type added callback wrapper that updates the local list
    Function(FishType)? customFishTypeAddedWrapper;
    if (onCustomFishTypeAdded != null) {
      customFishTypeAddedWrapper = (FishType newFish) {
        // Call the original callback
        onCustomFishTypeAdded(newFish);

        // Also update our local list
        fishTypesList.add(newFish);
      };
    }

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        print(
            '[FishTypeBottomSheet] Building sheet with ${fishTypesList.length} fish types');

        return FishTypeBottomSheet(
          availableFishTypes: fishTypesList,
          allowMultipleSelection: allowMultipleSelection,
          requireWeight: requireWeight,
          selectedFishTypeId: selectedFishTypeId,
          selectedFishTypeIds: selectedFishTypeIds,
          onFishSelected: onFishSelected,
          onMultipleFishSelected: onMultipleFishSelected,
          title: title,
          allowCustomFishType: allowCustomFishType,
          onCustomFishTypeAdded: customFishTypeAddedWrapper,
          initialWeight: initialWeight,
          displaySeasons: displaySeasons,
          requireSeasonsForCustomFish: requireSeasonsForCustomFish,
        );
      },
    );
  }

  @override
  State<FishTypeBottomSheet> createState() => _FishTypeBottomSheetState();
}

class _FishTypeBottomSheetState extends State<FishTypeBottomSheet> {
  // 当前选中的鱼类
  FishType? _selectedFishType;

  // 当前选中的多个鱼类ID列表
  List<int> _selectedFishTypeIds = [];

  // 重量控制器
  final TextEditingController _weightController = TextEditingController();

  // 本地鱼类列表 - 用于动态添加自定义鱼类
  late List<FishType> _localFishTypes;

  @override
  void initState() {
    super.initState();

    // 初始化本地鱼类列表
    _localFishTypes = List.from(widget.availableFishTypes);

    // 初始化选中状态
    if (widget.allowMultipleSelection) {
      _selectedFishTypeIds = List.from(widget.selectedFishTypeIds);
    } else if (widget.selectedFishTypeId != null) {
      try {
        _selectedFishType = _localFishTypes.firstWhere(
          (type) => type.id.toInt() == widget.selectedFishTypeId,
        );
      } catch (e) {
        _selectedFishType = null;
      }
    }

    // 如果提供了初始重量值，则设置到控制器中
    if (widget.initialWeight != null && widget.initialWeight!.isNotEmpty) {
      _weightController.text = widget.initialWeight!;
    }
  }

  // Inside _FishTypeBottomSheetState
  void _addCustomFishType(FishType newFish) {
    if (widget.onCustomFishTypeAdded != null) {
      widget
          .onCustomFishTypeAdded!(newFish); // Calls EquipmentForm -> ViewModel
    }
    // Add to local list for immediate display within the sheet
    // The check for duplicates here is less critical if the VM is fixed,
    // but doesn't hurt.
    if (!_localFishTypes.any((fish) => fish.id.toInt() == newFish.id.toInt())) {
      setState(() {
        _localFishTypes.add(newFish);
        print(
            '[BottomSheet] Added custom fish to _localFishTypes: ${newFish.name} (ID: ${newFish.id}), new list size: ${_localFishTypes.length}');
      });
    } else {
      print(
          '[BottomSheet] Custom fish ${newFish.name} (ID: ${newFish.id}) already in _localFishTypes. Skipping add.');
    }
  }

  @override
  void dispose() {
    _weightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print(
        '>>> [BottomSheet Build] Passing _localFishTypes to Picker: ${_localFishTypes.map((f) => '${f.name}(${f.id})').toList()}');
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16,
        right: 16,
        top: 16,
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和关闭按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // 使用FishTypePicker组件
            FishTypePicker(
              availableFishTypes: _localFishTypes,
              allowMultipleSelection: widget.allowMultipleSelection,
              selectedFishTypeId: widget.selectedFishTypeId,
              selectedFishTypeIds: _selectedFishTypeIds,
              allowCustomFishType: widget.allowCustomFishType,
              onFishSelected: (selectedFish) {
                setState(() {
                  _selectedFishType = selectedFish;
                });
              },
              onMultipleFishesSelected: (selectedFishesFromPicker) {
                final newIds = selectedFishesFromPicker
                    .map((fish) => fish.id.toInt())
                    .toList();
                print(
                    '>>>>> [BottomSheet <- Picker] Picker reported selected IDs: $newIds');
                setState(() {
                  _selectedFishTypeIds =
                      newIds; // Update the internal list of IDs
                });
              },
              onCustomFishTypeAdded: _addCustomFishType,
              helperText:
                  '请选择${widget.allowMultipleSelection ? "一个或多个" : "一个"}鱼类',
              displaySeasons: widget.displaySeasons,
              requireSeasonsForCustomFish: widget.requireSeasonsForCustomFish,
            ),

            // 重量输入（仅单选且要求重量时显示）
            if (widget.requireWeight && !widget.allowMultipleSelection) ...[
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '鱼的重量:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                  if (widget.initialWeight != null &&
                      widget.initialWeight!.isNotEmpty)
                    Text(
                      '(上次: ${widget.initialWeight} kg)',
                      style: TextStyle(
                        fontSize: 14,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey.shade600,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextField(
                  controller: _weightController,
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  textInputAction: TextInputAction.done,
                  autofocus: true,
                  decoration: InputDecoration(
                    hintText: '输入重量(kg)',
                    border: InputBorder.none,
                    suffixText: 'kg',
                    suffixIcon: _weightController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear, size: 18),
                            onPressed: () {
                              setState(() {
                                _weightController.clear();
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    // 强制UI更新以显示/隐藏清除按钮
                    setState(() {});
                  },
                ),
              ),
            ],

            const SizedBox(height: 24),

            // 操作按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _onConfirm,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  widget.allowMultipleSelection ? '确认选择' : '添加',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// 确认选择
  void _onConfirm() {
    if (widget.allowMultipleSelection) {
      // --- MODIFICATION START ---
      // 1. Get the potentially duplicated list of selected IDs
      final List<int> currentSelectedIds = _selectedFishTypeIds;
      print(
          '>>>>> [BottomSheet._onConfirm] Raw _selectedFishTypeIds: $currentSelectedIds');

      // 2. Create a Set to get unique IDs
      final Set<int> uniqueSelectedIds = Set<int>.from(currentSelectedIds);
      print(
          '>>>>> [BottomSheet._onConfirm] Unique selected IDs: $uniqueSelectedIds');
      // Multi-select mode
      if (_selectedFishTypeIds.isNotEmpty) {
        final List<FishType> selectedFishes = uniqueSelectedIds.map((id) {
          // Use uniqueSelectedIds here
          try {
            // Find the FishType object from the sheet's local list
            return _localFishTypes.firstWhere(
              (type) => type.id.toInt() == id,
              // Optional: Add orElse for robustness if an ID somehow doesn't match _localFishTypes
              // orElse: () {
              //   print('>>>>> [BottomSheet._onConfirm] Error: ID $id not found in _localFishTypes!');
              //   return FishType(id: id, name: 'Unknown ID $id', /* other defaults */);
              // }
            );
          } catch (e) {
            // Handle cases where firstWhere fails (though it shouldn't if IDs are from _localFishTypes)
            debugPrint(
                '[BottomSheet._onConfirm] Error mapping unique ID $id: $e');
            // Return a placeholder or handle error appropriately
            return FishType(
                id: id,
                name: "Error Fish ID $id",
                seasonSpring: false,
                seasonSummer: false,
                seasonAutumn: false,
                seasonWinter: false);
          }
        }).toList();

        print(
            '>>>>> [BottomSheet._onConfirm] Final unique selectedFishes to send: ${selectedFishes.map((f) => '${f.name}(${f.id})').toList()}');

        // 4. Pass the guaranteed unique list back
        if (widget.onMultipleFishSelected != null) {
          widget.onMultipleFishSelected!(selectedFishes);
        }
        Navigator.pop(context);
      } else {
        // Show hint: select at least one fish type
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('请至少选择一种鱼类'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } else {
      // Single-select mode
      if (_selectedFishType != null) {
        String? weight;
        if (widget.requireWeight) {
          weight = _weightController.text.trim();
          if (weight.isEmpty) {
            // Show hint: please enter fish weight
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('请输入鱼的重量'),
                behavior: SnackBarBehavior.floating,
              ),
            );
            return;
          }
        }

        if (widget.onFishSelected != null) {
          widget.onFishSelected!(_selectedFishType!, weight);
        }
        Navigator.pop(context);
      } else {
        // Show hint: please select a fish type
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('请选择一种鱼类'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}
