import 'dart:async';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:user_app/models/image/uploaded_image.dart';

class ImageUploadGrid extends StatefulWidget {
  final List<UploadedImage> images;
  final bool isUploading;
  final Function(int) onRemoveImage;
  final Function(int) onRetryUpload;
  final Function(int, int) onReorder;
  final Function(List<XFile>) onImagesSelected;
  final int maxImages;
  final double? aspectRatio;
  final double? spacing;
  final int? crossAxisCount;

  const ImageUploadGrid({
    super.key,
    required this.images,
    required this.isUploading,
    required this.onRemoveImage,
    required this.onRetryUpload,
    required this.onReorder,
    required this.onImagesSelected,
    this.maxImages = 9,
    this.aspectRatio,
    this.spacing = 8.0,
    this.crossAxisCount,
  });

  @override
  State<ImageUploadGrid> createState() => _ImageUploadGridState();
}

class _ImageUploadGridState extends State<ImageUploadGrid> {
  final ImagePicker _imagePicker = ImagePicker();

  Future<void> _pickImages() async {
    final List<XFile> images =
        await _imagePicker.pickMultiImage(imageQuality: 80);
    if (images.isEmpty) return;

    // Check if we would exceed the image limit
    if (widget.images.length + images.length > widget.maxImages) {
      final availableSlots = widget.maxImages - widget.images.length;
      images.removeRange(availableSlots, images.length);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('最多只能上传${widget.maxImages}张图片'),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }

    widget.onImagesSelected(images);
  }

  @override
  Widget build(BuildContext context) {
    final crossAxisCount =
        widget.crossAxisCount ?? (widget.images.length > 4 ? 3 : 2);
    final aspectRatio = widget.aspectRatio ?? 1.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Info Row: Count and Add Button
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '已选择 ${widget.images.length}/${widget.maxImages} 张',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
            ),
            if (widget.images.length < widget.maxImages)
              TextButton.icon(
                icon: Icon(
                  Icons.add_circle_outline,
                  size: 18,
                  color: Theme.of(context).colorScheme.primary,
                ),
                label: Text(
                  '继续添加',
                  style:
                      TextStyle(color: Theme.of(context).colorScheme.primary),
                ),
                onPressed: widget.isUploading ? null : _pickImages,
                style:
                    TextButton.styleFrom(visualDensity: VisualDensity.compact),
              ),
          ],
        ),
        const SizedBox(height: 12),

        // Image Grid
        if (widget.images.isEmpty)
          _buildImagePickerButton()
        else
          Wrap(
            spacing: widget.spacing!,
            runSpacing: widget.spacing!,
            children: List.generate(
              widget.images.length,
              (index) => _buildImageItem(widget.images[index], index),
            ),
          ),

        // Help text for reordering
        if (widget.images.length > 1)
          Padding(
            padding: const EdgeInsets.only(top: 12.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.info_outline, size: 14, color: Colors.grey.shade500),
                const SizedBox(width: 4),
                Text(
                  '长按拖动可调整图片顺序',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade500,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildImagePickerButton() {
    return GestureDetector(
      onTap: _pickImages,
      child: Container(
        height: 180,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300, width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 48,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.8),
            ),
            const SizedBox(height: 16),
            Text(
              '添加图片',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '点击选择或拖放图片到此区域',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageItem(UploadedImage image, int index) {
    return SizedBox(
      width: (MediaQuery.of(context).size.width -
              (widget.spacing! * (widget.crossAxisCount ?? 3) + 1)) /
          (widget.crossAxisCount ?? 3),
      height: (MediaQuery.of(context).size.width -
              (widget.spacing! * (widget.crossAxisCount ?? 3) + 1)) /
          (widget.crossAxisCount ?? 3),
      child: Stack(
        key: ValueKey(image.file?.path ?? image.url),
        children: [
          // Image container
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: image.hasError ? Colors.red : Colors.grey.shade300,
                width: 1,
              ),
              color: Colors.white,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: AspectRatio(
                aspectRatio: 1,
                child: Stack(
                  children: [
                    // Image
                    if (image.url != null && !image.hasError)
                      Image.network(
                        image.url!,
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey.shade200,
                            child: const Center(
                              child:
                                  Icon(Icons.broken_image, color: Colors.grey),
                            ),
                          );
                        },
                      )
                    else if (image.file != null)
                      FutureBuilder<Uint8List>(
                        future: image.file!.readAsBytes(),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                                  ConnectionState.done &&
                              snapshot.hasData) {
                            return Image.memory(
                              snapshot.data!,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                            );
                          }
                          return Container(
                            color: Colors.grey.shade200,
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          );
                        },
                      ),

                    // Upload indicator
                    if (image.isUploading)
                      Container(
                        color: Colors.black.withOpacity(0.5),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(
                                value: image.uploadProgress > 0
                                    ? image.uploadProgress
                                    : null,
                                color: Colors.white,
                                strokeWidth: 3,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '${(image.uploadProgress * 100).toStringAsFixed(0)}%',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                    // Error indicator
                    if (image.hasError)
                      Container(
                        color: Colors.black.withOpacity(0.5),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.error_outline,
                                color: Colors.white,
                                size: 32,
                              ),
                              const SizedBox(height: 8),
                              ElevatedButton.icon(
                                onPressed: () => widget.onRetryUpload(index),
                                icon: const Icon(
                                  Icons.refresh,
                                  size: 16,
                                ),
                                label: const Text('重试'),
                                style: ElevatedButton.styleFrom(
                                  visualDensity: VisualDensity.compact,
                                  backgroundColor: Colors.white,
                                  foregroundColor: Colors.red,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),

          // Remove button
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () => widget.onRemoveImage(index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),

          // Index badge
          Positioned(
            top: 4,
            left: 4,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                '${index + 1}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
