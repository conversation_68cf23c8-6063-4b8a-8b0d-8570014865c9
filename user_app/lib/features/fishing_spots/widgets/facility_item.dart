import 'package:flutter/material.dart';

class FacilityItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final String? tooltip;
  final EdgeInsetsGeometry padding;
  final BoxDecoration? decoration;

  const FacilityItem({
    super.key,
    required this.icon,
    required this.label,
    this.color = Colors.green,
    this.tooltip,
    this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    final defaultDecoration = BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: Colors.grey.shade200,
        width: 1,
      ),
    );

    final Widget item = Container(
      padding: padding,
      decoration: decoration ?? defaultDecoration,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 24,
            color: color,
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );

    if (tooltip != null) {
      return Tooltip(
        message: tooltip!,
        child: item,
      );
    }

    return item;
  }

  /// Helper method to get icon for facility based on string name
  static IconData getIconFromString(String iconName) {
    switch (iconName) {
      case 'restaurant':
        return Icons.restaurant_rounded;
      case 'wc':
        return Icons.wc_rounded;
      case 'local_parking':
        return Icons.local_parking_rounded;
      case 'shopping_basket':
        return Icons.shopping_basket_rounded;
      case 'wifi':
        return Icons.wifi_rounded;
      case 'access_time':
        return Icons.access_time_rounded;
      case 'shower':
        return Icons.shower_rounded;
      case 'outdoor_grill':
        return Icons.outdoor_grill_rounded;
      case 'directions_boat':
        return Icons.directions_boat_rounded;
      case 'cabin':
        return Icons.cabin_rounded;
      case 'pool':
        return Icons.pool_rounded;
      case 'fishing':
        return Icons.set_meal_rounded;
      case 'meeting_room':
        return Icons.meeting_room_rounded;
      case 'electrical_services':
        return Icons.electrical_services_rounded;
      case 'photo_camera':
        return Icons.photo_camera_rounded;
      case 'waves':
        return Icons.waves_rounded;
      default:
        // Attempt to parse by common categories
        if (iconName.contains('park')) return Icons.local_parking_rounded;
        if (iconName.contains('food') || iconName.contains('餐')) {
          return Icons.restaurant_rounded;
        }
        if (iconName.contains('bath') ||
            iconName.contains('wc') ||
            iconName.contains('toilet')) {
          return Icons.wc_rounded;
        }
        if (iconName.contains('shop') || iconName.contains('store')) {
          return Icons.shopping_basket_rounded;
        }
        // Default icon if no matching found
        return Icons.apps_rounded;
    }
  }
}
