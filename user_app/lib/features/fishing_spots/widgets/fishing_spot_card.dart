import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/widgets/checkin_button.dart';

/// 钓点卡片组件示例
/// 
/// 展示如何在钓点列表、网格等场景中使用签到功能
class FishingSpotCard extends StatefulWidget {
  final FishingSpotVo spot;
  final VoidCallback? onTap;
  final Function(FishingSpotVo updatedSpot)? onSpotUpdated;

  const FishingSpotCard({
    super.key,
    required this.spot,
    this.onTap,
    this.onSpotUpdated,
  });

  @override
  State<FishingSpotCard> createState() => _FishingSpotCardState();
}

class _FishingSpotCardState extends State<FishingSpotCard> {
  late FishingSpotVo _spot;

  @override
  void initState() {
    super.initState();
    _spot = widget.spot;
  }

  @override
  void didUpdateWidget(FishingSpotCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.spot != oldWidget.spot) {
      _spot = widget.spot;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 钓点基本信息
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _spot.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _spot.address,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  // 官方认证标识
                  if (_spot.isOfficial)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        '官方',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.orange,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // 钓点统计信息
              Row(
                children: [
                  _buildStatItem(
                    icon: Icons.visibility,
                    label: '访问',
                    value: _spot.visitorCount.toString(),
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 16),
                  _buildStatItem(
                    icon: Icons.person,
                    label: '签到',
                    value: _spot.checkinCount.toString(),
                    color: Colors.green,
                  ),
                  const SizedBox(width: 16),
                  _buildStatItem(
                    icon: Icons.star,
                    label: '评分',
                    value: _spot.rating.toStringAsFixed(1),
                    color: Colors.amber,
                  ),
                  const Spacer(),
                  // 收费标识
                  if (_spot.isPaid)
                    const Icon(
                      Icons.paid,
                      size: 16,
                      color: Colors.red,
                    ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // 操作按钮区域
              Row(
                children: [
                  // 签到按钮 - 使用新的CheckinButton组件
                  Expanded(
                    child: CheckinButton(
                      spotId: _spot.id,
                      spotName: _spot.name,
                      style: CheckinButtonStyle.outlined,
                      fullWidth: true,
                      onCheckinSuccess: _handleCheckinSuccess,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 导航按钮
                  OutlinedButton.icon(
                    onPressed: _handleNavigation,
                    icon: const Icon(Icons.navigation, size: 16),
                    label: const Text('导航'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.blue,
                      side: BorderSide(color: Colors.blue.withOpacity(0.5)),
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 14, color: color),
        const SizedBox(width: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  void _handleCheckinSuccess(int newCheckinCount) {
    debugPrint('🎯 [FishingSpotCard] 签到成功，更新钓点数据');
    
    // 更新本地状态
    setState(() {
      _spot = _spot.copyWith(checkinCount: _spot.checkinCount + newCheckinCount);
    });

    // 通知父组件更新
    widget.onSpotUpdated?.call(_spot);
  }

  void _handleNavigation() {
    debugPrint('🧭 [FishingSpotCard] 开始导航到钓点: ${_spot.name}');
    // TODO: 实现导航功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('正在为您规划到 ${_spot.name} 的路线...'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

/// 钓点列表项组件示例
/// 
/// 适用于紧凑的列表显示场景
class FishingSpotListTile extends StatefulWidget {
  final FishingSpotVo spot;
  final VoidCallback? onTap;
  final Function(FishingSpotVo updatedSpot)? onSpotUpdated;

  const FishingSpotListTile({
    super.key,
    required this.spot,
    this.onTap,
    this.onSpotUpdated,
  });

  @override
  State<FishingSpotListTile> createState() => _FishingSpotListTileState();
}

class _FishingSpotListTileState extends State<FishingSpotListTile> {
  late FishingSpotVo _spot;

  @override
  void initState() {
    super.initState();
    _spot = widget.spot;
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: widget.onTap,
      leading: CircleAvatar(
        backgroundColor: _spot.isOfficial ? Colors.orange : Colors.blue,
        child: Text(
          _spot.name.isNotEmpty ? _spot.name[0] : '钓',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      title: Text(
        _spot.name,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _spot.address,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(Icons.person, size: 12, color: Colors.grey[600]),
              const SizedBox(width: 2),
              Text(
                '${_spot.checkinCount}人签到',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 8),
              Icon(Icons.star, size: 12, color: Colors.amber),
              const SizedBox(width: 2),
              Text(
                _spot.rating.toStringAsFixed(1),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
      trailing: CheckinButton(
        spotId: _spot.id,
        spotName: _spot.name,
        style: CheckinButtonStyle.text,
        buttonText: '签到',
        onCheckinSuccess: (newCheckinCount) {
          setState(() {
            _spot = _spot.copyWith(checkinCount: _spot.checkinCount + newCheckinCount);
          });
          widget.onSpotUpdated?.call(_spot);
        },
      ),
    );
  }
}