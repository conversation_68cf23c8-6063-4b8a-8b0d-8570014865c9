import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';

class AdvancedFilterSheet extends StatefulWidget {
  final List<String> selectedFishTypes;
  final bool hasFacilities;
  final bool hasParking;
  final List<FishType> availableFishTypes;
  final Function(List<String>, bool, bool) onApply;

  const AdvancedFilterSheet({
    super.key,
    required this.selectedFishTypes,
    required this.hasFacilities,
    required this.hasParking,
    required this.availableFishTypes,
    required this.onApply,
  });

  @override
  State<AdvancedFilterSheet> createState() => _AdvancedFilterSheetState();
}

class _AdvancedFilterSheetState extends State<AdvancedFilterSheet> {
  late List<String> _selectedFishTypes;
  late bool _hasFacilities;
  late bool _hasParking;

  @override
  void initState() {
    super.initState();
    _selectedFishTypes = List.from(widget.selectedFishTypes);
    _hasFacilities = widget.hasFacilities;
    _hasParking = widget.hasParking;
  }

  void _reset() {
    setState(() {
      _selectedFishTypes.clear();
      _hasFacilities = false;
      _hasParking = false;
    });
  }

  void _apply() {
    widget.onApply(_selectedFishTypes, _hasFacilities, _hasParking);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.only(top: 12, bottom: 16),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 8, 24, 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '高级筛选',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                TextButton(
                  onPressed: _reset,
                  child: const Text('重置'),
                ),
              ],
            ),
          ),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildFishTypesSection(),
                  const SizedBox(height: 24),
                  _buildFacilitiesSection(),
                ],
              ),
            ),
          ),

          // Apply Button
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -4),
                ),
              ],
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _apply,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  '应用筛选',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFishTypesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '鱼类品种',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (_selectedFishTypes.isNotEmpty)
              Text(
                '已选 ${_selectedFishTypes.length}',
                style: TextStyle(
                  fontSize: 13,
                  color: Theme.of(context).primaryColor,
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        widget.availableFishTypes.isEmpty
            ? Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '加载鱼种数据中...',
                  style: TextStyle(color: Colors.grey),
                ),
              )
            : Wrap(
                spacing: 8,
                runSpacing: 8,
                children: widget.availableFishTypes.map((fishType) {
                  final isSelected = _selectedFishTypes.contains(fishType.name);
                  return FilterChip(
                    selected: isSelected,
                    label: Text(fishType.name),
                    onSelected: (selected) {
                      setState(() {
                        if (selected) {
                          _selectedFishTypes.add(fishType.name);
                        } else {
                          _selectedFishTypes.remove(fishType.name);
                        }
                      });
                    },
                    selectedColor:
                        Theme.of(context).primaryColor.withOpacity(0.2),
                    checkmarkColor: Theme.of(context).primaryColor,
                  );
                }).toList(),
              ),
      ],
    );
  }

  Widget _buildFacilitiesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '设施条件',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        _FacilityOption(
          title: '有便利设施',
          subtitle: '如卫生间、遮阳棚等',
          value: _hasFacilities,
          onChanged: (value) {
            setState(() {
              _hasFacilities = value ?? false;
            });
          },
        ),
        _FacilityOption(
          title: '有停车位',
          subtitle: '方便自驾前往',
          value: _hasParking,
          onChanged: (value) {
            setState(() {
              _hasParking = value ?? false;
            });
          },
        ),
      ],
    );
  }
}

class _FacilityOption extends StatelessWidget {
  final String title;
  final String subtitle;
  final bool value;
  final ValueChanged<bool?> onChanged;

  const _FacilityOption({
    required this.title,
    required this.subtitle,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: value
            ? Theme.of(context).primaryColor.withOpacity(0.05)
            : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: value
              ? Theme.of(context).primaryColor.withOpacity(0.3)
              : Colors.grey.shade200,
        ),
      ),
      child: CheckboxListTile(
        value: value,
        onChanged: onChanged,
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey.shade600,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        controlAffinity: ListTileControlAffinity.leading,
        activeColor: Theme.of(context).primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
