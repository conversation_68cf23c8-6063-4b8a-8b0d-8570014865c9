import 'package:flutter/material.dart';

class FishingCatchContent extends StatelessWidget {
  final Map<String, dynamic> data;

  const FishingCatchContent({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    final caughtFishes = data['caughtFishes'] as List? ?? [];
    final totalWeight = data['totalWeight'] ?? 0;
    final fishingMethod = data['fishingMethod'] ?? '';
    final weatherConditions = data['weatherConditions'] ?? '';

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.green.shade50,
            Colors.green.shade50.withOpacity(0.5),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.green.shade200,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.catching_pokemon,
                  size: 24,
                  color: Colors.green.shade700,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '今日钓获',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '总重量: ${totalWeight}kg',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.green.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Fish List
          if (caughtFishes.isNotEmpty) ...[
            ...caughtFishes.map((fish) => _FishItem(fish: fish)),
            const SizedBox(height: 12),
          ],

          // Method and Weather
          Wrap(
            spacing: 12,
            runSpacing: 8,
            children: [
              if (fishingMethod.isNotEmpty)
                _InfoChip(
                  icon: Icons.phishing,
                  label: fishingMethod,
                  color: Colors.blue,
                ),
              if (weatherConditions.isNotEmpty)
                _InfoChip(
                  icon: Icons.wb_sunny,
                  label: weatherConditions,
                  color: Colors.orange,
                ),
            ],
          ),
        ],
      ),
    );
  }
}

class _FishItem extends StatelessWidget {
  final Map<String, dynamic> fish;

  const _FishItem({required this.fish});

  @override
  Widget build(BuildContext context) {
    final fishTypeName = fish['fishTypeName'] ?? '未知鱼种';
    final count = fish['count'] ?? 0;
    final weight = fish['weight'] ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                '🐟',
                style: const TextStyle(fontSize: 24),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fishTypeName,
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '数量: $count条 · 重量: ${weight}kg',
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _InfoChip extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;

  const _InfoChip({
    required this.icon,
    required this.label,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 13,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
