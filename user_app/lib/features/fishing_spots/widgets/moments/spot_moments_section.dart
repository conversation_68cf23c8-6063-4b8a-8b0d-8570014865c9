import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/features/fishing_spots/widgets/moments/moments_list.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/services/moment_service.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/config/app_routes.dart';

class SpotMomentsSection extends StatefulWidget {
  final int fishingSpotId;
  final String fishingSpotName;

  const SpotMomentsSection({
    super.key,
    required this.fishingSpotId,
    required this.fishingSpotName,
  });

  @override
  State<SpotMomentsSection> createState() => _SpotMomentsSectionState();
}

class _SpotMomentsSectionState extends State<SpotMomentsSection>
    with TickerProviderStateMixin {
  final List<MomentVo> _moments = [];
  int _currentPage = 1;
  final int _pageSize = 10;
  bool _isLoading = false;
  bool _hasMore = true;
  String? _error;
  String _selectedType =
      'all'; // all, fishing_catch, technique, equipment, question

  // 滚动控制器
  final ScrollController _scrollController = ScrollController();
  bool _isHeaderVisible = true;
  double _lastScrollOffset = 0.0;

  @override
  void initState() {
    super.initState();

    // 添加滚动监听器
    _scrollController.addListener(_onScroll);

    _loadMoments();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final currentOffset = _scrollController.offset;
    final isScrollingDown = currentOffset > _lastScrollOffset;
    final isScrollingUp = currentOffset < _lastScrollOffset;

    // 只有在滚动距离超过一定阈值时才触发动画
    if ((currentOffset - _lastScrollOffset).abs() > 5) {
      if (isScrollingUp && !_isHeaderVisible) {
        // 上滑显示 header 和 filter
        setState(() {
          _isHeaderVisible = true;
        });
      } else if (isScrollingDown && _isHeaderVisible && currentOffset > 100) {
        // 下滑隐藏 header 和 filter (滚动超过100像素后才开始隐藏)
        setState(() {
          _isHeaderVisible = false;
        });
      }
      _lastScrollOffset = currentOffset;
    }
  }

  Future<void> _loadMoments({bool refresh = false}) async {
    if (_isLoading || (!refresh && !_hasMore)) return;

    setState(() {
      _isLoading = true;
      if (refresh) {
        _error = null;
      }
    });

    try {
      final momentService = Provider.of<MomentService>(context, listen: false);
      final response = await momentService.getMomentsByFishingSpot(
        widget.fishingSpotId,
        _currentPage,
        _pageSize,
        momentType: _selectedType == 'all' ? null : _selectedType,
      );

      setState(() {
        if (refresh) {
          _moments.clear();
          _currentPage = 1;
        }
        _moments.addAll(response.records);
        _currentPage++;
        _hasMore = response.records.length >= _pageSize;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = '加载动态失败：$e';
      });
    }
  }

  void _onTypeChanged(String type) {
    setState(() {
      _selectedType = type;
      _currentPage = 1;
      _hasMore = true;
    });
    _loadMoments(refresh: true);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle
          Center(
            child: Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),

          // Header with animation - 使用 AnimatedContainer 来动态调整高度
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: _isHeaderVisible ? null : 0,
            curve: Curves.easeInOut,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: _isHeaderVisible ? 1.0 : 0.0,
              child:
                  _isHeaderVisible ? _buildHeader() : const SizedBox.shrink(),
            ),
          ),

          // Type Filter with animation - 使用 AnimatedContainer 来动态调整高度
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: _isHeaderVisible ? null : 0,
            curve: Curves.easeInOut,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 200),
              opacity: _isHeaderVisible ? 1.0 : 0.0,
              child: _isHeaderVisible
                  ? _buildTypeFilter()
                  : const SizedBox.shrink(),
            ),
          ),

          // Content
          Expanded(
            child: _buildContentWithScrollController(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Icon
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.blue.shade400,
                  Colors.blue.shade600,
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.forum,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),

          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${widget.fishingSpotName}的动态',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  '${_moments.length}条动态',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // Publish Button
          _PublishButton(
            fishingSpotId: widget.fishingSpotId,
            fishingSpotName: widget.fishingSpotName,
            onPublished: () => _loadMoments(refresh: true),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeFilter() {
    final types = [
      {'value': 'all', 'label': '全部', 'icon': Icons.apps},
      {'value': 'fishing_catch', 'label': '钓获', 'icon': Icons.catching_pokemon},
      {'value': 'technique', 'label': '技巧', 'icon': Icons.lightbulb},
      {'value': 'equipment', 'label': '装备', 'icon': Icons.backpack},
      {'value': 'question', 'label': '问答', 'icon': Icons.help_outline},
    ];

    return Container(
      height: 50,
      color: Colors.white,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: types.length,
        itemBuilder: (context, index) {
          final type = types[index];
          final isSelected = _selectedType == type['value'];

          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              selected: isSelected,
              onSelected: (_) => _onTypeChanged(type['value'] as String),
              avatar: Icon(
                type['icon'] as IconData,
                size: 18,
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade600,
              ),
              label: Text(type['label'] as String),
              selectedColor:
                  Theme.of(context).primaryColor.withValues(alpha: 0.1),
              checkmarkColor: Theme.of(context).primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildContentWithScrollController() {
    if (_isLoading && _moments.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null && _moments.isEmpty) {
      return _ErrorState(
        error: _error!,
        onRetry: () => _loadMoments(refresh: true),
      );
    }

    if (_moments.isEmpty) {
      return _EmptyState(
        selectedType: _selectedType,
        onRefresh: () => _loadMoments(refresh: true),
      );
    }

    return MomentsList(
      moments: _moments,
      isLoading: _isLoading,
      hasMore: _hasMore,
      onLoadMore: _loadMoments,
      scrollController: _scrollController, // 传递滚动控制器
      onMomentDeleted: (momentId) {
        setState(() {
          _moments.removeWhere((m) => m.id == momentId);
        });
      },
      onMomentUpdated: (updatedMoment) {
        setState(() {
          final index = _moments.indexWhere((m) => m.id == updatedMoment.id);
          if (index != -1) {
            _moments[index] = updatedMoment;
          }
        });
      },
    );
  }
}

class _PublishButton extends StatelessWidget {
  final int fishingSpotId;
  final String fishingSpotName;
  final VoidCallback onPublished;

  const _PublishButton({
    required this.fishingSpotId,
    required this.fishingSpotName,
    required this.onPublished,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _showPublishOptions(context),
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.edit,
                size: 16,
                color: Colors.white,
              ),
              SizedBox(width: 4),
              Text(
                '发布',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPublishOptions(BuildContext context) {
    if (!_isAuthenticated(context)) {
      _showLoginDialog(context, '发布动态');
      return;
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2)),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text('发布动态',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildPublishOption(
                              context: context,
                              icon: Icons.set_meal,
                              label: '钓获分享',
                              color: Colors.green,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '钓获分享');
                              }),
                          _buildPublishOption(
                              context: context,
                              icon: Icons.backpack,
                              label: '装备展示',
                              color: Colors.blue,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '装备展示');
                              }),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildPublishOption(
                              context: context,
                              icon: Icons.tips_and_updates,
                              label: '技巧分享',
                              color: Colors.orange,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '技巧分享');
                              }),
                          _buildPublishOption(
                              context: context,
                              icon: Icons.help_outline,
                              label: '问答求助',
                              color: Colors.purple,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '问答求助');
                              }),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPublishOption({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16)),
            child: Icon(icon, color: color, size: 28),
          ),
          const SizedBox(height: 6),
          Text(label,
              style: TextStyle(fontSize: 13, color: Colors.grey.shade700)),
        ],
      ),
    );
  }

  bool _isAuthenticated(BuildContext context) {
    return context.read<AuthViewModel>().isUserLoggedIn();
  }

  void _showLoginDialog(BuildContext context, String action) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Text('需要登录'),
          content: Text('您需要先登录才能$action'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                context.push(AppRoutes.login);
              },
              style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8))),
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }
}

class _ErrorState extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

  const _ErrorState({
    required this.error,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _EmptyState extends StatelessWidget {
  final String selectedType;
  final VoidCallback onRefresh;

  const _EmptyState({
    required this.selectedType,
    required this.onRefresh,
  });

  String _getEmptyMessage() {
    switch (selectedType) {
      case 'fishing_catch':
        return '还没有钓获分享';
      case 'technique':
        return '还没有技巧分享';
      case 'equipment':
        return '还没有装备展示';
      case 'question':
        return '还没有问答求助';
      default:
        return '还没有动态';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.forum_outlined,
              size: 64,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              _getEmptyMessage(),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '成为第一个分享的人吧！',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),
            TextButton.icon(
              onPressed: onRefresh,
              icon: const Icon(Icons.refresh),
              label: const Text('刷新试试'),
            ),
          ],
        ),
      ),
    );
  }
}
