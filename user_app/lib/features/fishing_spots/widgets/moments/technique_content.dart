import 'package:flutter/material.dart';

class TechniqueContent extends StatelessWidget {
  final Map<String, dynamic> data;

  const TechniqueContent({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    final techniqueName = data['techniqueName'] ?? '';
    final difficulty = data['difficulty'] ?? '';
    final targetFishTypes = data['targetFishTypes'] as List? ?? [];
    final environments = data['environments'] as List? ?? [];
    final description = data['description'] ?? '';

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.shade50,
            Colors.blue.shade50.withOpacity(0.5),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.blue.shade200,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.lightbulb,
                  size: 24,
                  color: Colors.blue.shade700,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      techniqueName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (difficulty.isNotEmpty)
                      Text(
                        '难度: $difficulty',
                        style: TextStyle(
                          fontSize: 14,
                          color: _getDifficultyColor(difficulty),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),

          if (description.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
                height: 1.4,
              ),
            ),
          ],

          const SizedBox(height: 12),

          // Target Fish
          if (targetFishTypes.isNotEmpty) ...[
            _buildSection(
              icon: Icons.set_meal,
              title: '适用鱼种',
              child: Wrap(
                spacing: 6,
                runSpacing: 6,
                children: targetFishTypes
                    .map((fish) => _FishChip(
                          name: fish['name'] ?? '未知',
                        ))
                    .toList(),
              ),
            ),
            const SizedBox(height: 12),
          ],

          // Environments
          if (environments.isNotEmpty)
            _buildSection(
              icon: Icons.water,
              title: '适用环境',
              child: Wrap(
                spacing: 6,
                runSpacing: 6,
                children: environments
                    .map((env) => _EnvironmentChip(
                          name: env.toString(),
                        ))
                    .toList(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required IconData icon,
    required String title,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: Colors.grey.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        child,
      ],
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case '入门级':
        return Colors.green;
      case '初级':
        return Colors.lightGreen;
      case '中级':
        return Colors.orange;
      case '高级':
        return Colors.deepOrange;
      case '专业级':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

class _FishChip extends StatelessWidget {
  final String name;

  const _FishChip({required this.name});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.green.shade200,
        ),
      ),
      child: Text(
        name,
        style: TextStyle(
          fontSize: 12,
          color: Colors.green.shade700,
        ),
      ),
    );
  }
}

class _EnvironmentChip extends StatelessWidget {
  final String name;

  const _EnvironmentChip({required this.name});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.cyan.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.cyan.shade200,
        ),
      ),
      child: Text(
        name,
        style: TextStyle(
          fontSize: 12,
          color: Colors.cyan.shade700,
        ),
      ),
    );
  }
}
