import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/widgets/moments/equipment_content.dart';
import 'package:user_app/features/fishing_spots/widgets/moments/fishing_catch_content.dart';
import 'package:user_app/features/fishing_spots/widgets/moments/question_content.dart';
import 'package:user_app/features/fishing_spots/widgets/moments/technique_content.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/utils/time_formatter.dart';
import 'package:user_app/widgets/user_circle_avatar.dart';

class MomentCard extends StatelessWidget {
  final MomentVo moment;
  final Function(bool) onLikeChanged;
  final VoidCallback onCommentAdded;
  final VoidCallback onDeleted;

  const MomentCard({
    super.key,
    required this.moment,
    required this.onLikeChanged,
    required this.onCommentAdded,
    required this.onDeleted,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context),

          // Type-specific content
          _buildTypeSpecificContent(context),

          // Common content (if any)
          if (moment.content?.isNotEmpty == true) _buildCommonContent(context),

          // Images
          if (moment.images?.isNotEmpty == true) _buildImages(context),

          // Actions
          _buildActions(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Avatar
          GestureDetector(
            onTap: () {
              // Navigate to user profile
            },
            child: UserCircleAvatar(
              avatarUrl: moment.publisher?.avatarUrl ?? moment.userAvatar,
              radius: 20,
            ),
          ),
          const SizedBox(width: 12),

          // User info and time
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      moment.publisher?.name ?? moment.userName ?? '匿名用户',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    _buildMomentTypeTag(),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  TimeFormatter.format(moment.createdAt),
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          // More button
          IconButton(
            onPressed: () => _showMoreOptions(context),
            icon: Icon(
              Icons.more_vert,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMomentTypeTag() {
    String label;
    Color color;
    IconData icon;

    switch (moment.momentType) {
      case 'fishing_catch':
        label = '钓获';
        color = Colors.green;
        icon = Icons.catching_pokemon;
        break;
      case 'technique':
        label = '技巧';
        color = Colors.blue;
        icon = Icons.lightbulb;
        break;
      case 'equipment':
        label = '装备';
        color = Colors.orange;
        icon = Icons.backpack;
        break;
      case 'question':
        label = '问答';
        color = Colors.purple;
        icon = Icons.help_outline;
        break;
      default:
        label = '动态';
        color = Colors.grey;
        icon = Icons.article;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeSpecificContent(BuildContext context) {
    if (moment.typeSpecificData == null) return const SizedBox.shrink();

    try {
      final specificData = json.decode(moment.typeSpecificData!);

      switch (moment.momentType) {
        case 'fishing_catch':
          return FishingCatchContent(data: specificData);
        case 'technique':
          return TechniqueContent(data: specificData);
        case 'equipment':
          return EquipmentContent(data: specificData);
        case 'question':
          return QuestionContent(data: specificData);
        default:
          return const SizedBox.shrink();
      }
    } catch (e) {
      debugPrint('Error parsing type specific data: $e');
      return const SizedBox.shrink();
    }
  }

  Widget _buildCommonContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Text(
        moment.content!,
        style: const TextStyle(
          fontSize: 15,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildImages(BuildContext context) {
    final images = moment.images!;

    if (images.length == 1) {
      return _buildSingleImage(images.first);
    } else if (images.length == 2) {
      return _buildTwoImages(images);
    } else if (images.length == 3) {
      return _buildThreeImages(images);
    } else {
      return _buildMultipleImages(images);
    }
  }

  Widget _buildSingleImage(dynamic image) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: CachedNetworkImage(
            imageUrl: image.imageUrl,
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: Colors.grey.shade200,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey.shade200,
              child: const Icon(Icons.error),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTwoImages(List<dynamic> images) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Row(
        children: images
            .map((image) => Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(
                      right: images.last == image ? 0 : 4,
                    ),
                    child: _buildImageItem(image),
                  ),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildThreeImages(List<dynamic> images) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: _buildImageItem(images[0]),
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Column(
              children: [
                _buildImageItem(images[1]),
                const SizedBox(height: 4),
                _buildImageItem(images[2]),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMultipleImages(List<dynamic> images) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
        ),
        itemCount: images.length > 9 ? 9 : images.length,
        itemBuilder: (context, index) {
          if (index == 8 && images.length > 9) {
            return Stack(
              children: [
                _buildImageItem(images[index]),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      '+${images.length - 9}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            );
          }
          return _buildImageItem(images[index]);
        },
      ),
    );
  }

  Widget _buildImageItem(dynamic image) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: AspectRatio(
        aspectRatio: 1,
        child: CachedNetworkImage(
          imageUrl: image.imageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: Colors.grey.shade200,
          ),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey.shade200,
            child: const Icon(Icons.error, size: 20),
          ),
        ),
      ),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.grey.shade100,
          ),
        ),
      ),
      child: Row(
        children: [
          // Like
          _ActionButton(
            icon:
                moment.isLiked == true ? Icons.favorite : Icons.favorite_border,
            label: (moment.likeCount ?? 0).toString(),
            color: moment.isLiked == true ? Colors.red : null,
            onTap: () => onLikeChanged(!(moment.isLiked ?? false)),
          ),
          const SizedBox(width: 24),

          // Comment
          _ActionButton(
            icon: Icons.chat_bubble_outline,
            label: (moment.commentCount ?? 0).toString(),
            onTap: () => _showComments(context),
          ),
          const SizedBox(width: 24),

          // Share
          _ActionButton(
            icon: Icons.share_outlined,
            label: '分享',
            onTap: () => _shareMoment(context),
          ),

          const Spacer(),

          // Bookmark
          IconButton(
            onPressed: () => _toggleBookmark(context),
            icon: Icon(
              moment.isBookmarked == true
                  ? Icons.bookmark
                  : Icons.bookmark_border,
              color: moment.isBookmarked == true
                  ? Theme.of(context).primaryColor
                  : Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.flag_outlined),
            title: const Text('举报'),
            onTap: () {
              Navigator.pop(context);
              // Handle report
            },
          ),
          // Add more options if user is the owner
          if (moment.userId == 18) // TODO: Replace with actual current user ID
            ListTile(
              leading: const Icon(Icons.delete_outline, color: Colors.red),
              title: const Text('删除', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                _confirmDelete(context);
              },
            ),
        ],
      ),
    );
  }

  void _confirmDelete(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这条动态吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onDeleted();
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showComments(BuildContext context) {
    // Navigate to comments page
    onCommentAdded(); // Temporary
  }

  void _shareMoment(BuildContext context) {
    // Implement share functionality
  }

  void _toggleBookmark(BuildContext context) {
    // Implement bookmark functionality
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color? color;
  final VoidCallback onTap;

  const _ActionButton({
    required this.icon,
    required this.label,
    this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: color ?? Colors.grey.shade600,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: color ?? Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
