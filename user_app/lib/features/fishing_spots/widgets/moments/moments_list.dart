import 'package:flutter/material.dart';
import 'package:user_app/features/fishing_spots/widgets/moments/moment_card.dart';
import 'package:user_app/models/moment/moment_vo.dart';

class MomentsList extends StatefulWidget {
  final List<MomentVo> moments;
  final bool isLoading;
  final bool hasMore;
  final VoidCallback onLoadMore;
  final Function(int) onMomentDeleted;
  final Function(MomentVo) onMomentUpdated;
  final ScrollController? scrollController; // 可选的外部滚动控制器

  const MomentsList({
    super.key,
    required this.moments,
    required this.isLoading,
    required this.hasMore,
    required this.onLoadMore,
    required this.onMomentDeleted,
    required this.onMomentUpdated,
    this.scrollController, // 可选参数
  });

  @override
  State<MomentsList> createState() => _MomentsListState();
}

class _MomentsListState extends State<MomentsList> {
  late ScrollController _scrollController;
  bool _shouldDisposeController = false;

  @override
  void initState() {
    super.initState();
    // 如果外部传入了 ScrollController，使用它；否则创建新的
    if (widget.scrollController != null) {
      _scrollController = widget.scrollController!;
      _shouldDisposeController = false;
    } else {
      _scrollController = ScrollController();
      _shouldDisposeController = true;
    }
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    // 只有当我们创建了 ScrollController 时才销毁它
    if (_shouldDisposeController) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!widget.isLoading && widget.hasMore) {
        widget.onLoadMore();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.only(top: 8, bottom: 100),
      itemCount: widget.moments.length + (widget.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == widget.moments.length) {
          return _buildLoadMoreIndicator();
        }

        final moment = widget.moments[index];
        return MomentCard(
          moment: moment,
          onLikeChanged: (isLiked) {
            // Update local state
            final updatedMoment = moment.copyWith(
              isLiked: isLiked,
              likeCount: (moment.likeCount ?? 0) + (isLiked ? 1 : -1),
            );
            widget.onMomentUpdated(updatedMoment);
          },
          onCommentAdded: () {
            // Update comment count
            final updatedMoment = moment.copyWith(
              commentCount: (moment.commentCount ?? 0) + 1,
            );
            widget.onMomentUpdated(updatedMoment);
          },
          onDeleted: () => widget.onMomentDeleted(moment.id!),
        );
      },
    );
  }

  Widget _buildLoadMoreIndicator() {
    if (widget.isLoading) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '加载中...',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (!widget.hasMore) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 24),
        child: Center(
          child: Text(
            '没有更多动态了',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }
}
