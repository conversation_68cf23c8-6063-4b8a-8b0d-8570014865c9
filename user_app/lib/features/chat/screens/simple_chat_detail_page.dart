import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_elem_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_message.dart';
import 'package:user_app/features/chat/widgets/image_message_widget.dart';
import 'package:user_app/features/chat/widgets/message_status_indicator.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/view_models/chat_view_model.dart';

class SimpleChatDetailPage extends StatefulWidget {
  final String conversationID;
  final int conversationType;
  final String showName;
  final String? userID;

  const SimpleChatDetailPage({
    super.key,
    required this.conversationID,
    required this.conversationType,
    required this.showName,
    this.userID,
  });

  @override
  State<SimpleChatDetailPage> createState() => _SimpleChatDetailPageState();
}

class _SimpleChatDetailPageState extends State<SimpleChatDetailPage>
    with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();

  List<V2TimMessage> messages = [];
  bool isLoading = false;
  Function(V2TimMessage)? _messageListener;

  // 动画控制器
  late AnimationController _sendButtonAnimationController;
  late Animation<double> _sendButtonAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化动画
    _sendButtonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _sendButtonAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sendButtonAnimationController,
      curve: Curves.easeOut,
    ));

    // 监听输入框变化
    _messageController.addListener(() {
      if (_messageController.text.isNotEmpty) {
        _sendButtonAnimationController.forward();
      } else {
        _sendButtonAnimationController.reverse();
      }
    });

    _loadMessages();
    _setupMessageListener();
    _markAsReadOnEnter();
  }

  @override
  void dispose() {
    if (_messageListener != null) {
      try {
        final chatViewModel = context.read<ChatViewModel>();
        chatViewModel.removeMessageListener(_messageListener!);
      } catch (e) {
        debugPrint('⚠️ Failed to remove message listener: $e');
      }
    }
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    _sendButtonAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadMessages() async {
    if (isLoading) return;

    setState(() {
      isLoading = true;
    });

    try {
      final chatViewModel = context.read<ChatViewModel>();
      final userID = widget.userID ?? _extractUserIDFromConversationID();

      final messageList = await chatViewModel.getHistoryMessages(
        userID: userID,
        count: 20,
      );

      setState(() {
        messages = messageList;
      });

      // 延迟滚动确保列表已渲染
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });

      debugPrint('✅ Loaded ${messageList.length} messages');
    } catch (e) {
      debugPrint('❌ Failed to load messages: $e');
      _showErrorSnackBar('加载消息失败');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  String _extractUserIDFromConversationID() {
    if (widget.conversationID.startsWith('c2c_')) {
      return widget.conversationID.substring(4);
    } else if (widget.conversationID.startsWith('C2C')) {
      return widget.conversationID.substring(3);
    }
    return widget.userID ?? '';
  }

  void _setupMessageListener() {
    final chatViewModel = context.read<ChatViewModel>();

    _messageListener = (V2TimMessage newMessage) {
      final userID = widget.userID ?? _extractUserIDFromConversationID();
      final isFromTarget = newMessage.sender == userID;
      final isFromMe = newMessage.sender == chatViewModel.userId;
      final isRelevant = isFromTarget || isFromMe;

      if (isRelevant && mounted) {
        setState(() {
          final existingIndex =
              messages.indexWhere((msg) => msg.msgID == newMessage.msgID);

          if (existingIndex == -1) {
            messages.add(newMessage);
            messages
                .sort((a, b) => (a.timestamp ?? 0).compareTo(b.timestamp ?? 0));
          } else {
            messages[existingIndex] = newMessage;
          }
        });

        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });

        // 标记已读
        if (newMessage.sender == userID) {
          Future.delayed(const Duration(milliseconds: 100)).then((_) {
            chatViewModel.markConversationAsRead(widget.conversationID);
          });
        }
      }
    };

    chatViewModel.addMessageListener(_messageListener!);
  }

  void _markAsReadOnEnter() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final chatViewModel = context.read<ChatViewModel>();
        chatViewModel.markConversationAsRead(widget.conversationID);
      } catch (e) {
        debugPrint('⚠️ Failed to mark as read: $e');
      }
    });
  }

  /// 判断消息是否是自己发送的（核心修复）
  bool _isMyMessage(V2TimMessage message, String myUserId) {
    // 临时消息，直接判断为自己的消息
    if (message.msgID?.startsWith('temp_') ?? false) {
      return true;
    }

    // 优先使用 isSelf 字段判断
    if (message.isSelf == true) {
      return true;
    }

    // 其次，在 isSelf 为 null 或 false 时，回退到比较 sender ID
    final sender = (message.sender ?? '').toString().trim();
    final userId = myUserId.trim();

    // 调试信息
    debugPrint(
        '🔍 判断消息归属 - sender: "$sender", myUserId: "$userId", isSelf: ${message.isSelf}, status: ${message.status}, 是否相等: ${sender == userId}');

    // 如果 sender 或 userId 为空，则认为不是自己的消息
    if (sender.isEmpty || userId.isEmpty) {
      debugPrint('⚠️ sender或userId为空，判定为非本人消息');
      return false;
    }

    return sender == userId;
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    // 添加触觉反馈
    HapticFeedback.lightImpact();

    _messageController.clear();

    try {
      final chatViewModel = context.read<ChatViewModel>();
      final targetUserId = _extractUserIDFromConversationID();

      // 创建临时消息
      final tempMessage = V2TimMessage.fromJson({
        'msgID': 'temp_${DateTime.now().millisecondsSinceEpoch}',
        'sender': chatViewModel.userId,
        'nickName': chatViewModel.userId,
        'faceUrl': null, // Add a default value for faceUrl
        'timestamp': DateTime.now().millisecondsSinceEpoch ~/ 1000,
        'elemType': 1,
        'textElem': {'text': text},
        'status': 1, // 消息发送中
        'isSelf': true, // 明确是自己的消息
        'isPeerRead': false,
      });

      // 立即显示
      if (mounted) {
        setState(() {
          messages.add(tempMessage);
        });
        _scrollToBottom();
      }

      // 发送消息
      final result = await chatViewModel.sendMessage(targetUserId, text);

      if (result != null && result.code == 0) {
        final sentMessage = result.data;
        if (sentMessage != null && mounted) {
          setState(() {
            final tempIndex =
                messages.indexWhere((msg) => msg.msgID == tempMessage.msgID);
            if (tempIndex != -1) {
              messages[tempIndex] = sentMessage;
            } else {
              final existingIndex =
                  messages.indexWhere((msg) => msg.msgID == sentMessage.msgID);
              if (existingIndex == -1) {
                messages.add(sentMessage);
              }
            }
            messages
                .sort((a, b) => (a.timestamp ?? 0).compareTo(b.timestamp ?? 0));
          });

          WidgetsBinding.instance.addPostFrameCallback((_) {
            _scrollToBottom();
          });
        }
      } else {
        throw Exception('发送失败');
      }
    } catch (e) {
      debugPrint('❌ Send message error: $e');
      _showErrorSnackBar('发送失败');
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          widget.showName,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF1A1A1A),
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF1A1A1A)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert, color: Color(0xFF1A1A1A)),
            onPressed: () {
              // TODO: 显示更多选项
            },
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(
            height: 1,
            color: Colors.grey[200],
          ),
        ),
      ),
      body: Column(
        children: [
          // 消息列表
          Expanded(
            child: isLoading && messages.isEmpty
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : messages.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.chat_bubble_outline,
                              size: 64,
                              color: Colors.grey[300],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              '开始聊天吧',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      )
                    : Consumer<ChatViewModel>(
                        builder: (context, chatViewModel, child) {
                          return ListView.builder(
                            controller: _scrollController,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 16,
                            ),
                            itemCount: messages.length,
                            itemBuilder: (context, index) {
                              final message = messages[index];
                              final previousMessage =
                                  index > 0 ? messages[index - 1] : null;
                              return _buildMessageItem(
                                message,
                                previousMessage,
                                chatViewModel.userId,
                              );
                            },
                          );
                        },
                      ),
          ),

          // 输入框区域
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildMessageItem(
    V2TimMessage message,
    V2TimMessage? previousMessage,
    String myUserId,
  ) {
    final isMyMessage = _isMyMessage(message, myUserId);
    final showTimeGroup = _shouldShowTimeGroup(message, previousMessage);

    return Column(
      children: [
        if (showTimeGroup)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.8),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _formatMessageTime(message.timestamp ?? 0),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),
        Padding(
          padding: EdgeInsets.only(
            bottom: 8,
            left: isMyMessage ? 48 : 0,
            right: isMyMessage ? 0 : 48,
          ),
          child: Row(
            mainAxisAlignment:
                isMyMessage ? MainAxisAlignment.end : MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 对方头像
              if (!isMyMessage) ...[
                _buildAvatar(message, false),
                const SizedBox(width: 12),
              ],

              // 消息内容
              Flexible(
                child: Column(
                  crossAxisAlignment: isMyMessage
                      ? CrossAxisAlignment.end
                      : CrossAxisAlignment.start,
                  children: [
                    // 昵称（仅对方消息显示）
                    if (!isMyMessage &&
                        !_isSameSenderAsPrevious(message, previousMessage))
                      Padding(
                        padding: const EdgeInsets.only(bottom: 4, left: 4),
                        child: Text(
                          message.nickName ?? message.sender ?? '未知',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[600],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),

                    // 消息气泡
                    GestureDetector(
                      onLongPress: () =>
                          _showMessageActions(message, isMyMessage),
                      child: Container(
                        constraints: BoxConstraints(
                          maxWidth: MediaQuery.of(context).size.width * 0.65,
                        ),
                        padding: _getMessagePadding(message),
                        decoration: BoxDecoration(
                          color: isMyMessage
                              ? Theme.of(context).primaryColor
                              : Colors.white,
                          borderRadius: BorderRadius.only(
                            topLeft: const Radius.circular(18),
                            topRight: const Radius.circular(18),
                            bottomLeft: Radius.circular(isMyMessage ? 18 : 4),
                            bottomRight: Radius.circular(isMyMessage ? 4 : 18),
                          ),
                          boxShadow: [
                            BoxShadow(
                              offset: const Offset(0, 2),
                              blurRadius: 6,
                              color: Colors.black.withOpacity(0.08),
                            ),
                          ],
                        ),
                        child: _getMessageContent(message, isMyMessage),
                      ),
                    ),

                    // 消息状态（仅自己的消息显示）
                    if (isMyMessage)
                      Padding(
                        padding: const EdgeInsets.only(top: 4, right: 4),
                        child: MessageStatusIndicator(
                          message: message,
                          isMyMessage: isMyMessage,
                        ),
                      ),
                  ],
                ),
              ),

              // 自己的头像
              if (isMyMessage) ...[
                const SizedBox(width: 12),
                _buildAvatar(message, true),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAvatar(V2TimMessage message, bool isMyMessage) {
    final chatViewModel = context.read<ChatViewModel>();

    String? avatarUrl;
    String displayName;

    if (isMyMessage) {
      // 获取当前用户的头像和名称
      final authViewModel = context.read<AuthViewModel>();

      // 如果AuthViewModel中的currentUser为空，尝试从缓存获取
      if (authViewModel.currentUser == null) {
        final cachedUser = authViewModel.userService.getCachedUser();
        avatarUrl = cachedUser?.avatarUrl;
        displayName = cachedUser?.name ?? '我';
      } else {
        avatarUrl = authViewModel.currentUser?.avatarUrl;
        displayName = authViewModel.currentUser?.name ?? '我';
      }
    } else {
      // 对方的头像和名称
      avatarUrl = message.faceUrl;
      displayName = (message.nickName ?? message.sender ?? '?').trim();
    }

    final avatarChar = displayName.isNotEmpty ? displayName[0] : '?';

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: isMyMessage
              ? [Colors.blue.shade400, Colors.blue.shade600]
              : [Colors.grey.shade400, Colors.grey.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: avatarUrl != null && avatarUrl.isNotEmpty
          ? ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Image.network(
                avatarUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Text(
                      avatarChar.toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                },
              ),
            )
          : Center(
              child: Text(
                avatarChar.toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
    );
  }

  EdgeInsets _getMessagePadding(V2TimMessage message) {
    if (_getMessageElemType(message.elemType) ==
        MessageElemType.V2TIM_ELEM_TYPE_IMAGE) {
      return EdgeInsets.zero;
    }
    return const EdgeInsets.symmetric(horizontal: 16, vertical: 10);
  }

  Widget _getMessageContent(V2TimMessage message, bool isMyMessage) {
    final elemType = _getMessageElemType(message.elemType);

    switch (elemType) {
      case MessageElemType.V2TIM_ELEM_TYPE_TEXT:
        return Text(
          message.textElem?.text ?? '[文本消息]',
          style: TextStyle(
            color: isMyMessage ? Colors.white : const Color(0xFF1A1A1A),
            fontSize: 16,
            height: 1.4,
          ),
        );
      case MessageElemType.V2TIM_ELEM_TYPE_IMAGE:
        return ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: ImageMessageWidget(
            message: message,
            isMyMessage: isMyMessage,
          ),
        );
      default:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getMessageIcon(elemType),
              size: 20,
              color: isMyMessage ? Colors.white70 : Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Text(
              _getMessageTypeText(elemType),
              style: TextStyle(
                color: isMyMessage ? Colors.white : const Color(0xFF1A1A1A),
                fontSize: 16,
              ),
            ),
          ],
        );
    }
  }

  IconData _getMessageIcon(int elemType) {
    switch (elemType) {
      case MessageElemType.V2TIM_ELEM_TYPE_SOUND:
        return Icons.mic;
      case MessageElemType.V2TIM_ELEM_TYPE_VIDEO:
        return Icons.videocam;
      case MessageElemType.V2TIM_ELEM_TYPE_FILE:
        return Icons.attach_file;
      case MessageElemType.V2TIM_ELEM_TYPE_FACE:
        return Icons.emoji_emotions;
      default:
        return Icons.help_outline;
    }
  }

  String _getMessageTypeText(int elemType) {
    switch (elemType) {
      case MessageElemType.V2TIM_ELEM_TYPE_SOUND:
        return '语音';
      case MessageElemType.V2TIM_ELEM_TYPE_VIDEO:
        return '视频';
      case MessageElemType.V2TIM_ELEM_TYPE_FILE:
        return '文件';
      case MessageElemType.V2TIM_ELEM_TYPE_FACE:
        return '表情';
      default:
        return '未知消息';
    }
  }

  int _getMessageElemType(int? elemType) {
    return elemType ?? MessageElemType.V2TIM_ELEM_TYPE_TEXT;
  }

  bool _shouldShowTimeGroup(
      V2TimMessage message, V2TimMessage? previousMessage) {
    if (previousMessage == null) return true;

    final currentTime =
        DateTime.fromMillisecondsSinceEpoch((message.timestamp ?? 0) * 1000);
    final previousTime = DateTime.fromMillisecondsSinceEpoch(
        (previousMessage.timestamp ?? 0) * 1000);

    return currentTime.difference(previousTime).inMinutes >= 5;
  }

  bool _isSameSenderAsPrevious(
      V2TimMessage message, V2TimMessage? previousMessage) {
    if (previousMessage == null) return false;
    return message.sender == previousMessage.sender;
  }

  String _formatMessageTime(int timestamp) {
    final time = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    final now = DateTime.now();

    if (time.year == now.year &&
        time.month == now.month &&
        time.day == now.day) {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } else if (time.year == now.year) {
      return '${time.month}月${time.day}日 ${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } else {
      return '${time.year}年${time.month}月${time.day}日';
    }
  }

  Widget _buildMessageInput() {
    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 12,
        bottom: MediaQuery.of(context).padding.bottom + 12,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, -2),
            blurRadius: 8,
            color: Colors.black.withOpacity(0.05),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // 更多功能按钮
          IconButton(
            icon: Icon(Icons.add_circle_outline, color: Colors.grey[600]),
            onPressed: () {
              // TODO: 显示更多功能
            },
          ),

          // 输入框
          Expanded(
            child: Container(
              constraints: const BoxConstraints(
                minHeight: 40,
                maxHeight: 120,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F7FA),
                borderRadius: BorderRadius.circular(20),
              ),
              child: TextField(
                controller: _messageController,
                focusNode: _messageFocusNode,
                decoration: const InputDecoration(
                  hintText: '输入消息...',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                ),
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _sendMessage(),
              ),
            ),
          ),

          const SizedBox(width: 8),

          // 发送按钮（带动画）
          AnimatedBuilder(
            animation: _sendButtonAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: 0.8 + (0.2 * _sendButtonAnimation.value),
                child: IconButton(
                  icon: Icon(
                    Icons.send_rounded,
                    color: _sendButtonAnimation.value > 0.5
                        ? Theme.of(context).primaryColor
                        : Colors.grey[400],
                  ),
                  onPressed: _messageController.text.trim().isNotEmpty
                      ? _sendMessage
                      : null,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _showMessageActions(V2TimMessage message, bool isMyMessage) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('复制'),
              onTap: () {
                Navigator.pop(context);
                if (_getMessageElemType(message.elemType) ==
                    MessageElemType.V2TIM_ELEM_TYPE_TEXT) {
                  final text = message.textElem?.text ?? '';
                  Clipboard.setData(ClipboardData(text: text));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('已复制到剪贴板'),
                      duration: Duration(seconds: 1),
                    ),
                  );
                }
              },
            ),
            if (isMyMessage)
              ListTile(
                leading: const Icon(Icons.undo),
                title: const Text('撤回'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: 实现撤回功能
                },
              ),
            ListTile(
              leading: const Icon(Icons.delete_outline, color: Colors.red),
              title: const Text('删除', style: TextStyle(color: Colors.red)),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  messages.removeWhere((msg) => msg.msgID == message.msgID);
                });
              },
            ),
            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }
}
