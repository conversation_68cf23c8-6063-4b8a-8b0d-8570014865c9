import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_status.dart';

/// 消息状态指示器组件
class MessageStatusIndicator extends StatelessWidget {
  final V2TimMessage message;
  final bool isMyMessage;

  const MessageStatusIndicator({
    super.key,
    required this.message,
    required this.isMyMessage,
  });

  @override
  Widget build(BuildContext context) {
    // 只有自己发送的消息才显示状态
    if (!isMyMessage) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(left: 4),
      child: _buildStatusIcon(),
    );
  }

  Widget _buildStatusIcon() {
    switch (message.status) {
      case MessageStatus.V2TIM_MSG_STATUS_SENDING:
        return const SizedBox(
          width: 12,
          height: 12,
          child: CircularProgressIndicator(
            strokeWidth: 1,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
          ),
        );
      
      case MessageStatus.V2TIM_MSG_STATUS_SEND_SUCC:
        return Icon(
          Icons.check,
          size: 12,
          color: Colors.grey.shade600,
        );
      
      case MessageStatus.V2TIM_MSG_STATUS_SEND_FAIL:
        return Icon(
          Icons.error_outline,
          size: 12,
          color: Colors.red.shade600,
        );
      
      case MessageStatus.V2TIM_MSG_STATUS_HAS_DELETED:
        return Icon(
          Icons.delete_outline,
          size: 12,
          color: Colors.grey.shade400,
        );
      
      case MessageStatus.V2TIM_MSG_STATUS_LOCAL_REVOKED:
        return Icon(
          Icons.undo,
          size: 12,
          color: Colors.orange.shade600,
        );
      
      default:
        return const SizedBox.shrink();
    }
  }
}

/// 消息状态文本描述
class MessageStatusText extends StatelessWidget {
  final V2TimMessage message;
  final bool isMyMessage;

  const MessageStatusText({
    super.key,
    required this.message,
    required this.isMyMessage,
  });

  @override
  Widget build(BuildContext context) {
    if (!isMyMessage) {
      return const SizedBox.shrink();
    }

    final statusText = _getStatusText();
    if (statusText.isEmpty) {
      return const SizedBox.shrink();
    }

    return Text(
      statusText,
      style: TextStyle(
        fontSize: 10,
        color: _getStatusColor(),
      ),
    );
  }

  String _getStatusText() {
    switch (message.status) {
      case MessageStatus.V2TIM_MSG_STATUS_SENDING:
        return '发送中...';
      case MessageStatus.V2TIM_MSG_STATUS_SEND_SUCC:
        return '已送达';
      case MessageStatus.V2TIM_MSG_STATUS_SEND_FAIL:
        return '发送失败';
      case MessageStatus.V2TIM_MSG_STATUS_HAS_DELETED:
        return '已删除';
      case MessageStatus.V2TIM_MSG_STATUS_LOCAL_REVOKED:
        return '已撤回';
      default:
        return '';
    }
  }

  Color _getStatusColor() {
    switch (message.status) {
      case MessageStatus.V2TIM_MSG_STATUS_SENDING:
        return Colors.grey.shade600;
      case MessageStatus.V2TIM_MSG_STATUS_SEND_SUCC:
        return Colors.green.shade600;
      case MessageStatus.V2TIM_MSG_STATUS_SEND_FAIL:
        return Colors.red.shade600;
      case MessageStatus.V2TIM_MSG_STATUS_HAS_DELETED:
        return Colors.grey.shade400;
      case MessageStatus.V2TIM_MSG_STATUS_LOCAL_REVOKED:
        return Colors.orange.shade600;
      default:
        return Colors.grey.shade600;
    }
  }
}

/// 已读状态指示器
class ReadStatusIndicator extends StatelessWidget {
  final V2TimMessage message;
  final bool isMyMessage;

  const ReadStatusIndicator({
    super.key,
    required this.message,
    required this.isMyMessage,
  });

  @override
  Widget build(BuildContext context) {
    if (!isMyMessage) {
      return const SizedBox.shrink();
    }

    // 根据消息的已读状态显示不同的图标
    if (message.isPeerRead == true) {
      return Icon(
        Icons.done_all,
        size: 12,
        color: Colors.blue.shade600,
      );
    } else if (message.status == MessageStatus.V2TIM_MSG_STATUS_SEND_SUCC) {
      return Icon(
        Icons.done,
        size: 12,
        color: Colors.grey.shade600,
      );
    }

    return const SizedBox.shrink();
  }
}