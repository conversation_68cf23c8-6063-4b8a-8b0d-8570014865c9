import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_elem_type.dart';

/// 消息长按操作菜单
class MessageActionsMenu {
  static void show({
    required BuildContext context,
    required V2TimMessage message,
    required bool isMyMessage,
    required VoidCallback onCopy,
    required VoidCallback onDelete,
    required VoidCallback? onRevoke,
    required VoidCallback onQuote,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Safe<PERSON><PERSON>(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 拖拽指示器
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                
                // 消息预览
                _buildMessagePreview(message),
                
                const Divider(),
                
                // 操作选项
                ..._buildActionItems(
                  context: context,
                  message: message,
                  isMyMessage: isMyMessage,
                  onCopy: onCopy,
                  onDelete: onDelete,
                  onRevoke: onRevoke,
                  onQuote: onQuote,
                ),
                
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  static Widget _buildMessagePreview(V2TimMessage message) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getMessageIcon(_getMessageElemType(message.elemType)),
              color: Colors.blue.shade600,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getMessagePreviewText(message),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  _formatTime(message.timestamp),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static List<Widget> _buildActionItems({
    required BuildContext context,
    required V2TimMessage message,
    required bool isMyMessage,
    required VoidCallback onCopy,
    required VoidCallback onDelete,
    required VoidCallback? onRevoke,
    required VoidCallback onQuote,
  }) {
    final actions = <Widget>[];

    // 复制（仅文本消息）
    if (_getMessageElemType(message.elemType) == MessageElemType.V2TIM_ELEM_TYPE_TEXT) {
      actions.add(_buildActionItem(
        icon: Icons.copy,
        title: '复制',
        onTap: () {
          Navigator.pop(context);
          onCopy();
        },
      ));
    }

    // 引用回复
    actions.add(_buildActionItem(
      icon: Icons.reply,
      title: '引用',
      onTap: () {
        Navigator.pop(context);
        onQuote();
      },
    ));

    // 撤回（仅自己的消息，且时间不超过2分钟）
    if (isMyMessage && onRevoke != null && _canRevoke(message)) {
      actions.add(_buildActionItem(
        icon: Icons.undo,
        title: '撤回',
        color: Colors.orange,
        onTap: () {
          Navigator.pop(context);
          onRevoke();
        },
      ));
    }

    // 删除
    actions.add(_buildActionItem(
      icon: Icons.delete_outline,
      title: '删除',
      color: Colors.red,
      onTap: () {
        Navigator.pop(context);
        _showDeleteConfirmation(context, onDelete);
      },
    ));

    return actions;
  }

  static Widget _buildActionItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: color ?? Colors.grey.shade700,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: color ?? Colors.grey.shade700,
          fontSize: 16,
        ),
      ),
      onTap: onTap,
    );
  }

  static void _showDeleteConfirmation(BuildContext context, VoidCallback onDelete) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text('删除消息'),
          content: const Text('确定要删除这条消息吗？此操作不可撤销。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                onDelete();
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }

  static int _getMessageElemType(int? elemType) {
    return elemType ?? MessageElemType.V2TIM_ELEM_TYPE_TEXT;
  }

  static IconData _getMessageIcon(int elemType) {
    switch (elemType) {
      case MessageElemType.V2TIM_ELEM_TYPE_TEXT:
        return Icons.text_fields;
      case MessageElemType.V2TIM_ELEM_TYPE_IMAGE:
        return Icons.image;
      case MessageElemType.V2TIM_ELEM_TYPE_SOUND:
        return Icons.mic;
      case MessageElemType.V2TIM_ELEM_TYPE_VIDEO:
        return Icons.videocam;
      case MessageElemType.V2TIM_ELEM_TYPE_FILE:
        return Icons.attach_file;
      case MessageElemType.V2TIM_ELEM_TYPE_FACE:
        return Icons.emoji_emotions;
      default:
        return Icons.message;
    }
  }

  static String _getMessagePreviewText(V2TimMessage message) {
    final elemType = _getMessageElemType(message.elemType);
    switch (elemType) {
      case MessageElemType.V2TIM_ELEM_TYPE_TEXT:
        return message.textElem?.text ?? '[文本消息]';
      case MessageElemType.V2TIM_ELEM_TYPE_IMAGE:
        return '[图片]';
      case MessageElemType.V2TIM_ELEM_TYPE_SOUND:
        return '[语音]';
      case MessageElemType.V2TIM_ELEM_TYPE_VIDEO:
        return '[视频]';
      case MessageElemType.V2TIM_ELEM_TYPE_FILE:
        return '[文件]';
      case MessageElemType.V2TIM_ELEM_TYPE_FACE:
        return '[表情]';
      default:
        return '[未知消息]';
    }
  }

  static String _formatTime(int? timestamp) {
    if (timestamp == null) return '';
    
    final messageTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    final now = DateTime.now();
    final difference = now.difference(messageTime);

    if (difference.inDays > 0) {
      return '${messageTime.month}/${messageTime.day} ${messageTime.hour.toString().padLeft(2, '0')}:${messageTime.minute.toString().padLeft(2, '0')}';
    } else {
      return '${messageTime.hour.toString().padLeft(2, '0')}:${messageTime.minute.toString().padLeft(2, '0')}';
    }
  }

  static bool _canRevoke(V2TimMessage message) {
    if (message.timestamp == null) return false;
    
    final messageTime = DateTime.fromMillisecondsSinceEpoch(message.timestamp! * 1000);
    final now = DateTime.now();
    final difference = now.difference(messageTime);
    
    // 2分钟内可以撤回
    return difference.inMinutes < 2;
  }

  /// 复制文本到剪贴板
  static Future<void> copyToClipboard(BuildContext context, String text) async {
    await Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已复制到剪贴板'),
        duration: Duration(seconds: 1),
      ),
    );
  }
}