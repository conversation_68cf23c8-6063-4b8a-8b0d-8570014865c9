import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:photo_view/photo_view.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_image_elem.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_image_elem.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_image.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_image.dart';

/// 图片消息显示组件
class ImageMessageWidget extends StatelessWidget {
  final V2TimMessage message;
  final bool isMyMessage;

  const ImageMessageWidget({
    super.key,
    required this.message,
    required this.isMyMessage,
  });

  @override
  Widget build(BuildContext context) {
    final imageElem = message.imageElem;
    if (imageElem == null) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Text('图片加载失败'),
      );
    }

    // 获取合适的图片尺寸（优先使用原图，降级到大图、中图、小图）
    final imageInfo = _getBestImageInfo(imageElem);
    
    if (imageInfo == null) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Text('图片不可用'),
      );
    }

    return GestureDetector(
      onTap: () => _showFullImage(context, imageInfo.url ?? ''),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 200,
          maxHeight: 200,
          minWidth: 100,
          minHeight: 100,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: CachedNetworkImage(
            imageUrl: imageInfo.url ?? '',
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: Colors.grey.shade200,
              child: const Center(
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey.shade300,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.broken_image,
                    color: Colors.grey.shade600,
                    size: 32,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '图片加载失败',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 获取最佳的图片信息
  V2TimImage? _getBestImageInfo(V2TimImageElem imageElem) {
    // 优先级：原图 > 大图 > 中图 > 小图
    if (imageElem.imageList != null && imageElem.imageList!.isNotEmpty) {
      // 按优先级排序
      final imageList = List<V2TimImage>.from(imageElem.imageList!);
      imageList.sort((a, b) {
        final typeA = a.type ?? '';
        final typeB = b.type ?? '';
        
        // 原图 = 0, 大图 = 1, 中图 = 2, 小图 = 3
        final Map<String, int> priority = {
          '0': 0, // 原图
          '1': 1, // 大图
          '2': 2, // 中图
          '3': 3, // 小图
        };
        
        return (priority[typeA] ?? 999) - (priority[typeB] ?? 999);
      });
      
      return imageList.first;
    }
    
    return null;
  }

  /// 显示全屏图片
  void _showFullImage(BuildContext context, String imageUrl) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white),
            actions: [
              IconButton(
                icon: const Icon(Icons.download, color: Colors.white),
                onPressed: () => _downloadImage(context, imageUrl),
              ),
            ],
          ),
          body: Center(
            child: PhotoView(
              imageProvider: CachedNetworkImageProvider(imageUrl),
              minScale: PhotoViewComputedScale.contained,
              maxScale: PhotoViewComputedScale.covered * 2,
              heroAttributes: PhotoViewHeroAttributes(tag: imageUrl),
              loadingBuilder: (context, event) => const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
              errorBuilder: (context, error, stackTrace) => const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.broken_image,
                      color: Colors.white,
                      size: 64,
                    ),
                    SizedBox(height: 16),
                    Text(
                      '图片加载失败',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 下载图片
  void _downloadImage(BuildContext context, String imageUrl) {
    // TODO: 实现图片下载功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('图片下载功能即将上线'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}