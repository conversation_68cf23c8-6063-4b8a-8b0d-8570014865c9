import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/user/view_models/user_profile_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/moments/moment_card.dart';
import 'package:user_app/widgets/user_avatar.dart';

class UserProfilePage extends StatefulWidget {
  final int userId;

  const UserProfilePage({
    super.key,
    required this.userId,
  });

  @override
  State<UserProfilePage> createState() => _UserProfilePageState();
}

class _UserProfilePageState extends State<UserProfilePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late UserProfileViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _viewModel = UserProfileViewModel();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _viewModel.loadUserProfile(widget.userId);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Consumer<UserProfileViewModel>(
        builder: (context, viewModel, child) {
          return Scaffold(
            body: _buildBody(viewModel),
          );
        },
      ),
    );
  }

  Widget _buildBody(UserProfileViewModel viewModel) {
    if (viewModel.isLoading) {
      return const Scaffold(
        appBar: null,
        body: Center(
          child: CircularProgressIndicator(color: Colors.blue),
        ),
      );
    }

    if (viewModel.error != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('用户主页'),
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                viewModel.error!,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => viewModel.loadUserProfile(widget.userId),
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    if (viewModel.user == null) {
      return const Scaffold(
        body: Center(
          child: Text('用户不存在'),
        ),
      );
    }

    return NestedScrollView(
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          SliverAppBar(
            expandedHeight: 300,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              background: _buildProfileHeader(viewModel),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.more_vert),
                onPressed: _showMoreOptions,
              ),
            ],
          ),
          SliverPersistentHeader(
            delegate: _SliverAppBarDelegate(
              TabBar(
                controller: _tabController,
                labelColor: Colors.black,
                unselectedLabelColor: Colors.grey,
                indicatorColor: Colors.blue,
                tabs: const [
                  Tab(text: '动态'),
                  Tab(text: '钓点'),
                  Tab(text: '收藏'),
                ],
              ),
            ),
            pinned: true,
          ),
        ];
      },
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMomentsTab(viewModel),
          _buildSpotsTab(viewModel),
          _buildFavoritesTab(viewModel),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(UserProfileViewModel viewModel) {
    final user = viewModel.user!;
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          const SizedBox(height: 50), // 为AppBar留空间
          UserAvatar(
            avatarUrl: user.avatarUrl,
            radius: 50,
            placeholderText: user.name.isNotEmpty ? user.name[0] : '?',
          ),
          const SizedBox(height: 16),
          Text(
            user.name,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          if (user.introduce != null && user.introduce!.isNotEmpty)
            Text(
              user.introduce!,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          if (user.province != null || user.city != null)
            const SizedBox(height: 4),
          if (user.province != null || user.city != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                Text(
                  '${user.province ?? ''}${user.city ?? ''}${user.county ?? ''}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem('动态', user.momentCount ?? 0),
              _buildStatItem('关注', user.attentionCount ?? 0),
              _buildStatItem('粉丝', user.followCount ?? 0),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: viewModel.isFollowLoading ? null : () => viewModel.toggleFollow(widget.userId),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: viewModel.isFollowing ? Colors.grey : Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: viewModel.isFollowLoading
                      ? const SizedBox(
                          height: 16,
                          width: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(viewModel.isFollowing ? '已关注' : '关注'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _startChat(user),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('发消息'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildMomentsTab(UserProfileViewModel viewModel) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        if (!viewModel.momentsLoading &&
            viewModel.hasMomentsMore &&
            scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
          viewModel.loadUserMoments(widget.userId);
        }
        return false;
      },
      child: RefreshIndicator(
        onRefresh: () => viewModel.loadUserMoments(widget.userId, refresh: true),
        child: Builder(
          builder: (context) {
            // Initialize loading if not loaded yet
            if (viewModel.userMoments.isEmpty && !viewModel.momentsLoading) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                viewModel.loadUserMoments(widget.userId);
              });
            }

            if (viewModel.userMoments.isEmpty && viewModel.momentsLoading) {
              return const Center(
                child: CircularProgressIndicator(color: Colors.blue),
              );
            }

            if (viewModel.userMoments.isEmpty) {
              return const Center(
                child: Text(
                  '暂无动态',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: viewModel.userMoments.length + (viewModel.hasMomentsMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == viewModel.userMoments.length) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: CircularProgressIndicator(color: Colors.blue),
                    ),
                  );
                }

                final moment = viewModel.userMoments[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: MomentCard(
                    moment: moment,
                    onLikeChanged: (isLiked) {
                      viewModel.updateMomentLike(
                        moment.id!,
                        isLiked,
                        isLiked ? (moment.likeCount ?? 0) + 1 : (moment.likeCount ?? 0) - 1,
                      );
                    },
                    onCommentAdded: () {
                      viewModel.updateMomentComment(
                        moment.id!,
                        (moment.commentCount ?? 0) + 1,
                      );
                    },
                    onDeleted: () => viewModel.removeMoment(moment.id!),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildSpotsTab(UserProfileViewModel viewModel) {
    return Builder(
      builder: (context) {
        if (viewModel.userSpots.isEmpty && !viewModel.spotsLoading) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            viewModel.loadUserSpots(widget.userId);
          });
        }

        if (viewModel.userSpots.isEmpty && viewModel.spotsLoading) {
          return const Center(
            child: CircularProgressIndicator(color: Colors.blue),
          );
        }

        return const Center(
          child: Text(
            '暂无钓点',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        );
      },
    );
  }

  Widget _buildFavoritesTab(UserProfileViewModel viewModel) {
    return Builder(
      builder: (context) {
        if (viewModel.userBookmarks.isEmpty && !viewModel.bookmarksLoading) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            viewModel.loadUserBookmarks(widget.userId);
          });
        }

        if (viewModel.userBookmarks.isEmpty && viewModel.bookmarksLoading) {
          return const Center(
            child: CircularProgressIndicator(color: Colors.blue),
          );
        }

        return const Center(
          child: Text(
            '暂无收藏',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        );
      },
    );
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.report),
                title: const Text('举报'),
                onTap: () {
                  Navigator.pop(context);
                  _showSnackBar('举报功能待开发');
                },
              ),
              ListTile(
                leading: const Icon(Icons.block),
                title: const Text('拉黑'),
                onTap: () {
                  Navigator.pop(context);
                  _showSnackBar('拉黑功能待开发');
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _startChat(user) {
    context.push('/chat_detail', extra: {
      'conversationID': 'c2c_${user.id}',
      'conversationType': 1, // C2C
      'showName': user.name,
      'userID': user.id.toString(),
    });
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverAppBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.white,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}