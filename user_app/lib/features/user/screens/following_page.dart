import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/user/view_models/following_view_model.dart';
import 'package:user_app/models/user.dart';

class FollowingPage extends StatefulWidget {
  final String userId;

  const FollowingPage({
    super.key,
    required this.userId,
  });

  @override
  State<FollowingPage> createState() => _FollowingPageState();
}

class _FollowingPageState extends State<FollowingPage>
    with TickerProviderStateMixin {
  late FollowingViewModel _viewModel;
  final ScrollController _scrollController = ScrollController();

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _rotateController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _rotateAnimation;

  // 列表项动画控制器
  final List<AnimationController> _itemControllers = [];
  final Map<int, AnimationController> _unfollowAnimationControllers = {};

  @override
  void initState() {
    super.initState();
    _viewModel = FollowingViewModel();
    _scrollController.addListener(_onScroll);

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _rotateController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    _rotateAnimation = Tween<double>(
      begin: -0.1,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _rotateController,
      curve: Curves.easeOutBack,
    ));

    // 启动动画
    _fadeController.forward();
    _slideController.forward();
    _rotateController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _viewModel.loadFollowing(int.parse(widget.userId));
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _rotateController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    for (var controller in _unfollowAnimationControllers.values) {
      controller.dispose();
    }
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _viewModel.loadMoreFollowing();
    }
  }

  // 创建列表项动画控制器
  AnimationController _createItemController(int index) {
    if (index >= _itemControllers.length) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 500 + (index * 60)),
        vsync: this,
      );
      _itemControllers.add(controller);
      controller.forward();
    }
    return _itemControllers[index];
  }

  // 处理取消关注动画
  void _handleUnfollowAnimation(int userId) {
    if (!_unfollowAnimationControllers.containsKey(userId)) {
      _unfollowAnimationControllers[userId] = AnimationController(
        duration: const Duration(milliseconds: 400),
        vsync: this,
      );
    }
    final controller = _unfollowAnimationControllers[userId]!;
    controller.forward();
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    HapticFeedback.mediumImpact();
    await _viewModel.refreshFollowing(int.parse(widget.userId));
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F7FA),
        body: Stack(
          children: [
            // 渐变背景 - 使用不同的渐变方向
            Container(
              height: 300,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topRight,
                  end: Alignment.bottomLeft,
                  colors: [
                    Color(0xFF764BA2),
                    Color(0xFF667EEA),
                  ],
                ),
              ),
            ),
            // 装饰性图案
            Positioned(
              top: -50,
              right: -50,
              child: AnimatedBuilder(
                animation: _rotateController,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _rotateAnimation.value,
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.white.withOpacity(0.1),
                      ),
                    ),
                  );
                },
              ),
            ),
            // 主内容
            SafeArea(
              child: Column(
                children: [
                  // 自定义AppBar
                  _buildCustomAppBar(),
                  // 内容区域
                  Expanded(
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildContent(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // 返回按钮
          IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Icon(Icons.arrow_back_ios,
                  color: Colors.white, size: 20),
            ),
            onPressed: () {
              HapticFeedback.lightImpact();
              context.pop();
            },
          ),
          // 标题
          Expanded(
            child: AnimatedBuilder(
              animation: _rotateController,
              builder: (context, child) {
                return Transform.scale(
                  scale: 0.9 + (_rotateController.value * 0.1),
                  child: Column(
                    children: [
                      const Text(
                        '关注列表',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.8,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Consumer<FollowingViewModel>(
                        builder: (context, viewModel, child) {
                          return Text(
                            '已关注 ${viewModel.following.length} 人',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.9),
                              fontSize: 14,
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 48), // 平衡布局
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Consumer<FollowingViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.isLoading && viewModel.following.isEmpty) {
          return _buildLoadingState();
        }

        if (viewModel.following.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: _onRefresh,
          color: const Color(0xFF764BA2),
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.only(top: 20, bottom: 20),
            itemCount: viewModel.following.length + (viewModel.hasMore ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == viewModel.following.length) {
                return _buildLoadMoreIndicator();
              }
              return _buildFollowingItem(viewModel.following[index], index);
            },
          ),
        );
      },
    );
  }

  Widget _buildFollowingItem(User user, int index) {
    final controller = _createItemController(index);
    final unfollowController = _unfollowAnimationControllers[user.id];

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (controller.value * 0.2),
          child: Opacity(
            opacity: controller.value,
            child: AnimatedBuilder(
              animation: unfollowController ?? AlwaysStoppedAnimation(0.0),
              builder: (context, child) {
                final slideValue = unfollowController?.value ?? 0.0;
                return Transform.translate(
                  offset: Offset(
                    MediaQuery.of(context).size.width * slideValue,
                    0,
                  ),
                  child: Opacity(
                    opacity: 1.0 - slideValue,
                    child: Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white,
                            Colors.white.withOpacity(0.95),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF764BA2).withOpacity(0.1),
                            blurRadius: 15,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: InkWell(
                        onTap: () {
                          HapticFeedback.lightImpact();
                          context.push(
                            AppRoutes.profile
                                .replaceFirst(':userId', user.id.toString()),
                          );
                        },
                        borderRadius: BorderRadius.circular(25),
                        child: Padding(
                          padding: const EdgeInsets.all(18),
                          child: Row(
                            children: [
                              // 用户头像
                              _buildAvatar(user),
                              const SizedBox(width: 18),
                              // 用户信息
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      user.name ?? '未知用户',
                                      style: const TextStyle(
                                        fontSize: 17,
                                        fontWeight: FontWeight.bold,
                                        color: Color(0xFF1A1E25),
                                      ),
                                    ),
                                    const SizedBox(height: 6),
                                    Text(
                                      user.introduce ?? '这个人很懒，什么都没留下~',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[600],
                                        height: 1.3,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        _buildUserTag(
                                            '动态 ${user.momentCount ?? 0}'),
                                        const SizedBox(width: 10),
                                        _buildUserTag(
                                            '粉丝 ${user.followCount ?? 0}'),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              // 操作按钮组
                              Column(
                                children: [
                                  // 私信按钮
                                  _buildMessageButton(user),
                                  const SizedBox(height: 8),
                                  // 取消关注按钮
                                  _buildUnfollowButton(user),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildAvatar(User user) {
    return Hero(
      tag: 'avatar_${user.id}',
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF764BA2), Color(0xFF667EEA)],
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF764BA2).withOpacity(0.4),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        padding: const EdgeInsets.all(3),
        child: CircleAvatar(
          radius: 30,
          backgroundColor: Colors.white,
          child: CircleAvatar(
            radius: 28,
            backgroundImage: user.avatarUrl != null
                ? CachedNetworkImageProvider(user.avatarUrl!)
                : null,
            child: user.avatarUrl == null
                ? Text(
                    (user.name ?? 'U').substring(0, 1).toUpperCase(),
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF764BA2),
                    ),
                  )
                : null,
          ),
        ),
      ),
    );
  }

  Widget _buildUserTag(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFF764BA2).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 12,
          color: Color(0xFF764BA2),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildUnfollowButton(User user) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showUnfollowDialog(user);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.grey[300]!,
              Colors.grey[400]!,
            ],
          ),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check,
              size: 16,
              color: Colors.white,
            ),
            const SizedBox(width: 4),
            Text(
              '已关注',
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageButton(User user) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // 导航到聊天页面 - 使用正确的路由格式
        final conversationID = 'c2c_${user.id}';
        context.push(
          '/chat/simple/$conversationID',
          extra: {
            'conversationID': conversationID,
            'conversationType': 1, // C2C私聊
            'showName': user.name ?? '用户${user.id}',
            'userID': user.id.toString(),
            'userAvatar': user.avatarUrl,
          },
        );
      },
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              const Color(0xFF764BA2).withOpacity(0.1),
              const Color(0xFF667EEA).withOpacity(0.1),
            ],
          ),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF764BA2).withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Center(
          child: Icon(
            Icons.send_rounded,
            size: 20,
            color: Color(0xFF764BA2),
          ),
        ),
      ),
    );
  }

  void _showUnfollowDialog(User user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '确认取消关注',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          '确定要取消关注 ${user.name} 吗？',
          style: const TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              '取消',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _handleUnfollowAnimation(user.id!);
              // TODO: 实现取消关注逻辑
              Future.delayed(const Duration(milliseconds: 400), () {
                _viewModel.removeUser(user.id!);
              });
            },
            child: const Text(
              '确定',
              style: TextStyle(
                color: Color(0xFF764BA2),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF764BA2), Color(0xFF667EEA)],
              ),
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF764BA2).withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: const Center(
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 3,
              ),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            '加载中...',
            style: TextStyle(
              color: Color(0xFF764BA2),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: RotationTransition(
        turns: _rotateAnimation,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 140,
              height: 140,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF764BA2).withOpacity(0.1),
                    const Color(0xFF667EEA).withOpacity(0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.person_add_outlined,
                size: 70,
                color: Color(0xFF764BA2),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              '还没有关注任何人',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1A1E25),
              ),
            ),
            const SizedBox(height: 10),
            Text(
              '快去关注感兴趣的人吧',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () {
                HapticFeedback.lightImpact();
                // TODO: 跳转到发现页面
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF764BA2),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
                padding:
                    const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
              ),
              child: const Text(
                '去发现',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadMoreIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: const Center(
        child: CircularProgressIndicator(
          color: Color(0xFF764BA2),
          strokeWidth: 2,
        ),
      ),
    );
  }
}
