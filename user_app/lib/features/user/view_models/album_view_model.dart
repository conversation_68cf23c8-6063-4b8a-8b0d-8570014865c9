import 'package:flutter/foundation.dart';
import 'package:user_app/api/photo_api.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/models/photo/photo.dart';
import 'package:user_app/models/photo/photo_page_request.dart';

class AlbumViewModel extends ChangeNotifier {
  final PhotoApi _photoApi = getIt<PhotoApi>();
  
  List<Photo> _photos = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;
  static const int _pageSize = 30;
  
  // Selection mode
  bool _isSelectionMode = false;
  Set<int> _selectedPhotos = {};

  List<Photo> get photos => _photos;
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  bool get isSelectionMode => _isSelectionMode;
  Set<int> get selectedPhotos => _selectedPhotos;

  Future<void> loadAlbumPhotos() async {
    if (_isLoading) return;

    _isLoading = true;
    _currentPage = 1;
    notifyListeners();

    try {
      final request = PhotoPageRequest(
        pageNum: _currentPage,
        pageSize: _pageSize,
        onlyUserPhotos: true, // Only load current user's photos
      );

      final response = await _photoApi.getUserPhotos(request);
      
      _photos = response.records;
      _hasMore = _currentPage < response.pages;
      
    } catch (error) {
      debugPrint('Failed to load album photos: $error');
      _photos = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadMorePhotos() async {
    if (_isLoading || !_hasMore) return;

    _isLoading = true;
    notifyListeners();

    try {
      _currentPage++;
      final request = PhotoPageRequest(
        pageNum: _currentPage,
        pageSize: _pageSize,
        onlyUserPhotos: true,
      );

      final response = await _photoApi.getUserPhotos(request);
      
      _photos.addAll(response.records);
      _hasMore = _currentPage < response.pages;
      
    } catch (error) {
      debugPrint('Failed to load more photos: $error');
      _currentPage--; // Revert page increment on error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refreshPhotos() async {
    _photos.clear();
    _hasMore = true;
    _currentPage = 1;
    _clearSelection();
    await loadAlbumPhotos();
  }

  void toggleSelectionMode() {
    _isSelectionMode = !_isSelectionMode;
    if (!_isSelectionMode) {
      _clearSelection();
    }
    notifyListeners();
  }

  void togglePhotoSelection(int photoId) {
    if (_selectedPhotos.contains(photoId)) {
      _selectedPhotos.remove(photoId);
    } else {
      _selectedPhotos.add(photoId);
    }
    notifyListeners();
  }

  void selectAll() {
    _selectedPhotos = _photos.map((photo) => photo.id).toSet();
    notifyListeners();
  }

  void _clearSelection() {
    _selectedPhotos.clear();
    notifyListeners();
  }

  Future<void> deleteSelectedPhotos() async {
    if (_selectedPhotos.isEmpty) return;

    try {
      final photoIds = _selectedPhotos.toList();
      await _photoApi.deletePhotos(photoIds);
      
      // Remove deleted photos from local list
      _photos.removeWhere((photo) => _selectedPhotos.contains(photo.id));
      _clearSelection();
      _isSelectionMode = false;
      notifyListeners();
      
    } catch (error) {
      debugPrint('Failed to delete photos: $error');
      // Could show error message to user
    }
  }

  Future<void> deletePhoto(int photoId) async {
    try {
      await _photoApi.deletePhoto(photoId);
      
      // Remove from local list
      _photos.removeWhere((photo) => photo.id == photoId);
      _selectedPhotos.remove(photoId);
      notifyListeners();
      
    } catch (error) {
      debugPrint('Failed to delete photo: $error');
    }
  }
}