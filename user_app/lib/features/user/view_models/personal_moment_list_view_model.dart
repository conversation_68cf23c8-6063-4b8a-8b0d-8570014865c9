import 'package:flutter/foundation.dart';
import 'package:user_app/api/moment_api.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/moment/moment_page_request.dart';
import 'package:user_app/models/moment/moment_response.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class PersonalMomentListViewModel extends ChangeNotifier {
  final MomentApi _momentApi = getIt<MomentApi>();
  final AuthViewModel _authViewModel = getIt<AuthViewModel>();
  
  List<MomentVo> _moments = [];
  bool _isLoading = false;
  bool _hasMore = true;
  bool _hasError = false;
  String? _errorMessage;
  int _currentPage = 1;
  static const int _pageSize = 10;

  List<MomentVo> get moments => _moments;
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  bool get hasError => _hasError;
  String? get errorMessage => _errorMessage;

  Future<void> loadMoments() async {
    if (_isLoading) return;

    _isLoading = true;
    _currentPage = 1;
    _hasError = false;
    _errorMessage = null;
    notifyListeners();

    try {
      final request = MomentPageRequest(
        pageNum: _currentPage,
        pageSize: _pageSize,
        onlyMine: true, // Only load current user's moments
      );

      final userId = _authViewModel.getCurrentUserId();
      final response = await _momentApi.getPersonalMoments(request, userId);
      
      _moments = response.records;
      _hasMore = _currentPage < response.pages;
      
    } catch (error) {
      debugPrint('Failed to load personal moments: $error');
      _hasError = true;
      _errorMessage = '加载失败，请重试';
      _moments = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadMoreMoments() async {
    if (_isLoading || !_hasMore) return;

    _isLoading = true;
    notifyListeners();

    try {
      _currentPage++;
      final request = MomentPageRequest(
        pageNum: _currentPage,
        pageSize: _pageSize,
        onlyMine: true,
      );

      final userId = _authViewModel.getCurrentUserId();
      final response = await _momentApi.getPersonalMoments(request, userId);
      
      _moments.addAll(response.records);
      _hasMore = _currentPage < response.pages;
      
    } catch (error) {
      debugPrint('Failed to load more personal moments: $error');
      _currentPage--; // Revert page increment on error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refreshMoments() async {
    _moments.clear();
    _hasMore = true;
    _currentPage = 1;
    _hasError = false;
    _errorMessage = null;
    await loadMoments();
  }

  Future<void> deleteMoment(int momentId) async {
    try {
      await _momentApi.deleteMoment(momentId);
      
      // Remove from local list
      _moments.removeWhere((moment) => moment.id == momentId);
      notifyListeners();
      
    } catch (error) {
      debugPrint('Failed to delete moment: $error');
      // Could show error message to user
    }
  }

  Future<void> likeMoment(int momentId, bool isLike) async {
    try {
      await _momentApi.likeMoment(momentId, isLike);
      
      // Update local state
      final momentIndex = _moments.indexWhere((moment) => moment.id == momentId);
      if (momentIndex != -1) {
        final currentMoment = _moments[momentIndex];
        final newLikeCount = isLike 
            ? (currentMoment.likeCount ?? 0) + 1 
            : (currentMoment.likeCount ?? 0) - 1;
        
        _moments[momentIndex] = currentMoment.copyWith(
          isLiked: isLike,
          likeCount: newLikeCount,
        );
        notifyListeners();
      }
      
    } catch (error) {
      debugPrint('Failed to like moment: $error');
      // Revert local state if API call failed
      final momentIndex = _moments.indexWhere((moment) => moment.id == momentId);
      if (momentIndex != -1) {
        final currentMoment = _moments[momentIndex];
        _moments[momentIndex] = currentMoment.copyWith(
          isLiked: !isLike, // Revert to previous state
        );
        notifyListeners();
      }
      rethrow; // Let the UI handle the error
    }
  }

  Future<void> updateMomentVisibility(int momentId, String visibility) async {
    try {
      debugPrint('ViewModel: Updating moment $momentId visibility to $visibility');
      await _momentApi.updateMomentVisibility(momentId, visibility);
      
      // Update local state
      final momentIndex = _moments.indexWhere((moment) => moment.id == momentId);
      if (momentIndex != -1) {
        debugPrint('ViewModel: Found moment at index $momentIndex, updating local state');
        _moments[momentIndex] = _moments[momentIndex].copyWith(
          visibility: visibility,
        );
        notifyListeners();
        debugPrint('ViewModel: Local state updated successfully');
      } else {
        debugPrint('ViewModel: Warning - moment not found in local list');
      }
      
    } catch (error, stackTrace) {
      debugPrint('ViewModel: Failed to update moment visibility: $error');
      debugPrint('ViewModel: Stack trace: $stackTrace');
      rethrow; // Let the UI handle the error
    }
  }

  void updateMomentLike(int momentId, bool isLiked, int likeCount) {
    final momentIndex = _moments.indexWhere((moment) => moment.id == momentId);
    if (momentIndex != -1) {
      _moments[momentIndex] = _moments[momentIndex].copyWith(
        isLiked: isLiked,
        likeCount: likeCount,
      );
      notifyListeners();
    }
  }

  void updateMomentComment(int momentId, int commentCount) {
    final momentIndex = _moments.indexWhere((moment) => moment.id == momentId);
    if (momentIndex != -1) {
      _moments[momentIndex] = _moments[momentIndex].copyWith(
        commentCount: commentCount,
      );
      notifyListeners();
    }
  }
}