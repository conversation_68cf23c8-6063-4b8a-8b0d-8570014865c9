import 'package:flutter/foundation.dart';
import 'package:user_app/api/user_api.dart';
import 'package:user_app/api/moment_api.dart';
import 'package:user_app/api/fishing_spot_api.dart';
import 'package:user_app/api/bookmark_api.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/moment/moment_list_request.dart';
import 'package:user_app/models/fishing_spots/fishing_spot_vo.dart';

class UserProfileViewModel extends ChangeNotifier {
  final UserApi _userApi = getIt<UserApi>();
  final MomentApi _momentApi = getIt<MomentApi>();
  final FishingSpotApi _fishingSpotApi = getIt<FishingSpotApi>();
  final BookmarkApi _bookmarkApi = getIt<BookmarkApi>();

  User? _user;
  bool _isLoading = false;
  String? _error;
  bool _isFollowing = false;
  bool _isFollowLoading = false;

  // Tab data
  List<MomentVo> _userMoments = [];
  List<FishingSpotVo> _userSpots = [];
  List<dynamic> _userBookmarks = [];
  
  bool _momentsLoading = false;
  bool _spotsLoading = false;
  bool _bookmarksLoading = false;
  
  int _momentsPage = 1;
  int _spotsPage = 1;
  int _bookmarksPage = 1;
  
  bool _hasMomentsMore = true;
  bool _hasSpotsMore = true;
  bool _hasBookmarksMore = true;

  // Getters
  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isFollowing => _isFollowing;
  bool get isFollowLoading => _isFollowLoading;
  
  List<MomentVo> get userMoments => _userMoments;
  List<FishingSpotVo> get userSpots => _userSpots;
  List<dynamic> get userBookmarks => _userBookmarks;
  
  bool get momentsLoading => _momentsLoading;
  bool get spotsLoading => _spotsLoading;
  bool get bookmarksLoading => _bookmarksLoading;
  
  bool get hasMomentsMore => _hasMomentsMore;
  bool get hasSpotsMore => _hasSpotsMore;
  bool get hasBookmarksMore => _hasBookmarksMore;

  Future<void> loadUserProfile(int userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _user = await _userApi.getUserProfile(userId);
      await _checkFollowStatus(userId);
    } catch (e) {
      _error = '加载用户信息失败: $e';
      debugPrint('Failed to load user profile: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _checkFollowStatus(int userId) async {
    try {
      // _isFollowing = await _userFollowApi.isFollowingUser(userId);
      // 暂时设置为false，等API实现
      _isFollowing = false;
    } catch (e) {
      debugPrint('Failed to check follow status: $e');
    }
  }

  Future<void> toggleFollow(int userId) async {
    _isFollowLoading = true;
    notifyListeners();

    try {
      if (_isFollowing) {
        // await _userFollowApi.unfollowUser(userId);
        _isFollowing = false;
        // Update follower count - no direct update needed
      } else {
        // await _userFollowApi.followUser(userId);
        _isFollowing = true;
        // Update follower count - no direct update needed
      }
    } catch (e) {
      debugPrint('Failed to toggle follow: $e');
    } finally {
      _isFollowLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadUserMoments(int userId, {bool refresh = false}) async {
    if (_momentsLoading) return;

    if (refresh) {
      _momentsPage = 1;
      _userMoments.clear();
      _hasMomentsMore = true;
    }

    _momentsLoading = true;
    notifyListeners();

    try {
      final request = MomentListRequest(
        pageNum: _momentsPage,
        pageSize: 10,
        userId: userId,
      );
      
      final response = await _momentApi.getMoments(request);
      
      if (refresh) {
        _userMoments = response.records;
      } else {
        _userMoments.addAll(response.records);
      }
      
      _hasMomentsMore = _momentsPage < response.pages;
      if (_hasMomentsMore) {
        _momentsPage++;
      }
    } catch (e) {
      debugPrint('Failed to load user moments: $e');
    } finally {
      _momentsLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadUserSpots(int userId, {bool refresh = false}) async {
    if (_spotsLoading) return;

    if (refresh) {
      _spotsPage = 1;
      _userSpots.clear();
      _hasSpotsMore = true;
    }

    _spotsLoading = true;
    notifyListeners();

    try {
      // final response = await _fishingSpotApi.getUserSpots(userId, _spotsPage, 10);
      // _userSpots = response.records;
      // _hasSpotsMore = _spotsPage < response.pages;
      
      // 暂时使用空数据
      _userSpots = [];
      _hasSpotsMore = false;
    } catch (e) {
      debugPrint('Failed to load user spots: $e');
    } finally {
      _spotsLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadUserBookmarks(int userId, {bool refresh = false}) async {
    if (_bookmarksLoading) return;

    if (refresh) {
      _bookmarksPage = 1;
      _userBookmarks.clear();
      _hasBookmarksMore = true;
    }

    _bookmarksLoading = true;
    notifyListeners();

    try {
      // final response = await _bookmarkApi.getUserBookmarks(userId, _bookmarksPage, 10);
      // _userBookmarks = response.records;
      // _hasBookmarksMore = _bookmarksPage < response.pages;
      
      // 暂时使用空数据
      _userBookmarks = [];
      _hasBookmarksMore = false;
    } catch (e) {
      debugPrint('Failed to load user bookmarks: $e');
    } finally {
      _bookmarksLoading = false;
      notifyListeners();
    }
  }

  void updateMomentLike(int momentId, bool isLiked, int likeCount) {
    final momentIndex = _userMoments.indexWhere((moment) => moment.id == momentId);
    if (momentIndex != -1) {
      _userMoments[momentIndex] = _userMoments[momentIndex].copyWith(
        isLiked: isLiked,
        likeCount: likeCount,
      );
      notifyListeners();
    }
  }

  void updateMomentComment(int momentId, int commentCount) {
    final momentIndex = _userMoments.indexWhere((moment) => moment.id == momentId);
    if (momentIndex != -1) {
      _userMoments[momentIndex] = _userMoments[momentIndex].copyWith(
        commentCount: commentCount,
      );
      notifyListeners();
    }
  }

  void removeMoment(int momentId) {
    _userMoments.removeWhere((moment) => moment.id == momentId);
    // Update moment count - would need actual copyWith implementation
    notifyListeners();
  }
}