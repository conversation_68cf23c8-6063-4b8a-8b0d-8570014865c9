import 'package:flutter/foundation.dart';
import 'package:user_app/api/user_api.dart';
import 'package:user_app/api/user_follow_api.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/models/moment/user_fans_attentions_request.dart';
import 'package:user_app/models/user.dart';

class FansViewModel extends ChangeNotifier {
  final UserApi _userApi = getIt<UserApi>();
  final UserFollowApi _followApi = getIt<UserFollowApi>();
  
  List<User> _fans = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;
  static const int _pageSize = 20;
  int? _currentUserId;
  String? _error;
  
  // 正在操作的用户ID集合
  final Set<int> _followingUsers = {};

  List<User> get fans => _fans;
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  String? get error => _error;
  
  bool isFollowingUser(int userId) => _followingUsers.contains(userId);

  Future<void> loadFans(int userId) async {
    if (_isLoading) return;

    _currentUserId = userId;
    _isLoading = true;
    _currentPage = 1;
    _error = null;
    notifyListeners();

    try {
      final request = UserFansAttentionsRequest(
        userId: userId,
        pageNum: _currentPage,
        pageSize: _pageSize,
      );

      final response = await _userApi.getFans(request);
      
      _fans = response.records;
      _hasMore = _currentPage < response.pages;
      
    } catch (error) {
      debugPrint('Failed to load fans: $error');
      _error = '加载粉丝列表失败';
      _fans = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadMoreFans() async {
    if (_isLoading || !_hasMore || _currentUserId == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      _currentPage++;
      final request = UserFansAttentionsRequest(
        userId: _currentUserId!,
        pageNum: _currentPage,
        pageSize: _pageSize,
      );

      final response = await _userApi.getFans(request);
      
      _fans.addAll(response.records);
      _hasMore = _currentPage < response.pages;
      
    } catch (error) {
      debugPrint('Failed to load more fans: $error');
      _error = '加载更多失败';
      _currentPage--; // Revert page increment on error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refreshFans(int userId) async {
    _fans.clear();
    _hasMore = true;
    _currentPage = 1;
    await loadFans(userId);
  }

  void removeUser(int userId) {
    _fans.removeWhere((user) => user.id == userId);
    notifyListeners();
  }

  /// Follow a user
  Future<bool> followUser(int userId) async {
    if (_followingUsers.contains(userId)) return false;
    
    _followingUsers.add(userId);
    notifyListeners();
    
    try {
      await _followApi.followUser(userId);
      // Update local user status
      final userIndex = _fans.indexWhere((user) => user.id == userId);
      if (userIndex != -1) {
        _fans[userIndex] = _fans[userIndex].copyWith(isFollowing: true);
      }
      return true;
    } catch (error) {
      debugPrint('Failed to follow user: $error');
      _error = '关注失败，请重试';
      // Revert optimistic update
      final userIndex = _fans.indexWhere((user) => user.id == userId);
      if (userIndex != -1) {
        _fans[userIndex] = _fans[userIndex].copyWith(isFollowing: false);
      }
      return false;
    } finally {
      _followingUsers.remove(userId);
      notifyListeners();
    }
  }

  /// Unfollow a user
  Future<bool> unfollowUser(int userId) async {
    if (_followingUsers.contains(userId)) return false;
    
    _followingUsers.add(userId);
    notifyListeners();
    
    try {
      await _followApi.unfollowUser(userId);
      // Update local user status
      final userIndex = _fans.indexWhere((user) => user.id == userId);
      if (userIndex != -1) {
        _fans[userIndex] = _fans[userIndex].copyWith(isFollowing: false);
      }
      return true;
    } catch (error) {
      debugPrint('Failed to unfollow user: $error');
      _error = '取消关注失败，请重试';
      // Revert optimistic update
      final userIndex = _fans.indexWhere((user) => user.id == userId);
      if (userIndex != -1) {
        _fans[userIndex] = _fans[userIndex].copyWith(isFollowing: true);
      }
      return false;
    } finally {
      _followingUsers.remove(userId);
      notifyListeners();
    }
  }

  /// Toggle follow status
  Future<bool> toggleFollowStatus(int userId) async {
    final userIndex = _fans.indexWhere((user) => user.id == userId);
    if (userIndex == -1) return false;
    
    final user = _fans[userIndex];
    final isCurrentlyFollowing = user.isFollowing ?? false;
    
    if (isCurrentlyFollowing) {
      return await unfollowUser(userId);
    } else {
      return await followUser(userId);
    }
  }

  /// Clear error message
  void clearError() {
    _error = null;
    notifyListeners();
  }

  void updateUserFollowStatus(int userId, bool isFollowing) {
    final userIndex = _fans.indexWhere((user) => user.id == userId);
    if (userIndex != -1) {
      _fans[userIndex] = _fans[userIndex].copyWith(isFollowing: isFollowing);
      notifyListeners();
    }
  }
}