import 'package:flutter/foundation.dart';
import 'package:user_app/api/user_api.dart';
import 'package:user_app/api/user_follow_api.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/models/moment/user_fans_attentions_request.dart';
import 'package:user_app/models/user.dart';

class FollowingViewModel extends ChangeNotifier {
  final UserApi _userApi = getIt<UserApi>();
  final UserFollowApi _followApi = getIt<UserFollowApi>();
  
  List<User> _following = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;
  static const int _pageSize = 20;
  int? _currentUserId;
  String? _error;
  
  // 正在操作的用户ID集合
  final Set<int> _unfollowingUsers = {};

  List<User> get following => _following;
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  String? get error => _error;
  
  bool isUnfollowingUser(int userId) => _unfollowingUsers.contains(userId);

  Future<void> loadFollowing(int userId) async {
    if (_isLoading) return;

    _currentUserId = userId;
    _isLoading = true;
    _currentPage = 1;
    _error = null;
    notifyListeners();

    try {
      final request = UserFansAttentionsRequest(
        userId: userId,
        pageNum: _currentPage,
        pageSize: _pageSize,
      );

      final response = await _userApi.getAttentions(request);
      
      _following = response.records;
      _hasMore = _currentPage < response.pages;
      
    } catch (error) {
      debugPrint('Failed to load following: $error');
      _error = '加载关注列表失败';
      _following = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadMoreFollowing() async {
    if (_isLoading || !_hasMore || _currentUserId == null) return;

    _isLoading = true;
    notifyListeners();

    try {
      _currentPage++;
      final request = UserFansAttentionsRequest(
        userId: _currentUserId!,
        pageNum: _currentPage,
        pageSize: _pageSize,
      );

      final response = await _userApi.getAttentions(request);
      
      _following.addAll(response.records);
      _hasMore = _currentPage < response.pages;
      
    } catch (error) {
      debugPrint('Failed to load more following: $error');
      _error = '加载更多失败';
      _currentPage--; // Revert page increment on error
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refreshFollowing(int userId) async {
    _following.clear();
    _hasMore = true;
    _currentPage = 1;
    await loadFollowing(userId);
  }

  void removeUser(int userId) {
    _following.removeWhere((user) => user.id == userId);
    notifyListeners();
  }

  /// Unfollow a user
  Future<bool> unfollowUser(int userId) async {
    if (_unfollowingUsers.contains(userId)) return false;
    
    _unfollowingUsers.add(userId);
    notifyListeners();
    
    try {
      await _followApi.unfollowUser(userId);
      // Remove user from following list
      removeUser(userId);
      return true;
    } catch (error) {
      debugPrint('Failed to unfollow user: $error');
      _error = '取消关注失败，请重试';
      return false;
    } finally {
      _unfollowingUsers.remove(userId);
      notifyListeners();
    }
  }

  /// Clear error message
  void clearError() {
    _error = null;
    notifyListeners();
  }

  void updateUserFollowStatus(int userId, bool isFollowing) {
    if (!isFollowing) {
      // Remove user from following list when unfollowed
      removeUser(userId);
    }
  }
}