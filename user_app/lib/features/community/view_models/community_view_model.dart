import 'package:flutter/material.dart';
import 'package:user_app/api/bookmark_api.dart';
import 'package:user_app/api/report_api.dart';
import 'package:user_app/api/user_follow_api.dart';
import 'package:user_app/models/moment/moment_list_request.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/moment/query_moment_hot_request.dart';
import 'package:user_app/services/moment_service.dart';
import 'package:user_app/services/search_service.dart';

class CommunityViewModel extends ChangeNotifier {
  final MomentService _momentService;
  final SearchService? _searchService;
  final UserFollowApi? _userFollowApi;
  final BookmarkApi? _bookmarkApi;
  final ReportApi? _reportApi;

  CommunityViewModel(
    this._momentService, [
    this._searchService,
    this._userFollowApi,
    this._bookmarkApi,
    this._reportApi,
  ]);

  // State management
  List<MomentVo> _moments = [];
  bool _isLoading = false;
  bool _hasMore = true;
  String? _errorMessage;
  int _currentPage = 1;
  static const int _pageSize = 10;

  // Filters
  String _selectedMomentType = "全部";
  List<String> _selectedTags = [];
  String _searchQuery = "";
  String _sortBy = "latest"; // latest, hottest
  bool _showOnlyFollowing = false;

  // Filter options similar to fishing spots
  final List<String> _momentTypeFilters = [
    "全部",
    "钓获分享",
    "装备展示",
    "技巧分享",
    "问答求助"
  ];

  // Getters
  List<MomentVo> get moments => _moments;

  bool get isLoading => _isLoading;

  bool get hasMore => _hasMore;

  String? get errorMessage => _errorMessage;

  String get selectedMomentType => _selectedMomentType;

  List<String> get selectedTags => _selectedTags;

  String get searchQuery => _searchQuery;

  String get sortBy => _sortBy;

  bool get showOnlyFollowing => _showOnlyFollowing;

  List<String> get momentTypeFilters => _momentTypeFilters;

  // Convert filter type to API moment type
  String? _getApiMomentType(String filterType) {
    switch (filterType) {
      case "钓获分享":
        return "fishing_catch";
      case "装备展示":
        return "equipment";
      case "技巧分享":
        return "technique";
      case "问答求助":
        return "question";
      default:
        return null; // "全部" case
    }
  }

  void setSelectedTags(List<String> tags) {
    // Check if the list of tags has actually changed to avoid unnecessary reloads.
    if (tags.length != _selectedTags.length ||
        !tags.every((t) => _selectedTags.contains(t))) {
      _selectedTags = tags;
      loadMoments(refresh: true);
    }
  }

  Future<void> loadMoments({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _moments.clear();
    }

    if (_isLoading || !_hasMore) return;

    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      late final response;

      if (_searchQuery.isNotEmpty && _searchService != null) {
        // Use new search service for text search
        final searchResult = await _searchService.search(
          _searchQuery,
          type: 'moment', // 只搜索动态
          page: _currentPage, // 新API使用1-based分页
          size: _pageSize,
        );

        // 为了保持兼容性，我们仍然使用moment API获取数据
        // 在实际项目中，可以优化为直接使用搜索结果
        final request = MomentListRequest(
          pageNum: _currentPage,
          pageSize: _pageSize,
          momentType: _selectedMomentType != "全部"
              ? _getApiMomentType(_selectedMomentType)
              : null,
          // 注意：这里添加搜索查询支持需要后端API支持
        );

        response = await _momentService.getMoments(request);

        if (refresh) {
          _moments = response.data;
        } else {
          _moments.addAll(response.data);
        }

        _hasMore = response.hasMore;
      } else if (_showOnlyFollowing) {
        // Use following moments endpoint
        response =
            await _momentService.getFollowingMoments(_currentPage, _pageSize);

        if (refresh) {
          _moments = response.records;
        } else {
          _moments.addAll(response.records);
        }

        _hasMore = _currentPage < response.pages;
      } else if (_sortBy == "hottest") {
        // Use hot moments endpoint
        final hotRequest = QueryHotMomentDto(
          pageNum: _currentPage,
          pageSize: _pageSize,
        );
        response = await _momentService.fetchHotMoments(hotRequest);

        if (refresh) {
          _moments = response.records;
        } else {
          _moments.addAll(response.records);
        }

        _hasMore = _currentPage < response.pages;
      } else {
        // Use regular moments endpoint with filters
        final apiMomentType = _selectedMomentType != "全部"
            ? _getApiMomentType(_selectedMomentType)
            : null;

        final request = MomentListRequest(
          pageNum: _currentPage,
          pageSize: _pageSize,
          tag: _selectedTags.isNotEmpty ? _selectedTags.first : null,
          momentType: apiMomentType,
        );

        response = await _momentService.getMoments(request);

        if (refresh) {
          _moments = response.records;
        } else {
          _moments.addAll(response.records);
        }

        _hasMore = _currentPage < response.pages;
      }

      _currentPage++;
      _errorMessage = null;
    } catch (e) {
      _errorMessage = e.toString();
      debugPrint('Error loading moments: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> searchMoments(String query) async {
    _searchQuery = query;
    await loadMoments(refresh: true);
  }

  void setMomentTypeFilter(String momentType) {
    _selectedMomentType = momentType;
    loadMoments(refresh: true);
  }

  void toggleTag(String tag) {
    if (_selectedTags.contains(tag)) {
      _selectedTags.remove(tag);
    } else {
      _selectedTags.add(tag);
    }
    loadMoments(refresh: true);
  }

  void clearAllFilters() {
    _selectedMomentType = "全部";
    _selectedTags.clear();
    _searchQuery = "";
    _sortBy = "latest";
    _showOnlyFollowing = false;
    loadMoments(refresh: true);
  }

  // Sort methods
  void setSortBy(String sortBy) {
    _sortBy = sortBy;
    loadMoments(refresh: true);
  }

  // Following filter
  void setShowOnlyFollowing(bool showOnlyFollowing) {
    _showOnlyFollowing = showOnlyFollowing;
    loadMoments(refresh: true);
  }

  // Search with filters
  Future<void> searchWithFilters(String query) async {
    _searchQuery = query;
    await loadMoments(refresh: true);
  }

  // Bookmark functionality
  Future<void> toggleBookmark(int momentId) async {
    final momentIndex = _moments.indexWhere((m) => m.id == momentId);
    if (momentIndex == -1) return;

    final moment = _moments[momentIndex];
    final wasBookmarked = moment.isBookmarked ?? false;

    // Optimistic update
    final updatedMoment = moment.copyWith(
      isBookmarked: !wasBookmarked,
    );
    _moments[momentIndex] = updatedMoment;
    notifyListeners();

    try {
      // API call
      if (_bookmarkApi != null) {
        if (wasBookmarked) {
          await _bookmarkApi!.unbookmarkMoment(momentId);
        } else {
          await _bookmarkApi!.bookmarkMoment(momentId);
        }
      }
    } catch (e) {
      // Revert only this moment on error, don't reload entire list
      final revertedMoment = moment.copyWith(
        isBookmarked: wasBookmarked,
      );
      _moments[momentIndex] = revertedMoment;
      notifyListeners();
      debugPrint('Error toggling bookmark: $e');
      rethrow; // Let the UI handle the error display
    }
  }

  // Report functionality
  Future<void> reportMoment(int momentId, String reason,
      {String? description}) async {
    try {
      if (_reportApi != null) {
        await _reportApi!
            .reportMoment(momentId, reason, description: description);
      }
    } catch (e) {
      debugPrint('Error reporting moment: $e');
      rethrow;
    }
  }

  Future<List<String>> getReportReasons() async {
    try {
      if (_reportApi != null) {
        return await _reportApi!.getReportReasons();
      }
      return ['垃圾信息', '违法违规', '色情内容', '暴力内容', '虚假信息', '侵犯版权', '其他'];
    } catch (e) {
      debugPrint('Error getting report reasons: $e');
      return ['垃圾信息', '违法违规', '色情内容', '暴力内容', '虚假信息', '侵犯版权', '其他'];
    }
  }

  // Like/unlike functionality
  Future<void> toggleLikeMoment(int momentId) async {
    final momentIndex = _moments.indexWhere((m) => m.id == momentId);
    if (momentIndex == -1) return;

    final moment = _moments[momentIndex];
    final wasLiked = moment.isLiked ?? false;
    final originalLikeCount = moment.likeCount ?? 0;

    // Optimistic update
    final updatedMoment = moment.copyWith(
      isLiked: !wasLiked,
      likeCount: originalLikeCount + (wasLiked ? -1 : 1),
    );
    _moments[momentIndex] = updatedMoment;
    notifyListeners();

    try {
      // API call
      await _momentService.likeMoment(
        momentId,
        !wasLiked, // true for like, false for unlike
      );
    } catch (e) {
      // Revert only this moment on error, don't reload entire list
      final revertedMoment = moment.copyWith(
        isLiked: wasLiked,
        likeCount: originalLikeCount,
      );
      _moments[momentIndex] = revertedMoment;
      notifyListeners();
      debugPrint('Error toggling like: $e');
      rethrow; // Let the UI handle the error display
    }
  }

  // Update a specific moment in the list (useful for syncing from detail page)
  void updateMoment(MomentVo updatedMoment) {
    final momentIndex = _moments.indexWhere((m) => m.id == updatedMoment.id);
    if (momentIndex != -1) {
      _moments[momentIndex] = updatedMoment;
      notifyListeners();
    }
  }

  // Follow/unfollow user
  Future<void> toggleFollowUser(int userId) async {
    try {
      // Note: Follow functionality is no longer part of the moment model
      // This method is kept for backward compatibility but doesn't update moment data

      // API call
      if (_userFollowApi != null) {
        // For now, we'll just make the API call without updating local state
        // since follow status is no longer tracked in moments
        // TODO: Implement proper user follow state management
        await _userFollowApi!.followUser(userId);
      }
    } catch (e) {
      // Handle error
      debugPrint('Error toggling follow: $e');
    }
  }

  int getActiveFilterCount() {
    int count = 0;
    if (_selectedMomentType != "全部") count++;
    if (_selectedTags.isNotEmpty) count += _selectedTags.length;
    return count;
  }

  void reset() {
    _moments.clear();
    _currentPage = 1;
    _hasMore = true;
    _isLoading = false;
    _errorMessage = null;
    _selectedMomentType = "全部";
    _selectedTags.clear();
    _searchQuery = "";
    _sortBy = "latest";
    _showOnlyFollowing = false;
    notifyListeners();
  }
}
