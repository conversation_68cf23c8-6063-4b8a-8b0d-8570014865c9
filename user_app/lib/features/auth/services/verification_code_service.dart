import 'dart:async';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/models/session/validate_verification_code_result.dart';
import 'package:user_app/services/sms_service.dart';

/// Shared service for verification code functionality
class VerificationCodeService extends BaseViewModel {
  final SmsService smsService;
  final SharedPreferences sharedPreferences;

  Timer? _countdownTimer;
  int _countdown = 0;

  ValidateVerificationCodeResult? validateVerificationCodeResult;

  VerificationCodeService({
    required this.smsService,
    required this.sharedPreferences,
  });

  int get countdown => _countdown;

  void loadCountdownFromSharedPreferences() {
    int? savedCountdown = sharedPreferences.getInt('countdown');
    if (savedCountdown != null) {
      _countdown = savedCountdown;
      if (_countdown > 0 && _countdownTimer == null) {
        startCountdown(_countdown);
      }
    }
  }

  void startCountdown(int duration) {
    _countdown = duration;
    notifyListeners();

    if (_countdownTimer != null && _countdownTimer!.isActive) {
      return;
    }

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        _countdown--;
        notifyListeners();
        _saveCountdownToSharedPreferences();
      } else {
        _countdownTimer?.cancel();
        _clearCountdownFromSharedPreferences();
      }
    });
  }

  Future<ValidateVerificationCodeResult?> sendVerificationCode(
      BuildContext context, String phoneNumber) async {
    if (!_validatePhoneNumber(phoneNumber)) {
      // Phone number validation failed
      return null;
    }

    setBusy(true);
    try {
      validateVerificationCodeResult =
          await smsService.getVerificationCodeForRegister(phoneNumber);

      // Start the countdown for resending verification code
      startCountdown(60);
      setBusy(false);
      return validateVerificationCodeResult;
    } catch (error) {
      // Handle error sending verification code
      setBusy(false);
      return null;
    }
  }

  bool _validatePhoneNumber(String phoneNumber) {
    return RegExp(r'^1[3456789]\d{9}$').hasMatch(phoneNumber);
  }

  Future<void> _saveCountdownToSharedPreferences() async {
    await sharedPreferences.setInt('countdown', _countdown);
  }

  Future<void> _clearCountdownFromSharedPreferences() async {
    await sharedPreferences.remove('countdown');
  }

  String? validateCode(String enteredCode) {
    if (validateVerificationCodeResult == null) {
      return '验证码已过期，请重新获取';
    }

    if (validateVerificationCodeResult!.validateCode == enteredCode) {
      return null;
    } else {
      return '验证码输入错误';
    }
  }

  void resetValidateVerificationCodeResult() {
    validateVerificationCodeResult = null;
  }

  @override
  void dispose() {
    if (_countdownTimer != null) {
      _countdownTimer!.cancel();
    }
    super.dispose();
  }
}
