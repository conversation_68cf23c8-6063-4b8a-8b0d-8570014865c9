import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/auth/providers/login_view_model.dart';
import 'package:user_app/features/auth/widgets/shared/auth_animated_container.dart';
import 'package:user_app/features/auth/widgets/shared/auth_card.dart';
import 'package:user_app/features/auth/widgets/shared/auth_footer.dart';
import 'package:user_app/features/auth/widgets/shared/auth_header.dart';
import 'package:user_app/features/auth/widgets/shared/auth_input_field.dart';
import 'package:user_app/features/auth/widgets/shared/auth_scaffold.dart';

class LoginPage extends StatefulWidget {
  final String? fromPath;

  const LoginPage({super.key, this.fromPath});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  bool _isPasswordVisible = false;
  bool _isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _slideAnimation;
  final _usernameFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _fadeInAnimation = CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.1, 1.0, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<double>(begin: 50, end: 0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.7, curve: Curves.easeOutCubic),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _usernameFocusNode.dispose();
    _passwordFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final loginViewModel = context.watch<LoginViewModel>();

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Stack(
        children: [
          AuthScaffold(
            onBackPressed: () => context.go(AppRoutes.fishingSpots),
            header: AuthAnimatedContainer(
              fadeAnimation: _fadeInAnimation,
              slideAnimation: _slideAnimation,
              padding: const EdgeInsets.only(top: 40, bottom: 20),
              child: AuthHeader(
                title: '欢迎回来',
                subtitle: '登录您的账号以继续您的钓鱼之旅',
              ),
            ),
            body: AuthAnimatedContainer(
              fadeAnimation: _fadeInAnimation,
              slideAnimation: _slideAnimation,
              child: AuthCard(
                child: Form(
                  key: _formKey,
                  child: Padding(
                    padding: const EdgeInsets.all(28),
                    child:
                        _buildLoginForm(context, loginViewModel, colorScheme),
                  ),
                ),
              ),
            ),
            footer: AuthFooter(
              message: '没有账号？',
              actionText: '立即注册',
              onAction: () => context.push(AppRoutes.register),
            ),
          ),

          // 加载遮罩
          // Replace current loading overlay
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.4),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 50,
                        height: 50,
                        child: CircularProgressIndicator(
                          color: colorScheme.primary,
                          strokeWidth: 3,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        "登录中...",
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: Color(0xFF2c3e50),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoginForm(BuildContext context, LoginViewModel loginViewModel,
      ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          '登录账号',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2c3e50),
            letterSpacing: 0.5,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '请输入您的账号信息',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 24),
        AuthInputField(
          controller: loginViewModel.phoneNumberController,
          labelText: '手机号',
          hintText: '请输入11位手机号',
          prefixIcon:
              Icon(Icons.phone_android_rounded, color: colorScheme.primary),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入手机号';
            }
            if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
              return '请输入有效的手机号';
            }
            return null;
          },
          focusNode: _usernameFocusNode,
          onEditingComplete: () {
            _usernameFocusNode.unfocus();
          },
        ),
        const SizedBox(height: 20),
        AuthInputField(
          controller: loginViewModel.passwordController,
          labelText: '密码',
          hintText: '请输入登录密码',
          prefixIcon:
              Icon(Icons.lock_outline_rounded, color: colorScheme.primary),
          obscureText: !_isPasswordVisible,
          // Replace current IconButton with this
          suffixIcon: Padding(
            padding: const EdgeInsets.only(right: 4),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(20),
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Icon(
                    _isPasswordVisible
                        ? Icons.visibility_off_rounded
                        : Icons.visibility_rounded,
                    color: Colors.grey.shade500,
                    size: 22,
                  ),
                ),
              ),
            ),
          ),
          focusNode: _passwordFocusNode,
          onEditingComplete: () {
            _passwordFocusNode.unfocus();
            if (_formKey.currentState?.validate() ?? false) {
              _handleLogin(context);
            }
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入密码';
            }
            return null;
          },
        ),
        Align(
          alignment: Alignment.centerRight,
          child: TextButton(
            onPressed: () {
              context.push(AppRoutes.resetPassword);
            },
            style: TextButton.styleFrom(
              foregroundColor: colorScheme.primary,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            ),
            child: const Text(
              '忘记密码？',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        const SizedBox(height: 30),
        SizedBox(
          height: 58,
          child: ElevatedButton(
            onPressed: () => _handleLogin(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              // Match content background
              foregroundColor: colorScheme.primary,
              // Primary color for text
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: BorderSide(
                    color: colorScheme.primary,
                    width: 1.5), // Add border for definition
              ),
              padding: const EdgeInsets.symmetric(vertical: 14),
            ),
            child: const Text(
              '登录',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                letterSpacing: 1,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleLogin(BuildContext context) async {
    FocusScope.of(context).unfocus();

    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      final loginViewModel = context.read<LoginViewModel>();
      bool success = await loginViewModel.login(context);

      if (success && mounted) {
        final fromPath = widget.fromPath ??
            GoRouterState.of(context).uri.queryParameters['from'];
        context.go(fromPath ?? AppRoutes.fishingSpots);
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
