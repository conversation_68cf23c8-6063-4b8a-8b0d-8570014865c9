import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:user_app/features/auth/providers/reset_password_view_model.dart';
import 'package:user_app/features/auth/widgets/reset_password/reset_password_phone_number.dart';
import 'package:user_app/features/auth/widgets/reset_password/reset_password_set_password.dart';
import 'package:user_app/features/auth/widgets/reset_password/reset_password_verification_code.dart';
import 'package:user_app/features/auth/widgets/shared/auth_animated_container.dart';
import 'package:user_app/features/auth/widgets/shared/auth_card.dart';
import 'package:user_app/features/auth/widgets/shared/auth_header.dart';
import 'package:user_app/features/auth/widgets/shared/auth_scaffold.dart';

class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({super.key});

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _fadeInAnimation = CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.1, 0.9, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<double>(begin: 50, end: 0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final resetPasswordViewModel = context.read<ResetPasswordViewModel>();
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: AuthScaffold(
        header: AuthAnimatedContainer(
          fadeAnimation: _fadeInAnimation,
          slideAnimation: _slideAnimation,
          child: const AuthHeader(
            title: '重置密码',
            subtitle: '通过手机号重新设置您的账号密码',
          ),
        ),
        body: Column(
          children: [
            // 卡片容器与表单步骤
            AuthAnimatedContainer(
              fadeAnimation: _fadeInAnimation,
              slideAnimation: _slideAnimation,
              scaleAnimation: _scaleAnimation,
              child: AuthCard(
                height: 415,
                child: Column(
                  children: [
                    Expanded(
                      child: PageView(
                        controller: resetPasswordViewModel.pageController,
                        physics: const NeverScrollableScrollPhysics(),
                        children: [
                          // 使用RepaintBoundary优化性能
                          RepaintBoundary(
                            child: ResetPasswordPhoneNumber(),
                          ),
                          RepaintBoundary(
                            child: ResetPasswordVerificationCode(),
                          ),
                          RepaintBoundary(
                            child: ResetPasswordSetPassword(),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 20.0),
                      child: SmoothPageIndicator(
                        controller: resetPasswordViewModel.pageController,
                        count: 3,
                        effect: ExpandingDotsEffect(
                          dotWidth: 8,
                          dotHeight: 8,
                          activeDotColor: colorScheme.primary,
                          dotColor: Colors.grey.shade300,
                          spacing: 8,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
