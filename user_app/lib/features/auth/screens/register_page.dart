import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:user_app/features/auth/providers/register_view_model.dart';
import 'package:user_app/features/auth/widgets/register/flat_register_phone_number.dart';
import 'package:user_app/features/auth/widgets/register/flat_register_set_password.dart';
import 'package:user_app/features/auth/widgets/register/flat_register_verification_code.dart';
import 'package:user_app/features/auth/widgets/shared/auth_animated_container.dart';
import 'package:user_app/features/auth/widgets/shared/auth_card.dart';
import 'package:user_app/features/auth/widgets/shared/auth_footer.dart';
import 'package:user_app/features/auth/widgets/shared/auth_header.dart';
import 'package:user_app/features/auth/widgets/shared/auth_scaffold.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeInAnimation = CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<double>(begin: 30, end: 0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
      ),
    );

    _animationController.forward();

    // Listen for page changes to provide haptic feedback
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final registerViewModel = context.read<RegisterViewModel>();
      registerViewModel.addPageChangeListener(_onPageChanged);
    });
  }

  void _onPageChanged() {
    // Provide haptic feedback when changing pages
    HapticFeedback.mediumImpact();
  }

  @override
  void dispose() {
    final registerViewModel = context.read<RegisterViewModel>();
    registerViewModel.removePageChangeListener(_onPageChanged);
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final registerViewModel = context.read<RegisterViewModel>();
    final colorScheme = Theme.of(context).colorScheme;

    return WillPopScope(
      onWillPop: () async {
        // Handle back button press
        if (registerViewModel.currentPage > 0) {
          registerViewModel.goToPreviousPage();
          return false;
        }
        return true;
      },
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: AuthScaffold(
          useDefaultBackButton: false,
          backButton: registerViewModel.currentPage > 0
              ? IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: Colors.white,
                    size: 22,
                  ),
                  onPressed: () => registerViewModel.goToPreviousPage(),
                )
              : IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: Colors.white,
                    size: 22,
                  ),
                  onPressed: () => context.pop(),
                ),
          header: AuthAnimatedContainer(
            fadeAnimation: _fadeInAnimation,
            slideAnimation: _slideAnimation,
            padding: const EdgeInsets.only(top: 40, bottom: 16),
            child: const AuthHeader(
              title: '创建账号',
              subtitle: '注册一个新账号，开启您的钓鱼之旅',
            ),
          ),
          body: Column(
            children: [
              // Card container with registration steps
              AuthAnimatedContainer(
                fadeAnimation: _fadeInAnimation,
                slideAnimation: _slideAnimation,
                child: AuthCard(
                  height: 390, // Fixed height to reduce layout calculations
                  child: PageView(
                    controller: registerViewModel.pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    onPageChanged: (index) {
                      registerViewModel.setCurrentPage(index);
                    },
                    children: const [
                      // Using RepaintBoundary for performance optimization
                      RepaintBoundary(child: FlatRegisterPhoneNumber()),
                      RepaintBoundary(child: FlatRegisterVerificationCode()),
                      RepaintBoundary(child: FlatRegisterSetPassword()),
                    ],
                  ),
                ),
              ),

              // Page indicator
              Padding(
                padding: const EdgeInsets.only(bottom: 20.0, top: 20.0),
                child: SmoothPageIndicator(
                  controller: registerViewModel.pageController,
                  count: 3,
                  effect: ExpandingDotsEffect(
                    dotWidth: 8,
                    dotHeight: 8,
                    activeDotColor: colorScheme.primary,
                    dotColor: Colors.grey.shade300,
                    spacing: 8,
                    expansionFactor:
                        2.5, // A bit more expansion for better visibility
                  ),
                ),
              ),
            ],
          ),
          footer: AuthFooter(
            message: '已有账号？',
            actionText: '立即登录',
            onAction: () => context.pop(),
          ),
        ),
      ),
    );
  }
}
