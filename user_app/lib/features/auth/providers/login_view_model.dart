import 'package:flutter/material.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/features/auth/providers/base_auth_view_model.dart';
import 'package:user_app/models/session/login_response.dart';
import 'package:user_app/services/session_service.dart';

class LoginViewModel extends BaseViewModel {
  final BaseAuthViewModel baseAuthViewModel;
  final SessionService sessionService;

  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  LoginViewModel({
    required this.baseAuthViewModel,
    required this.sessionService,
  });

  Future<bool> login(BuildContext context) async {
    setBusy(true);
    final phoneNumber = phoneNumberController.text;
    final password = passwordController.text;

    if (!_validateInputs(context, phoneNumber, password)) {
      setBusy(false);
      return false;
    }

    LoginResponse loginResponse =
        await sessionService.login(phoneNumber, password);

    if (loginResponse.token == null || loginResponse.user == null) {
      _showError(context, loginResponse.message ?? '登录失败');
      setBusy(false);
      return false;
    }

    await baseAuthViewModel.storeUserAndToken(
        loginResponse.user!, loginResponse.token!);

    setBusy(false);
    return true;
  }

  bool _validateInputs(
      BuildContext context, String phoneNumber, String password) {
    if (phoneNumber.isEmpty) {
      _showError(context, '请输入手机号');
      return false;
    }

    if (!RegExp(r'^1[3456789]\d{9}$').hasMatch(phoneNumber)) {
      _showError(context, '请输入正确的手机号');
      return false;
    }

    if (password.isEmpty) {
      _showError(context, '请输入密码');
      return false;
    }

    return true;
  }

  void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text(message),
      backgroundColor: Colors.redAccent,
    ));
  }

  @override
  void dispose() {
    phoneNumberController.dispose();
    passwordController.dispose();
    super.dispose();
  }
}
