import 'package:flutter/material.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/features/auth/providers/base_auth_view_model.dart';
import 'package:user_app/models/statistics/statistics.dart';
import 'package:user_app/services/statistics_service.dart';

class ProfileInfoItem {
  final String title;
  final int value;
  final Function()? onTap;

  const ProfileInfoItem(this.title, this.value, {this.onTap});
}

class UserProfileViewModel extends BaseViewModel {
  final BaseAuthViewModel baseAuthViewModel;
  final StatisticsService statisticsService;

  Statistics statisticsInfo = Statistics(
    momentCount: 0,
    fansCount: 0,
    followCount: 0,
  );

  List<ProfileInfoItem> profileInfoItems = [
    const ProfileInfoItem('动态', 0),
    const ProfileInfoItem('粉丝', 0),
    const ProfileInfoItem('关注', 0),
  ];

  UserProfileViewModel({
    required this.baseAuthViewModel,
    required this.statisticsService,
  });

  Future<void> fetchStatistics(BuildContext context) async {
    setBusy(true);
    try {
      statisticsInfo = await statisticsService.fetchStatistics();

      final userId = baseAuthViewModel.getCurrentUserId();

      // Update profile info items
      updateProfileInfoItems(context, userId);

      setBusy(false);
    } catch (error) {
      setBusy(false);
      // Handle error fetching statistics
    }
  }

  void updateProfileInfoItems(BuildContext context, int userId) {
    // Implementation for updating profile info items with navigation actions
  }
}
