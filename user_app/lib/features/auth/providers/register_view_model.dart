import 'package:flutter/material.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/features/auth/providers/base_auth_view_model.dart';
import 'package:user_app/features/auth/services/verification_code_service.dart';
import 'package:user_app/models/session/login_response.dart';
import 'package:user_app/services/session_service.dart';

class RegisterViewModel extends BaseViewModel {
  final BaseAuthViewModel baseAuthViewModel;
  final SessionService sessionService;
  final VerificationCodeService verificationCodeService;

  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController verificationCodeController =
      TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  final PageController pageController = PageController();

  // Current page tracker for back button functionality
  int _currentPage = 0;

  int get currentPage => _currentPage;

  // Page change listeners for haptic feedback or other effects
  final List<VoidCallback> _pageChangeListeners = [];

  RegisterViewModel({
    required this.baseAuthViewModel,
    required this.sessionService,
    required this.verificationCodeService,
  }) {
    verificationCodeService.addListener(_forwardVerificationCodeChanges);
  }

  void _forwardVerificationCodeChanges() {
    notifyListeners();
  }

  void setCurrentPage(int page) {
    _currentPage = page;
    notifyListeners();
  }

  void addPageChangeListener(VoidCallback listener) {
    _pageChangeListeners.add(listener);
  }

  void removePageChangeListener(VoidCallback listener) {
    _pageChangeListeners.remove(listener);
  }

  void _notifyPageChange() {
    for (final listener in _pageChangeListeners) {
      listener();
    }
  }

  // Go to the next page with enhanced animation
  Future<void> goToNextPage() async {
    if (currentPage < 2) {
      // We have 3 pages (0, 1, 2)
      await pageController.animateToPage(
        currentPage + 1,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOutCubic,
      );
      _notifyPageChange();
    }
  }

  // Go to the previous page with enhanced animation
  Future<void> goToPreviousPage() async {
    if (currentPage > 0) {
      await pageController.animateToPage(
        currentPage - 1,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOutCubic,
      );
      _notifyPageChange();
    }
  }

  int get countdown => verificationCodeService.countdown;

  Future<void> sendVerificationCode(BuildContext context,
      {bool isResend = false}) async {
    if (!_validatePhoneNumber()) {
      _showError(context, '请输入正确的手机号');
      return;
    }

    setBusy(true);
    try {
      await verificationCodeService.sendVerificationCode(
          context, phoneNumberController.text);

      // 只有在初次发送验证码时才跳转页面，重新发送时不跳转
      if (!isResend) {
        await goToNextPage();
      }
    } catch (error) {
      _showError(context, '发送验证码失败: ${error.toString()}');
    } finally {
      setBusy(false);
    }
  }

  // 只返回验证结果，不显示SnackBar
  String? validateVerificationCodeOnly(String code) {
    return verificationCodeService.validateCode(code);
  }

  // 显示验证结果的SnackBar（应在构建阶段之外调用）
  void showValidationResultSnackBar(
      BuildContext context, String? validationResult) {
    if (validationResult == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('验证码验证成功'),
          behavior: SnackBarBehavior.floating,
          duration: Duration(seconds: 1),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('验证码验证失败'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  // 这个方法现在已弃用，应使用上面的两个方法代替
  @Deprecated('Use validateVerificationCodeOnly instead')
  String? validateVerificationCode(BuildContext context) {
    final result =
        verificationCodeService.validateCode(verificationCodeController.text);

    if (result == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('验证码验证成功'),
          behavior: SnackBarBehavior.floating,
          duration: Duration(seconds: 1),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('验证码验证失败'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    return result;
  }

  // Proceed to password page after verification
  Future<void> proceedToPasswordPage() async {
    await goToNextPage();
  }

  Future<bool> register(BuildContext context) async {
    final phoneNumber = phoneNumberController.text;
    final verificationCode = verificationCodeController.text;
    final password = passwordController.text;
    final confirmPassword = confirmPasswordController.text;

    if (!_validateInputs(
        context, phoneNumber, verificationCode, password, confirmPassword)) {
      return false;
    }

    try {
      setBusy(true);
      LoginResponse response = await sessionService.register(
        phoneNumber,
        verificationCode,
        password,
        confirmPassword,
      );

      // Store user data and token
      await baseAuthViewModel.storeUserAndToken(
          response.user!, response.token!);

      setBusy(false);
      return true;
    } catch (error) {
      // Handle registration error
      _handleRegistrationError(context, error);
      setBusy(false);
      return false;
    }
  }

  bool _validatePhoneNumber() {
    return RegExp(r'^1[3456789]\d{9}$').hasMatch(phoneNumberController.text);
  }

  bool _validateInputs(BuildContext context, String phoneNumber,
      String verificationCode, String password, String confirmPassword) {
    // Validate phone number
    if (phoneNumber.isEmpty ||
        !RegExp(r'^1[3456789]\d{9}$').hasMatch(phoneNumber)) {
      _showError(context, '请输入正确的手机号');
      return false;
    }

    // Validate verification code
    if (verificationCode.isEmpty) {
      _showError(context, '请输入验证码');
      return false;
    }

    // Validate password
    if (password.isEmpty) {
      _showError(context, '请输入密码');
      return false;
    }

    // Validate confirm password
    if (confirmPassword.isEmpty) {
      _showError(context, '请输入确认密码');
      return false;
    }

    // Validate passwords match
    if (password != confirmPassword) {
      _showError(context, '两次输入的密码不一致');
      return false;
    }

    return true;
  }

  void _handleRegistrationError(BuildContext context, dynamic error) {
    String errorMessage = '注册失败';

    if (error.toString().contains('验证码错误')) {
      errorMessage = '验证码错误，请重新输入';
    } else if (error.toString().contains('该手机号已注册')) {
      errorMessage = '手机号已注册';
    } else {
      errorMessage = '注册失败: ${error.toString()}';
    }

    _showError(context, errorMessage);
  }

  void _showError(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('提示'),
          content: Text(message),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void clearPhoneNumber() {
    phoneNumberController.clear();
    notifyListeners();
  }

  @override
  void dispose() {
    verificationCodeService.removeListener(_forwardVerificationCodeChanges);
    phoneNumberController.dispose();
    verificationCodeController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    pageController.dispose();

    _pageChangeListeners.clear();
    super.dispose();
  }
}
