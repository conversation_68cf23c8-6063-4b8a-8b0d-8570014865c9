import 'dart:async';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/services/user_service.dart';
import 'package:user_app/view_models/chat_view_model.dart';

class BaseAuthViewModel extends BaseViewModel {
  final UserService userService;
  final SharedPreferences sharedPreferences;

  // Stream for notifying auth state changes
  final StreamController<void> _authStateChangedController =
      StreamController<void>.broadcast();

  User? _currentUser;

  BaseAuthViewModel({
    required this.userService,
    required this.sharedPreferences,
  });

  User? get currentUser => _currentUser;

  Stream<void> get authStateChanged => _authStateChangedController.stream;

  bool isUserLoggedIn() {
    return userService.getAccessToken() != null;
  }

  int getCurrentUserId() {
    _currentUser = userService.getCachedUser();
    return _currentUser?.id ?? 0;
  }

  Future<void> getCurrentUser() async {
    setBusy(true);
    try {
      _currentUser = await userService.getUserProfile(getCurrentUserId());
      await userService.cacheUser(_currentUser!);
    } catch (error) {
      // Handle error, possibly logging out user if unauthorized
      if (error.toString().contains('401')) {
        await logout();
      }
    }
    setBusy(false);
  }

  Future<void> logout() async {
    setBusy(true);
    
    // 登出时清理IM状态
    await _cleanupIM();
    
    await userService.logout();
    _currentUser = null;
    _notifyAuthStateChanged();
    setBusy(false);
  }

  Future<void> storeUserAndToken(User user, String token) async {
    await userService.cacheUser(user);
    await userService.cacheToken(token);
    _currentUser = user;
    _notifyAuthStateChanged();
    
    // 登录成功后自动初始化IM
    await _initializeIM();
  }

  void _notifyAuthStateChanged() {
    _authStateChangedController.add(null);
  }

  bool checkAndPromptLogin(BuildContext context) {
    if (!isUserLoggedIn()) {
      showLoginPrompt(context);
      return false;
    }
    return true;
  }

  void showLoginPrompt(BuildContext context) {
    // Implementation of login prompt dialog
    // This can be moved to a separate UI utility class
  }

  /// 初始化IM
  Future<void> _initializeIM() async {
    try {
      debugPrint('🚀 [BaseAuthViewModel] 开始初始化IM...');
      final chatViewModel = getIt<ChatViewModel>();
      
      if (!chatViewModel.isInitialized) {
        await chatViewModel.initialize();
        if (chatViewModel.isInitialized) {
          debugPrint('✅ [BaseAuthViewModel] IM初始化成功');
        } else {
          debugPrint('❌ [BaseAuthViewModel] IM初始化失败: ${chatViewModel.error}');
        }
      } else {
        debugPrint('📝 [BaseAuthViewModel] IM已经初始化');
      }
    } catch (e) {
      debugPrint('❌ [BaseAuthViewModel] IM初始化异常: $e');
    }
  }
  
  /// 清理IM状态
  Future<void> _cleanupIM() async {
    try {
      debugPrint('🧹 [BaseAuthViewModel] 清理IM状态...');
      final chatViewModel = getIt<ChatViewModel>();
      await chatViewModel.logout();
      debugPrint('✅ [BaseAuthViewModel] IM状态清理完成');
    } catch (e) {
      debugPrint('❌ [BaseAuthViewModel] 清理IM状态异常: $e');
    }
  }

  @override
  void dispose() {
    _authStateChangedController.close();
    super.dispose();
  }
}
