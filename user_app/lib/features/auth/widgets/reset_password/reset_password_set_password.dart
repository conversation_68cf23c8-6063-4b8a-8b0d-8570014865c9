import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/auth/providers/reset_password_view_model.dart';
import 'package:user_app/features/auth/widgets/shared/auth_input_field.dart';

class ResetPasswordSetPassword extends StatefulWidget {
  const ResetPasswordSetPassword({super.key});

  @override
  State<ResetPasswordSetPassword> createState() =>
      _ResetPasswordSetPasswordState();
}

class _ResetPasswordSetPasswordState extends State<ResetPasswordSetPassword> {
  FocusNode setPasswordFocusNode = FocusNode();
  FocusNode confirmPasswordFocusNode = FocusNode();
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  @override
  void dispose() {
    setPasswordFocusNode.dispose();
    confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final resetPasswordViewModel = context.watch<ResetPasswordViewModel>();
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      onTap: () {
        setPasswordFocusNode.unfocus();
        confirmPasswordFocusNode.unfocus();
      },
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '设置密码',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2c3e50),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '创建一个安全的密码，长度为6-16位',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),

            // Password field using AuthInputField
            AuthInputField(
              controller: resetPasswordViewModel.passwordController,
              labelText: '密码',
              hintText: '请输入6-16位密码',
              obscureText: !_isPasswordVisible,
              focusNode: setPasswordFocusNode,
              prefixIcon:
                  const Icon(Icons.lock_outline, color: Color(0xFF3a7bd5)),
              suffixIcon: IconButton(
                icon: Icon(
                  _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                  color: Colors.grey.shade500,
                  size: 20,
                ),
                onPressed: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入密码';
                }
                if (value.length < 6 || value.length > 16) {
                  return '密码长度应为6-16位';
                }
                return null;
              },
            ),

            const SizedBox(height: 20),

            // Confirm password field using AuthInputField
            AuthInputField(
              controller: resetPasswordViewModel.confirmPasswordController,
              labelText: '确认密码',
              hintText: '请再次输入密码',
              obscureText: !_isConfirmPasswordVisible,
              focusNode: confirmPasswordFocusNode,
              prefixIcon:
                  const Icon(Icons.lock_outline, color: Color(0xFF3a7bd5)),
              suffixIcon: IconButton(
                icon: Icon(
                  _isConfirmPasswordVisible
                      ? Icons.visibility_off
                      : Icons.visibility,
                  color: Colors.grey.shade500,
                  size: 20,
                ),
                onPressed: () {
                  setState(() {
                    _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                  });
                },
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请再次输入密码';
                }
                if (value != resetPasswordViewModel.passwordController.text) {
                  return '两次输入的密码不一致';
                }
                return null;
              },
            ),

            const SizedBox(height: 24),

            SizedBox(
              height: 56,
              width: double.infinity,
              child: ElevatedButton(
                onPressed: resetPasswordViewModel.busy
                    ? null
                    : () async {
                        final success =
                            await resetPasswordViewModel.resetPassword(context);
                        if (success && context.mounted) {
                          context.go(AppRoutes.fishingSpots);
                        }
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: Colors.white,
                  disabledBackgroundColor: Colors.grey.shade400,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: resetPasswordViewModel.busy
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(
                        '完成',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
