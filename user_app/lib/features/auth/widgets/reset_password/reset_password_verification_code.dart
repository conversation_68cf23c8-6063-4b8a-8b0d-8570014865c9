import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/auth/providers/reset_password_view_model.dart';

class ResetPasswordVerificationCode extends StatefulWidget {
  const ResetPasswordVerificationCode({super.key});

  @override
  State<ResetPasswordVerificationCode> createState() =>
      _ResetPasswordVerificationCodeState();
}

class _ResetPasswordVerificationCodeState
    extends State<ResetPasswordVerificationCode> {
  FocusNode verificationCodeFocusNode = FocusNode();

  @override
  void dispose() {
    verificationCodeFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final resetPasswordViewModel = context.watch<ResetPasswordViewModel>();

    final defaultPinTheme = PinTheme(
      width: 56,
      height: 60,
      textStyle: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
      ),
    );

    final cursor = Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: 21,
        height: 2,
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: colorScheme.primary,
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );

    return GestureDetector(
      onTap: () {
        verificationCodeFocusNode.unfocus();
      },
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '验证码',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2c3e50),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '请输入发送到您手机的 4 位验证码',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),
            Center(
              child: Pinput(
                length: 4,
                controller: resetPasswordViewModel.verificationCodeController,
                focusNode: verificationCodeFocusNode,
                defaultPinTheme: defaultPinTheme,
                submittedPinTheme: defaultPinTheme.copyWith(
                  decoration: defaultPinTheme.decoration!.copyWith(
                    border: Border.all(color: colorScheme.primary, width: 2),
                  ),
                ),
                focusedPinTheme: defaultPinTheme.copyWith(
                  decoration: defaultPinTheme.decoration!.copyWith(
                    border: Border.all(color: colorScheme.primary, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: colorScheme.primary.withOpacity(0.2),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                ),
                cursor: cursor,
                onCompleted: (value) {
                  final validationResult =
                      resetPasswordViewModel.validateVerificationCode(context);
                  if (validationResult == null) {
                    // If validation is successful (returns null), proceed to the next page
                    Future.delayed(const Duration(milliseconds: 300), () {
                      resetPasswordViewModel.pageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeIn,
                      );
                    });
                  }
                },
                pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                showCursor: true,
              ),
            ),
            if (resetPasswordViewModel.phoneNumberController.text.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 24.0),
                child: Center(
                  child: Column(
                    children: [
                      Text(
                        '验证码已发送至',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '+86 ${resetPasswordViewModel.phoneNumberController.text}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Color(0xFF2c3e50),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton.icon(
                  onPressed: resetPasswordViewModel.busy ||
                          resetPasswordViewModel.countdown > 0
                      ? null
                      : () async => await resetPasswordViewModel
                          .sendVerificationCode(context),
                  icon: Icon(
                    Icons.refresh_rounded,
                    size: 18,
                    color: resetPasswordViewModel.busy ||
                            resetPasswordViewModel.countdown > 0
                        ? Colors.grey
                        : colorScheme.primary,
                  ),
                  label: Text(
                    resetPasswordViewModel.countdown > 0
                        ? '${resetPasswordViewModel.countdown}秒后重新发送'
                        : '重新发送',
                    style: TextStyle(
                      color: resetPasswordViewModel.busy ||
                              resetPasswordViewModel.countdown > 0
                          ? Colors.grey
                          : colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
