import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/auth/providers/reset_password_view_model.dart';
import 'package:user_app/features/auth/widgets/shared/auth_input_field.dart';

class ResetPasswordPhoneNumber extends StatefulWidget {
  const ResetPasswordPhoneNumber({super.key});

  @override
  State<ResetPasswordPhoneNumber> createState() =>
      _ResetPasswordPhoneNumberState();
}

class _ResetPasswordPhoneNumberState extends State<ResetPasswordPhoneNumber> {
  final FocusNode _phoneNumberFocusNode = FocusNode();

  @override
  void dispose() {
    _phoneNumberFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final resetPasswordViewModel = context.watch<ResetPasswordViewModel>();
    final verificationCodeService =
        resetPasswordViewModel.verificationCodeService;
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      onTap: () {
        _phoneNumberFocusNode.unfocus();
      },
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '手机号码',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2c3e50),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '请输入您注册账号时使用的手机号，我们将发送验证码到您的手机',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),

            // 使用共享输入字段组件
            AuthInputField(
              controller: resetPasswordViewModel.phoneNumberController,
              labelText: '手机号',
              hintText: '请输入11位手机号',
              keyboardType: TextInputType.phone,
              focusNode: _phoneNumberFocusNode,
              prefixIcon: Icon(Icons.phone_android, color: colorScheme.primary),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入手机号';
                }
                if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
                  return '请输入有效的手机号';
                }
                return null;
              },
            ),

            const Spacer(),
            SizedBox(
              height: 56,
              width: double.infinity,
              child: ElevatedButton(
                onPressed: resetPasswordViewModel.busy ||
                        verificationCodeService.countdown > 0
                    ? null
                    : () async {
                        await resetPasswordViewModel
                            .sendVerificationCode(context);
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: Colors.white,
                  disabledBackgroundColor: Colors.grey.shade400,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Text(
                  verificationCodeService.countdown > 0
                      ? '${verificationCodeService.countdown}秒后重新发送'
                      : '获取验证码',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
