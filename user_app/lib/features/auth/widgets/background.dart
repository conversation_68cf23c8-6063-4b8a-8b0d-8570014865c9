import 'package:flutter/material.dart';
import 'package:user_app/app_theme.dart';

class Background extends StatelessWidget {
  final BoxDecoration topBubbleDecoration;
  final BoxDecoration bottomBubbleDecoration;

  const Background({
    super.key,
    required this.topBubbleDecoration,
    required this.bottomBubbleDecoration,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.primaryGradientStart,
                AppColors.primaryGradientEnd,
              ],
            ),
          ),
        ),
        Positioned(
          top: -80,
          right: -80,
          child: Container(
            width: 200,
            height: 200,
            decoration: topBubbleDecoration,
          ),
        ),
        Positioned(
          bottom: -100,
          left: -70,
          child: Container(
            width: 220,
            height: 220,
            decoration: bottomBubbleDecoration,
          ),
        ),
      ],
    );
  }
}
