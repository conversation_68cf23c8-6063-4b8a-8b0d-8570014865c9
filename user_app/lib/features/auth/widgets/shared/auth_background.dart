import 'package:flutter/material.dart';
import 'package:user_app/app_theme.dart';

class AuthBackground extends StatelessWidget {
  final BoxDecoration? topBubbleDecoration;
  final BoxDecoration? bottomBubbleDecoration;
  final List<Color> gradientColors;

  const AuthBackground({
    super.key,
    this.topBubbleDecoration,
    this.bottomBubbleDecoration,
    this.gradientColors = const [
      AppColors.primaryGradientStart,
      AppColors.primaryGradientEnd,
    ],
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: gradientColors,
            ),
          ),
        ),
        if (topBubbleDecoration != null)
          Positioned(
            top: -80,
            right: -80,
            child: Container(
              width: 200,
              height: 200,
              decoration: topBubbleDecoration ??
                  BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white12,
                  ),
            ),
          ),
        if (bottomBubbleDecoration != null)
          Positioned(
            bottom: -100,
            left: -70,
            child: Container(
              width: 220,
              height: 220,
              decoration: bottomBubbleDecoration ??
                  BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white12,
                  ),
            ),
          ),
      ],
    );
  }
}
