import 'package:flutter/material.dart';

class AuthCard extends StatelessWidget {
  final Widget child;
  final double? height;
  final double? minHeight;
  final double? maxHeight;
  final EdgeInsetsGeometry margin;
  final BorderRadius borderRadius;

  const AuthCard({
    super.key,
    required this.child,
    this.height,
    this.minHeight,
    this.maxHeight,
    this.margin = const EdgeInsets.symmetric(horizontal: 24.0),
    this.borderRadius = const BorderRadius.all(Radius.circular(24)),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      constraints: BoxConstraints(
        minHeight: minHeight ?? 0,
        maxHeight: maxHeight ?? double.infinity,
      ),
      margin: margin,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: borderRadius,
        child: child,
      ),
    );
  }
}
