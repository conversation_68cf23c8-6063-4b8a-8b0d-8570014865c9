import 'package:flutter/material.dart';

class AuthAnimatedContainer extends StatelessWidget {
  final Animation<double> fadeAnimation;
  final Animation<double> slideAnimation;
  final Animation<double>? scaleAnimation;
  final Widget child;
  final EdgeInsetsGeometry padding;

  const AuthAnimatedContainer({
    super.key,
    required this.fadeAnimation,
    required this.slideAnimation,
    this.scaleAnimation,
    required this.child,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: slideAnimation,
      builder: (context, child) {
        Widget result = Transform.translate(
          offset: Offset(0, slideAnimation.value),
          child: FadeTransition(
            opacity: fadeAnimation,
            child: child,
          ),
        );

        if (scaleAnimation != null) {
          result = Transform.scale(
            scale: scaleAnimation!.value,
            child: result,
          );
        }

        return Padding(padding: padding, child: result);
      },
      child: child,
    );
  }
}
