import 'package:flutter/material.dart';
import 'package:user_app/features/auth/widgets/shared/auth_background.dart';

class AuthScaffold extends StatelessWidget {
  final Widget? header;
  final Widget? body;
  final Widget? footer;
  final Widget? backButton;
  final List<Color> gradientColors;
  final BoxDecoration? topBubbleDecoration;
  final BoxDecoration? bottomBubbleDecoration;
  final bool useDefaultBackButton;
  final VoidCallback? onBackPressed;

  const AuthScaffold({
    super.key,
    this.header,
    this.body,
    this.footer,
    this.backButton,
    this.gradientColors = const [
      Colors.blue,
      Colors.blueAccent,
    ],
    this.topBubbleDecoration,
    this.bottomBubbleDecoration,
    this.useDefaultBackButton = true,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          AuthBackground(
            gradientColors: gradientColors,
            topBubbleDecoration: topBubbleDecoration ??
                BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white12,
                ),
            bottomBubbleDecoration: bottomBubbleDecoration ??
                BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white12,
                ),
          ),

          // 主内容
          SafeArea(
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: Column(
                children: [
                  if (header != null) header!,
                  if (body != null) body!,
                  if (footer != null) footer!,
                  // SizedBox(
                  //   height: MediaQuery.of(context).padding.bottom > 0
                  //       ? MediaQuery.of(context).padding.bottom + 20
                  //       : 30,
                  // ),
                ],
              ),
            ),
          ),

          if (useDefaultBackButton)
            Positioned(
              top: MediaQuery.of(context).padding.top + 10,
              left: 16,
              child: backButton ??
                  IconButton(
                    icon: const Icon(
                      Icons.arrow_back_ios,
                      color: Colors.white,
                      size: 22,
                    ),
                    onPressed: onBackPressed ?? () => Navigator.pop(context),
                  ),
            ),
        ],
      ),
    );
  }
}
