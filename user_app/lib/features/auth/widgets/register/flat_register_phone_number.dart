import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/auth/providers/register_view_model.dart';
import 'package:user_app/features/auth/widgets/shared/auth_input_field.dart';

class FlatRegisterPhoneNumber extends StatefulWidget {
  const FlatRegisterPhoneNumber({super.key});

  @override
  State<FlatRegisterPhoneNumber> createState() =>
      _FlatRegisterPhoneNumberState();
}

class _FlatRegisterPhoneNumberState extends State<FlatRegisterPhoneNumber> {
  FocusNode phoneNumberFocusNode = FocusNode();

  @override
  void dispose() {
    phoneNumberFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final registerViewModel = context.watch<RegisterViewModel>();
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      onTap: () {
        phoneNumberFocusNode.unfocus();
      },
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '手机号码',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2c3e50),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '请输入您的手机号，我们将发送验证码',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),
            AuthInputField(
              controller: registerViewModel.phoneNumberController,
              labelText: '手机号',
              hintText: '请输入11位手机号',
              keyboardType: TextInputType.phone,
              focusNode: phoneNumberFocusNode,
              prefixIcon: Icon(Icons.phone_android, color: colorScheme.primary),
              suffixIcon:
                  registerViewModel.phoneNumberController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.grey),
                          onPressed: () {
                            registerViewModel.phoneNumberController.clear();
                            setState(() {});
                          },
                        )
                      : null,
            ),
            const Spacer(),
            SizedBox(
              height: 56,
              width: double.infinity,
              child: ElevatedButton(
                onPressed: registerViewModel.busy ||
                        registerViewModel.countdown > 0
                    ? null
                    : () async {
                        await registerViewModel.sendVerificationCode(context);
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: Colors.white,
                  disabledBackgroundColor: Colors.grey.shade400,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Text(
                  registerViewModel.countdown > 0
                      ? '${registerViewModel.countdown}秒后重新发送'
                      : '获取验证码',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
