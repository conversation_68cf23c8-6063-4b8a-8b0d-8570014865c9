import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pinput/pinput.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/auth/providers/register_view_model.dart';

class FlatRegisterVerificationCode extends StatefulWidget {
  const FlatRegisterVerificationCode({super.key});

  @override
  State<FlatRegisterVerificationCode> createState() =>
      _FlatRegisterVerificationCodeState();
}

class _FlatRegisterVerificationCodeState
    extends State<FlatRegisterVerificationCode>
    with SingleTickerProviderStateMixin {
  FocusNode verificationCodeFocusNode = FocusNode();
  late AnimationController _shakeController;
  late Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();

    // Setup shake animation for invalid code
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _shakeAnimation = Tween<double>(begin: 0.0, end: 10.0)
        .chain(CurveTween(curve: Curves.elasticIn))
        .animate(_shakeController)
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _shakeController.reverse();
        }
      });

    // Auto-focus the pin input when the widget is first built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      verificationCodeFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    verificationCodeFocusNode.dispose();
    _shakeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final registerViewModel = context.watch<RegisterViewModel>();

    final defaultPinTheme = PinTheme(
      width: 56,
      height: 60,
      textStyle: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration!.copyWith(
        border: Border.all(color: colorScheme.primary, width: 2),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withOpacity(0.2),
            blurRadius: 8,
            spreadRadius: 2,
          ),
        ],
      ),
    );

    final errorPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration!.copyWith(
        border: Border.all(color: Colors.redAccent, width: 2),
      ),
    );

    final cursor = Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: 21,
        height: 2,
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: colorScheme.primary,
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );

    return GestureDetector(
      onTap: () {
        verificationCodeFocusNode.unfocus();
      },
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '验证码',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2c3e50),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '请输入发送到您手机的 4 位验证码',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),
            Center(
              child: AnimatedBuilder(
                animation: _shakeAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(_shakeAnimation.value, 0),
                    child: child,
                  );
                },
                child: Pinput(
                  length: 4,
                  controller: registerViewModel.verificationCodeController,
                  focusNode: verificationCodeFocusNode,
                  defaultPinTheme: defaultPinTheme,
                  submittedPinTheme: defaultPinTheme.copyWith(
                    decoration: defaultPinTheme.decoration!.copyWith(
                      border: Border.all(color: colorScheme.primary, width: 2),
                    ),
                  ),
                  focusedPinTheme: focusedPinTheme,
                  errorPinTheme: errorPinTheme,
                  cursor: cursor,
                  onChanged: (value) {
                    // Provide subtle haptic feedback as each digit is entered
                    if (value.isNotEmpty) {
                      HapticFeedback.selectionClick();
                    }
                  },
                  onCompleted: (value) async {
                    // Provide slightly stronger haptic feedback when all digits are entered
                    HapticFeedback.lightImpact();

                    // 只检查验证结果，不显示SnackBar
                    final validationResult =
                        registerViewModel.validateVerificationCodeOnly(value);

                    // 使用SchedulerBinding.addPostFrameCallback来确保在构建完成后显示SnackBar
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (context.mounted) {
                        registerViewModel.showValidationResultSnackBar(
                            context, validationResult);
                      }
                    });

                    if (validationResult == null) {
                      // If validation is successful (returns null), proceed to the next page
                      await Future.delayed(const Duration(milliseconds: 300));
                      if (context.mounted) {
                        await registerViewModel.proceedToPasswordPage();
                      }
                    } else {
                      // Shake the input to indicate error
                      _shakeController.forward(from: 0.0);

                      // Clear the input and request focus again
                      registerViewModel.verificationCodeController.clear();
                      verificationCodeFocusNode.requestFocus();
                    }
                  },
                  pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                  showCursor: true,
                  forceErrorState: registerViewModel
                              .verificationCodeController.text.length ==
                          4 &&
                      registerViewModel.validateVerificationCodeOnly(
                              registerViewModel
                                  .verificationCodeController.text) !=
                          null,
                ),
              ),
            ),
            if (registerViewModel.phoneNumberController.text.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 24.0),
                child: Center(
                  child: Column(
                    children: [
                      Text(
                        '验证码已发送至',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '+86 ${registerViewModel.phoneNumberController.text}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Color(0xFF2c3e50),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Back button
                TextButton.icon(
                  onPressed: () => registerViewModel.goToPreviousPage(),
                  icon: Icon(
                    Icons.arrow_back_rounded,
                    size: 18,
                    color: colorScheme.primary,
                  ),
                  label: Text(
                    '返回',
                    style: TextStyle(
                      color: colorScheme.primary,
                    ),
                  ),
                ),
                // Resend button
                TextButton.icon(
                  onPressed: registerViewModel.busy ||
                          registerViewModel.countdown > 0
                      ? null
                      : () async {
                          // 传递isResend参数为true，表示是重新发送验证码
                          await registerViewModel.sendVerificationCode(context,
                              isResend: true);
                          // Auto-focus the input again
                          verificationCodeFocusNode.requestFocus();
                        },
                  icon: Icon(
                    Icons.refresh_rounded,
                    size: 18,
                    color: registerViewModel.busy ||
                            registerViewModel.countdown > 0
                        ? Colors.grey
                        : colorScheme.primary,
                  ),
                  label: Text(
                    registerViewModel.countdown > 0
                        ? '${registerViewModel.countdown}秒后重新发送'
                        : '重新发送',
                    style: TextStyle(
                      color: registerViewModel.busy ||
                              registerViewModel.countdown > 0
                          ? Colors.grey
                          : colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
