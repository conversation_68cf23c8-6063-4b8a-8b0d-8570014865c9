import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/features/search/models/search_result.dart';
import 'package:user_app/config/app_routes.dart';

class SearchResultItem extends StatelessWidget {
  final SearchResult result;
  final String query;

  const SearchResultItem({
    super.key,
    required this.result,
    required this.query,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _handleTap(context),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // 图片或图标
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey.shade200,
                ),
                child: result.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.network(
                          result.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildDefaultIcon();
                          },
                        ),
                      )
                    : _buildDefaultIcon(),
              ),
              const SizedBox(width: 12),
              
              // 内容
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    Text(
                      _highlightQuery(result.title, query),
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    if (result.subtitle != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        result.subtitle!,
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    
                    const SizedBox(height: 8),
                    
                    // 类型标签
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getTypeColor().withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _getTypeLabel(),
                        style: TextStyle(
                          fontSize: 11,
                          color: _getTypeColor(),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // 箭头
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey.shade400,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultIcon() {
    IconData iconData;
    Color iconColor;

    switch (result.type) {
      case SearchResultType.moment:
        iconData = Icons.article_outlined;
        iconColor = Colors.blue;
        break;
      case SearchResultType.fishingSpot:
        iconData = Icons.location_on_outlined;
        iconColor = Colors.green;
        break;
      case SearchResultType.user:
        iconData = Icons.person_outline;
        iconColor = Colors.orange;
        break;
    }

    return Icon(
      iconData,
      size: 30,
      color: iconColor,
    );
  }

  Color _getTypeColor() {
    switch (result.type) {
      case SearchResultType.moment:
        return Colors.blue;
      case SearchResultType.fishingSpot:
        return Colors.green;
      case SearchResultType.user:
        return Colors.orange;
    }
  }

  String _getTypeLabel() {
    switch (result.type) {
      case SearchResultType.moment:
        return '动态';
      case SearchResultType.fishingSpot:
        return '钓点';
      case SearchResultType.user:
        return '用户';
    }
  }

  String _highlightQuery(String text, String query) {
    // 简单的高亮实现，实际项目中可以使用更复杂的高亮逻辑
    return text;
  }

  void _handleTap(BuildContext context) {
    // 添加调试信息
    debugPrint('搜索结果点击调试信息:');
    debugPrint('- 结果类型: ${result.type}');
    debugPrint('- 结果ID: ${result.id}');
    debugPrint('- 结果ID类型: ${result.id.runtimeType}');
    debugPrint('- 结果标题: ${result.title}');
    debugPrint('- 结果数据: ${result.data}');
    debugPrint('- AppRoutes.momentDetail: ${AppRoutes.momentDetail}');
    debugPrint('- AppRoutes.fishingSpotDetail: ${AppRoutes.fishingSpotDetail}');
    debugPrint('- AppRoutes.profile: ${AppRoutes.profile}');
    
    // 立即进行路由跳转
    try {
        switch (result.type) {
          case SearchResultType.moment:
            // 导航到动态详情
            debugPrint('准备跳转到动态详情页面');
            debugPrint('路由: ${AppRoutes.momentDetail}');
            debugPrint('传递数据: ${result.data}');
            
            // 检查必要的数据字段
            final momentId = result.data['id'] ?? result.data['momentId'];
            if (momentId == null) {
              debugPrint('错误: 动态ID为空');
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('动态ID缺失，无法跳转')),
              );
              return;
            }
            
            debugPrint('即将调用context.push，路由: ${AppRoutes.momentDetail}');
            context.push(AppRoutes.momentDetail, extra: result.data);
            break;
            
          case SearchResultType.fishingSpot:
            // 导航到钓点详情
            debugPrint('准备跳转到钓点详情页面');
            debugPrint('路由: ${AppRoutes.fishingSpotDetail}');
            debugPrint('传递数据: ${result.data}');
            
            // 检查必要的数据字段
            final spotId = result.data['id'] ?? result.data['spotId'];
            if (spotId == null) {
              debugPrint('错误: 钓点ID为空');
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('钓点ID缺失，无法跳转')),
              );
              return;
            }
            
            debugPrint('即将调用context.push，路由: ${AppRoutes.fishingSpotDetail}');
            context.push(AppRoutes.fishingSpotDetail, extra: result.data);
            break;
            
          case SearchResultType.user:
            // 导航到用户详情
            debugPrint('准备跳转到用户详情页面');
            final userRoute = '${AppRoutes.profile}/${result.id}';
            debugPrint('路由: $userRoute');
            debugPrint('传递数据: ${result.data}');
            
            // 检查用户ID是否有效
            if (result.id.isEmpty) {
              debugPrint('错误: 用户ID为空');
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('用户ID缺失，无法跳转')),
              );
              return;
            }
            
            debugPrint('即将调用context.push，路由: $userRoute');
            context.push(userRoute, extra: result.data);
            break;
        }
        
        debugPrint('路由跳转命令已执行');
        
        // 跳转成功后显示确认信息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('正在打开${_getTypeLabel()}详情...'),
            duration: const Duration(seconds: 1),
          ),
        );
        
      } catch (e, stackTrace) {
        debugPrint('路由跳转错误: $e');
        debugPrint('错误堆栈: $stackTrace');
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('跳转失败: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
  }
}
