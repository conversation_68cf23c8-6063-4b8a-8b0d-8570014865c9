import 'package:flutter/foundation.dart';
import 'package:user_app/features/search/models/search_result.dart';
import 'package:user_app/services/search_service.dart' as global_search;
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/api/user_api.dart';

class SearchService {
  final global_search.SearchService _algoliaSearchService;
  final FishingSpotService _fishingSpotService;
  final UserApi _userApi;

  SearchService(
    this._algoliaSearchService,
    this._fishingSpotService,
    this._userApi,
  );

  /// 综合搜索
  Future<List<SearchResult>> search(String query) async {
    final results = <SearchResult>[];

    try {
      // 并行搜索不同类型的内容
      final futures = await Future.wait([
        _searchMoments(query),
        _searchFishingSpots(query),
        _searchUsers(query),
      ]);

      // 合并结果
      results.addAll(futures[0]); // moments
      results.addAll(futures[1]); // fishing spots
      results.addAll(futures[2]); // users

      // 按相关性排序（这里简单按类型排序，实际可以根据匹配度排序）
      results.sort((a, b) {
        // 动态优先，然后是钓点，最后是用户
        final typeOrder = {
          SearchResultType.moment: 0,
          SearchResultType.fishingSpot: 1,
          SearchResultType.user: 2,
        };
        return typeOrder[a.type]!.compareTo(typeOrder[b.type]!);
      });

      return results;
    } catch (e) {
      debugPrint('Search error: $e');
      return [];
    }
  }

  /// 搜索动态
  Future<List<SearchResult>> _searchMoments(String query) async {
    try {
      // 使用新的搜索API
      final searchResult = await _algoliaSearchService.search(
        query,
        type: 'moment',
        page: 1,
        size: 10,
      );

      // 将SearchResultVO转换为SearchResult
      return searchResult.records
          .map((result) => SearchResult.fromSearchResultVO(result))
          .toList();
    } catch (e) {
      debugPrint('Search moments error: $e');
      return [];
    }
  }

  /// 搜索钓点
  Future<List<SearchResult>> _searchFishingSpots(String query) async {
    try {
      final spots = await _fishingSpotService.searchFishingSpots(
        query: query,
        page: 0,
        pageSize: 10,
      );
      return spots
          .map((spot) => SearchResult.fromFishingSpot(spot.toMap()))
          .toList();
    } catch (e) {
      debugPrint('Search fishing spots error: $e');
      return [];
    }
  }

  /// 搜索用户
  Future<List<SearchResult>> _searchUsers(String query) async {
    try {
      final users = await _userApi.searchUsers(query, 0, 10);
      return users.map((user) => SearchResult.fromUser(user.toMap())).toList();
    } catch (e) {
      debugPrint('Search users error: $e');
      return [];
    }
  }

  /// 获取搜索建议
  Future<List<String>> getSuggestions(String query) async {
    final suggestions = <String>[];

    try {
      // 基于历史搜索和热门搜索生成建议
      final hotSearches = await getHotSearches();

      // 过滤匹配的热门搜索
      for (final hotSearch in hotSearches) {
        if (hotSearch.toLowerCase().contains(query.toLowerCase())) {
          suggestions.add(hotSearch);
        }
      }

      // 添加一些常见的搜索建议
      final commonSuggestions = [
        '${query}技巧',
        '${query}钓点',
        '${query}装备',
        '${query}饵料',
      ];

      suggestions.addAll(commonSuggestions);

      // 去重并限制数量
      return suggestions.toSet().take(8).toList();
    } catch (e) {
      debugPrint('Get suggestions error: $e');
      return [];
    }
  }

  /// 获取热门搜索
  Future<List<String>> getHotSearches() async {
    try {
      // 这里应该从服务器获取热门搜索
      // 暂时返回静态数据
      return [
        '鲫鱼',
        '路亚',
        '黑坑',
        '野钓',
        '台钓',
        '饵料配方',
        '钓点推荐',
        '新手入门',
        '冬季钓鱼',
        '鲤鱼',
        '草鱼',
        '鲢鳙',
        '翘嘴',
        '鲈鱼',
        '装备推荐',
      ];
    } catch (e) {
      debugPrint('Get hot searches error: $e');
      return [];
    }
  }
}
