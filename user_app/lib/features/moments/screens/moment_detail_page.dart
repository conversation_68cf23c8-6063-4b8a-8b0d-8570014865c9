import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/community/view_models/comment_view_model.dart';
import 'package:user_app/features/community/widgets/comment_section.dart';
import 'package:user_app/features/fishing_spots/utils/moment_type_data_parser.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/services/moment_service.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class MomentDetailPage extends StatefulWidget {
  final int momentId;
  final MomentVo? initialMoment;

  const MomentDetailPage({
    super.key,
    required this.momentId,
    this.initialMoment,
  });

  @override
  State<MomentDetailPage> createState() => _MomentDetailPageState();
}

class _MomentDetailPageState extends State<MomentDetailPage>
    with WidgetsBindingObserver {
  MomentVo? moment;
  bool isLoading = false;
  String? error;

  // State for the comment input field, moved from CommentSection
  final TextEditingController _commentController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isReplying = false;
  int? _replyToCommentId;
  String? _replyToUserName;
  
  // 评论提交状态管理
  bool _isSubmittingComment = false;
  
  // 输入验证状态
  int _commentLength = 0;
  static const int _maxCommentLength = 500;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    // 监听输入变化
    _commentController.addListener(_onCommentChanged);
    
    if (widget.initialMoment != null) {
      moment = widget.initialMoment;
    } else {
      _loadMomentDetail();
    }
  }
  
  // 监听评论输入变化
  void _onCommentChanged() {
    setState(() {
      _commentLength = _commentController.text.length;
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _commentController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Refresh moment data when app becomes active again
    if (state == AppLifecycleState.resumed && mounted) {
      _loadMomentDetail();
    }
  }

  Future<void> _loadMomentDetail() async {
    debugPrint('=== MomentDetailPage _loadMomentDetail 调试信息 ===');
    debugPrint('请求的momentId: ${widget.momentId}');
    debugPrint('momentId类型: ${widget.momentId.runtimeType}');
    setState(() => isLoading = true);
    try {
      final momentService = Provider.of<MomentService>(context, listen: false);
      debugPrint('开始调用 momentService.getById(${widget.momentId})');
      final momentDetail = await momentService.getById(widget.momentId);
      debugPrint('API调用成功，获得动态数据: ${momentDetail.id}');
      if (mounted) {
        setState(() {
          moment = momentDetail;
          isLoading = false;
          error = null;
        });
      }
    } catch (e) {
      debugPrint('API调用失败: $e');
      debugPrint('错误类型: ${e.runtimeType}');
      if (mounted) {
        setState(() {
          error = e.toString();
          isLoading = false;
        });
      }
    }
  }

  // Helper methods for comment input, moved from CommentSection
  bool get _isAuthenticated {
    return context.read<AuthViewModel>().isUserLoggedIn();
  }

  void _showLoginDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('需要登录'),
        content: const Text('请先登录后再进行评论'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.push(AppRoutes.login);
            },
            child: const Text('去登录'),
          ),
        ],
      ),
    );
  }

  void _startReply(int commentId, String userName) {
    if (!_isAuthenticated) {
      _showLoginDialog();
      return;
    }
    setState(() {
      _isReplying = true;
      _replyToCommentId = commentId;
      _replyToUserName = userName;
    });
    _focusNode.requestFocus();
  }

  void _cancelReply() {
    setState(() {
      _isReplying = false;
      _replyToCommentId = null;
      _replyToUserName = null;
    });
    _commentController.clear();
  }

  Future<void> _submitComment() async {
    if (!_isAuthenticated) {
      _showLoginDialog();
      return;
    }

    final content = _commentController.text.trim();
    
    // 增强输入验证
    if (content.isEmpty) {
      _showInputError('评论内容不能为空');
      return;
    }
    
    if (content.length > _maxCommentLength) {
      _showInputError('评论内容不能超过${_maxCommentLength}字符');
      return;
    }
    
    // 检查是否包含敏感词（可以扩展）
    if (_containsSensitiveWords(content)) {
      _showInputError('评论包含不当内容，请修改后重试');
      return;
    }

    // 防抖：防止多次提交
    if (_isSubmittingComment) return;

    setState(() {
      _isSubmittingComment = true;
    });

    final commentVm = context.read<CommentViewModel>();

    try {
      if (_isReplying && _replyToCommentId != null) {
        await commentVm.replyToComment(
          widget.momentId,
          _replyToCommentId!,
          content,
        );
      } else {
        await commentVm.addComment(
          widget.momentId,
          content,
        );
      }

      // 成功后的处理
      _commentController.clear();
      _cancelReply();
      FocusScope.of(context).unfocus();

      // Refresh comments
      await commentVm.loadComments(widget.momentId);
      
      // 触发成功反馈
      HapticFeedback.lightImpact();
      
      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(_isReplying ? '回复成功' : '评论成功'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      
    } catch (e) {
      debugPrint('Comment submit error: $e');
      if (mounted) {
        HapticFeedback.heavyImpact(); // 失败时使用不同的震动
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                const Expanded(child: Text('评论发送失败，请检查网络连接')),
                TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    _submitComment(); // 重试
                  },
                  child: const Text('重试', style: TextStyle(color: Colors.white)),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmittingComment = false;
        });
      }
    }
  }

  // 显示输入错误提示
  void _showInputError(String message) {
    HapticFeedback.heavyImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.warning, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  // 简单的敏感词检查（可以扩展为更复杂的逻辑）
  bool _containsSensitiveWords(String content) {
    final sensitiveWords = ['spam', '广告', '违规']; // 示例敏感词
    final lowerContent = content.toLowerCase();
    return sensitiveWords.any((word) => lowerContent.contains(word.toLowerCase()));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // Return the updated moment when navigating back
          if (moment != null && moment!.id != null) {
            Navigator.of(context).pop(moment);
          } else {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('动态详情'),
          elevation: 0,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              if (moment != null && moment!.id != null) {
                Navigator.of(context).pop(moment);
              } else {
                Navigator.of(context).pop();
              }
            },
          ),
        ),
        // The comment input field is now in the bottomNavigationBar
        bottomNavigationBar: _buildCommentInput(),
        body: Builder(
          builder: (context) {
            if (isLoading && moment == null) {
              return const Center(child: CircularProgressIndicator());
            }
            if (error != null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline_rounded,
                      size: 64,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '加载失败',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error!.contains('404') || error!.contains('未找到') 
                        ? '该动态可能已被删除或不存在'
                        : '网络连接异常，请检查网络后重试',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: _loadMomentDetail,
                      icon: const Icon(Icons.refresh_rounded),
                      label: const Text('重新加载'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
            if (moment == null) {
              return const Center(child: Text('动态不存在'));
            }
            return RefreshIndicator(
              onRefresh: _loadMomentDetail,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                // Padding at the bottom to ensure content isn't hidden by the input bar
                padding: const EdgeInsets.only(bottom: 100),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // User info, content, images, etc.
                          _buildMomentHeader(theme, colorScheme),
                          const SizedBox(height: 16),

                          // 动态内容整体容器
                          if (moment!.typeSpecificData != null)
                            _buildMomentContentCard(moment!)
                          else if (moment!.content != null ||
                              (moment!.images != null && moment!.images!.isNotEmpty))
                            _buildSimpleMomentContent(moment!),

                          const SizedBox(height: 16),
                          if (moment!.fishingSpotName != null &&
                              moment!.fishingSpotName!.isNotEmpty)
                            _buildLocationInfo(theme),
                          if (moment!.fishingSpotName != null &&
                              moment!.fishingSpotName!.isNotEmpty)
                            const SizedBox(height: 16),
                          _buildStatsRow(),
                        ],
                      ),
                    ),

                    // Divider
                    Divider(
                        color: Colors.grey.shade200, height: 1, thickness: 8),

                    // The refactored CommentSection widget
                    if (moment!.id != null)
                      CommentSection(
                        momentId: moment!.id!,
                        onStartReply: _startReply, // Pass the callback
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // Widget for the comment input bar at the bottom
  Widget _buildCommentInput() {
    return Container(
      padding: EdgeInsets.fromLTRB(
          16, 12, 16, 12 + MediaQuery.of(context).viewInsets.bottom),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey.shade200)),
        boxShadow: [
          BoxShadow(color: Colors.black.withOpacity(0.05), blurRadius: 10)
        ],
      ),
      child: SafeArea(
        top: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isReplying) ...[
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Text('回复 $_replyToUserName',
                        style: TextStyle(
                            fontSize: 13, color: Colors.blue.shade800)),
                    const Spacer(),
                    GestureDetector(
                      onTap: _cancelReply,
                      child: Icon(Icons.close,
                          size: 16, color: Colors.blue.shade800),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextField(
                        controller: _commentController,
                        focusNode: _focusNode,
                        decoration: InputDecoration(
                          hintText:
                              _isReplying ? '回复 $_replyToUserName...' : '写下你的评论...',
                          hintStyle: TextStyle(color: Colors.grey.shade500),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(20),
                            borderSide: BorderSide.none,
                          ),
                          fillColor: Colors.grey.shade100,
                          filled: true,
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 10),
                          // 根据字符数改变边框颜色
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(20),
                            borderSide: BorderSide(
                              color: _commentLength > _maxCommentLength 
                                  ? Colors.red 
                                  : Theme.of(context).primaryColor,
                              width: 2,
                            ),
                          ),
                        ),
                        maxLines: null,
                        maxLength: _maxCommentLength,
                        textInputAction: TextInputAction.send,
                        onSubmitted: (_) => _isSubmittingComment ? null : _submitComment(),
                      ),
                      // 字符计数显示
                      if (_commentLength > 0)
                        Padding(
                          padding: const EdgeInsets.only(left: 16, top: 4),
                          child: Text(
                            '$_commentLength/$_maxCommentLength',
                            style: TextStyle(
                              fontSize: 12,
                              color: _commentLength > _maxCommentLength 
                                  ? Colors.red 
                                  : Colors.grey[600],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                GestureDetector(
                  onTap: _isSubmittingComment || _commentLength > _maxCommentLength || _commentLength == 0
                      ? null 
                      : _submitComment,
                  child: CircleAvatar(
                    radius: 20,
                    backgroundColor: _isSubmittingComment || _commentLength > _maxCommentLength || _commentLength == 0
                        ? Colors.grey[400]
                        : (_isSubmittingComment 
                            ? Theme.of(context).primaryColor.withOpacity(0.6)
                            : Theme.of(context).primaryColor),
                    child: _isSubmittingComment
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.send, color: Colors.white, size: 20),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Extracted UI building methods for clarity
  Widget _buildMomentHeader(ThemeData theme, ColorScheme colorScheme) {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            if (moment?.publisher?.id != null) {
              context.push('${AppRoutes.profile}/${moment!.publisher!.id}');
            }
          },
          child: CircleAvatar(
            radius: 24,
            backgroundImage: moment?.publisher?.avatarUrl != null
                ? CachedNetworkImageProvider(moment!.publisher!.avatarUrl!)
                : null,
            child: moment?.publisher?.avatarUrl == null
                ? Icon(Icons.person,
                    size: 24, color: colorScheme.onPrimaryContainer)
                : null,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    moment!.publisher?.name ?? moment!.userName ?? '匿名用户',
                    style: theme.textTheme.titleMedium
                        ?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  if (moment!.momentType != null) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getMomentTypeColor(moment!.momentType!)
                            .withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getMomentTypeIcon(moment!.momentType!),
                            size: 14,
                            color: _getMomentTypeColor(moment!.momentType!),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            MomentTypeDataParser.getMomentTypeDisplayName(
                                moment!.momentType),
                            style: TextStyle(
                              color: _getMomentTypeColor(moment!.momentType!),
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 4),
              Text(
                _formatTime(moment!.createdAt),
                style: theme.textTheme.bodyMedium
                    ?.copyWith(color: Colors.grey.shade600),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLocationInfo(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.location_on, size: 16, color: theme.colorScheme.primary),
          const SizedBox(width: 6),
          Text(
            moment!.fishingSpotName!,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsRow() {
    return Row(
      children: [
        _buildStatItem(
            icon: Icons.thumb_up_outlined,
            count: moment!.likeCount ?? 0,
            label: '点赞'),
        const SizedBox(width: 24),
        _buildStatItem(
            icon: Icons.comment_outlined,
            count: moment!.commentCount ?? 0,
            label: '评论'),
        const SizedBox(width: 24),
        _buildStatItem(
            icon: Icons.remove_red_eye_outlined,
            count: 0, // View count not available in model
            label: '浏览'),
      ],
    );
  }

  Widget _buildImageGrid(List<MomentImageVo> images,
      {bool withRoundedCorners = true}) {
    if (images.isEmpty) return const SizedBox.shrink();

    // 获取图片URL
    final imageUrls = images
        .map((image) => image.imageUrl)
        .where((url) => url.isNotEmpty)
        .toList();

    if (imageUrls.isEmpty) return const SizedBox.shrink();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount:
            imageUrls.length == 1 ? 1 : (imageUrls.length == 2 ? 2 : 3),
        childAspectRatio: 1,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: imageUrls.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            // TODO: 实现图片查看器
          },
          child: withRoundedCorners
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: CachedNetworkImage(
                    imageUrl: imageUrls[index],
                    fit: BoxFit.cover,
                    placeholder: (context, url) =>
                        Container(color: Colors.grey.shade200),
                    errorWidget: (context, url, error) => Container(
                        color: Colors.grey.shade200,
                        child: const Icon(Icons.error)),
                  ),
                )
              : CachedNetworkImage(
                  imageUrl: imageUrls[index],
                  fit: BoxFit.cover,
                  placeholder: (context, url) =>
                      Container(color: Colors.grey.shade200),
                  errorWidget: (context, url, error) => Container(
                      color: Colors.grey.shade200,
                      child: const Icon(Icons.error)),
                ),
        );
      },
    );
  }

  Widget _buildStatItem(
      {required IconData icon, required int count, required String label}) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey.shade600),
        const SizedBox(width: 4),
        Text(count.toString(),
            style: TextStyle(
                color: Colors.grey.shade600, fontWeight: FontWeight.w500)),
        const SizedBox(width: 4),
        Text(label,
            style: TextStyle(color: Colors.grey.shade600, fontSize: 12)),
      ],
    );
  }

  String _formatTime(String? timeString) {
    if (timeString == null) return '未知时间';
    try {
      final time = DateTime.parse(timeString);
      final now = DateTime.now();
      final difference = now.difference(time);
      if (difference.inMinutes < 1) return '刚刚';
      if (difference.inHours < 1) return '${difference.inMinutes}分钟前';
      if (difference.inDays < 1) return '${difference.inHours}小时前';
      if (difference.inDays < 7) return '${difference.inDays}天前';
      return '${time.year}-${time.month.toString().padLeft(2, '0')}-${time.day.toString().padLeft(2, '0')}';
    } catch (e) {
      return timeString;
    }
  }

  // 构建动态内容卡片（包含类型特定内容、文字和图片）
  Widget _buildMomentContentCard(MomentVo moment) {
    final Color primaryColor;
    final Color backgroundColor;
    final IconData iconData;
    final String typeName;

    // 根据类型设置颜色和图标
    switch (moment.momentType) {
      case 'fishing_catch':
        primaryColor = Colors.blue.shade600;
        backgroundColor = Colors.blue.shade50;
        iconData = Icons.catching_pokemon;
        typeName = '钓获分享';
        break;
      case 'equipment':
        primaryColor = Colors.orange.shade600;
        backgroundColor = Colors.orange.shade50;
        iconData = Icons.shopping_bag;
        typeName = '装备展示';
        break;
      case 'technique':
        primaryColor = Colors.green.shade600;
        backgroundColor = Colors.green.shade50;
        iconData = Icons.lightbulb;
        typeName = '技巧分享';
        break;
      case 'question':
        primaryColor = Colors.purple.shade600;
        backgroundColor = Colors.purple.shade50;
        iconData = Icons.help_outline;
        typeName = '问答求助';
        break;
      default:
        primaryColor = Colors.grey.shade600;
        backgroundColor = Colors.grey.shade50;
        iconData = Icons.article;
        typeName = '动态分享';
    }

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: primaryColor.withOpacity(0.2),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 类型标题栏
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: backgroundColor.withOpacity(0.5),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    iconData,
                    size: 20,
                    color: primaryColor,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  typeName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: primaryColor,
                  ),
                ),
              ],
            ),
          ),

          // 类型特定内容
          Padding(
            padding: const EdgeInsets.all(16),
            child: _buildTypeSpecificData(moment),
          ),

          // 分隔线
          if (moment.content != null && moment.content!.isNotEmpty)
            Divider(
              height: 1,
              thickness: 1,
              color: primaryColor.withOpacity(0.1),
              indent: 16,
              endIndent: 16,
            ),

          // 文字内容
          if (moment.content != null && moment.content!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                moment.content!,
                style: TextStyle(
                  fontSize: 15,
                  height: 1.5,
                  color: Colors.grey.shade800,
                ),
              ),
            ),

          // 图片内容
          if (moment.images != null && moment.images!.isNotEmpty) ...[
            if (moment.content == null || moment.content!.isEmpty)
              const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildImageGrid(moment.images!),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 构建简单动态内容（无类型特定数据）
  Widget _buildSimpleMomentContent(MomentVo moment) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (moment.content != null && moment.content!.isNotEmpty)
          Text(
            moment.content!,
            style: const TextStyle(
              fontSize: 15,
              height: 1.5,
              color: Colors.black87,
            ),
          ),
        if (moment.content != null && moment.content!.isNotEmpty)
          const SizedBox(height: 16),
        if (moment.images != null && moment.images!.isNotEmpty)
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: _buildImageGrid(moment.images!),
          ),
      ],
    );
  }

  // 构建类型特定数据
  Widget _buildTypeSpecificData(MomentVo moment) {
    switch (moment.momentType) {
      case 'fishing_catch':
        return _buildFishingCatchData(moment);
      case 'equipment':
        return _buildEquipmentData(moment);
      case 'technique':
        return _buildTechniqueData(moment);
      case 'question':
        return _buildQuestionData(moment);
      default:
        return const SizedBox.shrink();
    }
  }

  // 构建钓获分享数据
  Widget _buildFishingCatchData(MomentVo moment) {
    final catchData =
        MomentTypeDataParser.parseFishingCatch(moment.typeSpecificData);
    if (catchData == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 总重量
        if (catchData.totalWeight != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.blue.shade600,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.fitness_center,
                  size: 16,
                  color: Colors.white,
                ),
                const SizedBox(width: 6),
                Text(
                  '总重量 ${catchData.totalWeight}kg',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

        // 渔获列表
        if (catchData.caughtFishes != null &&
            catchData.caughtFishes!.isNotEmpty) ...[
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: catchData.caughtFishes!.map((fish) {
              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.blue.shade200,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.set_meal,
                      size: 16,
                      color: Colors.blue.shade600,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '${fish.fishTypeName ?? "未知"}',
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        'x${fish.count ?? 1}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                    if (fish.weight != null) ...[
                      const SizedBox(width: 6),
                      Text(
                        '${fish.weight}kg',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ],
                ),
              );
            }).toList(),
          ),
        ],

        // 钓法和天气
        if (catchData.fishingMethod != null ||
            catchData.weatherConditions != null) ...[
          const SizedBox(height: 12),
          Row(
            children: [
              if (catchData.fishingMethod != null) ...[
                Icon(
                  Icons.phishing,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 6),
                Text(
                  catchData.fishingMethod!,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
              if (catchData.fishingMethod != null &&
                  catchData.weatherConditions != null)
                const SizedBox(width: 16),
              if (catchData.weatherConditions != null) ...[
                Icon(
                  Icons.wb_sunny_outlined,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 6),
                Text(
                  catchData.weatherConditions!,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ],
          ),
        ],
      ],
    );
  }

  // 构建装备展示数据
  Widget _buildEquipmentData(MomentVo moment) {
    final equipmentData =
        MomentTypeDataParser.parseEquipment(moment.typeSpecificData);
    if (equipmentData == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 装备名称和评分
        Row(
          children: [
            Expanded(
              child: Text(
                equipmentData.equipmentName ?? '装备',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (equipmentData.rating != null)
              Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(5, (index) {
                  return Icon(
                    index < equipmentData.rating!
                        ? Icons.star
                        : Icons.star_border,
                    size: 16,
                    color: Colors.amber.shade600,
                  );
                }),
              ),
          ],
        ),

        // 装备信息网格
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.orange.shade100,
              width: 1,
            ),
          ),
          child: Column(
            children: [
              if (equipmentData.category != null || equipmentData.brand != null)
                Row(
                  children: [
                    if (equipmentData.category != null)
                      Expanded(
                        child: Row(
                          children: [
                            Icon(Icons.category,
                                size: 16, color: Colors.grey.shade600),
                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                equipmentData.category!,
                                style: const TextStyle(fontSize: 13),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (equipmentData.category != null &&
                        equipmentData.brand != null)
                      Container(
                        width: 1,
                        height: 20,
                        color: Colors.grey.shade200,
                        margin: const EdgeInsets.symmetric(horizontal: 12),
                      ),
                    if (equipmentData.brand != null)
                      Expanded(
                        child: Row(
                          children: [
                            Icon(Icons.business,
                                size: 16, color: Colors.grey.shade600),
                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                equipmentData.brand!,
                                style: const TextStyle(fontSize: 13),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              if ((equipmentData.category != null ||
                      equipmentData.brand != null) &&
                  (equipmentData.model != null || equipmentData.price != null))
                const SizedBox(height: 8),
              if (equipmentData.model != null || equipmentData.price != null)
                Row(
                  children: [
                    if (equipmentData.model != null)
                      Expanded(
                        child: Row(
                          children: [
                            Icon(Icons.model_training,
                                size: 16, color: Colors.grey.shade600),
                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                equipmentData.model!,
                                style: const TextStyle(fontSize: 13),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (equipmentData.model != null &&
                        equipmentData.price != null)
                      Container(
                        width: 1,
                        height: 20,
                        color: Colors.grey.shade200,
                        margin: const EdgeInsets.symmetric(horizontal: 12),
                      ),
                    if (equipmentData.price != null)
                      Expanded(
                        child: Row(
                          children: [
                            Icon(Icons.attach_money,
                                size: 16, color: Colors.grey.shade600),
                            const SizedBox(width: 6),
                            Text(
                              equipmentData.price!,
                              style: const TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
            ],
          ),
        ),

        // 适用鱼种
        if (equipmentData.targetFishTypes != null &&
            equipmentData.targetFishTypes!.isNotEmpty) ...[
          const SizedBox(height: 12),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: equipmentData.targetFishTypes!.map((fish) {
              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.orange.shade200,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.set_meal,
                      size: 12,
                      color: Colors.orange.shade600,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      fish.name ?? "未知",
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  // 构建技巧分享数据
  Widget _buildTechniqueData(MomentVo moment) {
    final techniqueData =
        MomentTypeDataParser.parseTechnique(moment.typeSpecificData);
    if (techniqueData == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 技巧名称和难度
        Row(
          children: [
            Expanded(
              child: Text(
                techniqueData.techniqueName ?? '技巧',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (techniqueData.difficulty != null)
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: _getDifficultyColor(techniqueData.difficulty!),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  techniqueData.difficulty!,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),

        // 技巧描述
        if (techniqueData.description != null) ...[
          const SizedBox(height: 12),
          Text(
            techniqueData.description!,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.4,
            ),
          ),
        ],

        // 适用环境和目标鱼种
        if ((techniqueData.environments != null &&
                techniqueData.environments!.isNotEmpty) ||
            (techniqueData.targetFishTypes != null &&
                techniqueData.targetFishTypes!.isNotEmpty)) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.green.shade100,
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (techniqueData.environments != null &&
                    techniqueData.environments!.isNotEmpty) ...[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.landscape,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '适用环境',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              techniqueData.environments!.join('、'),
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
                if (techniqueData.targetFishTypes != null &&
                    techniqueData.targetFishTypes!.isNotEmpty) ...[
                  if (techniqueData.environments != null &&
                      techniqueData.environments!.isNotEmpty)
                    const SizedBox(height: 8),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.set_meal,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '目标鱼种',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Wrap(
                              spacing: 6,
                              runSpacing: 4,
                              children:
                                  techniqueData.targetFishTypes!.map((fish) {
                                return Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colors.green.shade50,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    fish.name ?? "未知",
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.green.shade700,
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ],
    );
  }

  // 构建问答求助数据
  Widget _buildQuestionData(MomentVo moment) {
    final questionData =
        MomentTypeDataParser.parseQuestion(moment.typeSpecificData);
    if (questionData == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 问题标题
        Text(
          questionData.questionTitle ?? '问题',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),

        // 问题详情
        if (questionData.detailedProblem != null) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.purple.shade100,
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.description_outlined,
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '问题详情',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  questionData.detailedProblem!,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey.shade800,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],

        // 标签
        if (questionData.tags != null && questionData.tags!.isNotEmpty) ...[
          const SizedBox(height: 12),
          Wrap(
            spacing: 6,
            runSpacing: 6,
            children: questionData.tags!.map((tag) {
              return Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.purple.shade50,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.purple.shade200,
                    width: 1,
                  ),
                ),
                child: Text(
                  tag,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.purple.shade700,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ],
    );
  }

  // 获取难度对应的颜色
  Color _getDifficultyColor(String difficulty) {
    switch (difficulty) {
      case '入门级':
        return Colors.green;
      case '进阶级':
        return Colors.orange;
      case '专家级':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getMomentTypeColor(String type) {
    switch (type) {
      case 'fishing_catch':
        return Colors.blue.shade600;
      case 'equipment':
        return Colors.orange.shade600;
      case 'technique':
        return Colors.green.shade600;
      case 'question':
        return Colors.purple.shade600;
      default:
        return Colors.grey;
    }
  }

  IconData _getMomentTypeIcon(String type) {
    switch (type) {
      case 'fishing_catch':
        return Icons.catching_pokemon;
      case 'equipment':
        return Icons.shopping_bag;
      case 'technique':
        return Icons.lightbulb;
      case 'question':
        return Icons.help;
      default:
        return Icons.article;
    }
  }
}
