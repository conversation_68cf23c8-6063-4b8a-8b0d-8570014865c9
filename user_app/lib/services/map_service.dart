import 'package:user_app/api/map_api.dart';
import 'package:user_app/features/fishing_spots/models/weather_data_dto.dart';
import 'package:user_app/models/map/re_geocode_dto.dart';

class MapService {
  final MapApi _mapApi;

  MapService(this._mapApi);

  Future<ReGeocodeDto> resolveCoordinate({
    required double latitude,
    required double longitude,
  }) =>
      _mapApi.resolveCoordinate(
        latitude: latitude,
        longitude: longitude,
      );

  Future<WeatherDataDto> getWeatherByIp() async {
    return await _mapApi.getWeatherByIp();
  }

  Future<WeatherDataDto> getWeatherForecast() async {
    return await _mapApi.getWeatherForecast();
  }
}
