import 'package:user_app/api/album_api.dart';
import 'package:user_app/models/album/album_item.dart';
import 'package:user_app/models/album/album_stats.dart';

class AlbumService {
  final AlbumApi albumApi;

  AlbumService(this.albumApi);

  /// 获取相册图片列表
  Future<Map<String, dynamic>> getAlbumImages({
    int page = 1,
    int pageSize = 20,
  }) async {
    final response = await albumApi.getAlbumImages(
      page: page,
      pageSize: pageSize,
    );
    return response.data ?? {};
  }

  /// 添加图片到相册
  Future<String> addToAlbum({
    required String imageUrl,
    String? description,
    String? tags,
  }) async {
    final response = await albumApi.addToAlbum(
      imageUrl: imageUrl,
      description: description,
      tags: tags,
    );
    return response.data ?? '上传成功';
  }

  /// 删除相册图片
  Future<String> deleteFromAlbum(int imageId) async {
    final response = await albumApi.deleteFromAlbum(imageId);
    return response.data ?? '删除成功';
  }

  /// 获取相册统计信息
  Future<AlbumStats?> getAlbumStats() async {
    final response = await albumApi.getAlbumStats();
    return response.data;
  }

  /// 获取最近上传的图片
  Future<List<AlbumItem>> getRecentImages({int limit = 10}) async {
    final response = await albumApi.getRecentImages(limit: limit);
    return response.data ?? [];
  }

  /// 根据标签搜索图片
  Future<List<AlbumItem>> searchByTags(String tags) async {
    final response = await albumApi.searchByTags(tags);
    return response.data ?? [];
  }

  /// 批量删除图片
  Future<String> batchDeleteImages(List<int> imageIds) async {
    final response = await albumApi.batchDeleteImages(imageIds);
    return response.data ?? '批量删除成功';
  }
}