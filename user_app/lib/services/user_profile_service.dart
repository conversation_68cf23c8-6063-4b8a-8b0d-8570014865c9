import 'package:flutter/foundation.dart';
import 'package:user_app/api/user_api.dart';
import 'package:user_app/api/statistics_api.dart';
import 'package:user_app/api/notification_api.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/models/statistics/statistics.dart';

/// 用户个人资料服务，整合用户信息、统计数据和通知等相关功能
class UserProfileService {
  final UserApi userApi;
  final StatisticsApi statisticsApi;
  final NotificationApi notificationApi;

  UserProfileService({
    required this.userApi,
    required this.statisticsApi,
    required this.notificationApi,
  });

  /// 获取完整的用户个人资料信息
  Future<UserProfileData> getCompleteUserProfile(int userId) async {
    try {
      // 并行获取所有数据
      final results = await Future.wait([
        userApi.getUserProfile(userId),
        statisticsApi.fetchStatistics(),
        notificationApi.getUnreadCount(),
      ]);

      final user = results[0] as User;
      final statistics = results[1] as Statistics;
      final unreadCount = (results[2] as Map<String, dynamic>)['data'] as int? ?? 0;

      return UserProfileData(
        user: user,
        statistics: statistics,
        unreadNotifications: unreadCount,
      );
    } catch (e) {
      debugPrint('获取用户完整资料失败: $e');
      rethrow;
    }
  }

  /// 获取当前用户的个人资料
  Future<UserProfileData> getCurrentUserProfile() async {
    try {
      // 并行获取所有数据
      final results = await Future.wait([
        userApi.getCurrentUser(),
        statisticsApi.fetchStatistics(),
        notificationApi.getUnreadCount(),
      ]);

      final user = results[0] as User;
      final statistics = results[1] as Statistics;
      final unreadResponse = results[2] as Map<String, dynamic>;
      final unreadCount = unreadResponse['data'] as int? ?? 0;

      return UserProfileData(
        user: user,
        statistics: statistics,
        unreadNotifications: unreadCount,
      );
    } catch (e) {
      debugPrint('获取当前用户完整资料失败: $e');
      rethrow;
    }
  }

  /// 刷新用户统计数据
  Future<Statistics> refreshStatistics() async {
    try {
      return await statisticsApi.fetchStatistics();
    } catch (e) {
      debugPrint('刷新统计数据失败: $e');
      rethrow;
    }
  }

  /// 刷新通知数量
  Future<int> refreshNotificationCount() async {
    try {
      final response = await notificationApi.getUnreadCount();
      return response.data ?? 0;
    } catch (e) {
      debugPrint('刷新通知数量失败: $e');
      return 0;
    }
  }

  /// 标记通知为已读并返回新的未读数量
  Future<int> markNotificationAsRead(int notificationId) async {
    try {
      await notificationApi.markAsRead(notificationId);
      return await refreshNotificationCount();
    } catch (e) {
      debugPrint('标记通知已读失败: $e');
      rethrow;
    }
  }

  /// 标记所有通知为已读
  Future<void> markAllNotificationsAsRead() async {
    try {
      await notificationApi.markAllAsRead();
    } catch (e) {
      debugPrint('标记所有通知已读失败: $e');
      rethrow;
    }
  }
}

/// 用户个人资料数据包装类
class UserProfileData {
  final User user;
  final Statistics statistics;
  final int unreadNotifications;

  UserProfileData({
    required this.user,
    required this.statistics,
    required this.unreadNotifications,
  });

  /// 转换为易于使用的格式
  Map<String, dynamic> toDisplayMap() {
    return {
      'user': {
        'id': user.id,
        'name': user.name,
        'avatarUrl': user.avatarUrl,
        'introduce': user.introduce,
        'city': user.city,
        'gender': user.gender,
      },
      'statistics': {
        'momentCount': statistics.momentCount,
        'fansCount': statistics.fansCount,
        'followCount': statistics.followCount,
      },
      'notifications': {
        'unreadCount': unreadNotifications,
      }
    };
  }

  /// 获取用于StatisticsSection的数据
  List<ProfileInfoItem> getProfileInfoItems() {
    return [
      ProfileInfoItem('动态', statistics.momentCount),
      ProfileInfoItem('粉丝', statistics.fansCount),
      ProfileInfoItem('关注', statistics.followCount),
    ];
  }
}

/// 个人资料信息项
class ProfileInfoItem {
  final String title;
  final int value;
  final VoidCallback? onTap;

  const ProfileInfoItem(this.title, this.value, {this.onTap});
}