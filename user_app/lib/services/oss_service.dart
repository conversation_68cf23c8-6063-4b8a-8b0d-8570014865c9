import 'package:image_picker/image_picker.dart';
import 'package:user_app/api/oss_api.dart';

class OssService {
  final OssApi ossApi;
  bool _isInitialized = false;
  bool _isInitializing = false;
  String? _lastError;

  OssService({required this.ossApi});

  bool get isInitialized => _isInitialized;

  bool get isInitializing => _isInitializing;

  String? get lastError => _lastError;

  Future<String> saveFile(XFile file) async {
    if (!_isInitialized) {
      throw Exception('OssService is not initialized. Call init() first.');
    }

    try {
      var data = await file.readAsBytes();
      var name = file.name;
      var suffix = name.split(".").last;
      final result = await ossApi.saveFile(data, suffix);
      return result;
    } catch (e) {
      _lastError = 'Failed to save file: $e';
      rethrow;
    }
  }

  Future<void> init() async {
    if (_isInitialized) {
      return;
    }

    if (_isInitializing) {
      // Wait for the ongoing initialization to complete
      while (_isInitializing) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return;
    }

    _isInitializing = true;
    _lastError = null;

    try {
      await ossApi.init();
      _isInitialized = true;
    } catch (e) {
      _lastError = 'Failed to initialize OssService: $e';
      rethrow;
    } finally {
      _isInitializing = false;
    }
  }

  void reset() {
    _isInitialized = false;
    _isInitializing = false;
    _lastError = null;
  }
}
