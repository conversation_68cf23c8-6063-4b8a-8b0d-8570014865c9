import 'package:user_app/api/social_insights_api.dart';
import 'package:user_app/models/social_insights/social_insights.dart';

class SocialInsightsService {
  final SocialInsightsApi _socialInsightsApi;

  SocialInsightsService(this._socialInsightsApi);

  // Simple in-memory cache
  SocialInsights? _cachedData;
  DateTime? _cacheTime;
  static const Duration _cacheValidDuration = Duration(minutes: 5);

  Future<SocialInsights> fetchSocialInsights({String? timeRange}) async {
    // Check if we have valid cached data
    if (_cachedData != null && 
        _cacheTime != null && 
        DateTime.now().difference(_cacheTime!) < _cacheValidDuration) {
      return _cachedData!;
    }

    try {
      final insights = await _socialInsightsApi.fetchSocialInsights(timeRange: timeRange);
      
      // Cache the data
      _cachedData = insights;
      _cacheTime = DateTime.now();
      
      return insights;
    } catch (e) {
      // If we have cached data, return it even if stale
      if (_cachedData != null) {
        return _cachedData!;
      }
      rethrow;
    }
  }

  void clearCache() {
    _cachedData = null;
    _cacheTime = null;
  }
}