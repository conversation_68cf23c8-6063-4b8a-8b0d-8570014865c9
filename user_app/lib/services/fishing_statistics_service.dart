import 'package:user_app/api/fishing_statistics_api.dart';
import 'package:user_app/models/fishing_statistics/fishing_statistics.dart';

class FishingStatisticsService {
  final FishingStatisticsApi _fishingStatisticsApi;

  FishingStatisticsService(this._fishingStatisticsApi);

  // Cache for statistics
  FishingStatistics? _cachedStatistics;
  List<TripRecord>? _cachedTripRecords;
  List<EquipmentUsage>? _cachedEquipmentStats;
  List<CatchData>? _cachedCatchTrend;
  List<FishSpeciesData>? _cachedFishSpecies;
  List<SpotStatistics>? _cachedSpotStats;
  
  DateTime? _statisticsCacheTime;
  DateTime? _tripsCacheTime;
  DateTime? _equipmentCacheTime;
  DateTime? _trendCacheTime;
  DateTime? _speciesCacheTime;
  DateTime? _spotsCacheTime;
  
  static const Duration _cacheValidDuration = Duration(minutes: 10);

  Future<FishingStatistics> fetchStatistics({String? timeRange}) async {
    if (_cachedStatistics != null && 
        _statisticsCacheTime != null && 
        DateTime.now().difference(_statisticsCacheTime!) < _cacheValidDuration) {
      return _cachedStatistics!;
    }

    try {
      final statistics = await _fishingStatisticsApi.fetchFishingStatistics(
        timeRange: timeRange ?? 'week',
      );
      _cachedStatistics = statistics;
      _statisticsCacheTime = DateTime.now();
      return statistics;
    } catch (e) {
      if (_cachedStatistics != null) {
        return _cachedStatistics!;
      }
      
      // Fallback data
      return FishingStatistics(
        totalCatch: 156,
        totalTrips: 32,
        totalWeight: 45.2,
        timeRange: timeRange ?? 'week',
        lastUpdated: DateTime.now(),
      );
    }
  }

  Future<List<TripRecord>> fetchTripRecords({String? timeRange}) async {
    if (_cachedTripRecords != null && 
        _tripsCacheTime != null && 
        DateTime.now().difference(_tripsCacheTime!) < _cacheValidDuration) {
      return _cachedTripRecords!;
    }

    try {
      final records = await _fishingStatisticsApi.fetchTripRecords(
        timeRange: timeRange ?? 'week',
        limit: 20,
      );
      _cachedTripRecords = records;
      _tripsCacheTime = DateTime.now();
      return records;
    } catch (e) {
      if (_cachedTripRecords != null) {
        return _cachedTripRecords!;
      }
      
      // Fallback data
      return [
        const TripRecord(
          id: 1,
          date: '2024-01-15',
          location: '东湖公园',
          duration: '6小时',
          catchCount: 12,
          result: '良好',
          weight: 3.2,
          fishTypes: ['鲫鱼', '鲤鱼'],
        ),
        const TripRecord(
          id: 2,
          date: '2024-01-12',
          location: '西山水库',
          duration: '4小时',
          catchCount: 8,
          result: '一般',
          weight: 2.1,
          fishTypes: ['草鱼'],
        ),
        const TripRecord(
          id: 3,
          date: '2024-01-10',
          location: '南河钓场',
          duration: '5小时',
          catchCount: 15,
          result: '优秀',
          weight: 4.8,
          fishTypes: ['鲫鱼', '鲤鱼', '草鱼'],
        ),
      ];
    }
  }

  Future<List<EquipmentUsage>> fetchEquipmentStats({String? category}) async {
    if (_cachedEquipmentStats != null && 
        _equipmentCacheTime != null && 
        DateTime.now().difference(_equipmentCacheTime!) < _cacheValidDuration) {
      return _cachedEquipmentStats!;
    }

    try {
      final equipment = await _fishingStatisticsApi.fetchEquipmentStatistics(
        category: category,
      );
      _cachedEquipmentStats = equipment;
      _equipmentCacheTime = DateTime.now();
      return equipment;
    } catch (e) {
      if (_cachedEquipmentStats != null) {
        return _cachedEquipmentStats!;
      }
      
      // Fallback data
      return [
        EquipmentUsage(
          id: 1,
          name: '碳素竿3.6m',
          category: '鱼竿',
          usagePercentage: 85.0,
          usageCount: 28,
          lastUsed: DateTime.now().subtract(const Duration(days: 2)),
        ),
        EquipmentUsage(
          id: 2,
          name: '0.8号主线',
          category: '鱼线',
          usagePercentage: 90.0,
          usageCount: 32,
          lastUsed: DateTime.now().subtract(const Duration(days: 1)),
        ),
        EquipmentUsage(
          id: 3,
          name: '3号袖钩',
          category: '鱼钩',
          usagePercentage: 80.0,
          usageCount: 25,
          lastUsed: DateTime.now().subtract(const Duration(days: 3)),
        ),
      ];
    }
  }

  Future<List<CatchData>> fetchCatchTrend({String? timeRange}) async {
    if (_cachedCatchTrend != null && 
        _trendCacheTime != null && 
        DateTime.now().difference(_trendCacheTime!) < _cacheValidDuration) {
      return _cachedCatchTrend!;
    }

    try {
      final trend = await _fishingStatisticsApi.fetchCatchTrend(
        timeRange: timeRange ?? 'week',
      );
      _cachedCatchTrend = trend;
      _trendCacheTime = DateTime.now();
      return trend;
    } catch (e) {
      if (_cachedCatchTrend != null) {
        return _cachedCatchTrend!;
      }
      
      // Fallback data  
      return const [
        CatchData(date: '周一', count: 3, weight: 1.2),
        CatchData(date: '周二', count: 7, weight: 2.8),
        CatchData(date: '周三', count: 5, weight: 2.1),
        CatchData(date: '周四', count: 12, weight: 4.5),
        CatchData(date: '周五', count: 8, weight: 3.2),
        CatchData(date: '周六', count: 15, weight: 5.8),
        CatchData(date: '周日', count: 10, weight: 3.9),
      ];
    }
  }

  Future<List<FishSpeciesData>> fetchFishSpeciesDistribution({String? timeRange}) async {
    if (_cachedFishSpecies != null && 
        _speciesCacheTime != null && 
        DateTime.now().difference(_speciesCacheTime!) < _cacheValidDuration) {
      return _cachedFishSpecies!;
    }

    try {
      final species = await _fishingStatisticsApi.fetchFishSpeciesDistribution(
        timeRange: timeRange ?? 'week',
      );
      _cachedFishSpecies = species;
      _speciesCacheTime = DateTime.now();
      return species;
    } catch (e) {
      if (_cachedFishSpecies != null) {
        return _cachedFishSpecies!;
      }
      
      // Fallback data
      return const [
        FishSpeciesData(species: '鲫鱼', count: 62, percentage: 40.0, color: '#2196F3'),
        FishSpeciesData(species: '鲤鱼', count: 47, percentage: 30.0, color: '#4CAF50'),
        FishSpeciesData(species: '草鱼', count: 31, percentage: 20.0, color: '#FF9800'),
        FishSpeciesData(species: '其他', count: 16, percentage: 10.0, color: '#F44336'),
      ];
    }
  }

  Future<List<SpotStatistics>> fetchSpotStatistics({String? timeRange}) async {
    if (_cachedSpotStats != null && 
        _spotsCacheTime != null && 
        DateTime.now().difference(_spotsCacheTime!) < _cacheValidDuration) {
      return _cachedSpotStats!;
    }

    try {
      final spots = await _fishingStatisticsApi.fetchSpotStatistics(
        timeRange: timeRange ?? 'week',
        limit: 10,
      );
      _cachedSpotStats = spots;
      _spotsCacheTime = DateTime.now();
      return spots;
    } catch (e) {
      if (_cachedSpotStats != null) {
        return _cachedSpotStats!;
      }
      
      // Fallback data
      return const [
        SpotStatistics(
          id: 1,
          name: '东湖公园',
          tripCount: 28,
          catchCount: 85,
          successRate: 78.5,
          lastVisit: '2024-01-15',
        ),
        SpotStatistics(
          id: 2,
          name: '西山水库',
          tripCount: 15,
          catchCount: 42,
          successRate: 65.2,
          lastVisit: '2024-01-12',
        ),
        SpotStatistics(
          id: 3,
          name: '南河钓场',
          tripCount: 12,
          catchCount: 29,
          successRate: 58.9,
          lastVisit: '2024-01-10',
        ),
      ];
    }
  }

  void clearCache() {
    _cachedStatistics = null;
    _cachedTripRecords = null;
    _cachedEquipmentStats = null;
    _cachedCatchTrend = null;
    _cachedFishSpecies = null;
    _cachedSpotStats = null;
    
    _statisticsCacheTime = null;
    _tripsCacheTime = null;
    _equipmentCacheTime = null;
    _trendCacheTime = null;
    _speciesCacheTime = null;
    _spotsCacheTime = null;
  }
}