import 'package:user_app/api/session_api.dart';
import 'package:user_app/models/session/login_response.dart';

class SessionService {
  final SessionApi sessionApi;

  SessionService(this.sessionApi);

  Future<LoginResponse> login(String phoneNumber, String password) {
    return sessionApi.login(phoneNumber, password);
  }

  Future<LoginResponse> register(
    String phoneNumber,
    String verificationCode,
    String password,
    String confirmPassword,
  ) {
    return sessionApi.register(
      phoneNumber,
      verificationCode,
      password,
      confirmPassword,
    );
  }

  Future<LoginResponse> resetPassword(
    String phoneNumber,
    String verificationCode,
    String password,
    String confirmPassword,
  ) {
    return sessionApi.resetPassword(
      phoneNumber,
      verificationCode,
      password,
      confirmPassword,
    );
  }
}
