import 'package:user_app/api/notification_api.dart';
import 'package:user_app/models/notification/notification_vo.dart';

class NotificationService {
  final NotificationApi notificationApi;

  NotificationService(this.notificationApi);

  Future<List<NotificationVo>> getNotifications({
    int page = 1,
    int pageSize = 20,
    String? type,
  }) async {
    final response = await notificationApi.getNotifications(
      page: page,
      pageSize: pageSize,
      type: type,
    );
    return response.data ?? [];
  }

  Future<int> getUnreadCount() async {
    final response = await notificationApi.getUnreadCount();
    return response.data ?? 0;
  }

  Future<void> markAsRead(int notificationId) async {
    await notificationApi.markAsRead(notificationId);
  }

  Future<void> markAllAsRead() async {
    await notificationApi.markAllAsRead();
  }

  Future<void> deleteNotification(int notificationId) async {
    await notificationApi.deleteNotification(notificationId);
  }

  Future<void> clearAllNotifications() async {
    await notificationApi.clearAllNotifications();
  }
}