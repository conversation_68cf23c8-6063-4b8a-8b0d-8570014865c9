import 'package:flutter/foundation.dart';
import 'package:algoliasearch/algoliasearch_lite.dart';
import 'package:user_app/models/search/search_models.dart';
import 'package:user_app/config/algolia_config.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import 'dart:convert';

/// Algolia 搜索服务
///
/// 基于 Algolia 的高性能搜索服务：
/// 1. 实时搜索建议
/// 2. 分面搜索和过滤
/// 3. 高亮显示
/// 4. 地理位置搜索
/// 5. 个性化搜索
class AlgoliaSearchService {
  late final SearchClient _client;

  // 搜索历史和热门关键词（本地存储）
  final List<String> _searchHistory = [];
  final List<String> _hotKeywords = [];

  // 防抖Timer
  Timer? _debounceTimer;
  static const Duration _debounceDelay = Duration(milliseconds: 300);

  // 本地存储键
  static const String _searchHistoryKey = 'search_history';
  static const String _hotKeywordsKey = 'hot_keywords';

  AlgoliaSearchService() {
    _initializeAlgolia();
    _loadLocalData();
  }

  void _initializeAlgolia() {
    // 从依赖注入容器获取已配置的 SearchClient
    _client = getIt<SearchClient>();
  }

  /// 从本地存储加载数据
  Future<void> _loadLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 加载搜索历史
      final historyJson = prefs.getString(_searchHistoryKey);
      if (historyJson != null) {
        final historyList = jsonDecode(historyJson) as List;
        _searchHistory.clear();
        _searchHistory.addAll(historyList.cast<String>());
      }

      // 加载热门关键词
      final hotKeywordsJson = prefs.getString(_hotKeywordsKey);
      if (hotKeywordsJson != null) {
        final keywordsList = jsonDecode(hotKeywordsJson) as List;
        _hotKeywords.clear();
        _hotKeywords.addAll(keywordsList.cast<String>());
      }
    } catch (e) {
      debugPrint('加载本地搜索数据失败: $e');
    }
  }

  /// 保存搜索历史到本地存储
  Future<void> _saveSearchHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_searchHistoryKey, jsonEncode(_searchHistory));
    } catch (e) {
      debugPrint('保存搜索历史失败: $e');
    }
  }

  /// 保存热门关键词到本地存储
  Future<void> _saveHotKeywords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_hotKeywordsKey, jsonEncode(_hotKeywords));
    } catch (e) {
      debugPrint('保存热门关键词失败: $e');
    }
  }

  /// 综合搜索
  Future<SearchPage<SearchResultVO>> search(
    String keyword, {
    String type = 'all',
    String sortBy = 'relevance',
    int page = 1,
    int size = 20,
    String? province,
    String? city,
    int? dayRange,
  }) async {
    if (keyword.trim().isEmpty) {
      return SearchPage<SearchResultVO>(
        records: [],
        total: 0,
        size: size,
        current: page,
      );
    }

    try {
      // 记录搜索历史
      if (page == 1) {
        await addToSearchHistory(keyword);
      }

      List<SearchResultVO> allResults = [];
      int totalCount = 0;

      if (type == 'all') {
        // 并行搜索所有类型
        final futures = await Future.wait([
          _searchInIndex(_getMomentsIndexName(sortBy), keyword, 'moment', page,
              size, province, city, dayRange),
          _searchInIndex(AlgoliaConfig.usersIndex, keyword, 'user', page, size,
              province, city, dayRange),
          _searchInIndex(AlgoliaConfig.fishingSpotsIndex, keyword, 'spot', page,
              size, province, city, dayRange),
        ]);

        // 合并结果
        for (final results in futures) {
          allResults.addAll(results);
        }

        // 根据排序类型排序
        _sortResults(allResults, sortBy);

        // 分页处理
        final startIndex = (page - 1) * size;
        final endIndex = startIndex + size;
        final paginatedResults = allResults.length > startIndex
            ? allResults.sublist(
                startIndex, endIndex.clamp(0, allResults.length))
            : <SearchResultVO>[];

        totalCount = allResults.length;
        allResults = paginatedResults;
      } else {
        // 单类型搜索
        String indexName;
        switch (type) {
          case 'moment':
            indexName = _getMomentsIndexName(sortBy);
            break;
          case 'user':
            indexName = AlgoliaConfig.usersIndex;
            break;
          case 'spot':
            indexName = AlgoliaConfig.fishingSpotsIndex;
            break;
          default:
            indexName = _getMomentsIndexName(sortBy);
        }

        allResults = await _searchInIndex(
            indexName, keyword, type, page, size, province, city, dayRange);
        totalCount = allResults.length;
      }

      return SearchPage<SearchResultVO>(
        records: allResults,
        total: totalCount,
        size: size,
        current: page,
      );
    } catch (e) {
      debugPrint('Algolia 搜索失败: $e');
      return SearchPage<SearchResultVO>(
        records: [],
        total: 0,
        size: size,
        current: page,
      );
    }
  }

  /// 根据排序类型获取动态索引名称
  String _getMomentsIndexName(String sortBy) {
    switch (sortBy) {
      case 'time':
        return 'moments_by_createdAt_desc';
      case 'hot':
        return 'moments_by_hotScore_desc';
      case 'relevance':
      default:
        return AlgoliaConfig.momentsIndex; // 主索引
    }
  }

  /// 根据排序类型对结果进行排序
  void _sortResults(List<SearchResultVO> results, String sortBy) {
    switch (sortBy) {
      case 'time':
        results.sort((a, b) => (b.createTime ?? DateTime(1970))
            .compareTo(a.createTime ?? DateTime(1970)));
        break;
      case 'hot':
        results.sort((a, b) {
          // 计算热度分数：点赞*3 + 评论*5 + 浏览*0.1
          final aHotScore = (a.likeCount ?? 0) * 3 +
              (a.commentCount ?? 0) * 5 +
              (a.viewCount ?? 0) * 0.1;
          final bHotScore = (b.likeCount ?? 0) * 3 +
              (b.commentCount ?? 0) * 5 +
              (b.viewCount ?? 0) * 0.1;
          return bHotScore.compareTo(aHotScore);
        });
        break;
      case 'relevance':
      default:
        results.sort(
            (a, b) => (b.relevanceScore ?? 0).compareTo(a.relevanceScore ?? 0));
        break;
    }
  }

  /// 在指定索引中搜索
  Future<List<SearchResultVO>> _searchInIndex(
    String indexName,
    String keyword,
    String type,
    int page,
    int size,
    String? province,
    String? city,
    int? dayRange,
  ) async {
    try {
      // 构建过滤条件
      final filters = <String>[];

      if (province != null && province.isNotEmpty) {
        filters.add('province:"$province"');
      }

      if (city != null && city.isNotEmpty) {
        filters.add('city:"$city"');
      }

      if (dayRange != null && dayRange > 0) {
        final timestamp = DateTime.now()
                .subtract(Duration(days: dayRange))
                .millisecondsSinceEpoch ~/
            1000;
        filters.add('createdAt > $timestamp');
      }

      // 创建搜索请求
      final searchRequest = SearchForHits(
        indexName: indexName,
        query: keyword,
        hitsPerPage: size,
        page: page - 1, // Algolia 使用 0 基索引
        attributesToRetrieve: AlgoliaConfig.attributesToRetrieve,
        attributesToHighlight: AlgoliaConfig.attributesToHighlight,
        highlightPreTag: AlgoliaConfig.highlightPreTag,
        highlightPostTag: AlgoliaConfig.highlightPostTag,
        facetFilters: filters.isNotEmpty ? [filters] : null,
      );

      // 执行搜索
      final response = await _client.searchIndex(request: searchRequest);

      // 转换结果并过滤无效ID
      return response.hits
          .map((hit) => _convertHitToSearchResult(hit, type))
          .where((result) => result.id > 0) // 过滤掉ID为0的无效结果
          .toList();
    } catch (e) {
      debugPrint('搜索索引 $indexName 失败: $e');
      return [];
    }
  }

  /// 将 Algolia Hit 转换为 SearchResultVO
  SearchResultVO _convertHitToSearchResult(
      Map<String, dynamic> hit, String type) {
    // 解析图片列表
    List<String>? images;
    if (hit['images'] != null) {
      if (hit['images'] is List) {
        images = List<String>.from(hit['images']);
      } else if (hit['images'] is String) {
        images = hit['images'].split(',').where((s) => s.isNotEmpty).toList();
      }
    }

    // 解析类型特定数据
    Map<String, dynamic>? typeSpecificData;
    if (hit['typeSpecificData'] != null && hit['typeSpecificData'] is Map) {
      typeSpecificData = Map<String, dynamic>.from(hit['typeSpecificData']);
    }

    // 获取ID，优先级：数据id字段 > objectID解析
    int resultId = 0;

    // 方案1：直接使用数据中的 id 字段
    if (hit['id'] != null) {
      if (hit['id'] is int) {
        resultId = hit['id'];
        debugPrint('✅ 使用数据ID字段: ${hit['id']} -> $resultId');
      } else if (hit['id'] is String) {
        resultId = int.tryParse(hit['id']) ?? 0;
        debugPrint('✅ 解析字符串ID: ${hit['id']} -> $resultId');
      }
    }

    // 方案2：如果id字段无效，尝试解析objectID
    if (resultId == 0) {
      resultId = _parseId(hit['objectID']);
      debugPrint('🔄 备用objectID解析: ${hit['objectID']} -> $resultId');
    }

    // 方案3：如果都失败，从其他可能的字段获取
    if (resultId == 0) {
      // 检查是否有数字形式的其他ID字段
      for (String field in ['spot_id', 'moment_id', 'user_id']) {
        if (hit[field] != null) {
          if (hit[field] is int) {
            resultId = hit[field];
            debugPrint('🎯 使用$field字段: ${hit[field]} -> $resultId');
            break;
          }
        }
      }
    }

    return SearchResultVO(
      id: resultId,
      type: type,
      title: _extractTitle(hit, type),
      content: hit['content'] ?? hit['description'] ?? hit['bio'],
      authorName: hit['authorName'] ?? hit['name'],
      authorAvatar: hit['authorAvatar'] ?? hit['avatarUrl'],
      coverImage: hit['coverImage'],
      images: images,
      location: hit['location'] ?? hit['address'],
      createTime: _parseDateTime(hit['createdAt']),
      likeCount: hit['likeCount'],
      commentCount: hit['commentCount'],
      viewCount: hit['viewCount'],
      momentType: hit['momentType'],
      typeSpecificData: typeSpecificData,
      relevanceScore: 1.0, // Algolia 会自动计算相关性
      highlightContent: _extractHighlight(hit['_highlightResult']),
    );
  }

  /// 提取标题
  String _extractTitle(Map<String, dynamic> hit, String type) {
    // 优先使用后端提供的 title 字段
    if (hit['title'] != null && hit['title'].toString().isNotEmpty) {
      return hit['title'];
    }

    // 降级处理：根据类型提取标题
    switch (type) {
      case 'moment':
        final content = hit['content'] ?? '';
        return content.length > 50 ? '${content.substring(0, 50)}...' : content;
      case 'user':
        return hit['name'] ?? hit['authorName'] ?? '';
      case 'spot':
        return hit['name'] ?? '';
      default:
        return hit['name'] ?? hit['content'] ?? '';
    }
  }

  /// 解析 ID
  int _parseId(dynamic objectId) {
    debugPrint('=== _parseId 调试信息 ===');
    debugPrint('原始 objectId: $objectId');
    debugPrint('objectId 类型: ${objectId.runtimeType}');

    if (objectId is int && objectId > 0) {
      debugPrint('返回 int ID: $objectId');
      return objectId;
    }
    if (objectId is String && objectId.isNotEmpty) {
      // 处理 "moment_123", "user_456", "spot_789" 格式
      if (objectId.contains('_')) {
        final parts = objectId.split('_');
        debugPrint('分割后的部分: $parts');
        if (parts.length == 2) {
          final parsedId = int.tryParse(parts[1]) ?? 0;
          if (parsedId > 0) {
            debugPrint('解析出的ID (分割格式): $parsedId');
            return parsedId;
          }
        }
      }
      final directParsedId = int.tryParse(objectId) ?? 0;
      if (directParsedId > 0) {
        debugPrint('解析出的ID (直接解析): $directParsedId');
        return directParsedId;
      }
    }

    // 如果objectID无效，尝试从其他字段获取ID
    debugPrint('警告: objectId无效，返回默认ID: 0');
    return 0;
  }

  /// 解析日期时间
  DateTime? _parseDateTime(dynamic timestamp) {
    if (timestamp == null) return null;
    if (timestamp is int) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    }
    if (timestamp is String) {
      return DateTime.tryParse(timestamp);
    }
    return null;
  }

  /// 提取高亮内容
  String? _extractHighlight(Map<String, dynamic>? highlightResult) {
    if (highlightResult == null) return null;

    for (final key in ['title', 'content', 'name', 'description']) {
      final highlight = highlightResult[key];
      if (highlight != null && highlight['value'] != null) {
        return highlight['value'];
      }
    }
    return null;
  }

  /// 搜索建议（带防抖）
  Future<List<SearchSuggestionVO>> getSearchSuggestions(
    String keyword, {
    int limit = 10,
  }) async {
    if (keyword.trim().isEmpty) {
      return _getDefaultSuggestions(limit);
    }

    final completer = Completer<List<SearchSuggestionVO>>();

    _debounceTimer?.cancel();
    _debounceTimer = Timer(_debounceDelay, () async {
      try {
        final suggestions = <SearchSuggestionVO>[];

        // 从不同索引获取建议
        final futures = await Future.wait([
          _getSuggestionsFromIndex(
              AlgoliaConfig.momentsIndex, keyword, 'moment'),
          _getSuggestionsFromIndex(AlgoliaConfig.usersIndex, keyword, 'user'),
          _getSuggestionsFromIndex(
              AlgoliaConfig.fishingSpotsIndex, keyword, 'spot'),
        ]);

        for (final indexSuggestions in futures) {
          suggestions.addAll(indexSuggestions);
        }

        // 按相关性排序并限制数量
        suggestions.sort((a, b) => (b.count ?? 0).compareTo(a.count ?? 0));
        final limitedSuggestions = suggestions.take(limit).toList();

        completer.complete(limitedSuggestions);
      } catch (e) {
        debugPrint('获取 Algolia 搜索建议失败: $e');
        completer.complete(_getDefaultSuggestions(limit));
      }
    });

    return completer.future;
  }

  /// 从索引获取搜索建议
  Future<List<SearchSuggestionVO>> _getSuggestionsFromIndex(
    String indexName,
    String keyword,
    String type,
  ) async {
    try {
      debugPrint('=== 获取搜索建议 ===');
      debugPrint('索引: $indexName');
      debugPrint('关键词: $keyword');
      debugPrint('类型: $type');

      // 如果是空查询，先尝试获取一些示例数据来检查索引状态
      if (keyword.trim().isEmpty || keyword == '*') {
        debugPrint('空查询，获取索引示例数据...');
        try {
          final testRequest = SearchForHits(
            indexName: indexName,
            query: '', // 空查询获取前几条记录
            hitsPerPage: 3,
            attributesToRetrieve: ['name', 'title', 'content', 'id'],
          );
          final testResponse = await _client.searchIndex(request: testRequest);
          debugPrint('索引 $indexName 总记录数: ${testResponse.nbHits}');
          debugPrint('前3条记录: ${testResponse.hits}');
        } catch (e) {
          debugPrint('获取索引示例数据失败: $e');
        }
      }

      // 根据索引类型优化搜索属性
      List<String> searchAttributes;
      if (indexName == AlgoliaConfig.fishingSpotsIndex) {
        // 钓点搜索：包含鱼类信息
        searchAttributes = [
          'name',
          'title',
          'content',
          'description',
          'fish_type_list',
          'fishTypes',
          'tags',
          'address',
          'province',
          'city',
          'county'
        ];
      } else if (indexName == AlgoliaConfig.momentsIndex) {
        // 动态搜索：包含内容和标签
        searchAttributes = [
          'name',
          'title',
          'content',
          'description',
          'tags',
          'momentType',
          'location'
        ];
      } else {
        // 用户搜索：基本信息
        searchAttributes = ['name', 'title', 'content', 'bio', 'description'];
      }

      final searchRequest = SearchForHits(
        indexName: indexName,
        query: keyword,
        hitsPerPage: 5,
        attributesToRetrieve: searchAttributes,
        // 注意：这里不设置restrictSearchableAttributes，让Algolia使用索引的默认配置
        // 这样可以搜索所有已配置为可搜索的字段
      );

      final response = await _client.searchIndex(request: searchRequest);
      debugPrint('索引 $indexName 搜索结果数量: ${response.nbHits}');
      debugPrint('返回的命中数: ${response.hits.length}');

      final suggestions = <SearchSuggestionVO>[];

      for (final hit in response.hits) {
        // 提取基本建议
        final basicSuggestion =
            hit['name'] ?? hit['title'] ?? hit['content'] ?? '';
        if (basicSuggestion.isNotEmpty) {
          suggestions.add(SearchSuggestionVO(
            suggestion: basicSuggestion,
            type: type,
            count: response.nbHits,
          ));
          debugPrint('基本建议: $basicSuggestion');
        }

        // 对于钓点，额外提取鱼类信息作为建议
        if (type == 'spot' && hit['fish_type_list'] != null) {
          try {
            final fishTypeList = hit['fish_type_list'] as List<dynamic>?;
            if (fishTypeList != null) {
              for (final fishType in fishTypeList) {
                if (fishType is Map && fishType['name'] != null) {
                  final fishName = fishType['name'].toString();
                  if (fishName.contains(keyword) && fishName.isNotEmpty) {
                    suggestions.add(SearchSuggestionVO(
                      suggestion: '$fishName (在$basicSuggestion)',
                      type: type,
                      count: response.nbHits,
                    ));
                    debugPrint('鱼类建议: $fishName (在$basicSuggestion)');
                  }
                }
              }
            }
          } catch (e) {
            debugPrint('解析鱼类信息失败: $e');
          }
        }
      }

      debugPrint('有效建议数量: ${suggestions.length}');
      return suggestions;
    } catch (e) {
      debugPrint('获取 $type 建议失败: $e');
      return [];
    }
  }

  /// 获取搜索历史
  Future<List<String>> getSearchHistory() async {
    return _searchHistory;
  }

  /// 添加到搜索历史
  Future<bool> addToSearchHistory(String keyword) async {
    if (keyword.trim().isEmpty) return false;

    // 移除重复项
    _searchHistory.remove(keyword);
    // 添加到开头
    _searchHistory.insert(0, keyword);
    // 限制历史记录数量
    if (_searchHistory.length > AlgoliaConfig.maxSearchHistory) {
      _searchHistory.removeRange(
          AlgoliaConfig.maxSearchHistory, _searchHistory.length);
    }

    // 保存到本地存储
    await _saveSearchHistory();
    return true;
  }

  /// 从搜索历史中移除指定关键词
  Future<bool> removeFromSearchHistory(String keyword) async {
    if (keyword.trim().isEmpty) return false;

    final removed = _searchHistory.remove(keyword);
    if (removed) {
      // 更新本地存储
      await _saveSearchHistory();
    }
    return removed;
  }

  /// 清除搜索历史
  Future<bool> clearSearchHistory() async {
    _searchHistory.clear();
    // 清除本地存储
    await _saveSearchHistory();
    return true;
  }

  /// 获取热门搜索关键词
  Future<List<String>> getHotKeywords() async {
    // 如果本地没有缓存，返回默认热门关键词
    if (_hotKeywords.isEmpty) {
      final defaultKeywords = [
        '鲫鱼',
        '黑坑',
        '野钓',
        '路亚',
        '海钓',
        '钓鲤鱼',
        '钓草鱼',
        '夜钓',
      ];
      _hotKeywords.addAll(defaultKeywords);
      await _saveHotKeywords();
    }
    return _hotKeywords;
  }

  /// 更新热门关键词（通常由后端API调用）
  Future<void> updateHotKeywords(List<String> keywords) async {
    _hotKeywords.clear();
    _hotKeywords.addAll(keywords);
    await _saveHotKeywords();
  }

  /// 获取默认搜索建议
  List<SearchSuggestionVO> _getDefaultSuggestions(int limit) {
    final defaultSuggestions = [
      SearchSuggestionVO(suggestion: '鲫鱼钓法', type: 'keyword'),
      SearchSuggestionVO(suggestion: '黑坑技巧', type: 'keyword'),
      SearchSuggestionVO(suggestion: '野钓装备', type: 'keyword'),
      SearchSuggestionVO(suggestion: '路亚入门', type: 'keyword'),
      SearchSuggestionVO(suggestion: '海钓攻略', type: 'keyword'),
    ];
    return defaultSuggestions.take(limit).toList();
  }

  /// 释放资源
  void dispose() {
    _debounceTimer?.cancel();
  }
}
