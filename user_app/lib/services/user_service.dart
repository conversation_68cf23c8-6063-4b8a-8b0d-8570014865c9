import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/api/user_api.dart';
import 'package:user_app/api/user_follow_api.dart';
import 'package:user_app/models/moment/user_fans_attentions_request.dart';
import 'package:user_app/models/moment/user_fans_attentions_response.dart';
import 'package:user_app/models/user.dart';

class UserService {
  final UserApi userApi;
  final UserFollowApi userFollowApi;
  final SharedPreferences sharedPreferences;

  UserService(this.userApi, this.userFollowApi, this.sharedPreferences);

  Future<User> getCurrentUser() async {
    return await userApi.getCurrentUser();
  }

  User? getCachedUser() {
    final userString = sharedPreferences.getString('user');
    if (userString == null) {
      return null;
    }

    try {
      debugPrint('🔍 [UserService] 原始用户数据长度: ${userString.length}');
      debugPrint('🔍 [UserService] 用户数据前100字符: ${userString.length > 100 ? userString.substring(0, 100) + "..." : userString}');
      
      // 解码 JSON
      dynamic decodedData = jsonDecode(userString);
      
      // 处理双重编码问题：如果解码结果是字符串，再次解码
      if (decodedData is String) {
        debugPrint('⚠️ [UserService] 检测到双重编码，进行二次解码');
        decodedData = jsonDecode(decodedData);
      }
      
      // 确保最终结果是 Map
      if (decodedData is! Map<String, dynamic>) {
        throw FormatException('解码后的数据不是 Map 类型: ${decodedData.runtimeType}');
      }
      
      Map<String, dynamic> userMap = decodedData;
      return User.fromMap(userMap);
    } catch (e) {
      // Handle decode error with more details
      debugPrint('❌ [UserService] 用户数据解码失败: $e');
      debugPrint('❌ [UserService] 问题数据: $userString');
      
      // 清除损坏的数据
      sharedPreferences.remove('user');
      debugPrint('🧹 [UserService] 已清除损坏的用户缓存数据');
      
      return null;
    }
  }

  Future<void> cacheUser(User user) async {
    try {
      String userJson = jsonEncode(user);
      debugPrint('💾 [UserService] 缓存用户数据长度: ${userJson.length}');
      
      await sharedPreferences.setString('user', userJson);
      
      // 验证存储是否成功
      final storedData = sharedPreferences.getString('user');
      if (storedData != null && storedData == userJson) {
        debugPrint('✅ [UserService] 用户数据缓存成功');
      } else {
        debugPrint('❌ [UserService] 用户数据缓存验证失败');
      }
    } catch (e) {
      debugPrint('❌ [UserService] 用户数据缓存失败: $e');
      rethrow;
    }
  }

  Future<void> cacheToken(String token) async {
    debugPrint('🔍 [UserService] 存储token到SharedPreferences实例: ${sharedPreferences.hashCode}');
    debugPrint('🔍 [UserService] 存储的token长度: ${token.length}');
    await sharedPreferences.setString('token', token);
    
    // 立即验证存储结果
    final storedToken = sharedPreferences.getString('token');
    debugPrint('🔍 [UserService] 存储后立即验证: ${storedToken != null ? "成功" : "失败"}');
  }

  String? getAccessToken() {
    return sharedPreferences.getString('token');
  }

  Future<void> logout() async {
    await sharedPreferences.remove('token');
  }

  Future<void> followUser(num userId) async {
    await userFollowApi.followUser(userId);
  }

  Future<void> unfollowUser(num userId) async {
    await userFollowApi.unfollowUser(userId);
  }

  Future<bool> isFollowingUser(num userId) async {
    return await userFollowApi.isFollowingUser(userId);
  }

  Future<User> getUserProfile(num userId) async {
    return await userApi.getUserProfile(userId);
  }

  Future<UserFansAttentionsResponse> getFans(
      UserFansAttentionsRequest request) async {
    return await userApi.getFans(request);
  }

  Future<UserFansAttentionsResponse> getAttentions(
      UserFansAttentionsRequest request) async {
    return await userApi.getAttentions(request);
  }
}
