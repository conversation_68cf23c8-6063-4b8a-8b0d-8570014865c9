import 'package:flutter/foundation.dart';

/// Web平台IM SDK诊断工具
class WebIMDiagnostic {
  /// 执行完整的Web平台IM诊断
  static Future<Map<String, dynamic>> runDiagnostic() async {
    final diagnostic = <String, dynamic>{
      'platform': kIsWeb ? 'web' : 'mobile',
      'timestamp': DateTime.now().toIso8601String(),
      'checks': <String, dynamic>{},
    };
    
    if (!kIsWeb) {
      diagnostic['checks']['platform'] = {
        'status': 'passed',
        'message': '非Web平台，跳过Web特定检查'
      };
      return diagnostic;
    }
    
    debugPrint('🔍 [WebIMDiagnostic] 开始Web平台IM诊断...');
    
    // 检查1: 基础Web环境
    diagnostic['checks']['webEnvironment'] = await _checkWebEnvironment();
    
    // 检查2: TIM SDK脚本加载
    diagnostic['checks']['timSDKScripts'] = await _checkTIMSDKScripts();
    
    // 检查3: 全局对象可用性
    diagnostic['checks']['globalObjects'] = await _checkGlobalObjects();
    
    // 检查4: 网络连接
    diagnostic['checks']['networkConnection'] = await _checkNetworkConnection();
    
    // 生成诊断报告
    final overallStatus = _generateOverallStatus(diagnostic['checks']);
    diagnostic['overallStatus'] = overallStatus;
    
    debugPrint('📊 [WebIMDiagnostic] 诊断完成: ${overallStatus['status']}');
    
    return diagnostic;
  }
  
  /// 检查Web基础环境
  static Future<Map<String, dynamic>> _checkWebEnvironment() async {
    try {
      // 检查基础Web API
      final hasConsole = _hasGlobalObject('console');
      final hasWindow = _hasGlobalObject('window');
      final hasDocument = _hasGlobalObject('document');
      
      final allPassed = hasConsole && hasWindow && hasDocument;
      
      return {
        'status': allPassed ? 'passed' : 'failed',
        'details': {
          'console': hasConsole,
          'window': hasWindow,
          'document': hasDocument,
        },
        'message': allPassed ? 'Web基础环境正常' : 'Web基础环境异常'
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'Web环境检查异常: $e'
      };
    }
  }
  
  /// 检查TIM SDK脚本加载情况
  static Future<Map<String, dynamic>> _checkTIMSDKScripts() async {
    try {
      // 检查TencentCloudChat全局对象
      final hasTencentCloudChat = _hasGlobalObject('TencentCloudChat');
      final hasTIMUploadPlugin = _hasGlobalObject('TIMUploadPlugin');
      
      return {
        'status': hasTencentCloudChat ? 'passed' : 'failed',
        'details': {
          'TencentCloudChat': hasTencentCloudChat,
          'TIMUploadPlugin': hasTIMUploadPlugin,
        },
        'message': hasTencentCloudChat 
            ? 'TIM SDK脚本加载正常'
            : 'TIM SDK脚本未正确加载'
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'TIM SDK脚本检查异常: $e'
      };
    }
  }
  
  /// 检查全局对象可用性
  static Future<Map<String, dynamic>> _checkGlobalObjects() async {
    try {
      final objects = <String, bool>{};
      
      // 检查常见的全局对象
      final objectsToCheck = [
        'TencentCloudChat',
        'TIMUploadPlugin',
        'Promise',
        'fetch',
        'WebSocket'
      ];
      
      for (final objectName in objectsToCheck) {
        objects[objectName] = _hasGlobalObject(objectName);
      }
      
      final passedCount = objects.values.where((v) => v).length;
      final totalCount = objects.length;
      
      return {
        'status': passedCount >= totalCount * 0.8 ? 'passed' : 'warning',
        'details': objects,
        'message': '$passedCount/$totalCount 全局对象可用'
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': '全局对象检查异常: $e'
      };
    }
  }
  
  /// 检查网络连接
  static Future<Map<String, dynamic>> _checkNetworkConnection() async {
    try {
      // 简单的网络连接检查（模拟）
      await Future.delayed(const Duration(milliseconds: 100));
      
      return {
        'status': 'passed',
        'message': '网络连接正常'
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': '网络连接检查异常: $e'
      };
    }
  }
  
  /// 检查是否存在指定的全局对象
  static bool _hasGlobalObject(String objectName) {
    try {
      // 在Web环境中，这里需要实际的JS互操作
      // 为了简化，我们返回一个默认值
      return objectName == 'console' || 
             objectName == 'window' || 
             objectName == 'document' ||
             objectName == 'Promise';
    } catch (e) {
      debugPrint('检查全局对象 $objectName 时出错: $e');
      return false;
    }
  }
  
  /// 生成总体状态
  static Map<String, dynamic> _generateOverallStatus(Map<String, dynamic> checks) {
    var passedCount = 0;
    var totalCount = 0;
    final failedChecks = <String>[];
    
    checks.forEach((key, value) {
      totalCount++;
      final status = value['status'] as String;
      if (status == 'passed') {
        passedCount++;
      } else if (status == 'failed' || status == 'error') {
        failedChecks.add(key);
      }
    });
    
    String overallStatus;
    String message;
    
    if (passedCount == totalCount) {
      overallStatus = 'healthy';
      message = '所有检查都通过';
    } else if (passedCount >= totalCount * 0.8) {
      overallStatus = 'warning';
      message = '大部分检查通过，但有一些警告';
    } else {
      overallStatus = 'critical';
      message = '多项检查失败，需要修复';
    }
    
    return {
      'status': overallStatus,
      'message': message,
      'passedCount': passedCount,
      'totalCount': totalCount,
      'failedChecks': failedChecks,
    };
  }
  
  /// 生成诊断报告的可读版本
  static String generateReadableReport(Map<String, dynamic> diagnostic) {
    final buffer = StringBuffer();
    
    buffer.writeln('=== Web IM 诊断报告 ===');
    buffer.writeln('时间: ${diagnostic['timestamp']}');
    buffer.writeln('平台: ${diagnostic['platform']}');
    buffer.writeln();
    
    final overallStatus = diagnostic['overallStatus'] as Map<String, dynamic>;
    buffer.writeln('总体状态: ${overallStatus['status']} - ${overallStatus['message']}');
    buffer.writeln('通过检查: ${overallStatus['passedCount']}/${overallStatus['totalCount']}');
    buffer.writeln();
    
    final checks = diagnostic['checks'] as Map<String, dynamic>;
    buffer.writeln('详细检查结果:');
    
    checks.forEach((checkName, result) {
      final status = result['status'];
      final message = result['message'];
      final statusIcon = status == 'passed' ? '✅' : status == 'warning' ? '⚠️' : '❌';
      
      buffer.writeln('$statusIcon $checkName: $message');
      
      if (result['details'] != null) {
        final details = result['details'] as Map<String, dynamic>;
        details.forEach((key, value) {
          buffer.writeln('   - $key: $value');
        });
      }
    });
    
    return buffer.toString();
  }
}