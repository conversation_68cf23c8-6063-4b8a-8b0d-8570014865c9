import 'package:flutter/foundation.dart';
import 'package:user_app/api/search_api.dart';
import 'package:user_app/models/search/search_models.dart';
import 'dart:async';

/// 增强的搜索服务
///
/// 基于后端MySQL的智能搜索服务：
/// 1. 实时搜索建议
/// 2. 搜索历史管理
/// 3. 热门搜索展示
/// 4. 防抖和缓存优化
class SearchService {
  final SearchApi _searchApi;

  // 搜索缓存
  final Map<String, SearchPage<SearchResultVO>> _searchCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // 防抖Timer
  Timer? _debounceTimer;
  static const Duration _debounceDelay = Duration(milliseconds: 500);

  SearchService(this._searchApi);

  /// 综合搜索
  Future<SearchPage<SearchResultVO>> search(
    String keyword, {
    String type = 'all',
    String sortBy = 'relevance',
    int page = 1,
    int size = 10,
    String? province,
    String? city,
    int? dayRange,
    String? momentType,
    bool includeHighlight = true,
  }) async {
    if (keyword.trim().isEmpty) {
      return SearchPage<SearchResultVO>(
        records: [],
        total: 0,
        size: size,
        current: page,
      );
    }

    // 构建搜索请求
    final request = SearchRequest(
      keyword: keyword,
      type: type,
      sortBy: sortBy,
      page: page,
      size: size,
      province: province,
      city: city,
      dayRange: dayRange,
      momentType: momentType,
      includeHighlight: includeHighlight,
    );

    // 检查缓存
    final cacheKey = _buildCacheKey(request);
    if (_isCacheValid(cacheKey)) {
      return _searchCache[cacheKey]!;
    }

    try {
      // 记录搜索行为
      if (page == 1) {
        _recordSearchAsync(keyword);
      }

      final response = await _searchApi.search(request);

      // 缓存结果
      _searchCache[cacheKey] = response;
      _cacheTimestamps[cacheKey] = DateTime.now();

      return response;
    } catch (e) {
      debugPrint('搜索失败: $e');
      return SearchPage<SearchResultVO>(
        records: [],
        total: 0,
        size: size,
        current: page,
      );
    }
  }

  /// 搜索建议（带防抖）
  Future<List<SearchSuggestionVO>> getSearchSuggestions(
    String keyword, {
    int limit = 10,
  }) async {
    if (keyword.trim().isEmpty || keyword.length < 2) {
      return [];
    }

    final completer = Completer<List<SearchSuggestionVO>>();

    _debounceTimer?.cancel();
    _debounceTimer = Timer(_debounceDelay, () async {
      try {
        final response =
            await _searchApi.getSearchSuggestions(keyword, limit: limit);

        completer.complete(response);
      } catch (e) {
        debugPrint('获取搜索建议失败: $e');
        completer.complete([]);
      }
    });

    return completer.future;
  }

  /// 获取搜索历史
  Future<List<String>> getSearchHistory() async {
    try {
      final response = await _searchApi.getSearchHistory();
      return response;
    } catch (e) {
      debugPrint('获取搜索历史失败: $e');
      return [];
    }
  }

  /// 清除搜索历史
  Future<bool> clearSearchHistory() async {
    try {
      await _searchApi.clearSearchHistory();
      return true;
    } catch (e) {
      debugPrint('清除搜索历史失败: $e');
      return false;
    }
  }

  /// 获取热门搜索
  Future<List<String>> getHotSearchKeywords({int limit = 10}) async {
    try {
      final response = await _searchApi.getHotSearchKeywords(limit: limit);
      return response;
    } catch (e) {
      debugPrint('获取热门搜索失败: $e');
      return [];
    }
  }

  /// 获取热门关键词（别名方法）
  Future<List<String>> getHotKeywords({int limit = 10}) async {
    return getHotSearchKeywords(limit: limit);
  }

  /// 添加到搜索历史
  Future<void> addToSearchHistory(String keyword) async {
    // 这里可以实现本地存储或调用API
    debugPrint('添加搜索历史: $keyword');
  }

  /// 从搜索历史中移除
  Future<void> removeFromSearchHistory(String keyword) async {
    // 这里可以实现本地存储或调用API
    debugPrint('移除搜索历史: $keyword');
  }

  /// 异步记录搜索行为
  void _recordSearchAsync(String keyword) {
    _searchApi.recordSearch(keyword);
  }

  /// 构建缓存键
  String _buildCacheKey(SearchRequest request) {
    return '${request.keyword}_${request.type}_${request.sortBy}_${request.page}_${request.size}_${request.province}_${request.city}_${request.dayRange}_${request.momentType}';
  }

  /// 检查缓存是否有效
  bool _isCacheValid(String cacheKey) {
    if (!_searchCache.containsKey(cacheKey) ||
        !_cacheTimestamps.containsKey(cacheKey)) {
      return false;
    }

    final cacheTime = _cacheTimestamps[cacheKey]!;
    return DateTime.now().difference(cacheTime) < _cacheExpiry;
  }

  /// 销毁资源
  void dispose() {
    _debounceTimer?.cancel();
    _searchCache.clear();
    _cacheTimestamps.clear();
  }
}
