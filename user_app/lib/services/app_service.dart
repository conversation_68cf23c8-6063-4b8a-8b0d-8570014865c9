import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/services/chat_service.dart';
import 'package:user_app/services/comment_service.dart';
import 'package:user_app/services/map_service.dart';
import 'package:user_app/services/moment_service.dart';
import 'package:user_app/services/oss_service.dart';
import 'package:user_app/services/search_service.dart';
import 'package:user_app/services/session_service.dart';
import 'package:user_app/services/sms_service.dart';
import 'package:user_app/services/statistics_service.dart';
import 'package:user_app/services/user_service.dart';
import 'package:user_app/services/notification_service.dart';

class AppServices {
  final UserService userService;
  final OssService ossService;
  final CommentService commentService;
  final MomentService momentService;
  final SessionService sessionService;
  final SmsService smsService;
  final StatisticsService statisticsService;
  final SearchService searchService;
  final ChatService chatService;
  final MapService mapService;
  final FishingSpotService fishingSpotService;
  final NotificationService notificationService;

  AppServices({
    required this.userService,
    required this.ossService,
    required this.commentService,
    required this.momentService,
    required this.sessionService,
    required this.smsService,
    required this.statisticsService,
    required this.searchService,
    required this.chatService,
    required this.mapService,
    required this.fishingSpotService,
    required this.notificationService,
  });
}
