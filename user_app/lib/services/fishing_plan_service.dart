import 'package:user_app/api/fishing_plan_api.dart';
import 'package:user_app/models/fishing_plan/fishing_plan.dart';

class FishingPlanService {
  final FishingPlanApi _fishingPlanApi;

  FishingPlanService(this._fishingPlanApi);

  // Cache for plans
  List<FishingPlan>? _cachedUpcomingPlans;
  List<FishingPlan>? _cachedCompletedPlans;
  DateTime? _upcomingCacheTime;
  DateTime? _completedCacheTime;
  static const Duration _cacheValidDuration = Duration(minutes: 3);

  Future<List<FishingPlan>> fetchUpcomingPlans() async {
    // Check cache first
    if (_cachedUpcomingPlans != null && 
        _upcomingCacheTime != null && 
        DateTime.now().difference(_upcomingCacheTime!) < _cacheValidDuration) {
      return _cachedUpcomingPlans!;
    }

    try {
      final plans = await _fishingPlanApi.fetchFishingPlans(status: 0); // 0 = upcoming
      _cachedUpcomingPlans = plans.where((plan) => plan.isUpcoming).toList();
      _upcomingCacheTime = DateTime.now();
      return _cachedUpcomingPlans!;
    } catch (e) {
      // Return cached data if available, otherwise provide fallback
      if (_cachedUpcomingPlans != null) {
        return _cachedUpcomingPlans!;
      }
      
      // Fallback mock data
      return _getMockUpcomingPlans();
    }
  }

  Future<List<FishingPlan>> fetchCompletedPlans() async {
    // Check cache first
    if (_cachedCompletedPlans != null && 
        _completedCacheTime != null && 
        DateTime.now().difference(_completedCacheTime!) < _cacheValidDuration) {
      return _cachedCompletedPlans!;
    }

    try {
      final plans = await _fishingPlanApi.fetchFishingPlans(status: 1); // 1 = completed
      _cachedCompletedPlans = plans.where((plan) => plan.isCompleted).toList();
      _completedCacheTime = DateTime.now();
      return _cachedCompletedPlans!;
    } catch (e) {
      // Return cached data if available, otherwise provide fallback
      if (_cachedCompletedPlans != null) {
        return _cachedCompletedPlans!;
      }
      
      // Fallback mock data
      return _getMockCompletedPlans();
    }
  }

  Future<FishingPlan> createPlan(FishingPlan plan) async {
    final createdPlan = await _fishingPlanApi.createFishingPlan(plan);
    
    // Clear cache to force refresh
    _clearCache();
    
    return createdPlan;
  }

  Future<void> joinPlan(int planId) async {
    await _fishingPlanApi.joinFishingPlan(planId);
    _clearCache();
  }

  Future<void> leavePlan(int planId) async {
    await _fishingPlanApi.leaveFishingPlan(planId);
    _clearCache();
  }

  void _clearCache() {
    _cachedUpcomingPlans = null;
    _cachedCompletedPlans = null;
    _upcomingCacheTime = null;
    _completedCacheTime = null;
  }

  // Fallback mock data
  List<FishingPlan> _getMockUpcomingPlans() {
    return [
      FishingPlan(
        id: 1,
        title: '东湖公园夜钓',
        location: '东湖公园钓场',
        date: DateTime.now().add(const Duration(days: 2)),
        time: '19:00 - 23:00',
        participants: 3,
        maxParticipants: 5,
        weather: '多云',
        temperature: '22°C - 18°C',
        description: '夜钓鲫鱼，带好装备和照明设备',
        isOwner: true,
        status: 'upcoming',
        ownerId: 1,
        ownerName: '渔夫小李',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now(),
      ),
      FishingPlan(
        id: 2,
        title: '西山水库深水钓',
        location: '西山水库',
        date: DateTime.now().add(const Duration(days: 5)),
        time: '06:00 - 12:00',
        participants: 4,
        maxParticipants: 4,
        weather: '晴天',
        temperature: '25°C - 20°C',
        description: '早起钓鲤鱼，准备长竿和玉米饵',
        isOwner: false,
        status: 'upcoming',
        ownerId: 2,
        ownerName: '钓鱼大师',
        createdAt: DateTime.now().subtract(const Duration(hours: 12)),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  List<FishingPlan> _getMockCompletedPlans() {
    return [
      FishingPlan(
        id: 3,
        title: '南河钓场聚会',
        location: '南河钓场',
        date: DateTime.now().subtract(const Duration(days: 3)),
        time: '08:00 - 16:00',
        participants: 6,
        maxParticipants: 6,
        weather: '阴天',
        temperature: '18°C - 15°C',
        description: '钓友聚会，收获颇丰',
        isOwner: true,
        status: 'completed',
        ownerId: 1,
        ownerName: '渔夫小李',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
    ];
  }
}