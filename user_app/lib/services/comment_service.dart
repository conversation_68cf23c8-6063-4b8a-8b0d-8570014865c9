import 'package:user_app/api/comment_api.dart';
import 'package:user_app/constants/vote_type.dart';
import 'package:user_app/models/comment/comment_vo.dart';

class CommentService {
  final CommentApi commentApi;

  CommentService(this.commentApi);

  Future<List<CommentVo>> fetchComments(int momentId) async {
    return commentApi.fetchComments(momentId);
  }

  Future<void> addComment(int id, String content) async {
    await commentApi.addComment(id, content);
  }

  Future<void> voteComment(num commentId, VoteType voteType) async {
    await commentApi.voteComment(commentId, voteType);
  }

  Future<void> replyToComment(num momentId, num commentId, String text) async {
    await commentApi.replyToComment(momentId, commentId, text);
  }
}
