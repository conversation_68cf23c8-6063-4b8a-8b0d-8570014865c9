import 'dart:convert';
import 'package:user_app/api/moment_api.dart';
import 'package:user_app/models/moment/coordinate.dart';
import 'package:user_app/models/moment/create_moment_model.dart';
import 'package:user_app/models/moment/moment_list_request.dart';
import 'package:user_app/models/moment/moment_response.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/moment/query_comment_most_dto.dart';
import 'package:user_app/models/moment/query_moment_hot_request.dart';
import 'package:user_app/models/moment/user_moment_status_dto.dart';

class MomentService {
  final MomentApi momentApi;

  MomentService(this.momentApi);

  Future<void> createMoment(CreateMomentModel createMomentModel) async {
    await momentApi.createMoment(createMomentModel);
  }

  Future<MomentResponse> getMoments(MomentListRequest request) async {
    return await momentApi.getMoments(request);
  }

  Future<MomentVo> getById(int momentId) async {
    return await momentApi.getById(momentId);
  }

  Future<void> likeMoment(int momentId, bool isLike) async {
    await momentApi.likeMoment(momentId, isLike);
  }

  Future<void> incrementViewCount(int momentId) async {
    await momentApi.incrementViewCount(momentId);
  }

  Future<UserMomentStatusDto> getUserMomentStatus(int momentId) async {
    return await momentApi.getUserMomentStatus(momentId);
  }

  Future<MomentResponse> fetchHotMoments(
      QueryHotMomentDto queryHotMomentDto) async {
    // Convert QueryHotMomentDto to MomentListRequest
    final request = MomentListRequest(
      pageNum: queryHotMomentDto.pageNum,
      pageSize: queryHotMomentDto.pageSize,
    );
    return await momentApi.fetchHotMoments(request);
  }

  Future<MomentResponse> fetchMostCommentedMoments(
      QueryCommentMostDto request) async {
    return await momentApi.fetchMostCommentedMoments(request);
  }

  Future<List<Coordinate>> coordinates(MomentListRequest request) async {
    return await momentApi.coordinates(request);
  }

  /// Creates a moment from a map of data
  /// This is a convenience method for creating a moment from data received from a view model
  Future<void> createMomentFromData(Map<String, dynamic> data) async {
    // Extract required fields
    final String content = data['content'] as String;
    final List<String> imageUrls = List<String>.from(data['imageUrls']);
    final String momentType = data['momentType'] as String;
    final String visibility = data['visibility'] as String;
    final Map<String, dynamic>? typeSpecificDataMap =
        data['typeSpecificData'] as Map<String, dynamic>?;

    // Convert typeSpecificData map to JSON string if not null
    final String? typeSpecificData =
        typeSpecificDataMap != null ? jsonEncode(typeSpecificDataMap) : null;

    // Create a CreateMomentModel
    final createMomentModel = CreateMomentModel(
      content: content,
      pictures: imageUrls,
      momentType: momentType,
      visibility: visibility,
      fishingSpotId: data['fishingSpotId'] as int?,
      typeSpecificData: typeSpecificData,
    );

    // Call the API
    await createMoment(createMomentModel);
  }

  Future<MomentResponse> getMomentsByFishingSpot(
      int fishingSpotId, int page, int size,
      {String? momentType}) async {
    return await momentApi.getMomentsByFishingSpot(
      fishingSpotId,
      page: page,
      size: size,
      momentType: momentType,
    );
  }

  Future<MomentResponse> getFollowingMoments(int page, int size) async {
    return await momentApi.getFollowingMoments(page, size);
  }
}
