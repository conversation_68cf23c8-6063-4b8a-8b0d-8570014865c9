/// Algolia 搜索配置
class AlgoliaConfig {
  // TODO: 在生产环境中，这些值应该从环境变量或安全配置文件中获取
  static const String appId = 'LE1PW7ZSUA';
  static const String searchApiKey = '********************************';

  // 索引名称
  static const String momentsIndex = 'moments';
  static const String usersIndex = 'users';
  static const String fishingSpotsIndex = 'fishing_spots';

  // 搜索配置
  static const int defaultHitsPerPage = 20;
  static const int maxSearchHistory = 20;
  static const int defaultSuggestionsLimit = 10;

  // 高亮标签
  static const String highlightPreTag = '<mark>';
  static const String highlightPostTag = '</mark>';

  // 搜索属性
  static const List<String> searchableAttributes = [
    'title',
    'content',
    'name',
    'description',
    'tags',
  ];

  // 高亮属性
  static const List<String> attributesToHighlight = [
    'title',
    'content',
    'name',
    'description',
  ];

  // 检索属性
  static const List<String> attributesToRetrieve = [
    'objectID',
    'id',  // 添加id字段
    'title',
    'content',
    'name',
    'description',
    'authorName',
    'authorAvatar',
    'coverImage',
    'images',
    'location',
    'province',
    'city',
    'county',  // 添加县区字段
    'address',  // 添加地址字段
    'createdAt',
    'likeCount',
    'commentCount',
    'viewCount',
    'momentType',
    'typeSpecificData',
    'tags',
    // 钓点特有字段
    'latitude',
    'longitude',
    'isOfficial',
    'rating',
    'fishTypes',
    'facilities',
    'prices',
  ];
}
