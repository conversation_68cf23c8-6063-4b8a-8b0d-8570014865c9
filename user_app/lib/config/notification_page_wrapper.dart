import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';
import 'package:user_app/screens/mine/notifications_page.dart';
import 'package:user_app/view_models/notification_view_model.dart' as vm;

class NotificationPageWrapper extends Page<void> {
  const NotificationPageWrapper({super.key});

  @override
  Route<void> createRoute(BuildContext context) {
    return MaterialPageRoute<void>(
      settings: this,
      builder: (BuildContext context) {
        return ChangeNotifierProvider<vm.NotificationViewModel>(
          create: (_) => GetIt.instance<vm.NotificationViewModel>(),
          child: const NotificationsPage(),
        );
      },
    );
  }
}
