import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/config/notification_page_wrapper.dart';
import 'package:user_app/features/auth/screens/login_page.dart';
import 'package:user_app/features/auth/screens/register_page.dart';
import 'package:user_app/features/auth/screens/reset_password_page.dart';
import 'package:user_app/features/chat/screens/chat_list_page.dart';
import 'package:user_app/features/chat/screens/followed_users_page.dart';
import 'package:user_app/features/chat/screens/simple_chat_detail_page.dart';
import 'package:user_app/features/community/screens/community_page.dart';
import 'package:user_app/features/fishing_spots/screens/fishing_spot_detail_page.dart';
import 'package:user_app/features/fishing_spots/screens/fishing_spots_page.dart';
import 'package:user_app/features/fishing_spots/screens/publish_moment_page.dart';
import 'package:user_app/features/moments/screens/moment_detail_page.dart';
import 'package:user_app/features/tips/screens/tips_page.dart';
import 'package:user_app/features/user/screens/album_page.dart';
import 'package:user_app/features/user/screens/fans_page.dart';
import 'package:user_app/features/user/screens/following_page.dart';
import 'package:user_app/features/user/screens/personal_moment_list_page.dart';
import 'package:user_app/features/user/screens/user_profile_page.dart';
import 'package:user_app/fishing_scaffold.dart';
import 'package:user_app/pages/community/community_search_page.dart';
import 'package:user_app/screens/mine/about_page.dart';
import 'package:user_app/screens/mine/blacklist_page.dart';
import 'package:user_app/screens/mine/browsing_history_page.dart';
import 'package:user_app/screens/mine/change_password_page.dart';
import 'package:user_app/screens/mine/change_phone_page.dart';
import 'package:user_app/screens/mine/edit_profile_page.dart';
import 'package:user_app/screens/mine/feedback_page.dart';
import 'package:user_app/screens/mine/help_center_page.dart';
import 'package:user_app/screens/mine/login_history_page.dart';
import 'package:user_app/screens/mine/mine_page.dart';
import 'package:user_app/screens/mine/my_bookmarks_page.dart';
import 'package:user_app/screens/mine/my_qr_code_page.dart';
import 'package:user_app/screens/mine/my_spots_page.dart';
import 'package:user_app/screens/mine/notification_settings_page.dart';
import 'package:user_app/screens/mine/privacy_settings_page.dart';
import 'package:user_app/screens/mine/profile_setting_page.dart';
import 'package:user_app/screens/msg/message_page.dart';
import 'package:user_app/screens/other_profile_page.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/widgets/fade_transition_page.dart';
import 'package:user_app/widgets/pictures_page_view.dart';
import 'package:user_app/widgets/privacy_policy_page.dart';

final rootShellNavigatorKey = GlobalKey<NavigatorState>();
final appShellNavigatorKey = GlobalKey<NavigatorState>();

class AppRouterConfig {
  static final List<String> _authRoutes = [
    AppRoutes.message,
    AppRoutes.mine,
  ];

  static final router = GoRouter(
    initialLocation: AppRoutes.fishingSpots,
    navigatorKey: rootShellNavigatorKey,
    redirect: (BuildContext context, GoRouterState state) {
      try {
        final isGoingToAuth = _authRoutes.contains(state.matchedLocation);
        final authViewModel = context.read<AuthViewModel>();
        final isAuthed = authViewModel.isUserLoggedIn();

        if (isGoingToAuth && !isAuthed) {
          return '${AppRoutes.login}?from=${state.fullPath}';
        }

        if (isAuthed && state.matchedLocation == AppRoutes.login) {
          final fromPath = state.uri.queryParameters['from'];
          return fromPath ?? AppRoutes.fishingSpots;
        }

        return null;
      } catch (e) {
        // If there's an error reading auth state (e.g., during initialization),
        // don't redirect and let the app load normally
        debugPrint('Auth redirect error: $e');
        return null;
      }
    },
    routes: <RouteBase>[
      GoRoute(
        path: '/',
        redirect: (_, __) => AppRoutes.fishingSpots,
      ),
      ShellRoute(
        navigatorKey: appShellNavigatorKey,
        builder: (context, state, child) {
          return FishingScaffold(
            selectedIndex: switch (state.uri.path) {
              var p when p.startsWith(AppRoutes.fishingSpots) => 0,
              var p when p.startsWith(AppRoutes.community) => 1,
              var p when p.startsWith(AppRoutes.tips) => 2,
              var p when p.startsWith(AppRoutes.message) => 3,
              var p when p.startsWith(AppRoutes.mine) => 4,
              _ => 0,
            },
            child: child,
          );
        },
        routes: [
          GoRoute(
            path: AppRoutes.fishingSpots,
            pageBuilder: _fadeTransitionBuilder(
              (context) => const FishingSpotsPage(),
            ),
          ),
          GoRoute(
            path: AppRoutes.community,
            pageBuilder: _fadeTransitionBuilder(
              (context) => const CommunityPage(),
            ),
          ),
          GoRoute(
            path: AppRoutes.tips,
            pageBuilder: _fadeTransitionBuilder(
              (context) => const TipsPage(),
            ),
          ),
          GoRoute(
            path: AppRoutes.message,
            pageBuilder:
                _fadeTransitionBuilder((context) => const MessagePage()),
          ),
          GoRoute(
            path: AppRoutes.mine,
            pageBuilder: _fadeTransitionBuilder((context) => const MinePage()),
          ),
        ],
      ),
      GoRoute(
        path: AppRoutes.login,
        builder: (context, state) {
          final fromPath = state.uri.queryParameters['from'];
          return Builder(builder: (context) {
            return LoginPage(fromPath: fromPath);
          });
        },
      ),
      GoRoute(
        path: AppRoutes.register,
        pageBuilder: _fadeTransitionBuilder(
          (context) => const RegisterPage(),
        ),
      ),
      GoRoute(
        path: AppRoutes.resetPassword,
        pageBuilder:
            _fadeTransitionBuilder((context) => const ResetPasswordPage()),
      ),
      GoRoute(
        path: '${AppRoutes.profile}/:userId',
        builder: (context, state) {
          final userId = state.pathParameters['userId']!;
          return Builder(builder: (context) {
            return OtherProfilePage(userId: int.parse(userId));
          });
        },
      ),
      GoRoute(
        path: AppRoutes.fansPage,
        builder: (context, state) {
          final userId = state.pathParameters['userId']!;
          return Builder(builder: (context) {
            return FansPage(userId: userId);
          });
        },
      ),
      GoRoute(
        path: AppRoutes.attentionsPage,
        builder: (context, state) {
          final userId = state.pathParameters['userId']!;
          return Builder(builder: (context) {
            return FollowingPage(userId: userId);
          });
        },
      ),
      GoRoute(
        path: AppRoutes.previewPictures,
        pageBuilder: _fadeTransitionBuilder(
          (context) => const PicturesPageView(),
        ),
      ),
      GoRoute(
        path: AppRoutes.publishMoment,
        builder: (context, state) {
          final momentType = state.extra as String?;
          return Builder(
            builder: (context) {
              return PublishMomentPage(initialMomentType: momentType);
            },
          );
        },
      ),
      GoRoute(
        path: AppRoutes.search,
        pageBuilder: _fadeTransitionBuilder(
          (context) => const CommunitySearchPage(),
        ),
      ),
      GoRoute(
        path: AppRoutes.momentDetail,
        builder: (context, state) {
          final momentData = state.extra as Map<String, dynamic>?;
          final momentId =
              momentData?['momentId'] ?? state.uri.queryParameters['momentId'];

          if (momentId != null) {
            try {
              final parsedMomentId = int.parse(momentId.toString());
              debugPrint('成功创建动态详情页面，momentId: $parsedMomentId');
              return MomentDetailPage(momentId: parsedMomentId);
            } catch (e) {
              debugPrint('解析momentId失败: $e');
              return const Scaffold(
                body: Center(child: Text('动态ID格式错误')),
              );
            }
          } else {
            debugPrint('错误: 无法获取momentId，返回错误页面');
            return const Scaffold(
              body: Center(child: Text('动态不存在')),
            );
          }
        },
      ),
      GoRoute(
        path: AppRoutes.fishingSpotDetail,
        builder: (context, state) {
          debugPrint('钓点详情路由调试信息:');
          debugPrint('- 匹配路径: ${state.matchedLocation}');
          debugPrint('- 完整路径: ${state.fullPath}');
          debugPrint('- URI: ${state.uri}');
          debugPrint('- 查询参数: ${state.uri.queryParameters}');
          debugPrint('- 传递的额外数据: ${state.extra}');

          final spotData = state.extra as Map<String, dynamic>?;
          final spotId =
              spotData?['spotId'] ?? state.uri.queryParameters['spotId'];

          if (spotId != null) {
            try {
              final parsedSpotId = int.parse(spotId.toString());
              debugPrint('成功创建钓点详情页面，spotId: $parsedSpotId');
              return FishingSpotDetailPage(spotId: parsedSpotId);
            } catch (e) {
              debugPrint('解析spotId失败: $e');
              return const Scaffold(
                body: Center(child: Text('钓点ID格式错误')),
              );
            }
          } else {
            debugPrint('错误: 无法获取spotId，返回错误页面');
            return const Scaffold(
              body: Center(child: Text('钓点不存在')),
            );
          }
        },
      ),
      GoRoute(
        path: AppRoutes.chatList,
        pageBuilder: _fadeTransitionBuilder(
          (context) => const ChatListPage(),
        ),
      ),
      GoRoute(
        path: AppRoutes.chatDetail,
        builder: (context, state) {
          final conversationID = state.pathParameters['conversationID']!;
          final chatData = state.extra as Map<String, dynamic>?;
          final conversationType = chatData?['conversationType'] ?? 1;
          final showName = chatData?['showName'] ?? '聊天';
          final userID = chatData?['userID'];

          return SimpleChatDetailPage(
            conversationID: conversationID,
            conversationType: conversationType,
            showName: showName,
            userID: userID,
          );
        },
      ),
      GoRoute(
        path: AppRoutes.followedUsers,
        pageBuilder: _fadeTransitionBuilder(
          (context) => const FollowedUsersPage(),
        ),
      ),
      GoRoute(
        path: AppRoutes.userProfile,
        builder: (context, state) {
          final userData = state.extra as Map<String, dynamic>?;
          final userId = userData?['userId'];

          if (userId != null) {
            return UserProfilePage(userId: userId);
          } else {
            return const Scaffold(
              body: Center(child: Text('用户不存在')),
            );
          }
        },
      ),
      // 添加新路由
      GoRoute(
        path: AppRoutes.profileSetting,
        builder: (context, state) => const ProfileSettingPage(),
      ),
      GoRoute(
        path: AppRoutes.notifications,
        pageBuilder: (context, state) => const NotificationPageWrapper(),
      ),
      GoRoute(
        path: AppRoutes.myBookmarks,
        builder: (context, state) => const MyBookmarksPage(),
      ),
      GoRoute(
        path: AppRoutes.mySpots,
        builder: (context, state) => const MySpotsPage(),
      ),
      GoRoute(
        path: AppRoutes.myAlbum,
        builder: (context, state) => const AlbumPage(),
      ),
      GoRoute(
        path: AppRoutes.personalMomentList,
        builder: (context, state) => const PersonalMomentListPage(),
      ),
      GoRoute(
        path: AppRoutes.browsingHistory,
        builder: (context, state) => const BrowsingHistoryPage(),
      ),
      GoRoute(
        path: AppRoutes.feedback,
        builder: (context, state) => const FeedbackPage(),
      ),
      GoRoute(
        path: AppRoutes.helpCenter,
        builder: (context, state) => const HelpCenterPage(),
      ),
      GoRoute(
        path: AppRoutes.about,
        builder: (context, state) => const AboutPage(),
      ),
      GoRoute(
        path: AppRoutes.myQrCode,
        builder: (context, state) => const MyQrCodePage(),
      ),
      GoRoute(
        path: AppRoutes.loginHistory,
        builder: (context, state) => const LoginHistoryPage(),
      ),
      // 设置相关页面
      GoRoute(
        path: AppRoutes.editProfile,
        builder: (context, state) => const EditProfilePage(),
      ),
      GoRoute(
        path: AppRoutes.changePassword,
        builder: (context, state) => const ChangePasswordPage(),
      ),
      GoRoute(
        path: AppRoutes.changePhone,
        builder: (context, state) => const ChangePhonePage(),
      ),
      GoRoute(
        path: AppRoutes.accountSecurity,
        builder: (context, state) =>
            const LoginHistoryPage(), // 使用登录历史页面作为账号安全页面
      ),
      GoRoute(
        path: AppRoutes.privacySettings,
        builder: (context, state) => const PrivacySettingsPage(),
      ),
      GoRoute(
        path: AppRoutes.blacklist,
        builder: (context, state) => const BlacklistPage(),
      ),
      GoRoute(
        path: AppRoutes.notificationSettings,
        builder: (context, state) => const NotificationSettingsPage(),
      ),
      GoRoute(
        path: AppRoutes.privacyPolicy,
        builder: (context, state) => const PrivacyPolicyPage(),
      ),
      GoRoute(
        path: AppRoutes.userAgreement,
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('用户协议页面开发中...')),
        ),
      ),
    ],
  );

  static GoRouterPageBuilder _fadeTransitionBuilder(WidgetBuilder builder) {
    return (context, state) {
      return FadeTransitionPage<dynamic>(
        key: state.pageKey,
        child: Builder(
          builder: builder,
        ),
      );
    };
  }
}
