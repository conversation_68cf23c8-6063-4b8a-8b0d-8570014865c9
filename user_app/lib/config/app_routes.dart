class AppRoutes {
  // Main navigation tabs
  static const String fishingSpots = '/fishing_spots';
  static const String publishMoment = '/publish_moment';

  static const String community = '/community';
  static const String tips = '/tips';
  static const String message = '/message';
  static const String mine = '/mine';

  // Other routes
  static const String search = '/search';
  static const String login = '/login';
  static const String register = '/register';
  static const String resetPassword = '/resetPassword';
  static const String profile = '/other_profile_page/:userId';
  static const String fansPage = '/fansPage/:userId';
  static const String attentionsPage = '/attentionsPage/:userId';

  ///预览图片
  static const String previewPictures = '/previewPictures';

  static const String momentDetail = '/momentDetail';
  static const String fishingSpotDetail = '/fishingSpotDetail';

  ///个人动态列表
  static const String personalMomentList = '/personalMomentList';

  // Chat routes
  static const String chatList = '/chat_list';
  static const String chatDetail = '/chat/simple/:conversationID';
  static const String followedUsers = '/followed_users';

  // User routes
  static const String userProfile = '/user_profile';

// 设置相关
  static const String profileSetting = '/profile-setting';
  static const String editProfile = '/edit-profile';
  static const String changePassword = '/change-password';
  static const String changePhone = '/change-phone';
  static const String accountSecurity = '/account-security';
  static const String privacySettings = '/privacy-settings';
  static const String blacklist = '/blacklist';
  static const String notificationSettings = '/notification-settings';
  static const String privacyPolicy = '/privacy-policy';
  static const String userAgreement = '/user-agreement';
  static const String myQrCode = '/my-qr-code';
  static const String loginHistory = '/login-history';

// 通知相关
  static const String notifications = '/notifications';

// 收藏相关
  static const String myBookmarks = '/my-bookmarks';

// 个人中心相关
  static const String mySpots = '/my-spots';
  static const String myAlbum = '/my-album';
  static const String browsingHistory = '/browsing-history';

// 帮助与反馈
  static const String feedback = '/feedback';
  static const String helpCenter = '/help-center';
  static const String about = '/about';
}
