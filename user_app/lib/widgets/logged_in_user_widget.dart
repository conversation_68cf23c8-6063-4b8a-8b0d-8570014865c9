import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/widgets/modern/modern_feature_grid.dart';
import 'package:user_app/widgets/modern/modern_profile_header.dart';
import 'package:user_app/widgets/modern/modern_profile_skeleton.dart';
import 'package:user_app/widgets/modern/modern_quick_actions.dart';
import 'package:user_app/widgets/modern/modern_settings_section.dart';
import 'package:user_app/widgets/modern/modern_social_insights.dart';
import 'package:user_app/widgets/modern/modern_stats_card.dart';

class LoggedInUserWidget extends StatefulWidget {
  const LoggedInUserWidget({super.key});

  @override
  State<LoggedInUserWidget> createState() => _LoggedInUserWidgetState();
}

class _LoggedInUserWidgetState extends State<LoggedInUserWidget>
    with TickerProviderStateMixin {
  late final AuthViewModel authViewModel;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      authViewModel = context.read<AuthViewModel>();

      // Start animations immediately if we have cached data
      if (authViewModel.currentUser != null) {
        _fadeController.forward();
        _scaleController.forward();
      }

      // Load fresh data
      await _loadUserData();

      // Start animations if not already started
      if (!_fadeController.isCompleted) {
        _fadeController.forward();
        _scaleController.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    await Future.wait([
      authViewModel.loadUserProfile(context),
      authViewModel.fetchStatistics(context),
      authViewModel.fetchUnreadNotifications(),
    ]);
  }

  Future<void> _handleRefresh() async {
    await _loadUserData();
  }

  Widget _buildErrorState() {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '请检查网络连接后重试',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: _loadUserData,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: const Text(
                '重新加载',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }


  @override
  Widget build(BuildContext context) {
    return Consumer<AuthViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.isLoading && viewModel.currentUser == null) {
          return const ModernProfileSkeleton();
        }

        // Show error state if data failed to load and no cached data
        if (viewModel.currentUser == null && !viewModel.isLoading) {
          return _buildErrorState();
        }

        return Scaffold(
          backgroundColor: const Color(0xFFF5F7FA),
          extendBodyBehindAppBar: true,
          body: Stack(
            children: [
              // Subtle gradient background
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFFF0F4F8),
                      const Color(0xFFE8EEF5),
                    ],
                  ),
                ),
              ),
              // Floating orbs with subtle colors
              Positioned(
                top: -100,
                right: -100,
                child: AnimatedBuilder(
                  animation: _fadeController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: 0.8 + (_fadeAnimation.value * 0.2),
                      child: Container(
                        width: 300,
                        height: 300,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              Colors.blue.withValues(alpha: 0.08),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              Positioned(
                bottom: -50,
                left: -50,
                child: AnimatedBuilder(
                  animation: _fadeController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: 0.8 + (_fadeAnimation.value * 0.2),
                      child: Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              Colors.purple.withValues(alpha: 0.06),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              // Main content
              SafeArea(
                child: RefreshIndicator(
                  onRefresh: _handleRefresh,
                  color: Colors.blue,
                  backgroundColor: Colors.white,
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      return CustomScrollView(
                        physics: const BouncingScrollPhysics(),
                        slivers: [
                          // Modern Profile Header
                          SliverToBoxAdapter(
                            child: Container(
                              constraints: BoxConstraints(
                                minHeight: 0,
                                maxWidth: constraints.maxWidth,
                              ),
                              child: FadeTransition(
                                opacity: _fadeAnimation,
                                child: const ModernProfileHeader(),
                              ),
                            ),
                          ),

                          // Stats Card
                          SliverToBoxAdapter(
                            child: Container(
                              constraints: BoxConstraints(
                                minHeight: 0,
                                maxWidth: constraints.maxWidth,
                              ),
                              child: SlideTransition(
                                position: Tween<Offset>(
                                  begin: const Offset(0, 0.3),
                                  end: Offset.zero,
                                ).animate(_fadeAnimation),
                                child: FadeTransition(
                                  opacity: _fadeAnimation,
                                  child: const ModernStatsCard(),
                                ),
                              ),
                            ),
                          ),

                          // Quick Actions
                          SliverToBoxAdapter(
                            child: Container(
                              constraints: BoxConstraints(
                                minHeight: 0,
                                maxWidth: constraints.maxWidth,
                              ),
                              child: SlideTransition(
                                position: Tween<Offset>(
                                  begin: const Offset(0, 0.3),
                                  end: Offset.zero,
                                ).animate(CurvedAnimation(
                                  parent: _fadeController,
                                  curve: const Interval(0.2, 1.0),
                                )),
                                child: FadeTransition(
                                  opacity: CurvedAnimation(
                                    parent: _fadeController,
                                    curve: const Interval(0.2, 1.0),
                                  ),
                                  child: const ModernQuickActions(),
                                ),
                              ),
                            ),
                          ),

                          // Feature Grid
                          SliverToBoxAdapter(
                            child: Container(
                              constraints: BoxConstraints(
                                minHeight: 0,
                                maxWidth: constraints.maxWidth,
                              ),
                              child: SlideTransition(
                                position: Tween<Offset>(
                                  begin: const Offset(0, 0.3),
                                  end: Offset.zero,
                                ).animate(CurvedAnimation(
                                  parent: _fadeController,
                                  curve: const Interval(0.3, 1.0),
                                )),
                                child: FadeTransition(
                                  opacity: CurvedAnimation(
                                    parent: _fadeController,
                                    curve: const Interval(0.3, 1.0),
                                  ),
                                  child: const ModernFeatureGrid(),
                                ),
                              ),
                            ),
                          ),

                          // Social Insights
                          SliverToBoxAdapter(
                            child: Container(
                              constraints: BoxConstraints(
                                minHeight: 0,
                                maxWidth: constraints.maxWidth,
                              ),
                              child: SlideTransition(
                                position: Tween<Offset>(
                                  begin: const Offset(0, 0.3),
                                  end: Offset.zero,
                                ).animate(CurvedAnimation(
                                  parent: _fadeController,
                                  curve: const Interval(0.4, 1.0),
                                )),
                                child: FadeTransition(
                                  opacity: CurvedAnimation(
                                    parent: _fadeController,
                                    curve: const Interval(0.4, 1.0),
                                  ),
                                  child: const ModernSocialInsights(),
                                ),
                              ),
                            ),
                          ),

                          // Settings Section
                          SliverToBoxAdapter(
                            child: Container(
                              constraints: BoxConstraints(
                                minHeight: 0,
                                maxWidth: constraints.maxWidth,
                              ),
                              child: SlideTransition(
                                position: Tween<Offset>(
                                  begin: const Offset(0, 0.3),
                                  end: Offset.zero,
                                ).animate(CurvedAnimation(
                                  parent: _fadeController,
                                  curve: const Interval(0.5, 1.0),
                                )),
                                child: FadeTransition(
                                  opacity: CurvedAnimation(
                                    parent: _fadeController,
                                    curve: const Interval(0.5, 1.0),
                                  ),
                                  child: const ModernSettingsSection(),
                                ),
                              ),
                            ),
                          ),

                          const SliverToBoxAdapter(
                            child: SizedBox(height: 30),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
