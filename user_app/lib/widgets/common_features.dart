import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';
import 'package:user_app/config/app_routes.dart';

class CommonFeatures extends StatelessWidget {
  const CommonFeatures({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildListTile(
            context,
            icon: Icons.settings_outlined,
            title: '设置',
            subtitle: '账号与隐私设置',
            onTap: () => context.push(AppRoutes.profileSetting),
          ),
          _buildDivider(),
          _buildListTile(
            context,
            icon: Icons.dark_mode_outlined,
            title: '主题模式',
            subtitle: '切换深色/浅色主题',
            onTap: () => _showThemeDialog(context),
          ),
          _buildDivider(),
          _buildListTile(
            context,
            icon: Icons.language_outlined,
            title: '语言设置',
            subtitle: '选择应用语言',
            onTap: () => _showLanguageDialog(context),
          ),
          _buildDivider(),
          _buildListTile(
            context,
            icon: Icons.feedback_outlined,
            title: '反馈与建议',
            subtitle: '帮助我们改进产品',
            onTap: () => context.push(AppRoutes.feedback),
          ),
          _buildDivider(),
          _buildListTile(
            context,
            icon: Icons.share_outlined,
            title: '分享给朋友',
            subtitle: '邀请好友一起来钓鱼',
            onTap: () => _shareApp(context),
          ),
          _buildDivider(),
          _buildListTile(
            context,
            icon: Icons.help_outline,
            title: '帮助中心',
            subtitle: '常见问题与使用指南',
            onTap: () => context.push(AppRoutes.helpCenter),
          ),
          _buildDivider(),
          _buildListTile(
            context,
            icon: Icons.info_outline,
            title: '关于我们',
            subtitle: '版本信息与更新日志',
            onTap: () => context.push(AppRoutes.about),
          ),
        ],
      ),
    );
  }

  Widget _buildListTile(
    BuildContext context,
    {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.blue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: Colors.blue[600], size: 24),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 13,
          color: Colors.grey[600],
        ),
      ),
      trailing:
          Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
      onTap: onTap,
    );
  }

  Widget _buildDivider() {
    return const Divider(
      height: 1,
      thickness: 0.5,
      indent: 20,
      endIndent: 20,
    );
  }

  void _shareApp(BuildContext context) {
    Share.share('快来下载我们的APP，一起享受钓鱼的乐趣吧！下载地址：https://example.com');
  }

  void _showThemeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择主题'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.light_mode),
                title: const Text('浅色主题'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('浅色主题已设置')),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.dark_mode),
                title: const Text('深色主题'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('深色主题已设置')),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.auto_mode),
                title: const Text('跟随系统'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('已设置跟随系统主题')),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择语言'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Text('🇨🇳'),
                title: const Text('简体中文'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('已切换至简体中文')),
                  );
                },
              ),
              ListTile(
                leading: const Text('🇺🇸'),
                title: const Text('English'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Language switched to English')),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
