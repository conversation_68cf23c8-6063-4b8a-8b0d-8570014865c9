// import 'package:flutter/material.dart';
// import 'package:flutter_2d_amap/flutter_2d_amap.dart';
// import 'package:user_app/core/di/injection.dart';
// import 'package:user_app/view_models/publish_moment_view_model.dart';
//
// class TakeAddress extends StatefulWidget {
//   const TakeAddress({super.key});
//
//   @override
//   State<TakeAddress> createState() => _TakeAddressState();
// }
//
// class _TakeAddressState extends State<TakeAddress> {
//   AMap2DController? _mapController;
//   String? _selectedAddress;
//   num? _selectedLatitude;
//   num? _selectedLongitude;
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('选择地址'),
//         actions: [
//           if (_selectedAddress != null)
//             TextButton(
//               onPressed: () {
//                 Navigator.pop(context);
//               },
//               child: const Text('确定'),
//             ),
//         ],
//       ),
//       body: Column(
//         children: [
//           Expanded(
//             child: AMap2DView(
//               onAMap2DViewCreated: (controller) {
//                 _mapController = controller;
//               },
//               onGetLocation: (location) {
//                 _mapController?.reGeocode(
//                   location.latitude,
//                   location.longitude,
//                 );
//                 _selectedLatitude = location.latitude;
//                 _selectedLongitude = location.longitude;
//                 getIt<PublishMomentViewModel>().latitude = location.latitude;
//                 getIt<PublishMomentViewModel>().longitude = location.longitude;
//               },
//               onReGeocode: (reGeocode) {
//                 setState(() {
//                   _selectedAddress = reGeocode.regeocode.formattedAddress;
//                 });
//                 getIt<PublishMomentViewModel>().locationController.text =
//                     reGeocode.regeocode.formattedAddress;
//               },
//               onClick: (lat, lon) {
//                 _mapController?.clearMarkers();
//                 _mapController?.addMarkers([
//                   {
//                     'latitude': lat.toString(),
//                     'longitude': lon.toString(),
//                   },
//                 ]);
//                 _mapController?.reGeocode(lat, lon);
//                 _selectedLatitude = lat;
//                 _selectedLongitude = lon;
//                 getIt<PublishMomentViewModel>().latitude = lat;
//                 getIt<PublishMomentViewModel>().longitude = lon;
//               },
//             ),
//           ),
//           if (_selectedAddress != null)
//             Container(
//               padding: const EdgeInsets.all(16),
//               color: Theme.of(context).cardColor,
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   const Text(
//                     '当前位置',
//                     style: TextStyle(
//                       fontWeight: FontWeight.bold,
//                       fontSize: 16,
//                     ),
//                   ),
//                   const SizedBox(height: 8),
//                   Text(_selectedAddress!),
//                 ],
//               ),
//             ),
//         ],
//       ),
//     );
//   }
// }
