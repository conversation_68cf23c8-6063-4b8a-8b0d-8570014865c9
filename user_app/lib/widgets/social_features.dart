import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class SocialFeatures extends StatelessWidget {
  const SocialFeatures({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.people_outline,
                  color: Colors.blue[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '社交互动',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
          ),
          _buildSocialGrid(context),
        ],
      ),
    );
  }

  Widget _buildSocialGrid(BuildContext context) {
    return Consumer<AuthViewModel>(
      builder: (context, viewModel, child) {
        // 使用真实的用户数据显示社交统计
        final user = viewModel.currentUser;
        
        final socialFeatures = [
          _SocialFeature(
            icon: Icons.favorite_outline,
            title: '获赞总数',
            value: '${user?.commentCount ?? 0}', // 使用真实的评论数作为获赞参考
            color: Colors.red,
            subtitle: '最近30天',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('点赞详情功能即将上线')),
              );
            },
          ),
          _SocialFeature(
            icon: Icons.comment_outlined,
            title: '评论互动',
            value: '${user?.commentCount ?? 0}', // 使用真实的评论数据
            color: Colors.green,
            subtitle: '最近30天',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('评论详情功能即将上线')),
              );
            },
          ),
          _SocialFeature(
            icon: Icons.share_outlined,
            title: '分享次数',
            value: '--', // 等待后端提供真实分享数据API
            color: Colors.orange,
            subtitle: '暂无数据',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('分享统计功能需要后端API支持')),
              );
            },
          ),
          _SocialFeature(
            icon: Icons.visibility_outlined,
            title: '个人主页访问',
            value: '--', // 等待后端提供真实访问数据API
            color: Colors.purple,
            subtitle: '暂无数据',
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('访问统计功能需要后端API支持')),
              );
            },
          ),
        ];

        return _buildGrid(context, socialFeatures);
      },
    );
  }

  Widget _buildGrid(BuildContext context, List<_SocialFeature> socialFeatures) {

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.only(bottom: 16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.6, // 进一步增加高度比例
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: socialFeatures.length,
        itemBuilder: (context, index) {
          final feature = socialFeatures[index];
          return _buildSocialItem(feature);
        },
      ),
    );
  }

  Widget _buildSocialItem(_SocialFeature feature) {
    return GestureDetector(
      onTap: feature.onTap,
      child: Container(
        padding: const EdgeInsets.all(8), // 减少填充
        decoration: BoxDecoration(
          color: feature.color.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: feature.color.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(4), // 减少图标容器填充
                  decoration: BoxDecoration(
                    color: feature.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    feature.icon,
                    color: feature.color,
                    size: 16,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 12,
                  color: Colors.grey[400],
                ),
              ],
            ),
            const SizedBox(height: 4), // 调整间距
            Text(
              feature.value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: feature.color,
              ),
            ),
            const SizedBox(height: 2), // 调整间距
            Text(
              feature.title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
            Text(
              feature.subtitle,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SocialFeature {
  final IconData icon;
  final String title;
  final String value;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  const _SocialFeature({
    required this.icon,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });
}