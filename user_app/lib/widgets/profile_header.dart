import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/view_models/auth_view_model.dart';
import 'package:user_app/widgets/notification_badge.dart';

class ProfileHeader extends StatelessWidget {
  const ProfileHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[400]!, Colors.blue[600]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // 头像
          GestureDetector(
            onTap: () => context.push(AppRoutes.profileSetting),
            child: <PERSON>(
              tag: 'user_avatar',
              child: Container(
                padding: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 3),
                ),
                child: Selector<AuthViewModel, String?>(
                  selector: (context, viewModel) =>
                      viewModel.currentUser?.avatarUrl,
                  builder: (context, avatarUrl, child) {
                    return CircleAvatar(
                      radius: 35,
                      backgroundImage: avatarUrl != null && avatarUrl.isNotEmpty
                          ? CachedNetworkImageProvider(
                              '$avatarUrl?x-oss-process=image/resize,m_lfit,w_200/crop,g_center/quality,Q_100',
                            )
                          : const AssetImage('assets/default_avatar.png')
                              as ImageProvider,
                    );
                  },
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),

          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Selector<AuthViewModel, String?>(
                  selector: (context, viewModel) => viewModel.currentUser?.name,
                  builder: (context, name, child) {
                    return Text(
                      name ?? '钓友',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
                const SizedBox(height: 4),
                Selector<AuthViewModel, String?>(
                  selector: (context, viewModel) =>
                      viewModel.currentUser?.introduce,
                  builder: (context, introduce, child) {
                    return Text(
                      introduce?.isNotEmpty == true
                          ? introduce!
                          : '这个人很懒，什么都没留下~',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    );
                  },
                ),
              ],
            ),
          ),

          // 快捷操作菜单
          Row(
            children: [
              // 通知图标
              Selector<AuthViewModel, int>(
                selector: (context, viewModel) => viewModel.unreadNotifications,
                builder: (context, unreadNotifications, child) {
                  return NotificationBadge(
                    count: unreadNotifications,
                    size: 20,
                    child: IconButton(
                      icon: const Icon(Icons.notifications_outlined,
                          color: Colors.white),
                      onPressed: () => context.push(AppRoutes.notifications),
                    ),
                  );
                },
              ),
              // 二维码图标
              IconButton(
                icon: const Icon(Icons.qr_code, color: Colors.white),
                onPressed: () => context.push(AppRoutes.myQrCode),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
