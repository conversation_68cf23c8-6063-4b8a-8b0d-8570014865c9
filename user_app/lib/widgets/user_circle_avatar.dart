import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class UserCircleAvatar extends StatelessWidget {
  final String? avatarUrl;
  final double radius;

  const UserCircleAvatar({super.key, this.avatarUrl, this.radius = 20});

  @override
  Widget build(BuildContext context) {
    if (avatarUrl == null || avatarUrl == "") {
      return CircleAvatar(
        radius: radius,
        backgroundImage: const AssetImage('assets/default_avatar.png'),
      );
    }

    return CircleAvatar(
      radius: radius,
      backgroundImage: CachedNetworkImageProvider(
          '$avatarUrl?x-oss-process=image/resize,m_lfit,w_200/crop,g_center/quality,Q_100'),
    );
  }
}
