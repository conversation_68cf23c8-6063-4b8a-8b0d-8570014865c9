import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class PrivacyPolicyPage extends StatefulWidget {
  const PrivacyPolicyPage({super.key});

  @override
  State<PrivacyPolicyPage> createState() => _PrivacyPolicyPageState();
}

class _PrivacyPolicyPageState extends State<PrivacyPolicyPage> {
  final ScrollController _scrollController = ScrollController();
  double _scrollProgress = 0.0;
  bool _hasScrolledToBottom = false;

  // 目录项
  final List<PolicySection> _sections = [
    PolicySection(
      id: 'intro',
      title: '引言',
      icon: Icons.info_outline,
    ),
    PolicySection(
      id: 'collect',
      title: '信息收集',
      icon: Icons.folder_open,
    ),
    PolicySection(
      id: 'use',
      title: '信息使用',
      icon: Icons.settings_applications,
    ),
    PolicySection(
      id: 'share',
      title: '信息共享',
      icon: Icons.share,
    ),
    PolicySection(
      id: 'security',
      title: '信息安全',
      icon: Icons.security,
    ),
    PolicySection(
      id: 'rights',
      title: '您的权利',
      icon: Icons.person,
    ),
    PolicySection(
      id: 'children',
      title: '儿童隐私',
      icon: Icons.child_care,
    ),
    PolicySection(
      id: 'changes',
      title: '政策变更',
      icon: Icons.update,
    ),
    PolicySection(
      id: 'contact',
      title: '联系我们',
      icon: Icons.contact_support,
    ),
  ];

  // 用于定位的GlobalKey
  final Map<String, GlobalKey> _sectionKeys = {};

  @override
  void initState() {
    super.initState();
    // 初始化section keys
    for (final section in _sections) {
      _sectionKeys[section.id] = GlobalKey();
    }

    _scrollController.addListener(_updateScrollProgress);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_updateScrollProgress);
    _scrollController.dispose();
    super.dispose();
  }

  void _updateScrollProgress() {
    if (_scrollController.hasClients) {
      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;

      setState(() {
        _scrollProgress = maxScroll > 0 ? currentScroll / maxScroll : 0;
        _hasScrolledToBottom = currentScroll >= maxScroll - 50;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('隐私政策'),
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share, size: 20),
                    SizedBox(width: 8),
                    Text('分享'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'download',
                child: Row(
                  children: [
                    Icon(Icons.download, size: 20),
                    SizedBox(width: 8),
                    Text('下载PDF'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Stack(
        children: [
          Row(
            children: [
              // 左侧目录
              if (MediaQuery.of(context).size.width > 600)
                Container(
                  width: 200,
                  color: Colors.white,
                  child: _buildTableOfContents(),
                ),

              // 右侧内容
              Expanded(
                child: Column(
                  children: [
                    // 进度条
                    LinearProgressIndicator(
                      value: _scrollProgress,
                      backgroundColor: Colors.grey[200],
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      minHeight: 3,
                    ),

                    // 内容区域
                    Expanded(
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 更新时间
                            _buildUpdateInfo(),
                            const SizedBox(height: 20),

                            // 政策内容
                            _buildPolicyContent(),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // 移动端浮动目录按钮
          if (MediaQuery.of(context).size.width <= 600)
            Positioned(
              right: 16,
              bottom: 16,
              child: FloatingActionButton(
                onPressed: _showMobileTableOfContents,
                mini: true,
                child: const Icon(Icons.list),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTableOfContents() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 16),
      itemCount: _sections.length,
      itemBuilder: (context, index) {
        final section = _sections[index];
        return ListTile(
          leading: Icon(section.icon, size: 20, color: Colors.grey[600]),
          title: Text(
            section.title,
            style: const TextStyle(fontSize: 14),
          ),
          dense: true,
          onTap: () => _scrollToSection(section.id),
        );
      },
    );
  }

  Widget _buildUpdateInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.update, color: Colors.blue[700], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '最后更新时间：2025年7月22日',
                  style: TextStyle(
                    color: Colors.blue[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '生效日期：2025年7月22日',
                  style: TextStyle(
                    color: Colors.blue[700],
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPolicyContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSection(
          key: _sectionKeys['intro']!,
          title: '引言',
          content: '''
钓鱼之旅（以下简称"我们"）非常重视用户的隐私保护。本隐私政策详细说明了我们如何收集、使用、存储和保护您的个人信息。

在使用我们的服务前，请您仔细阅读并理解本政策的全部内容。如果您不同意本政策的任何内容，请您立即停止使用我们的服务。
          ''',
        ),

        _buildSection(
          key: _sectionKeys['collect']!,
          title: '一、我们收集的信息',
          content: '''
为了向您提供服务，我们可能会收集以下信息：

**1.1 您主动提供的信息**
- 账号信息：手机号码、昵称、头像
- 个人资料：性别、地区、个人简介
- 钓点信息：位置、照片、描述
- 动态内容：文字、图片、视频

**1.2 我们自动收集的信息**
- 设备信息：设备型号、操作系统、唯一设备标识符
- 日志信息：IP地址、浏览记录、点击记录、使用时长
- 位置信息：GPS定位、IP地址定位（需要您的授权）

**1.3 第三方分享的信息**
当您使用第三方账号登录时，我们可能获得您授权共享的账号信息。
          ''',
        ),

        _buildSection(
          key: _sectionKeys['use']!,
          title: '二、我们如何使用信息',
          content: '''
我们收集的信息将用于以下用途：

**2.1 提供服务**
- 创建和管理您的账号
- 提供钓点推荐和导航服务
- 发布和分享钓鱼动态
- 社交互动功能

**2.2 改善服务**
- 分析用户行为，优化产品功能
- 进行数据统计和分析
- 提供个性化推荐

**2.3 保障安全**
- 验证用户身份
- 检测和防范欺诈行为
- 处理投诉和举报

**2.4 法律要求**
根据法律法规要求、诉讼需要或政府部门依法提出的要求。
          ''',
        ),

        _buildSection(
          key: _sectionKeys['share']!,
          title: '三、信息共享',
          content: '''
我们不会出售、出租或以其他方式共享您的个人信息，除非：

**3.1 获得您的同意**
在获得您的明确同意后，我们会与其他方共享您的个人信息。

**3.2 法律要求**
根据法律法规、法律程序、诉讼或政府主管部门的强制性要求。

**3.3 关联公司**
我们可能会与关联公司共享您的信息，但仅限于本政策所述目的。

**3.4 业务合作伙伴**
仅共享实现服务目的所必需的信息，如地图服务商、支付服务商等。

**3.5 匿名化信息**
我们可能对外提供匿名化的统计信息。
          ''',
        ),

        _buildSection(
          key: _sectionKeys['security']!,
          title: '四、信息安全',
          content: '''
我们采取以下措施保护您的信息安全：

**4.1 技术措施**
- 使用SSL加密传输技术
- 数据加密存储
- 访问权限控制
- 定期安全审计

**4.2 管理措施**
- 建立数据安全管理制度
- 员工签署保密协议
- 定期进行安全培训
- 最小化授权原则

**4.3 应急响应**
如发生个人信息泄露等安全事件，我们将：
- 及时通知您事件情况
- 采取措施减少损失
- 向监管部门报告
          ''',
        ),

        _buildSection(
          key: _sectionKeys['rights']!,
          title: '五、您的权利',
          content: '''
您对自己的个人信息享有以下权利：

**5.1 访问权**
您有权访问您的个人信息，法律法规规定的例外情况除外。

**5.2 更正权**
当您发现个人信息有错误时，您有权要求我们更正。

**5.3 删除权**
您有权要求删除您的个人信息，包括：
- 处理目的已实现或无法实现
- 我们停止提供服务
- 您撤回同意

**5.4 撤回同意**
您可以撤回授权，但不影响撤回前的信息处理。

**5.5 注销账号**
您可以申请注销账号，注销后我们将删除或匿名化您的信息。

**5.6 获取副本**
您有权获取个人信息副本。
          ''',
        ),

        _buildSection(
          key: _sectionKeys['children']!,
          title: '六、儿童隐私保护',
          content: '''
我们非常重视对儿童个人信息的保护：

**6.1 年龄限制**
我们的服务面向14周岁以上的用户。

**6.2 监护人同意**
14周岁以上未满18周岁的用户，需要在监护人同意下使用我们的服务。

**6.3 特殊保护**
对于儿童的个人信息，我们会采取更加严格的保护措施。

如果我们发现在未事先获得监护人同意的情况下收集了儿童的个人信息，则会设法尽快删除相关信息。
          ''',
        ),

        _buildSection(
          key: _sectionKeys['changes']!,
          title: '七、政策变更',
          content: '''
**7.1 更新通知**
我们可能适时修订本政策，更新后的政策将在APP内公布。

**7.2 重大变更**
涉及以下重大变更时，我们会通过显著方式通知您：
- 服务模式发生重大变化
- 个人信息使用目的、方式和范围发生变化
- 您的权利及行使方式发生重大变化
- 安全保护措施发生变化

**7.3 继续使用**
政策更新后，如您继续使用我们的服务，即表示同意接受修订后的政策。
          ''',
        ),

        _buildSection(
          key: _sectionKeys['contact']!,
          title: '八、联系我们',
          content: '''
如您对本隐私政策有任何疑问、意见或建议，请通过以下方式联系我们：

**公司名称**：云渺科技昆明有限责任公司

**注册地址**：云南省昆明市西山区马街街道办事处张峰社区

**联系邮箱**：<EMAIL>

**工作时间**：周一至周五 9:00-18:00

我们将在收到您的意见或建议后的15个工作日内进行回复。
          ''',
        ),

        const SizedBox(height: 40),

        // 同意按钮（如果需要）
        if (!_hasScrolledToBottom)
          Center(
            child: Text(
              '请滑动到底部查看完整内容',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSection({
    required Key key,
    required String title,
    required String content,
  }) {
    return Container(
      key: key,
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildFormattedText(content),
        ],
      ),
    );
  }

  Widget _buildFormattedText(String text) {
    final spans = <TextSpan>[];
    final lines = text.split('\n');

    for (final line in lines) {
      if (line.startsWith('**') && line.endsWith('**')) {
        // 加粗文本
        spans.add(TextSpan(
          text: '${line.substring(2, line.length - 2)}\n',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            height: 1.6,
          ),
        ));
      } else if (line.contains('@') || line.contains('http')) {
        // 包含链接的文本
        final words = line.split(' ');
        for (final word in words) {
          if (word.contains('@')) {
            spans.add(TextSpan(
              text: word + ' ',
              style: const TextStyle(
                color: Colors.blue,
                decoration: TextDecoration.underline,
                height: 1.6,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () => _launchEmail(word),
            ));
          } else if (word.startsWith('http')) {
            spans.add(TextSpan(
              text: word + ' ',
              style: const TextStyle(
                color: Colors.blue,
                decoration: TextDecoration.underline,
                height: 1.6,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () => _launchUrl(word),
            ));
          } else {
            spans.add(TextSpan(
              text: word + ' ',
              style: const TextStyle(height: 1.6),
            ));
          }
        }
        spans.add(const TextSpan(text: '\n'));
      } else {
        // 普通文本
        spans.add(TextSpan(
          text: '$line\n',
          style: const TextStyle(height: 1.6),
        ));
      }
    }

    return RichText(
      text: TextSpan(
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey[700],
        ),
        children: spans,
      ),
    );
  }

  void _scrollToSection(String sectionId) {
    final key = _sectionKeys[sectionId];
    if (key?.currentContext != null) {
      Scrollable.ensureVisible(
        key!.currentContext!,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _showMobileTableOfContents() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                '目录',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const Divider(),
            Expanded(
              child: ListView.builder(
                itemCount: _sections.length,
                itemBuilder: (context, index) {
                  final section = _sections[index];
                  return ListTile(
                    leading: Icon(section.icon, color: Colors.grey[600]),
                    title: Text(section.title),
                    onTap: () {
                      Navigator.pop(context);
                      _scrollToSection(section.id);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        // TODO: 实现分享
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('分享功能开发中...')),
        );
        break;
      case 'download':
        // TODO: 实现下载PDF
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('PDF下载功能开发中...')),
        );
        break;
    }
  }

  Future<void> _launchEmail(String email) async {
    final uri = Uri.parse('mailto:$email');
    if (!await launchUrl(uri)) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('无法打开邮箱: $email')),
      );
    }
  }

  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('无法打开链接: $url')),
      );
    }
  }
}

// 政策章节数据模型
class PolicySection {
  final String id;
  final String title;
  final IconData icon;

  PolicySection({
    required this.id,
    required this.title,
    required this.icon,
  });
}
