import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class ModernStatsCard extends StatefulWidget {
  const ModernStatsCard({super.key});

  @override
  State<ModernStatsCard> createState() => _ModernStatsCardState();
}

class _ModernStatsCardState extends State<ModernStatsCard>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    // Start animations
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(30),
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Color(0xFFF8FAFC),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 25,
                offset: const Offset(0, 12),
              ),
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.05),
                blurRadius: 15,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Consumer<AuthViewModel>(
            builder: (context, viewModel, child) {
              return Row(
                children: viewModel.profileInfoItems.asMap().entries.map((entry) {
                  final index = entry.key;
                  final item = entry.value;
                  return Expanded(
                    child: _buildStatItem(
                      context,
                      title: item.title,
                      value: item.value.toString(),
                      onTap: item.onTap,
                      index: index,
                    ),
                  );
                }).toList(),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required String title,
    required String value,
    VoidCallback? onTap,
    required int index,
  }) {
    return GestureDetector(
      onTap: () {
        if (onTap != null) {
          HapticFeedback.lightImpact();
          onTap();
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 8),
        child: Column(
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                // Subtle pulse effect for numbers
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Container(
                      width: 80 + (_pulseAnimation.value * 4),
                      height: 80 + (_pulseAnimation.value * 4),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _getStatColor(index).withValues(alpha: 0.03 + (_pulseAnimation.value * 0.02)),
                      ),
                    );
                  },
                ),
                // Animated number
                TweenAnimationBuilder<double>(
                  tween: Tween(begin: 0, end: double.tryParse(value) ?? 0),
                  duration: Duration(milliseconds: 1200 + (index * 200)),
                  curve: Curves.elasticOut,
                  builder: (context, animatedValue, child) {
                    return Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _getStatColor(index).withValues(alpha: 0.1),
                        border: Border.all(
                          color: _getStatColor(index).withValues(alpha: 0.2),
                          width: 2,
                        ),
                      ),
                      child: Text(
                        animatedValue.toInt().toString(),
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: _getStatColor(index),
                          letterSpacing: 1,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Title with subtle animation
            AnimatedBuilder(
              animation: _slideController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, (1 - _slideController.value) * 10),
                  child: Opacity(
                    opacity: _slideController.value,
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 15,
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.w600,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                );
              },
            ),
            // Subtle indicator line
            Container(
              margin: const EdgeInsets.only(top: 8),
              height: 3,
              width: 30,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                gradient: LinearGradient(
                  colors: [
                    _getStatColor(index).withValues(alpha: 0.3),
                    _getStatColor(index),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatColor(int index) {
    switch (index) {
      case 0:
        return const Color(0xFF667EEA); // 动态 - Blue
      case 1:
        return const Color(0xFFE74C3C); // 粉丝 - Red
      case 2:
        return const Color(0xFF27AE60); // 关注 - Green
      default:
        return Colors.blue;
    }
  }
}
