import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/fishing_spots/screens/create_new_spot_page.dart';

class ModernQuickActions extends StatefulWidget {
  const ModernQuickActions({super.key});

  @override
  State<ModernQuickActions> createState() => _ModernQuickActionsState();
}

class _ModernQuickActionsState extends State<ModernQuickActions>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _slideAnimations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
        3,
        (index) => AnimationController(
              duration: Duration(milliseconds: 600 + (index * 100)),
              vsync: this,
            ));

    _scaleAnimations = _controllers
        .map((controller) =>
            Tween<double>(begin: 0.8, end: 1.0).animate(CurvedAnimation(
              parent: controller,
              curve: Curves.elasticOut,
            )))
        .toList();

    _slideAnimations = _controllers
        .map((controller) =>
            Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero)
                .animate(CurvedAnimation(
              parent: controller,
              curve: Curves.easeOutBack,
            )))
        .toList();

    // Start animations with staggered delay
    WidgetsBinding.instance.addPostFrameCallback((_) {
      for (int i = 0; i < _controllers.length; i++) {
        Future.delayed(Duration(milliseconds: i * 150), () {
          if (mounted) _controllers[i].forward();
        });
      }
    });
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final actions = [
      QuickAction(
        icon: Icons.camera_alt_outlined,
        title: '发布动态',
        gradient: const LinearGradient(
          colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
        ),
        onTap: () => context.push(AppRoutes.publishMoment),
      ),
      QuickAction(
        icon: Icons.location_on_outlined,
        title: '添加钓点',
        gradient: const LinearGradient(
          colors: [Color(0xFFF093FB), Color(0xFFF5576C)],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CreateNewSpotPage(
                onSpotCreated: (spotId) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('钓点创建成功！')),
                  );
                },
              ),
            ),
          );
        },
      ),
      QuickAction(
        icon: Icons.explore_outlined,
        title: '探索',
        gradient: const LinearGradient(
          colors: [Color(0xFF4FACFE), Color(0xFF00F2FE)],
        ),
        onTap: () => context.push(AppRoutes.search),
      ),
    ];

    return Container(
      height: 130,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: actions.asMap().entries.map((entry) {
          final index = entry.key;
          final action = entry.value;
          return Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                left: index > 0 ? 7.5 : 0,
                right: index < actions.length - 1 ? 7.5 : 0,
              ),
              child: AnimatedBuilder(
                animation: _controllers[index],
                builder: (context, child) {
                  return SlideTransition(
                    position: _slideAnimations[index],
                    child: ScaleTransition(
                      scale: _scaleAnimations[index],
                      child: _buildActionCard(context, action, index),
                    ),
                  );
                },
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActionCard(BuildContext context, QuickAction action, int index) {
    return GestureDetector(
      onTapDown: (_) => _controllers[index].reverse(),
      onTapUp: (_) => _controllers[index].forward(),
      onTapCancel: () => _controllers[index].forward(),
      onTap: () {
        HapticFeedback.lightImpact();
        action.onTap();
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: action.gradient,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: action.gradient.colors.first.withValues(alpha: 0.4),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
            BoxShadow(
              color: action.gradient.colors.last.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Enhanced gloss effect
            Positioned(
              top: -30,
              right: -30,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      Colors.white.withValues(alpha: 0.15),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
            // Subtle floating orb
            Positioned(
              bottom: -20,
              left: -20,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      Colors.white.withValues(alpha: 0.08),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(18),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Icon with enhanced styling
                  Container(
                    padding: const EdgeInsets.all(14),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.25),
                      borderRadius: BorderRadius.circular(18),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      action.icon,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Title with better typography
                  Flexible(
                    child: Text(
                      action.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w700,
                        letterSpacing: 0.5,
                        shadows: [
                          Shadow(
                            offset: Offset(0, 1),
                            blurRadius: 3,
                            color: Colors.black26,
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class QuickAction {
  final IconData icon;
  final String title;
  final Gradient gradient;
  final VoidCallback onTap;

  const QuickAction({
    required this.icon,
    required this.title,
    required this.gradient,
    required this.onTap,
  });
}
