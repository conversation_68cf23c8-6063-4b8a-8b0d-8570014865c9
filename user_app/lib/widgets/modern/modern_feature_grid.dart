import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/screens/fishing_statistics_page.dart';
import 'package:user_app/screens/fishing_plans_page.dart';

class ModernFeatureGrid extends StatefulWidget {
  const ModernFeatureGrid({super.key});

  @override
  State<ModernFeatureGrid> createState() => _ModernFeatureGridState();
}

class _ModernFeatureGridState extends State<ModernFeatureGrid>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _staggerController;
  late List<Animation<double>> _itemAnimations;
  late List<Animation<Offset>> _slideAnimations;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _staggerController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Create staggered animations for grid items
    const itemCount = 7;
    _itemAnimations = List.generate(itemCount, (index) {
      final start = (index * 0.08).clamp(0.0, 0.5);
      final end = (start + 0.4).clamp(0.1, 1.0);
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: _staggerController,
          curve: Interval(
            start,
            end,
            curve: Curves.elasticOut,
          ),
        ),
      );
    });

    _slideAnimations = List.generate(itemCount, (index) {
      final start = (index * 0.08).clamp(0.0, 0.4);
      final end = (start + 0.5).clamp(0.1, 1.0);
      return Tween<Offset>(
        begin: const Offset(0, 0.8),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: _staggerController,
          curve: Interval(
            start,
            end,
            curve: Curves.easeOutBack,
          ),
        ),
      );
    });

    // Start animations
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _staggerController.forward();
    });
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _staggerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final features = [
      FeatureItem(
        icon: Icons.bookmark_outline,
        title: '我的收藏',
        color: const Color(0xFFFF6B6B),
        route: AppRoutes.myBookmarks,
      ),
      FeatureItem(
        icon: Icons.location_on_outlined,
        title: '钓点记录',
        color: const Color(0xFF4ECDC4),
        route: AppRoutes.mySpots,
      ),
      FeatureItem(
        icon: Icons.photo_library_outlined,
        title: '相册',
        color: const Color(0xFF845EC2),
        route: AppRoutes.myAlbum,
      ),
      FeatureItem(
        icon: Icons.history,
        title: '浏览历史',
        color: const Color(0xFF4E8BF7),
        route: AppRoutes.browsingHistory,
      ),
      FeatureItem(
        icon: Icons.analytics_outlined,
        title: '钓鱼统计',
        color: const Color(0xFF00C9A7),
        route: null,
        customAction: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const FishingStatisticsPage(),
            ),
          );
        },
      ),
      FeatureItem(
        icon: Icons.groups_outlined,
        title: '钓友圈',
        color: const Color(0xFFC34A36),
        route: AppRoutes.followedUsers,
      ),
      FeatureItem(
        icon: Icons.calendar_today_outlined,
        title: '钓鱼计划',
        color: const Color(0xFFFF8066),
        route: null,
        customAction: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const FishingPlansPage(),
            ),
          );
        },
      ),
    ];

    return Container(
      margin: const EdgeInsets.all(20),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(30),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Color(0xFFFBFCFE),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 25,
              offset: const Offset(0, 12),
            ),
            BoxShadow(
              color: Colors.blue.withValues(alpha: 0.05),
              blurRadius: 15,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Enhanced header with floating particles
            Stack(
              children: [
                // Subtle floating orbs
                Positioned(
                  top: -10,
                  right: 20,
                  child: AnimatedBuilder(
                    animation: _rotationController,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(
                          math.sin(_rotationController.value * 2 * math.pi) * 3,
                          math.cos(_rotationController.value * 2 * math.pi) * 3,
                        ),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                Colors.blue.withValues(alpha: 0.1),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                Row(
                  children: [
                    AnimatedBuilder(
                      animation: _rotationController,
                      builder: (context, child) {
                        return Transform.rotate(
                          angle: _rotationController.value * 2 * math.pi,
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.blue.withValues(alpha: 0.1),
                                  Colors.purple.withValues(alpha: 0.08),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.grey.withValues(alpha: 0.2),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              Icons.apps,
                              color: Colors.grey.shade700,
                              size: 22,
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(width: 14),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '功能中心',
                          style: TextStyle(
                            color: Colors.grey.shade800,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 0.5,
                          ),
                        ),
                        Text(
                          '探索更多精彩功能',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 24),
            // Enhanced grid with staggered animations
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                childAspectRatio: 0.9,
                crossAxisSpacing: 16,
                mainAxisSpacing: 24,
              ),
              itemCount: features.length,
              itemBuilder: (context, index) {
                return AnimatedBuilder(
                  animation: _staggerController,
                  builder: (context, child) {
                    return SlideTransition(
                      position: _slideAnimations[index],
                      child: ScaleTransition(
                        scale: _itemAnimations[index],
                        child: _buildFeatureItem(context, features[index], index),
                      ),
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(BuildContext context, FeatureItem item, int index) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        if (item.customAction != null) {
          item.customAction!();
        } else if (item.comingSoon) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${item.title}功能即将上线')),
          );
        } else if (item.route != null) {
          context.push(item.route!);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: item.color.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Enhanced icon container with floating animation
            AnimatedBuilder(
              animation: _rotationController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(
                    math.sin((_rotationController.value + index * 0.3) * 2 * math.pi) * 1.5,
                    math.cos((_rotationController.value + index * 0.2) * 2 * math.pi) * 1.5,
                  ),
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          item.color.withValues(alpha: 0.15),
                          item.color.withValues(alpha: 0.25),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: item.color.withValues(alpha: 0.3),
                        width: 1.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: item.color.withValues(alpha: 0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Subtle glow effect
                        Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                item.color.withValues(alpha: 0.1),
                                Colors.transparent,
                              ],
                            ),
                          ),
                        ),
                        Icon(
                          item.icon,
                          color: item.color,
                          size: 24,
                        ),
                        if (item.comingSoon)
                          Positioned(
                            top: -2,
                            right: -2,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [Color(0xFFFF6B6B), Color(0xFFFF8E53)],
                                ),
                                borderRadius: BorderRadius.circular(10),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.orange.withValues(alpha: 0.3),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Text(
                                'SOON',
                                style: TextStyle(
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 10),
            // Enhanced title
            Flexible(
              child: Text(
                item.title,
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.3,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FeatureItem {
  final IconData icon;
  final String title;
  final Color color;
  final String? route;
  final bool comingSoon;
  final VoidCallback? customAction;

  const FeatureItem({
    required this.icon,
    required this.title,
    required this.color,
    this.route,
    this.comingSoon = false,
    this.customAction,
  });
}