import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';
import 'package:user_app/config/app_routes.dart';

class ModernSettingsSection extends StatefulWidget {
  const ModernSettingsSection({super.key});

  @override
  State<ModernSettingsSection> createState() => _ModernSettingsSectionState();
}

class _ModernSettingsSectionState extends State<ModernSettingsSection>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _slideAnimations;

  @override
  void initState() {
    super.initState();
    const itemCount = 3;
    _controllers = List.generate(itemCount, (index) => AnimationController(
      duration: Duration(milliseconds: 600 + (index * 100)),
      vsync: this,
    ));

    _scaleAnimations = _controllers.map((controller) => 
      Tween<double>(begin: 0.8, end: 1.0).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.elasticOut,
      ))
    ).toList();

    _slideAnimations = _controllers.map((controller) => 
      Tween<Offset>(begin: const Offset(0.3, 0), end: Offset.zero).animate(CurvedAnimation(
        parent: controller,
        curve: Curves.easeOutBack,
      ))
    ).toList();

    // Start animations with staggered delay
    WidgetsBinding.instance.addPostFrameCallback((_) {
      for (int i = 0; i < _controllers.length; i++) {
        Future.delayed(Duration(milliseconds: i * 150), () {
          if (mounted) _controllers[i].forward();
        });
      }
    });
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settingsItems = [
      SettingsItem(
        icon: Icons.settings_outlined,
        title: '设置',
        subtitle: '账号与隐私设置',
        gradient: const LinearGradient(
          colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
        ),
        onTap: () => context.push(AppRoutes.profileSetting),
      ),
      SettingsItem(
        icon: Icons.share_outlined,
        title: '分享应用',
        subtitle: '推荐给好友',
        gradient: const LinearGradient(
          colors: [Color(0xFFF093FB), Color(0xFFF5576C)],
        ),
        onTap: () {
          Share.share('来试试这个超棒的钓鱼应用吧！');
        },
      ),
      SettingsItem(
        icon: Icons.help_outline,
        title: '帮助与反馈',
        subtitle: '联系我们',
        gradient: const LinearGradient(
          colors: [Color(0xFF4FACFE), Color(0xFF00F2FE)],
        ),
        onTap: () => context.push(AppRoutes.feedback),
      ),
    ];

    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        children: settingsItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: AnimatedBuilder(
              animation: _controllers[index],
              builder: (context, child) {
                return SlideTransition(
                  position: _slideAnimations[index],
                  child: ScaleTransition(
                    scale: _scaleAnimations[index],
                    child: _buildSettingsItem(context, item, index),
                  ),
                );
              },
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSettingsItem(BuildContext context, SettingsItem item, int index) {
    return GestureDetector(
      onTapDown: (_) => _controllers[index].reverse(),
      onTapUp: (_) => _controllers[index].forward(),
      onTapCancel: () => _controllers[index].forward(),
      onTap: () {
        HapticFeedback.lightImpact();
        item.onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Color(0xFFFBFCFE),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 25,
              offset: const Offset(0, 12),
            ),
            BoxShadow(
              color: item.gradient.colors.first.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Subtle floating orb
            Positioned(
              top: -10,
              right: -10,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      item.gradient.colors.first.withValues(alpha: 0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
            // Main content
            Row(
              children: [
                // Enhanced icon container
                Container(
                  padding: const EdgeInsets.all(14),
                  decoration: BoxDecoration(
                    gradient: item.gradient,
                    borderRadius: BorderRadius.circular(18),
                    boxShadow: [
                      BoxShadow(
                        color: item.gradient.colors.first.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    item.icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 18),
                // Enhanced text content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.title,
                        style: TextStyle(
                          color: Colors.grey.shade800,
                          fontSize: 17,
                          fontWeight: FontWeight.w700,
                          letterSpacing: 0.3,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        item.subtitle,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.2,
                        ),
                      ),
                    ],
                  ),
                ),
                // Enhanced arrow icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey.shade500,
                    size: 16,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class SettingsItem {
  final IconData icon;
  final String title;
  final String subtitle;
  final Gradient gradient;
  final VoidCallback onTap;

  const SettingsItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.gradient,
    required this.onTap,
  });
}
