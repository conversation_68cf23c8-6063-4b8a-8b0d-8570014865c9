import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:provider/provider.dart';
import 'package:user_app/view_models/preview_image_view_model.dart';

class PicturesPageView extends StatelessWidget {
  const PicturesPageView({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<PreviewImageViewModel>(
      builder: (context, viewModel, child) {
        return GestureDetector(
          onTap: () {
            context.pop();
          },
          child: Scaffold(
            body: Container(
              constraints: BoxConstraints.expand(
                height: MediaQuery.of(context).size.height,
              ),
              child: Stack(
                alignment: Alignment.bottomRight,
                children: <Widget>[
                  PhotoViewGallery.builder(
                    scrollPhysics: const BouncingScrollPhysics(),
                    builder: _buildItem,
                    itemCount: viewModel.currentPictureUrls.length,
                    pageController: viewModel.pageController,
                    onPageChanged: viewModel.onPageChanged,
                    scrollDirection: Axis.horizontal,
                  ),
                  Container(
                    padding: const EdgeInsets.all(20.0),
                    child: Text(
                      "${viewModel.currentIndex + 1}/${viewModel.currentPictureUrls.length}",
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 17.0,
                        decoration: null,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  PhotoViewGalleryPageOptions _buildItem(BuildContext context, int index) {
    final String item =
        context.read<PreviewImageViewModel>().currentPictureUrls[index];
    return PhotoViewGalleryPageOptions(
      imageProvider: NetworkImage(
        item,
      ),
      initialScale: PhotoViewComputedScale.contained,
      minScale: PhotoViewComputedScale.contained * (0.5 + index / 10),
      maxScale: PhotoViewComputedScale.covered * 4.1,
      heroAttributes: PhotoViewHeroAttributes(tag: item),
    );
  }
}
