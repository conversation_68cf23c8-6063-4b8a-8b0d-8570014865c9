import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/view_models/follow_button_view_model.dart';

enum FollowButtonSize { small, medium, large }

class FollowButton extends StatelessWidget {
  final num userId;
  final FollowButtonSize size;
  final VoidCallback? onFollowChanged;

  const FollowButton({
    super.key, 
    required this.userId,
    this.size = FollowButtonSize.medium,
    this.onFollowChanged,
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<FollowButtonViewModel>(
      create: (context) => getIt<FollowButtonViewModel>(param1: userId)..init(),
      child: Consumer<FollowButtonViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.busy) {
            return _buildLoadingButton();
          }

          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            child: ElevatedButton(
              onPressed: () async {
                HapticFeedback.lightImpact();
                await viewModel.toggleFollow(context);
                onFollowChanged?.call();
              },
              style: _getButtonStyle(viewModel.isFollowing),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    viewModel.isFollowing ? Icons.check : Icons.person_add,
                    size: _getIconSize(),
                    color: viewModel.isFollowing ? Colors.grey[600] : Colors.white,
                  ),
                  SizedBox(width: _getSpacing()),
                  Text(
                    viewModel.isFollowing ? '已关注' : '关注',
                    style: TextStyle(
                      fontSize: _getFontSize(),
                      fontWeight: FontWeight.w600,
                      color: viewModel.isFollowing ? Colors.grey[600] : Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingButton() {
    return Container(
      width: _getButtonWidth(),
      height: _getButtonHeight(),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(_getBorderRadius()),
      ),
      child: Center(
        child: SizedBox(
          width: _getIconSize(),
          height: _getIconSize(),
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: Colors.grey[600],
          ),
        ),
      ),
    );
  }

  ButtonStyle _getButtonStyle(bool isFollowing) {
    return ElevatedButton.styleFrom(
      backgroundColor: isFollowing ? Colors.grey[100] : Colors.blue,
      foregroundColor: isFollowing ? Colors.grey[600] : Colors.white,
      elevation: isFollowing ? 0 : 2,
      shadowColor: Colors.blue.withValues(alpha: 0.3),
      padding: EdgeInsets.symmetric(
        horizontal: _getHorizontalPadding(),
        vertical: _getVerticalPadding(),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_getBorderRadius()),
        side: isFollowing 
          ? BorderSide(color: Colors.grey[300]!, width: 1)
          : BorderSide.none,
      ),
      minimumSize: Size(_getButtonWidth(), _getButtonHeight()),
    );
  }

  double _getButtonWidth() {
    switch (size) {
      case FollowButtonSize.small:
        return 80;
      case FollowButtonSize.medium:
        return 100;
      case FollowButtonSize.large:
        return 120;
    }
  }

  double _getButtonHeight() {
    switch (size) {
      case FollowButtonSize.small:
        return 32;
      case FollowButtonSize.medium:
        return 40;
      case FollowButtonSize.large:
        return 48;
    }
  }

  double _getIconSize() {
    switch (size) {
      case FollowButtonSize.small:
        return 16;
      case FollowButtonSize.medium:
        return 18;
      case FollowButtonSize.large:
        return 20;
    }
  }

  double _getFontSize() {
    switch (size) {
      case FollowButtonSize.small:
        return 12;
      case FollowButtonSize.medium:
        return 14;
      case FollowButtonSize.large:
        return 16;
    }
  }

  double _getSpacing() {
    switch (size) {
      case FollowButtonSize.small:
        return 4;
      case FollowButtonSize.medium:
        return 6;
      case FollowButtonSize.large:
        return 8;
    }
  }

  double _getHorizontalPadding() {
    switch (size) {
      case FollowButtonSize.small:
        return 8;
      case FollowButtonSize.medium:
        return 12;
      case FollowButtonSize.large:
        return 16;
    }
  }

  double _getVerticalPadding() {
    switch (size) {
      case FollowButtonSize.small:
        return 4;
      case FollowButtonSize.medium:
        return 6;
      case FollowButtonSize.large:
        return 8;
    }
  }

  double _getBorderRadius() {
    switch (size) {
      case FollowButtonSize.small:
        return 16;
      case FollowButtonSize.medium:
        return 20;
      case FollowButtonSize.large:
        return 24;
    }
  }
}
