import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class FishingScaffold extends StatelessWidget {
  final Widget child;
  final int selectedIndex;

  const FishingScaffold({
    required this.child,
    required this.selectedIndex,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: NavigationBar(
        destinations: const <NavigationDestination>[
          NavigationDestination(
            icon: Icon(Icons.location_on_outlined),
            selectedIcon: Icon(Icons.location_on),
            label: '钓点',
          ),
          NavigationDestination(
            icon: Icon(Icons.people_outline),
            selectedIcon: Icon(Icons.people),
            label: '社区',
          ),
          NavigationDestination(
            icon: Icon(Icons.lightbulb_outline),
            selectedIcon: Icon(Icons.lightbulb),
            label: '技巧',
          ),
          NavigationDestination(
            icon: Icon(Icons.chat_bubble_outline),
            selectedIcon: Icon(Icons.chat_bubble),
            label: '消息',
          ),
          NavigationDestination(
            icon: Icon(Icons.person_outline),
            selectedIcon: Icon(Icons.person),
            label: '我的',
          ),
        ],
        animationDuration: const Duration(seconds: 1),
        selectedIndex: _calculateSelectedIndex(context),
        onDestinationSelected: (idx) => onDestinationSelected(idx, context),
      ),
    );
  }

  static int _calculateSelectedIndex(BuildContext context) {
    final String location = GoRouterState.of(context).uri.path;
    if (location.startsWith('/fishing_spots')) {
      return 0;
    } else if (location.startsWith('/community')) {
      return 1;
    } else if (location.startsWith('/tips')) {
      return 2;
    } else if (location.startsWith('/message')) {
      return 3;
    } else if (location.startsWith('/mine')) {
      return 4;
    } else {
      return 0; // Default to fishing spots tab
    }
  }

  void onDestinationSelected(int index, BuildContext context) {
    final goRouter = GoRouter.of(context);
    switch (index) {
      case 0:
        goRouter.go('/fishing_spots');
        break;
      case 1:
        goRouter.go('/community');
        break;
      case 2:
        goRouter.go('/tips');
        break;
      case 3:
        goRouter.go('/message');
        break;
      case 4:
        goRouter.go('/mine');
        break;
    }
  }
}
