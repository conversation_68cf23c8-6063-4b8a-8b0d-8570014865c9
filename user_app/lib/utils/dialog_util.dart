import 'package:flutter/material.dart';

void showAlertDialog(BuildContext context, String title, String content,
    [List<Widget>? actions]) {
  final defaultActions = [
    TextButton(
      onPressed: () => Navigator.of(context).pop(false),
      child: const Text('取消'),
    ),
    TextButton(
      onPressed: () => Navigator.of(context).pop(true),
      child: const Text('确定'),
    ),
  ];

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(
          child: ListBody(
            children: <Widget>[
              Text(content),
            ],
          ),
        ),
        actions: actions ?? defaultActions,
      );
    },
  );
}
