class DateTimeUtil {
  static String formatTime(DateTime createTime) {
    final now = DateTime.now();
    final difference = now.difference(createTime);

    if (difference.inDays > 30) {
      final months = difference.inDays ~/ 30;
      return '$months月前';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}
