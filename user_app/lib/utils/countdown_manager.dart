import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CountdownManager {
  CountdownManager._privateConstructor() {
    _countdown.addListener(saveCountdownToSharedPreferences);
  }

  static final CountdownManager instance =
      CountdownManager._privateConstructor();

  final ValueNotifier<int> _countdown = ValueNotifier<int>(0);
  Timer? _countdownTimer;

  ValueNotifier<int> get countdown => _countdown;

  void startCountdown(int seconds) {
    _countdown.value = seconds;
    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _countdown.value--;
      if (_countdown.value <= 0) {
        _countdown.value = 0;
        _countdownTimer?.cancel();
        _countdownTimer = null;
      }
    });
  }

  void cancelCountdown() {
    _countdownTimer?.cancel();
    _countdown.value = 0;
  }

  Future<void> saveCountdownToSharedPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('countdown', _countdown.value);
  }
}
