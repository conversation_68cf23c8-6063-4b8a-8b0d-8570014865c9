import 'package:flutter/material.dart';
import 'package:user_app/models/image/uploaded_image.dart';

Widget buildTextField({
  required BuildContext context, // <--- ADD THIS PARAMETER
  required TextEditingController controller,
  required String label,
  required String hintText,
  required IconData prefixIcon,
  int maxLines = 1,
  TextInputType keyboardType = TextInputType.text,
  bool mandatory = false,
  String? Function(String?)? validator,
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (mandatory) ...[
            const SizedBox(width: 4),
            const Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
      const SizedBox(height: 8),
      TextFormField(
        controller: controller,
        decoration: InputDecoration(
          hintText: hintText,
          prefixIcon: Icon(prefixIcon),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              // --- FIX: Use the passed context ---
              color: Theme.of(context).colorScheme.primary,
              // --- END FIX ---
              width: 2,
            ),
          ),
          filled: true,
          fillColor: Colors.grey.shade50,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        ),
        maxLines: maxLines,
        keyboardType: keyboardType,
        textAlignVertical:
            maxLines > 1 ? TextAlignVertical.top : TextAlignVertical.center,
        validator: validator,
        key: ValueKey('${label}_${controller.text.isNotEmpty}'),
      ),
    ],
  );
}

// Helper method to build section headers (can be reused)
Widget buildSectionHeader(
    BuildContext context, String title, IconData icon, String subtitle) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      if (subtitle.isNotEmpty)
        Padding(
          padding: const EdgeInsets.only(top: 8, left: 4),
          child: Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
        ),
    ],
  );
}

// Icon Mapper (can be reused)
IconData getIconData(String iconName) {
  switch (iconName) {
    case 'restaurant':
      return Icons.restaurant_rounded;
    case 'wc':
      return Icons.wc_rounded;
    case 'local_parking':
      return Icons.local_parking_rounded;
    case 'shopping_basket':
      return Icons.shopping_basket_rounded;
    case 'wifi':
      return Icons.wifi_rounded;
    case 'access_time':
      return Icons.access_time_rounded;
    case 'shower':
      return Icons.shower_rounded;
    case 'outdoor_grill':
      return Icons.outdoor_grill_rounded;
    case 'directions_boat':
      return Icons.directions_boat_rounded;
    case 'cabin':
      return Icons.cabin_rounded;
    case 'pool':
      return Icons.pool_rounded;
    case 'set_meal':
      return Icons.set_meal_rounded;
    case 'meeting_room':
      return Icons.meeting_room_rounded;
    case 'electrical_services':
      return Icons.electrical_services_rounded;
    case 'photo_camera':
      return Icons.photo_camera_rounded;
    case 'waves':
      return Icons.waves_rounded;
    default:
      return Icons.apps_rounded;
  }
}

// Helper to build image preview (specific to UploadedImage)
Widget buildImagePreview(UploadedImage image) {
  if (image.isUploading) {
    return Container(
      color: Colors.grey.shade200,
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
  if (image.hasError) {
    return Container(
      color: Colors.grey.shade200,
      child: const Center(
        child: Icon(Icons.error_outline, color: Colors.red, size: 32),
      ),
    );
  }
  // Assuming XFile path points to a locally accessible file temporarily
  // For web, you might need Image.network(image.file.path) or other logic
  // For mobile, Image.file(File(image.file.path)) is usually correct
  // Let's stick with Image.network for broader compatibility assuming path might be a URL later
  // or if picker returns web paths. Adjust if using `dart:io`'s File.
  return Image.network(
    image.file.path, // May need adjustment based on platform/picker result
    fit: BoxFit.cover,
    errorBuilder: (context, error, stackTrace) {
      return Container(
        color: Colors.grey.shade300,
        child: const Center(
          child: Icon(
            Icons.broken_image,
            color: Colors.grey,
          ),
        ),
      );
    },
    // Optional: Add loading builder if needed
    // loadingBuilder: (context, child, loadingProgress) {
    //   if (loadingProgress == null) return child;
    //   return Center(child: CircularProgressIndicator());
    // },
  );
}
