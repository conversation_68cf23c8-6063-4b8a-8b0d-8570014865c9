import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:flutter/material.dart';

class SnakeBarUtil {
  static void showSnakeBar(BuildContext context, String title, String message,
      ContentType contentType) {
    ScaffoldMessenger.of(context).showMaterialBanner(
      MaterialBanner(
        elevation: 0,
        backgroundColor: Colors.transparent,
        forceActionsBelow: true,
        content: AwesomeSnackbarContent(
          title: title,
          message: message,
          contentType: contentType,
          inMaterialBanner: true,
        ),
        actions: const [SizedBox.shrink()],
      ),
    );
  }
}
