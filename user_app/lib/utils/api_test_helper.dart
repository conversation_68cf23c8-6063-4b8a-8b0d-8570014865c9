import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/services/user_service.dart';
import 'package:user_app/services/statistics_service.dart';
import 'package:user_app/services/notification_service.dart';

/// API接口测试助手
class ApiTestHelper {
  static final GetIt _getIt = GetIt.instance;

  /// 测试用户相关接口
  static Future<Map<String, dynamic>> testUserApis() async {
    final results = <String, dynamic>{};
    
    try {
      // 测试获取当前用户
      debugPrint('🧪 测试获取当前用户...');
      final userService = _getIt<UserService>();
      final user = await userService.getCurrentUser();
      results['getCurrentUser'] = {
        'success': true,
        'data': {
          'id': user.id,
          'name': user.name,
          'avatarUrl': user.avatarUrl,
        }
      };
      debugPrint('✅ getCurrentUser 成功');
    } catch (e) {
      results['getCurrentUser'] = {
        'success': false,
        'error': e.toString(),
      };
      debugPrint('❌ getCurrentUser 失败: $e');
    }

    try {
      // 测试获取缓存用户
      debugPrint('🧪 测试获取缓存用户...');
      final userService = _getIt<UserService>();
      final cachedUser = userService.getCachedUser();
      results['getCachedUser'] = {
        'success': cachedUser != null,
        'data': cachedUser != null ? {
          'id': cachedUser.id,
          'name': cachedUser.name,
        } : null,
      };
      debugPrint('✅ getCachedUser 成功');
    } catch (e) {
      results['getCachedUser'] = {
        'success': false,
        'error': e.toString(),
      };
      debugPrint('❌ getCachedUser 失败: $e');
    }

    return results;
  }

  /// 测试统计相关接口
  static Future<Map<String, dynamic>> testStatisticsApis() async {
    final results = <String, dynamic>{};

    try {
      debugPrint('🧪 测试获取统计数据...');
      final statisticsService = _getIt<StatisticsService>();
      final statistics = await statisticsService.fetchStatistics();
      results['fetchStatistics'] = {
        'success': true,
        'data': {
          'momentCount': statistics.momentCount,
          'fansCount': statistics.fansCount,
          'followCount': statistics.followCount,
        }
      };
      debugPrint('✅ fetchStatistics 成功');
    } catch (e) {
      results['fetchStatistics'] = {
        'success': false,
        'error': e.toString(),
      };
      debugPrint('❌ fetchStatistics 失败: $e');
    }

    return results;
  }

  /// 测试通知相关接口
  static Future<Map<String, dynamic>> testNotificationApis() async {
    final results = <String, dynamic>{};

    try {
      debugPrint('🧪 测试获取未读通知数量...');
      final notificationService = _getIt<NotificationService>();
      final count = await notificationService.getUnreadCount();
      results['getUnreadCount'] = {
        'success': true,
        'data': count,
      };
      debugPrint('✅ getUnreadCount 成功: $count');
    } catch (e) {
      results['getUnreadCount'] = {
        'success': false,
        'error': e.toString(),
      };
      debugPrint('❌ getUnreadCount 失败: $e');
    }

    try {
      debugPrint('🧪 测试获取通知列表...');
      final notificationService = _getIt<NotificationService>();
      final notifications = await notificationService.getNotifications();
      results['getNotifications'] = {
        'success': true,
        'data': {
          'count': notifications.length,
          'firstNotification': notifications.isNotEmpty ? {
            'id': notifications.first.id,
            'title': notifications.first.title,
            'isRead': notifications.first.isRead,
          } : null,
        }
      };
      debugPrint('✅ getNotifications 成功: ${notifications.length} 条通知');
    } catch (e) {
      results['getNotifications'] = {
        'success': false,
        'error': e.toString(),
      };
      debugPrint('❌ getNotifications 失败: $e');
    }

    return results;
  }

  /// 运行所有API测试
  static Future<Map<String, dynamic>> runAllTests() async {
    debugPrint('🚀 开始API接口测试...');
    
    final allResults = <String, dynamic>{};
    
    allResults['user'] = await testUserApis();
    allResults['statistics'] = await testStatisticsApis();
    allResults['notifications'] = await testNotificationApis();
    
    // 统计结果
    int totalTests = 0;
    int successTests = 0;
    
    void countResults(Map<String, dynamic> results) {
      results.forEach((key, value) {
        if (value is Map<String, dynamic> && value.containsKey('success')) {
          totalTests++;
          if (value['success'] == true) {
            successTests++;
          }
        } else if (value is Map<String, dynamic>) {
          countResults(value);
        }
      });
    }
    
    countResults(allResults);
    
    allResults['summary'] = {
      'total': totalTests,
      'success': successTests,
      'failed': totalTests - successTests,
      'successRate': totalTests > 0 ? (successTests / totalTests * 100).toStringAsFixed(1) : '0.0',
    };
    
    debugPrint('📊 API测试完成: $successTests/$totalTests 成功 (${allResults['summary']['successRate']}%)');
    
    return allResults;
  }

  /// 测试特定接口的网络连通性
  static Future<bool> testConnectivity() async {
    try {
      debugPrint('🧪 测试网络连通性...');
      // 尝试获取当前用户作为连通性测试
      final userService = _getIt<UserService>();
      await userService.getCurrentUser();
      debugPrint('✅ 网络连通性正常');
      return true;
    } catch (e) {
      debugPrint('❌ 网络连通性测试失败: $e');
      return false;
    }
  }

  /// 模拟接口错误处理
  static void testErrorHandling() {
    debugPrint('🧪 测试错误处理机制...');
    
    // 测试不同类型的API错误
    final testErrors = [
      ApiError(code: 401, message: '未授权'),
      ApiError(code: 404, message: '资源不存在'),
      ApiError(code: 500, message: '服务器错误'),
    ];
    
    for (final error in testErrors) {
      debugPrint('测试错误: ${error.code} - ${error.message}');
      // 这里可以添加更多的错误处理测试逻辑
    }
    
    debugPrint('✅ 错误处理测试完成');
  }

  /// 输出测试报告
  static void printTestReport(Map<String, dynamic> results) {
    debugPrint('');
    debugPrint('================== API测试报告 ==================');
    debugPrint('');
    
    void printResults(Map<String, dynamic> results, [String prefix = '']) {
      results.forEach((key, value) {
        if (value is Map<String, dynamic>) {
          if (value.containsKey('success')) {
            final status = value['success'] == true ? '✅' : '❌';
            final error = value['error'] ?? '';
            debugPrint('$prefix$status $key ${error.isNotEmpty ? "- $error" : ""}');
          } else {
            debugPrint('$prefix📂 $key:');
            printResults(value, '$prefix  ');
          }
        }
      });
    }
    
    printResults(results);
    
    if (results.containsKey('summary')) {
      final summary = results['summary'] as Map<String, dynamic>;
      debugPrint('');
      debugPrint('📊 总计: ${summary['success']}/${summary['total']} 成功 (${summary['successRate']}%)');
    }
    
    debugPrint('================================================');
  }
}