package com.weilu.flutter.flutter_2d_amap;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.maps2d.AMap;
import com.amap.api.maps2d.CameraUpdateFactory;
import com.amap.api.maps2d.LocationSource;
import com.amap.api.maps2d.MapView;
import com.amap.api.maps2d.model.BitmapDescriptor;
import com.amap.api.maps2d.model.BitmapDescriptorFactory;
import com.amap.api.maps2d.model.LatLng;
import com.amap.api.maps2d.model.Marker;
import com.amap.api.maps2d.model.MarkerOptions;
import com.amap.api.maps2d.model.MyLocationStyle;
import com.amap.api.services.core.AMapException;
import com.amap.api.services.core.LatLonPoint;
import com.amap.api.services.core.PoiItemV2;
import com.amap.api.services.geocoder.GeocodeResult;
import com.amap.api.services.geocoder.GeocodeSearch;
import com.amap.api.services.geocoder.RegeocodeQuery;
import com.amap.api.services.geocoder.RegeocodeResult;
import com.amap.api.services.poisearch.PoiResultV2;
import com.amap.api.services.poisearch.PoiSearchV2;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.platform.PlatformView;

/**
 * <AUTHOR>
 * 2019/6/26 0026 10:18.
 */
public class AMap2DView implements PlatformView, MethodChannel.MethodCallHandler, LocationSource, AMapLocationListener,
        AMap.OnMapClickListener, PoiSearchV2.OnPoiSearchListener, GeocodeSearch.OnGeocodeSearchListener {

    private static final String SEARCH_CONTENT = "010000|010100|020000|030000|040000|050000|050100|060000|060100|060200|060300|060400|070000|080000|080100|080300|080500|080600|090000|090100|090200|090300|100000|100100|110000|110100|120000|120200|120300|130000|140000|141200|150000|150100|150200|160000|160100|170000|170100|170200|180000|190000|200000";
    private static final String IS_POI_SEARCH = "isPoiSearch";
    private final MethodChannel methodChannel;
    private final Handler platformThreadHandler;
    private final Context context;
    private final StringBuilder builder = new StringBuilder();
    private MapView mAMap2DView;
    private AMap aMap;
    private PoiSearchV2.Query query;
    private OnLocationChangedListener mListener;
    private AMapLocationClient mLocationClient;
    private Runnable postMessageRunnable;
    private String keyWord = "";
    private boolean isPoiSearch;
    private String city = "";
    private Marker mMarker;

    AMap2DView(final Context context, BinaryMessenger messenger, int id, Map<String, Object> params, AMap2DDelegate delegate) {
        this.context = context;
        platformThreadHandler = new Handler(context.getMainLooper());
        createMap(context);
        setAMap2DDelegate(delegate);
        mAMap2DView.onResume();
        methodChannel = new MethodChannel(messenger, "plugins.weilu/flutter_2d_amap_" + id);
        methodChannel.setMethodCallHandler(this);

        if (params.containsKey(IS_POI_SEARCH)) {
            isPoiSearch = (boolean) params.get(IS_POI_SEARCH);
        }
    }

    void setAMap2DDelegate(AMap2DDelegate delegate) {
        if (delegate != null) {
            delegate.requestPermissions(new AMap2DDelegate.RequestPermission() {
                @Override
                public void onRequestPermissionSuccess() {
                    setUpMap();
                }

                @Override
                public void onRequestPermissionFailure() {
                    Toast.makeText(context, "定位失败，请检查定位权限是否开启！", Toast.LENGTH_SHORT).show();
                }
            });
        }
    }

    private void createMap(Context context) {
        mAMap2DView = new MapView(context);
        mAMap2DView.onCreate(new Bundle());
        aMap = mAMap2DView.getMap();
    }

    private void setUpMap() {
        CameraUpdateFactory.zoomTo(32);
        aMap.setOnMapClickListener(this);
        // 设置定位监听
        aMap.setLocationSource(this);
        // 设置默认定位按钮是否显示
        aMap.getUiSettings().setMyLocationButtonEnabled(true);
        MyLocationStyle myLocationStyle = new MyLocationStyle();
        myLocationStyle.strokeWidth(1f);
        myLocationStyle.strokeColor(Color.parseColor("#8052A3FF"));
        myLocationStyle.radiusFillColor(Color.parseColor("#3052A3FF"));
        myLocationStyle.showMyLocation(true);
        myLocationStyle.myLocationIcon(BitmapDescriptorFactory.fromResource(R.drawable.yd));
        myLocationStyle.myLocationType(MyLocationStyle.LOCATION_TYPE_LOCATE);
        aMap.setMyLocationStyle(myLocationStyle);
        // 设置为true表示显示定位层并可触发定位，false表示隐藏定位层并不可触发定位，默认是false
        aMap.setMyLocationEnabled(true);
    }

    @Override
    public void onMethodCall(MethodCall methodCall, @NonNull MethodChannel.Result result) {
        String method = methodCall.method;
        Map<String, Object> request = (Map<String, Object>) methodCall.arguments;
        switch (method) {
            case "search":
                keyWord = (String) request.get("keyWord");
                city = (String) request.get("city");
                search();
                break;
            case "move":
                move(toDouble((String) request.get("lat")), toDouble((String) request.get("lon")));
                break;
            case "location":
                if (mLocationClient != null) {
                    mLocationClient.startLocation();
                }
                break;
            case "reGeocode":
                GeocodeSearch geocodeSearch;
                try {
                    geocodeSearch = new GeocodeSearch(context);
                } catch (AMapException e) {
                    throw new RuntimeException(e);
                }
                geocodeSearch.setOnGeocodeSearchListener(this);
                LatLonPoint latLonPoint = new LatLonPoint(toDouble(Objects.requireNonNull(request.get("lat")).toString()), toDouble(Objects.requireNonNull(request.get("lon")).toString()));
                geocodeSearch.getFromLocationAsyn(new RegeocodeQuery(latLonPoint, 200, GeocodeSearch.AMAP));
                break;
            case "setZoom":
                aMap.moveCamera(CameraUpdateFactory.zoomTo(Float.parseFloat(Objects.requireNonNull(request.get("zoomLevel")).toString())));
                break;
            case "addMarkers":
                addMarkers(methodCall, result);
                break;
            default:
                break;
        }
    }

    private void addMarkers(MethodCall call, MethodChannel.Result result) {
        try {
            List<Map<String, Object>> markers = call.argument("markers");
            if (markers == null || markers.isEmpty()) {
                result.error("INVALID_ARGUMENTS", "markers cannot be null or empty", null);
                return;
            }

            for (Map<String, Object> markerData : markers) {
                double latitude = Double.parseDouble(Objects.requireNonNull(markerData.get("latitude")).toString());
                double longitude = Double.parseDouble(Objects.requireNonNull(markerData.get("longitude")).toString());

                MarkerOptions markerOptions = new MarkerOptions()
                        .position(new LatLng(latitude, longitude))
                        .draggable(false)  // 设置Marker可拖动
                        .visible(true);    // 设置Marker可见

                aMap.addMarker(markerOptions);
            }

            result.success(null);
        } catch (Exception e) {
            result.error("MARKERS_ADD_FAILED", e.getMessage(), null);
        }
    }

    private double toDouble(String obj) {
        try {
            return Double.parseDouble(obj);
        } catch (Exception e) {
            e.fillInStackTrace();
        }
        return 0D;
    }

    @Override
    public View getView() {
        return mAMap2DView;
    }

    @Override
    public void dispose() {
        mAMap2DView.onDestroy();
        platformThreadHandler.removeCallbacks(postMessageRunnable);
        methodChannel.setMethodCallHandler(null);
    }

    @Override
    public void onLocationChanged(AMapLocation aMapLocation) {
        if (mListener != null && aMapLocation != null) {
            if (aMapLocation.getErrorCode() == 0) {
                mListener.onLocationChanged(aMapLocation);
//                aMap.moveCamera(CameraUpdateFactory.zoomTo(13));
                System.out.println("latitude: " + aMapLocation.getLatitude() + ", longitude: " + aMapLocation.getLongitude());
                search(aMapLocation.getLatitude(), aMapLocation.getLongitude());

                postMessageRunnable = () -> {
                    Map<String, String> map = new HashMap<>();
                    map.put("latitude", String.valueOf(aMapLocation.getLatitude()));
                    map.put("longitude", String.valueOf(aMapLocation.getLongitude()));
                    methodChannel.invokeMethod("onLocationChanged", map);
                };
                if (platformThreadHandler.getLooper() == Looper.myLooper()) {
                    postMessageRunnable.run();
                } else {
                    platformThreadHandler.post(postMessageRunnable);
                }
            } else {
                Toast.makeText(context, "定位失败，请检查GPS是否开启！", Toast.LENGTH_SHORT).show();
            }
            if (mLocationClient != null) {
                mLocationClient.stopLocation();
            }
        }
    }

    private void search() {
        if (!isPoiSearch) {
            return;
        }
        query = new PoiSearchV2.Query(keyWord, SEARCH_CONTENT, city);
        query.setPageSize(50);
        query.setPageNum(0);
        try {
            PoiSearchV2 poiSearch = new PoiSearchV2(context, query);
            poiSearch.setOnPoiSearchListener(this);
            poiSearch.searchPOIAsyn();
        } catch (AMapException e) {
            e.printStackTrace();
        }

    }

    private void move(double lat, double lon) {
        LatLng latLng = new LatLng(lat, lon);
        drawMarkers(latLng, BitmapDescriptorFactory.defaultMarker());
    }

    private void search(double latitude, double longitude) {
        if (!isPoiSearch) {
            return;
        }
        query = new PoiSearchV2.Query("", SEARCH_CONTENT, "");
        query.setPageSize(50);
        query.setPageNum(0);

        try {
            PoiSearchV2 poiSearch = new PoiSearchV2(context, query);
            poiSearch.setOnPoiSearchListener(this);
            LatLonPoint latLonPoint = new LatLonPoint(latitude, longitude);
            poiSearch.setBound(new PoiSearchV2.SearchBound(latLonPoint, 2000, true));
            poiSearch.searchPOIAsyn();
        } catch (AMapException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onMapClick(LatLng latLng) {
        drawMarkers(latLng, BitmapDescriptorFactory.defaultMarker());
        search(latLng.latitude, latLng.longitude);
    }

    private void drawMarkers(LatLng latLng, BitmapDescriptor bitmapDescriptor) {
        aMap.animateCamera(CameraUpdateFactory.changeLatLng(new LatLng(latLng.latitude, latLng.longitude)));
        if (mMarker == null) {
            mMarker = aMap.addMarker(new MarkerOptions().position(latLng).icon(bitmapDescriptor).draggable(true));
        } else {
            mMarker.setPosition(latLng);
        }
    }

    @Override
    public void activate(OnLocationChangedListener onLocationChangedListener) {
        mListener = onLocationChangedListener;
        if (mLocationClient == null) {
            try {
                mLocationClient = new AMapLocationClient(context);
                AMapLocationClientOption locationOption = new AMapLocationClientOption();
                mLocationClient.setLocationListener(this);
                locationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
                mLocationClient.setLocationOption(locationOption);
                mLocationClient.startLocation();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void deactivate() {
        mListener = null;
        if (mLocationClient != null) {
            mLocationClient.stopLocation();
            mLocationClient.onDestroy();
        }
        mLocationClient = null;
    }

    @Override
    public void onPoiItemSearched(PoiItemV2 poiItemV2, int i) {

    }

    private String processPoiItems(List<PoiItemV2> list) {
        if (list == null || list.isEmpty()) {
            return "[]";
        }

        List<Map<String, String>> poiDataList = new ArrayList<>(list.size());

        for (PoiItemV2 item : list) {
            Map<String, String> poiData = new LinkedHashMap<>();
            poiData.put("cityCode", item.getCityCode());
            poiData.put("cityName", item.getCityName());
            poiData.put("provinceName", item.getProvinceName());
            poiData.put("title", item.getTitle());
            poiData.put("adName", item.getAdName());
            poiData.put("provinceCode", item.getProvinceCode());
            poiData.put("latitude", String.valueOf(item.getLatLonPoint().getLatitude()));
            poiData.put("longitude", String.valueOf(item.getLatLonPoint().getLongitude()));

            poiDataList.add(poiData);
        }

        return new Gson().toJson(poiDataList);
    }

    @Override
    public void onPoiSearched(PoiResultV2 result, int code) {
        builder.delete(0, builder.length());
        if (code == AMapException.CODE_AMAP_SUCCESS) {
            if (result != null && result.getQuery() != null) {
                if (result.getQuery().equals(query)) {
                    final List<PoiItemV2> list = result.getPois();
                    String processedItems = processPoiItems(list);
                    builder.append(processedItems);
                    if (!list.isEmpty()) {
                        aMap.moveCamera(CameraUpdateFactory.zoomTo(16));
                        move(list.get(0).getLatLonPoint().getLatitude(), list.get(0).getLatLonPoint().getLongitude());
                    }
                }
            }
        }
        postMessageRunnable = () -> {
            Map<String, String> map = new HashMap<>(2);
            map.put("poiSearchResult", builder.toString());
            methodChannel.invokeMethod("poiSearchResult", map);
        };
        if (platformThreadHandler.getLooper() == Looper.myLooper()) {
            postMessageRunnable.run();
        } else {
            platformThreadHandler.post(postMessageRunnable);
        }
    }

    @Override
    public void onRegeocodeSearched(RegeocodeResult regeocodeResult, int i) {
        builder.delete(0, builder.length());
        Map<String, String> map = new HashMap<>();
        if (i == AMapException.CODE_AMAP_SUCCESS) {
            if (regeocodeResult != null && regeocodeResult.getRegeocodeAddress() != null) {
                map.put("city", regeocodeResult.getRegeocodeAddress().getCity());
                map.put("province", regeocodeResult.getRegeocodeAddress().getProvince());
                map.put("district", regeocodeResult.getRegeocodeAddress().getDistrict());
                map.put("formatAddress", regeocodeResult.getRegeocodeAddress().getFormatAddress());
                map.put("township", regeocodeResult.getRegeocodeAddress().getTownship());
                if (regeocodeResult.getRegeocodeAddress().getStreetNumber() != null) {
                    map.put("street", regeocodeResult.getRegeocodeAddress().getStreetNumber().getStreet());
                    map.put("streetNumber", regeocodeResult.getRegeocodeAddress().getStreetNumber().getNumber());
                }
                map.put("building", regeocodeResult.getRegeocodeAddress().getBuilding());
            }
        }
        postMessageRunnable = () -> {
            methodChannel.invokeMethod("onReGeocode", map);
        };
        if (platformThreadHandler.getLooper() == Looper.myLooper()) {
            postMessageRunnable.run();
        } else {
            platformThreadHandler.post(postMessageRunnable);
        }
    }

    @Override
    public void onGeocodeSearched(GeocodeResult geocodeResult, int i) {

    }
}
