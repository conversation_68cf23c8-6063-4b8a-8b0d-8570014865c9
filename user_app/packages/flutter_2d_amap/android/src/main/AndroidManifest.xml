<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.weilu.flutter.flutter_2d_amap">

    <!--允许访问网络，必选权限-->
    <uses-permission android:name="android.permission.INTERNET" />
    <!--允许获取粗略位置，若用GPS实现定位小蓝点功能则必选-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!--用于访问GPS定位-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!--允许获取设备和运营商信息，用于问题排查和网络定位，若无gps但仍需实现定位小蓝点功能则此权限必选-->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <!--允许获取网络状态，用于网络定位，若无gps但仍需实现定位小蓝点功能则此权限必选-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!--允许获取wifi网络信息，用于网络定位，若无gps但仍需实现定位小蓝点功能则此权限必选-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!--允许获取wifi状态改变，用于网络定位，若无gps但仍需实现定位小蓝点功能则此权限必选-->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <!--用于申请调用A-GPS模块-->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />

    <application>
        <!-- 定位需要的服务 使用2.0的定位需要加上这个 -->
        <service android:name="com.amap.api.location.APSService" />
    </application>

</manifest>
