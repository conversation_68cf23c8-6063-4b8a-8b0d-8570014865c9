group 'com.weilu.flutter.flutter_2d_amap'
version '1.1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.1'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    namespace 'com.weilu.flutter.flutter_2d_amap'

    compileSdk 35

    defaultConfig {
        minSdkVersion 19
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
    api 'com.amap.api:map2d:6.0.0'
    api 'com.amap.api:search:9.4.0'
    api 'com.amap.api:location:6.4.3'
    api 'androidx.core:core:1.13.1'
    implementation 'com.google.code.gson:gson:2.11.0'
}
