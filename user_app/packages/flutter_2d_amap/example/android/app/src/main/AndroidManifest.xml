<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.weilu.flutter.flutter_2d_amap_example">

    <!-- io.flutter.app.FlutterApplication is an android.app.Application that
         calls FlutterMain.startInitialization(this); in its onCreate method.
         In most cases you can leave this as-is, but you if you want to provide
         additional functionality it is fine to subclass or reimplement
         FlutterApplication and put your custom class here. -->
    <application
        android:networkSecurityConfig="@xml/network_security_config"
        android:label="flutter_2d_amap_example"
        android:icon="@mipmap/ic_launcher">

        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="aa86b8755beb4415f83b75044f1cd792"/>

        <activity
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:name=".MainActivity"
            android:exported="true"
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize">
            <!-- Specify that the launch screen should continue being displayed -->
            <!-- until Flutter renders its first frame. -->
            <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/launch_background" />

            <!-- Theme to apply as soon as Flutter begins rendering frames -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@android:style/Theme.Black.NoTitleBar"
                />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>
