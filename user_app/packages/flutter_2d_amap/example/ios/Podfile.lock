PODS:
  - AMap2DMap (5.6.1):
    - AMapFoundation (~> 1.4)
  - AMapFoundation (1.6.9)
  - AMapLocation (2.8.0):
    - AMapFoundation (~> 1.6.9)
  - AMapSearch (8.1.0):
    - AMapFoundation (~> 1.6.9)
  - Flutter (1.0.0)
  - flutter_2d_amap (0.0.1):
    - AMap2DMap
    - AMapLocation (~> 2.8.0)
    - AMapSearch (~> 8.1.0)
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_2d_amap (from `.symlinks/plugins/flutter_2d_amap/ios`)

SPEC REPOS:
  trunk:
    - AMap2DMap
    - AMapFoundation
    - AMapLocation
    - AMapSearch

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_2d_amap:
    :path: ".symlinks/plugins/flutter_2d_amap/ios"

SPEC CHECKSUMS:
  AMap2DMap: cac76bc057de18a1641f34df6b50bf5bc6b23571
  AMapFoundation: 8d8ecbb0b2e9ce5487995360d26c885d94642bfd
  AMapLocation: 5ef44a1117be7dc541cb7a7d43d03c5ee91e4387
  AMapSearch: 5c1cc07429f04b9cc76438fcb2411c66fdbbb178
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_2d_amap: d116fde15b095d09399c5f754f52015c829fa718

PODFILE CHECKSUM: 5f03b1eaae5eca60e9a57a199f5f74fa6f5f3c21

COCOAPODS: 1.11.2
