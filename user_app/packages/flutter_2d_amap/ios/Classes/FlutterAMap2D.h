//
//  FlutterAMap2D.h
//  flutter_2d_amap
//
//  Created by weilu on 2019/7/1.
//

#import <Flutter/Flutter.h>
#import <AMapLocationKit/AMapLocationKit.h>
#import <AMapSearchKit/AMapSearchKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface FlutterAMap2DController : NSObject<FlutterPlatformView>
    
- (instancetype)initWithFrame:(CGRect)frame
               viewIdentifier:(int64_t)viewId
                    arguments:(id _Nullable)args
              binaryMessenger:(NSObject<FlutterBinaryMessenger>*)messenger;
    
- (UIView*)view;

- (void)amapLocationManager:(AMapLocationManager *)manager didUpdateLocation:(CLLocation *)location reGeocode:(AMapLocationReGeocode *)reGeocode;

- (void)handleSearchError:(NSString *)errorMessage;
- (void)handleJSONError:(NSError *)error;
- (void)updateMapViewWithPOI:(AMapPOI *)poi;

@end

@interface FlutterAMap2DFactory : NSObject<FlutterPlatformViewFactory>
- (instancetype)initWithMessenger:(NSObject<FlutterBinaryMessenger>*)messenger;
@end

NS_ASSUME_NONNULL_END
