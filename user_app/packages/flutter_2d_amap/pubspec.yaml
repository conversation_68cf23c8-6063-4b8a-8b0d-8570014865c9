name: flutter_2d_amap
description: A flutter plugin for 2D amap.
version: 0.3.0+3
homepage: 'https://github.com/simplezhli/flutter_2d_amap'

publish_to: 'none'

environment:
  sdk: ">=3.0.0-0 <4.0.0"
  flutter: ">=1.10.0"

dependencies:
  flutter:
    sdk: flutter
  #  https://pub.flutter-io.cn/packages/js
  js: ^0.6.7

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://www.dartlang.org/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The androidPackage and pluginClass identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: com.weilu.flutter.flutter_2d_amap
        pluginClass: Flutter2dAmapPlugin
      ios:
        pluginClass: Flutter2dAmapPlugin
