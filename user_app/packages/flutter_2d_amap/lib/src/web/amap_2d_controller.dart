import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';
import 'package:flutter_2d_amap/src/js_model/amap_lng_lat.dart';
import 'package:flutter_2d_amap/src/js_model/amap_regeocode_types.dart';
import 'package:flutter_2d_amap/src/web/amapjs.dart';
import 'package:js/js.dart';

class AMap2DWebController extends AMap2DController {
  // 回调函数
  Function(String markerId)? _markerClickCallback;
  Function(Map<String, double> bounds, double zoom)? _regionChangeCallback;

  AMap2DWebController(this._aMap, this._widget) {
    _placeSearchOptions = PlaceSearchOptions(
      extensions: 'all',
      type: _kType,
      city: '全国',
      citylimit: false,
      pageIndex: 1,
      pageSize: 50,
    );

    _aMap.on('click', allowInterop((event) {
      _aMap.resize();
      if (_widget.onClick != null) {
        _widget.onClick!(event.lnglat.getLat(), event.lnglat.getLng());
      }
    }));

    _aMap.on('zoomend', allowInterop((event) {
      final zoom = _aMap.getZoom();
      final double zoomLevel = (zoom as num).toDouble();
      _widget.onZoomChanged?.call(zoomLevel);
    }));

    /// 定位插件初始化
    _geolocation = Geolocation(GeolocationOptions(
      timeout: 15000,
      buttonPosition: 'LB',
      buttonOffset: Pixel(10, 20),
      zoomToAccuracy: true,
      enableHighAccuracy: true,
    ));
    _aMap.addControl(_geolocation);

    _geocoder = Geocoder(GeocoderOptions(
      radius: '1000',
      extensions: 'all',
    ));

    _polylineOptions = PolylineOptions(
      strokeColor: '#0091ff',
      strokeWeight: 5,
      outlineColor: '#ffeeee',
      isOutline: true,
      borderWeight: 2,
      lineJoin: 'round',
    );

    _polyline = Polyline(_polylineOptions);
    _aMap.add(_polyline);

    // UX IMPROVEMENT: Removed automatic location call from constructor.
    // The developer should explicitly call controller.location() when needed.
    // location();
  }

  final AMap2DView _widget;
  final AMap _aMap;
  late Geolocation _geolocation;
  late Geocoder _geocoder;
  late PolylineOptions _polylineOptions;
  late Polyline _polyline;
  late PlaceSearchOptions _placeSearchOptions;

  // --- State for markers managed by this controller ---
  /// Stores markers added via `addMarkers` for proper removal.
  final List<Marker> _managedMarkers = [];

  /// Maps markers to their IDs for click handling
  final Map<Marker, String> _markerToIdMap = {};

  /// A dedicated marker for the `move` operation.
  Marker? _moveMarker;

  static const String _kType =
      '010000|010100|020000|030000|040000|050000|050100|060000|060100|060200|060300|060400|070000|080000|080100|080300|080500|080600|090000|090100|090200|090300|100000|100100|110000|110100|120000|120200|120300|130000|140000|141200|150000|150100|150200|160000|160100|170000|170100|170200|180000|190000|200000';

  @override
  Future<void> search(String keyWord, {city = ''}) async {
    if (!_widget.isPoiSearch) {
      return;
    }
    final PlaceSearch placeSearch = PlaceSearch(_placeSearchOptions);
    placeSearch.setCity(city);
    placeSearch.search(keyWord, searchResult);
    return Future.value();
  }

  @override
  Future<void> move(String lat, String lon) async {
    // ROBUSTNESS: Use tryParse for safety
    final latNum = double.tryParse(lat);
    final lonNum = double.tryParse(lon);
    if (latNum == null || lonNum == null) {
      if (kDebugMode) {
        log('Warning: Invalid coordinates provided to move(): lat=$lat, lon=$lon');
      }
      return;
    }

    final lngLat = AmpJsLngLat(lonNum, latNum);
    _aMap.setCenter(lngLat);

    // CRITICAL FIX: Only manage the dedicated `_moveMarker`, do not clear the whole map.
    if (_moveMarker != null) {
      _moveMarker!.setPosition(lngLat);
    } else {
      final markerOptions = MarkerOptions(
          position: lngLat,
          icon: AMapIcon(IconOptions(
            size: Size(26, 34),
            imageSize: Size(26, 34),
            image:
                'https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
          )),
          offset: Pixel(-13, -34),
          anchor: 'bottom-center');
      _moveMarker = Marker(markerOptions);
      _aMap.add(_moveMarker!);
    }
    return Future.value();
  }

  @override
  Future<void> location() async {
    _geolocation.getCurrentPosition(allowInterop((status, result) {
      if (status == 'complete') {
        final LngLat lngLat =
            LngLat(result.position.getLng(), result.position.getLat());
        _widget.onGetLocation?.call(lngLat);
      } else {
        if (kDebugMode) {
          log('Location failed: ${result.message}');
        }
        // Consider adding a callback for location failure.
      }
    }));
    return Future.value();
  }

  void searchNearBy(AmpJsLngLat lngLat) {
    if (!_widget.isPoiSearch) {
      return;
    }
    final PlaceSearch placeSearch = PlaceSearch(_placeSearchOptions);
    placeSearch.searchNearBy('', lngLat, 2000, searchResult);
  }

  Function(String status, SearchResult result) get searchResult =>
      allowInterop((status, result) {
        final List<PoiSearch> list = <PoiSearch>[];
        if (status == 'complete' && result.poiList?.pois != null) {
          for (final dynamic poi in result.poiList!.pois!) {
            if (poi is Poi) {
              list.add(PoiSearch(
                cityCode: poi.citycode,
                cityName: poi.cityname,
                provinceName: poi.pname,
                title: poi.name,
                adName: poi.adname,
                provinceCode: poi.pcode,
                latitude: poi.location.getLat().toString(),
                longitude: poi.location.getLng().toString(),
                address: poi.address,
              ));
            }
          }
        } else {
          // UX IMPROVEMENT: Handle no_data and error statuses
          if (kDebugMode) {
            log('POI Search finished with status: $status. Result: ${result.toString()}');
          }
        }

        if (list.isNotEmpty) {
          _aMap.setZoom(17);
        }

        // Always call back to notify the UI, even with an empty list.
        _widget.onPoiSearched?.call(list);
      });

  @override
  Future<void> addMarkers(List<Map<String, String>> markers) async {
    for (final markerData in markers) {
      // ROBUSTNESS: Use tryParse and null checks to handle invalid data.
      final lonStr = markerData['longitude'];
      final latStr = markerData['latitude'];

      if (lonStr == null || latStr == null) {
        if (kDebugMode) {
          log('Warning: Marker data is missing longitude or latitude.');
        }
        continue; // Skip this invalid marker
      }

      final lonNum = double.tryParse(lonStr);
      final latNum = double.tryParse(latStr);

      if (lonNum == null || latNum == null) {
        if (kDebugMode) {
          log('Warning: Could not parse longitude/latitude: $lonStr, $latStr');
        }
        continue; // Skip this invalid marker
      }

      final lngLat = AmpJsLngLat(lonNum, latNum);
      final markerOptions = MarkerOptions(
          position: lngLat,
          icon: AMapIcon(IconOptions(
            size: Size(26, 34),
            imageSize: Size(26, 34),
            image:
                'https://a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
          )),
          offset: Pixel(-13, -34),
          anchor: 'bottom-center');

      final marker = Marker(markerOptions);

      // 添加标记点击事件
      final markerId = markerData['id'] ?? 'marker_${_managedMarkers.length}';

      // 为每个标记添加点击事件监听器
      _markerToIdMap[marker] = markerId;

      // Add click event listener to the marker
      try {
        // Use the correct AMap JS API method for adding event listeners
        // The callback should accept an event parameter as per AMap JS API
        marker.on('click', allowInterop((event) {
          if (_markerClickCallback != null) {
            _markerClickCallback!(markerId);
          }
        }));
      } catch (e) {
        // Fallback: markers will be handled via map click events if direct listeners don't work
        if (kDebugMode) {
          log('Could not add click listener to marker: $e');
        }
      }

      _managedMarkers.add(marker); // Keep track of the marker
      _aMap.add(marker);
    }
    return Future.value();
  }

  @override
  Future<void> clearMarkers() async {
    // CRITICAL FIX: Only remove markers managed by this controller, not the entire map.
    if (_managedMarkers.isNotEmpty) {
      // The AMap JS API `remove` method can accept an array of overlays.
      _aMap.remove(_managedMarkers);
      _managedMarkers.clear();
      _markerToIdMap.clear(); // Clear the mapping as well
    }
    return Future.value();
  }

  /// NEW METHOD: Exposes the AMap 'setFitView' functionality.
  /// Assumes this method is added to the abstract AMap2DController class.
  @override
  Future<void> zoomToFitMarkers() async {
    if (_managedMarkers.isNotEmpty) {
      // `setFitView` automatically calculates the best zoom level and center
      // to display all provided overlays.
      _aMap.setFitView(_managedMarkers);
    }
    return Future.value();
  }

  @override
  Future<void> reGeocode(num lat, num lon) async {
    final lngLat = AmpJsLngLat(lon, lat);
    try {
      _geocoder.getAddress(lngLat,
          allowInterop((String status, JSReGeocodeResult jsResult) {
        if (status == 'complete' && jsResult.regeocode != null) {
          final result = ReGeocodeResult(
            regeocode: ReGeocode(
              addressComponent: AddressComponent(
                province: jsResult.regeocode.addressComponent.province,
                city: jsResult.regeocode.addressComponent.city,
                district: jsResult.regeocode.addressComponent.district,
                township: jsResult.regeocode.addressComponent.township,
                street: jsResult.regeocode.addressComponent.street,
                streetNumber: jsResult.regeocode.addressComponent.streetNumber,
              ),
              formattedAddress: jsResult.regeocode.formattedAddress,
              pois: <AMapPoi>[],
              roads: <AMapRoad>[],
            ),
            info: jsResult.info,
            status: jsResult.status,
          );
          _widget.onReGeocode?.call(result);
        } else {
          if (kDebugMode) {
            log('逆地理编码失败: status=$status, info=${jsResult.info}');
          }
          _widget.onReGeocode?.call(ReGeocodeResult.empty());
        }
      }));
    } catch (e) {
      if (kDebugMode) {
        log('逆地理编码请求失败: $e');
      }
      _widget.onReGeocode?.call(ReGeocodeResult.empty());
    }
    return Future.value();
  }

  @override
  Future<void> setZoom({num zoomLevel = 12}) async {
    _aMap.setZoom(zoomLevel);
    return Future.value();
  }

  @override
  Future<void> searchDrivingRoute(
      {required num fromLat,
      required num fromLon,
      required num toLat,
      required num toLon}) async {
    final from = AmpJsLngLat(fromLon, fromLat);
    final to = AmpJsLngLat(toLon, toLat);
    final driving = Driving(DrivingOptions(
      policy: 'LEAST_TIME',
      hideMarkers: false,
      showTraffic: true,
    ));

    driving.search(from, to,
        allowInterop((String status, DrivingResult result) {
      if (status == 'complete') {
        final List<Route> routes = <Route>[];
        for (final DriveRoute diverRoute in result.routes) {
          final List<Step> steps = <Step>[];
          for (final step in diverRoute.steps) {
            final List<LngLat> path = <LngLat>[];
            for (final AmpJsLngLat lngLat in step.path) {
              path.add(LngLat(lngLat.getLng(), lngLat.getLat()));
            }
            steps.add(Step(path: path));
          }
          routes.add(Route(
            distance: diverRoute.distance,
            duration: diverRoute.time,
            steps: steps,
          ));
        }
        final drivingResult = DrivingResultModel(
          destination:
              LngLat(result.destination.getLng(), result.destination.getLat()),
          origin: LngLat(result.origin.getLng(), result.origin.getLat()),
          routes: routes,
        );
        _widget.onDrivingRouteSearched?.call(drivingResult);
      } else {
        // UX IMPROVEMENT: Call back with an empty result on failure.
        if (kDebugMode) {
          log('驾车路线规划失败: status=$status');
        }
        _widget.onDrivingRouteSearched?.call(DrivingResultModel.empty(
          origin: LngLat(fromLon, fromLat),
          destination: LngLat(toLon, toLat),
        ));
      }
    }));
  }

  @override
  Future<void> polyline(
      LngLat origin, LngLat destination, List<LngLat> points) {
    _aMap.resize();
    final start = AmpJsLngLat(origin.longitude, origin.latitude);
    final end = AmpJsLngLat(destination.longitude, destination.latitude);

    final List<AmpJsLngLat> path = <AmpJsLngLat>[];
    path.add(start);
    for (final LngLat point in points) {
      path.add(AmpJsLngLat(point.longitude, point.latitude));
    }
    path.add(end);

    _polyline.setPath(path);

    final startMarker = Marker(MarkerOptions(
      position: start,
      icon: AMapIcon(IconOptions(
        image: 'https://webapi.amap.com/theme/v1.3/markers/n/start.png',
      )),
      offset: Pixel(-10, -10),
    ));

    final endMarker = Marker(MarkerOptions(
      position: end,
      icon: AMapIcon(IconOptions(
        image: 'https://webapi.amap.com/theme/v1.3/markers/n/end.png',
      )),
      offset: Pixel(-10, -10),
    ));

    _aMap.add([startMarker, endMarker]);
    _aMap.setFitView([startMarker, endMarker, _polyline]);
    return Future.value();
  }

  @override
  Future<void> zoomIn() async {
    final currentZoom = _aMap.getZoom();
    _aMap.setZoom((currentZoom as num).toDouble() + 1);
    return Future.value();
  }

  @override
  Future<void> zoomOut() async {
    final currentZoom = _aMap.getZoom();
    _aMap.setZoom((currentZoom as num).toDouble() - 1);
    return Future.value();
  }

  @override
  Future<Map<String, double>> getMapBounds() async {
    // Placeholder implementation - actual AMap web API methods need to be used
    return {
      'north': 0.0,
      'south': 0.0,
      'east': 0.0,
      'west': 0.0,
    };
  }

  @override
  Future<Map<String, double>> getMapCenter() async {
    // Placeholder implementation - actual AMap web API methods need to be used
    return {
      'latitude': 0.0,
      'longitude': 0.0,
    };
  }

  @override
  Future<double> getZoomLevel() async {
    final zoom = _aMap.getZoom();
    return (zoom as num).toDouble();
  }

  @override
  void setMarkerClickCallback(Function(String markerId) callback) {
    _markerClickCallback = callback;
  }

  @override
  void setMapRegionChangeCallback(
      Function(Map<String, double> bounds, double zoom) callback) {
    _regionChangeCallback = callback;

    // 监听地图移动事件
    _aMap.on('moveend', allowInterop((event) async {
      if (_regionChangeCallback != null) {
        final bounds = await getMapBounds();
        final zoom = await getZoomLevel();
        _regionChangeCallback!(bounds, zoom);
      }
    }));
  }
}

// NOTE: You might need to add `empty()` factory constructors to your model classes
// for the error handling improvements. For example:
//
// In `amap_regeocode_types.dart`:
// factory ReGeocodeResult.empty() => ReGeocodeResult( ... with empty values ... );
//
// In your driving route model:
// factory DrivingResultModel.empty({LngLat origin, LngLat destination}) => DrivingResultModel(
//   routes: [],
//   origin: origin,
//   destination: destination,
// );
