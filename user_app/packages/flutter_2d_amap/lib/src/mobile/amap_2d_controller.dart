import 'dart:convert';
import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';

class AMap2DMobileController extends AMap2DController {
  AMap2DMobileController(
    int id,
    this._widget,
  ) : _channel = MethodChannel('plugins.weilu/flutter_2d_amap_$id') {
    _channel.setMethodCallHandler(_handleMethod);
  }

  final MethodChannel _channel;
  final AMap2DView _widget;
  
  // 回调函数
  Function(String markerId)? _markerClickCallback;
  Function(Map<String, double> bounds, double zoom)? _regionChangeCallback;

  Future<dynamic> _handleMethod(MethodCall call) async {
    try {
      final String method = call.method;
      final args = call.arguments as Map<Object?, Object?>?;

      switch (method) {
        case 'poiSearchResult':
          {
            if (_widget.onPoiSearched != null && args != null) {
              final searchResultJson = args['poiSearchResult'] as String?;
              if (searchResultJson != null) {
                final List<PoiSearch> list =
                    (json.decode(searchResultJson) as List)
                        .map((item) =>
                            PoiSearch.fromJsonMap(item as Map<String, dynamic>))
                        .toList();
                _widget.onPoiSearched!(list);
              }
            }
            break;
          }
        case 'onLocationChanged':
          {
            if (_widget.onGetLocation != null && args != null) {
              final lon = double.tryParse(args['longitude']?.toString() ?? '');
              final lat = double.tryParse(args['latitude']?.toString() ?? '');

              if (lon != null && lat != null) {
                _widget.onGetLocation!(LngLat(lon, lat));
              }
            }
            break;
          }
        case 'onReGeocode':
          {
            if (_widget.onReGeocode != null && args != null) {
              final reGeocode = ReGeocode(
                addressComponent: AddressComponent(
                  province: args['province']?.toString() ?? '',
                  city: args['city']?.toString() ?? '',
                  district: args['district']?.toString() ?? '',
                  township: args['township']?.toString() ?? '',
                  street: args['street']?.toString() ?? '',
                  streetNumber: args['streetNumber']?.toString() ?? '',
                ),
                formattedAddress: args['formatAddress']?.toString() ?? '未能获取地址',
                pois: [],
                roads: [],
              );
              final reGeocodeResult = ReGeocodeResult(
                regeocode: reGeocode,
                info: args['info']?.toString() ?? 'SUCCESS',
                status: args['status']?.toString() ?? '1',
              );

              _widget.onReGeocode!(reGeocodeResult);
            }
            break;
          }
        case 'onMarkerClick':
          {
            if (_markerClickCallback != null && args != null) {
              final markerId = args['markerId']?.toString();
              if (markerId != null) {
                _markerClickCallback!(markerId);
              }
            }
            break;
          }
        case 'onMapRegionChange':
          {
            if (_regionChangeCallback != null && args != null) {
              final north = double.tryParse(args['north']?.toString() ?? '');
              final south = double.tryParse(args['south']?.toString() ?? '');
              final east = double.tryParse(args['east']?.toString() ?? '');
              final west = double.tryParse(args['west']?.toString() ?? '');
              final zoom = double.tryParse(args['zoom']?.toString() ?? '');
              
              if (north != null && south != null && east != null && west != null && zoom != null) {
                final bounds = {
                  'north': north,
                  'south': south,
                  'east': east,
                  'west': west,
                };
                _regionChangeCallback!(bounds, zoom);
              }
            }
            break;
          }
      }
    } catch (e, s) {
      log(
        'Error in _handleMethod: ${call.method}',
        error: e,
        stackTrace: s,
      );
    }
    return Future<dynamic>.value('');
  }

  Future<void> _invokeMethod(String method, [dynamic arguments]) async {
    try {
      await _channel.invokeMethod(method, arguments);
    } on PlatformException catch (e, s) {
      log(
        'PlatformException invoking $method',
        error: e,
        stackTrace: s,
      );
    } catch (e, s) {
      log(
        'Error invoking $method',
        error: e,
        stackTrace: s,
      );
    }
  }

  @override
  Future<void> search(String keyWord, {String city = ''}) async {
    return _invokeMethod('search', <String, dynamic>{
      'keyWord': keyWord,
      'city': city,
    });
  }

  @override
  Future<void> move(String lat, String lon) async {
    return _invokeMethod('move', <String, dynamic>{'lat': lat, 'lon': lon});
  }

  @override
  Future<void> location() async {
    return _invokeMethod('location');
  }

  @override
  Future<void> addMarkers(List<Map<String, String>> markers) {
    return _invokeMethod('addMarkers', <String, dynamic>{
      'markers': markers,
    });
  }

  @override
  Future<void> clearMarkers() {
    return _invokeMethod('clearMarkers');
  }

  @override
  Future<void> reGeocode(num lat, num lon) {
    return _invokeMethod('reGeocode', <String, dynamic>{
      'lat': lat,
      'lon': lon,
    });
  }

  @override
  Future<void> setZoom({num zoomLevel = 12}) async {
    return _invokeMethod('setZoom', <String, dynamic>{
      'zoomLevel': zoomLevel,
    });
  }

  @override
  Future<void> zoomToFitMarkers() {
    // [COMPLETED] - Native functionality for iOS and Android is now implemented.
    return _invokeMethod('zoomToFitMarkers');
  }

  @override
  Future<void> searchDrivingRoute(
      {required num fromLat,
      required num fromLon,
      required num toLat,
      required num toLon}) {
    // TODO: 在 iOS 和 Android 端实现 searchDrivingRoute 功能
    log('searchDrivingRoute is not implemented for mobile yet.');
    throw UnimplementedError(
        'searchDrivingRoute is not implemented for mobile');
  }

  @override
  Future<void> polyline(
      LngLat origin, LngLat destination, List<LngLat> points) {
    // TODO: 在 iOS 和 Android 端实现 polyline 功能
    log('polyline is not implemented for mobile yet.');
    throw UnimplementedError('polyline is not implemented for mobile');
  }

  @override
  Future<void> zoomIn() {
    return _invokeMethod('zoomIn');
  }

  @override
  Future<void> zoomOut() {
    return _invokeMethod('zoomOut');
  }

  @override
  Future<Map<String, double>> getMapBounds() async {
    final result = await _invokeMethod('getMapBounds');
    return Map<String, double>.from(result as Map);
  }

  @override
  Future<Map<String, double>> getMapCenter() async {
    final result = await _invokeMethod('getMapCenter');
    return Map<String, double>.from(result as Map);
  }

  @override
  Future<double> getZoomLevel() async {
    final result = await _invokeMethod('getZoomLevel');
    return (result as num).toDouble();
  }

  @override
  void setMarkerClickCallback(Function(String markerId) callback) {
    _markerClickCallback = callback;
  }

  @override
  void setMapRegionChangeCallback(Function(Map<String, double> bounds, double zoom) callback) {
    _regionChangeCallback = callback;
  }
}
