@JS()
library amap_regeocode;

import 'package:flutter_2d_amap/src/js_model/amap_lng_lat.dart';
import 'package:js/js.dart';

@JS()
@anonymous
class JSReGeocodeResult {
  external JSReGeocode get regeocode;

  external String get info;

  external String get status;
}

@JS()
@anonymous
class JSReGeocode {
  external JSAddressComponent get addressComponent;

  external String get formattedAddress;

  external List<JSAMapPoi> get pois;

  external List<JSAMapRoad> get roads;
}

@JS()
@anonymous
class JSAddressComponent {
  external String get province;

  external String get city;

  external String get district;

  external String get township;

  external String get street;

  external String get streetNumber;
}

@JS()
@anonymous
class JSAMapPoi {
  external String get id;

  external String get name;

  external String get type;

  external num get distance;

  external AmpJsLngLat get location;

  external String get address;
}

@JS()
@anonymous
class JSAMapRoad {
  external String get id;

  external String get name;

  external num get distance;

  external String get direction;

  external AmpJsLngLat get location;
}
