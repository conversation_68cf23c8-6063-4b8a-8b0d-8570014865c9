import 'package:flutter/material.dart';
import 'package:flutter_2d_amap/src/interface/amap_2d_controller.dart';
import 'package:flutter_2d_amap/src/model/driving_result_model.dart';
import 'package:flutter_2d_amap/src/model/lng_lat.dart';
import 'package:flutter_2d_amap/src/model/poi_search_model.dart';
import 'package:flutter_2d_amap/src/model/re_geocode_result_model.dart';

// fmt: off
import 'amap_2d_view_state.dart'
    if (dart.library.html) 'web/amap_2d_view_state.dart'
    if (dart.library.io) 'mobile/amap_2d_view_state.dart';
// fmt: on

typedef AMap2DViewCreatedCallback = void Function(AMap2DController controller);

class AMap2DView extends StatefulWidget {
  const AMap2DView({
    super.key,
    this.isPoiSearch = true,
    this.onPoiSearched,
    this.onAMap2DViewCreated,
    this.onReGeocode,
    this.onGetLocation,
    this.onZoomChanged,
    this.onDrivingRouteSearched,
    this.onClick,
  });

  final bool isPoiSearch;
  final AMap2DViewCreatedCallback? onAMap2DViewCreated;
  final Function(List<PoiSearch>)? onPoiSearched;
  final Function(ReGeocodeResult)? onReGeocode;
  final Function(LngLat)? onGetLocation;
  final Function(double)? onZoomChanged;
  final Function(DrivingResultModel)? onDrivingRouteSearched;
  final Function(num lat, num lon)? onClick;

  @override
  AMap2DViewState createState() => AMap2DViewState();
}
