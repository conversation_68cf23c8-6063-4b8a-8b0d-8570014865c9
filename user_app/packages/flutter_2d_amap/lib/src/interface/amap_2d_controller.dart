import 'package:flutter_2d_amap/src/model/lng_lat.dart';

abstract class AMap2DController {
  /// POI关键字搜索
  ///
  /// city：cityName（中文或中文全拼）、cityCode均可
  Future<void> search(String keyWord, {String city = ''});

  /// 移动地图中心点
  Future<void> move(String lat, String lon);

  /// 开始定位
  Future<void> location();

  /// 逆地理编码
  Future<void> reGeocode(num lat, num lon);

  /// 添加一组标记点
  Future<void> addMarkers(List<Map<String, String>> markers);

  /// 清除所有通过 `addMarkers` 添加的标记
  Future<void> clearMarkers();

  /// 自动缩放地图以适应所有通过 `addMarkers` 添加的标记点
  Future<void> zoomToFitMarkers();

  /// 设置地图缩放级别
  Future<void> setZoom({num zoomLevel = 12});

  /// 放大地图
  Future<void> zoomIn();

  /// 缩小地图
  Future<void> zoomOut();

  /// 获取当前地图边界
  Future<Map<String, double>> getMapBounds();

  /// 获取当前地图中心点
  Future<Map<String, double>> getMapCenter();

  /// 获取当前缩放级别
  Future<double> getZoomLevel();

  /// 设置标记点击回调
  void setMarkerClickCallback(Function(String markerId) callback);

  /// 设置地图区域变化回调
  void setMapRegionChangeCallback(Function(Map<String, double> bounds, double zoom) callback);

  /// 驾车路线规划
  Future<void> searchDrivingRoute({
    required num fromLat,
    required num fromLon,
    required num toLat,
    required num toLon,
  });

  /// 在地图上绘制折线
  Future<void> polyline(LngLat origin, LngLat destination, List<LngLat> points);
}
