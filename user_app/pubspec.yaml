name: user_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.3.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  dio: ^5.7.0
  shared_preferences: ^2.3.2
  provider: ^6.1.4
  image_picker: ^0.8.9
  flutter_oss_aliyun: ^6.4.1
  image_picker_for_web: ^2.1.0
  flutter_2d_amap:
    path: ./packages/flutter_2d_amap
  photo_view: ^0.15.0
  expandable_text: ^2.3.0
  algoliasearch: ^1.27.3
  permission_handler: ^11.4.0
  pinput: ^5.0.0
  smooth_page_indicator: ^1.1.0
  confetti: ^0.8.0
  cached_network_image: ^3.3.1
  go_router: ^14.2.7
  awesome_snackbar_content: ^0.1.3
  flutter_staggered_grid_view: ^0.7.0
  auto_size_text: ^3.0.0
  lottie: ^3.1.3
  dotlottie_loader: ^0.0.4
  get_it: ^7.2.0
  timeago: ^3.7.0
  dart_mappable: ^4.3.0
  url_launcher: ^6.3.2
  weather_icons: ^3.0.0
  file_picker: ^9.0.2
  flutter_reorderable_grid_view: ^5.5.0
  uuid: ^3.0.6
  geolocator: ^12.0.0
  collection: ^1.19.1
  share_plus: ^11.0.0
  shimmer: ^3.0.0
  tencent_cloud_chat_sdk: ^8.6.7019+5
  package_info_plus: ^8.3.0
  qr_flutter: ^4.1.0
  fl_chart: ^1.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  change_app_package_name: ^1.4.0
  dart_mappable_builder: ^4.3.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/lottie/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
