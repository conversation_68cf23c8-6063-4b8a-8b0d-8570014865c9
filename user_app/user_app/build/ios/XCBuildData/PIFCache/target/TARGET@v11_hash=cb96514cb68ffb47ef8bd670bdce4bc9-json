{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984f53422c710d7a091e1b6d37892b00b5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98209191ad06c6c7b788157e0d4e0ddd80", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98209191ad06c6c7b788157e0d4e0ddd80", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987c89e895976430144cc4b4d47290571a", "guid": "bfdfe7dc352907fc980b868725387e981c80b1aed9ea61b4bdfa2682393b1822", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c639d8d99c9dfc7b0dea4e969ca26fa", "guid": "bfdfe7dc352907fc980b868725387e98dd93c8e9a69d7330c101c7eb5903f5af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835ae1b380c67c796f8f21728a66abd8a", "guid": "bfdfe7dc352907fc980b868725387e9855a23697b102025fb93a72422f191f5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98636632a9f20fd2bbc1d8388c1a084cdf", "guid": "bfdfe7dc352907fc980b868725387e987029a8515732bab4879453564b0ccd86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2d3170e237093069db9b4f7d85368fe", "guid": "bfdfe7dc352907fc980b868725387e98ec140dbd734a0fdd2dcfd4c6c8663513", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e21b806574d8a8d6005b368d9c0a1445", "guid": "bfdfe7dc352907fc980b868725387e98f1da7c189d76f568081d6e52979e2979", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837640fa64b7ad99f1706d019b1d91089", "guid": "bfdfe7dc352907fc980b868725387e98f9352a0c063bcd57ac0ce1b6b70fc943", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7724b9b79a7031e78396eefbfd2041b", "guid": "bfdfe7dc352907fc980b868725387e9803a34c4e4aab5ff267205b8cae98b707", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982452bd4406cdfc646b64f397f6766f06", "guid": "bfdfe7dc352907fc980b868725387e986c7fd18c28a701b0eee626f006c28a89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879c32dc182b8a159cbe453c25432d8c4", "guid": "bfdfe7dc352907fc980b868725387e987c9eda89583e367dd8aad82d627f0828", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834ee7e122aacd8330dc6b772500f371e", "guid": "bfdfe7dc352907fc980b868725387e989ed323e71cf5dcf71a74f56108fa1ab5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98affb58ba1c9ab6eeae66f4b52bbddd73", "guid": "bfdfe7dc352907fc980b868725387e98e1d93f7d29e5a4818b34cb77c1a9e0f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885ba0fd6dfbeaf341eb2753beb171255", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e338941e23c87b0c14f6b6b3e5de85d", "guid": "bfdfe7dc352907fc980b868725387e98e782185ab2b828ef2d4e39e33a094ab9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4fdaa294b901c2aead63b6ee4073c4e", "guid": "bfdfe7dc352907fc980b868725387e98609cb6e0496ee0ae9feaea0dd4e301b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986390e63b5265ffb55cbc0017076bf794", "guid": "bfdfe7dc352907fc980b868725387e986ec53d6c3a7da9d6c4b877654a53e492", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afeacd57ed910622f2e84cb4c6b61273", "guid": "bfdfe7dc352907fc980b868725387e9812cea8bb4cd6d45ebd46bd6f3e51015f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdacb49df7f6d1245505e1e623c5ae82", "guid": "bfdfe7dc352907fc980b868725387e98c424752b4841387e11f8c9b094f9d40b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983336d71835c58bb818c7207047f8947b", "guid": "bfdfe7dc352907fc980b868725387e98f50037cc2253234c688fef565bcc7ef3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894f002f1f5cedb68174f6a88878221bc", "guid": "bfdfe7dc352907fc980b868725387e987d9f8933148c05004460eab2dd43b87e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c17e3f85debd406a410e3b1826f4dc5", "guid": "bfdfe7dc352907fc980b868725387e98e81ac65ea8cc78e5c5e9f5b0e2608c7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842785468cea2b7d632ccec324690e41c", "guid": "bfdfe7dc352907fc980b868725387e98b65b4e294015352d2eb01ef9541b8a1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b4555788fc53de8f6ca5fd6b9ee2c29", "guid": "bfdfe7dc352907fc980b868725387e986a24b6c989b3dbe18a55623e6ca69723", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bc96fb267e6e26a1ecce8ad9407606b6", "guid": "bfdfe7dc352907fc980b868725387e98598eeea3e9320c79460b2b77867fe68e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ea1c0a8c1f86b4a3547c178e30a28bd", "guid": "bfdfe7dc352907fc980b868725387e986002824cd08dce22f72ba90a1e3adf98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98997360bfcec5db8ccd68e78f809ec2a4", "guid": "bfdfe7dc352907fc980b868725387e989455a1e619faa7dfbce4cfc5ace6eaab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881b1ab26ce808129957c5d39815b11bc", "guid": "bfdfe7dc352907fc980b868725387e98b3fe8f49a074019b14073a06c8b09607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a617c484030748764ddf91e7ed5c3df7", "guid": "bfdfe7dc352907fc980b868725387e98d27db33baf6f00010d6c00f6ac011cb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988afe3e3235f1e02e047af4ec4369661f", "guid": "bfdfe7dc352907fc980b868725387e988add6e2634723ce378a6772d9b8e9008"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986492772d6f9337076dfb235dcd941d00", "guid": "bfdfe7dc352907fc980b868725387e982de3c1823d27318b4392da0faedb56ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98858ff4006c82fbe5848483629291d961", "guid": "bfdfe7dc352907fc980b868725387e983e5d823f38a040225e281bf23d0eb589"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1c99c71a96256de054bfb32f88e6d65", "guid": "bfdfe7dc352907fc980b868725387e984b22d083cc4324e8867fd30d35ea7b29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e97ab4295232e9d803cf8656918507b8", "guid": "bfdfe7dc352907fc980b868725387e985f136772974c7d64c05da05e36fca520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e09e969472606f688868a26c073c5432", "guid": "bfdfe7dc352907fc980b868725387e989a762dbdff7464d366f41d8b048882ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800b7533e448fb533b1764857cfd59d74", "guid": "bfdfe7dc352907fc980b868725387e988d2526f581ac12165ccef48048c006ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985db00803f0d6c32d9b21b3c2a8f1085c", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800059a77c3c0b6951bdd557b7b44fb0d", "guid": "bfdfe7dc352907fc980b868725387e9835e621543c349c7e73f36567c33c08b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c42c39b9f1a064f5eb634e94a8460d1b", "guid": "bfdfe7dc352907fc980b868725387e981b0db355ed5212127eba406aa4eba8f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0376a023f068f78ff1d3895314167de", "guid": "bfdfe7dc352907fc980b868725387e981967bd80f144fcad8682bc5f2a1f79f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876eac23ad80a6eae6a4a09eedff19b2c", "guid": "bfdfe7dc352907fc980b868725387e9843486162a48b47ef5160af11148d7233"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e3acb322ee4652702ea952f34071027", "guid": "bfdfe7dc352907fc980b868725387e98b154fee533e2ba1c4cb2300d49e3f6dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb3bf524a9c0c481b6695e33795b50f7", "guid": "bfdfe7dc352907fc980b868725387e98c9a63c0e293ddfe788b85649ceb935f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f674557f7a0bc71df34807d7f2393955", "guid": "bfdfe7dc352907fc980b868725387e98b3deadf21cb05439797bdcfd0270a3a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e06058f7ba1e97107934afa97bf058c1", "guid": "bfdfe7dc352907fc980b868725387e981c87fc23a7cf8c788ba994de6d482bd0"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d56474ef5850c087d24d24a69093581f", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}