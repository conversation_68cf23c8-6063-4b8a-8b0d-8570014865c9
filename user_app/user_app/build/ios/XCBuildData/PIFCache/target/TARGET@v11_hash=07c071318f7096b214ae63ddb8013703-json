{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988be8efe704d54a84744daad4b652b074", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c7a85e77c970caa761c358c5b20b4007", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c7a85e77c970caa761c358c5b20b4007", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/homebrew/Caskroom/flutter/3.19.2/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98efbe89929836c3bba724656fe4907716", "guid": "bfdfe7dc352907fc980b868725387e987f28047e02a7d34a46f8ada17b829c9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b07557decf3b055a7906dfe8a0611bc1", "guid": "bfdfe7dc352907fc980b868725387e98f6c67c4679e7f7927d29cc6777b4854d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a419bdbe573e7ce4ba5ac738c87de187", "guid": "bfdfe7dc352907fc980b868725387e98fcb7e61931146dcd711e99248d6ccb4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98772e56cee943d04d198bd90f877dbdee", "guid": "bfdfe7dc352907fc980b868725387e9895fe4b33f65c965da7dc45a450744ad5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ae9418f4f8f5fd300f1ddc881692e2c", "guid": "bfdfe7dc352907fc980b868725387e9881f94adb811a31f238513440b73eb9ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988073988a5cde3f2ec158fa1a5db17a5a", "guid": "bfdfe7dc352907fc980b868725387e98d2993dc352971d9dec3337f6492eedd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cc92c1fd8ff4bde6ac1bc5c387a698b", "guid": "bfdfe7dc352907fc980b868725387e987744d4bc1141e8c3af7814a94ab5d191", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98459ab40b5c6a5c5b6f92a4a65025da8b", "guid": "bfdfe7dc352907fc980b868725387e98d44ce0ad517ce468c11abe8a1a7ee842", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3272f49e988fd7f4fdc21bb2cbe2c36", "guid": "bfdfe7dc352907fc980b868725387e986dfc835f86ba61de853986e0c332c9bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98838c40f364bba8060410d5c0cd1afa46", "guid": "bfdfe7dc352907fc980b868725387e98832bcfeadec19c9672e7d13bba695944", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ec022904e1421cd437564832f3f70de", "guid": "bfdfe7dc352907fc980b868725387e987522b569e1888c59443fabbfd9eec3d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ec32406d1388def46a16ff8679a423d", "guid": "bfdfe7dc352907fc980b868725387e9826df2a365ab1701d04b81b250e704469", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b930d51c532bc670f959039daf68390c", "guid": "bfdfe7dc352907fc980b868725387e98683700ed014e2584117d9919dc247bfe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896b90d1953fee0f041d7fa5ca778e3fd", "guid": "bfdfe7dc352907fc980b868725387e98a95b46a3d3c80a8cb5734bbb7717809c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dc1559288f6228716adc63cec034112", "guid": "bfdfe7dc352907fc980b868725387e98c814b053e6b465d54fefe875995c908b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbddc628a92a15406e14f721198ff313", "guid": "bfdfe7dc352907fc980b868725387e980e4020bf220b21c849a1247a598ef500", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98918b897b97f1f6170815ab128d33e06c", "guid": "bfdfe7dc352907fc980b868725387e9867e1d59733a195924e43c7221ab3b507", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9843b1f6dafb8faba0f8065104baf242d4", "guid": "bfdfe7dc352907fc980b868725387e9836a664f9634f39c09eb263d32eb33de8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ffe10e94e8a03e01b5859a36c32a289", "guid": "bfdfe7dc352907fc980b868725387e981012013b7c46227b76d2a26c3973c5f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897a2a8407e53f03bcfc40e8232d03b53", "guid": "bfdfe7dc352907fc980b868725387e986adce0f63de9503184a2571c68ae8037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c780dfe8894a1f368ed7677adda37cf", "guid": "bfdfe7dc352907fc980b868725387e98c97111306fd51e26ac236698d182d1fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841eba30b7d27b8f4fa68985c85ad73a5", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803a7180b2f084967589a4674715acef7", "guid": "bfdfe7dc352907fc980b868725387e98c97bce089e46629de083eac4838dd800"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983160c3f3671ec33d0b528bac62316597", "guid": "bfdfe7dc352907fc980b868725387e985817ad63c8e4f8db81700ac14cc618cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea35280769d8781da8c7a1a2a5133c8c", "guid": "bfdfe7dc352907fc980b868725387e986e494a42d2c0843cec709c09ffdc69a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b90d473b65c1ca629046154ca351f47", "guid": "bfdfe7dc352907fc980b868725387e9805c8314d750be278604bc8677fcf4420"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbeb004725c49e3441ef54acdf131605", "guid": "bfdfe7dc352907fc980b868725387e988270e34481adcde5ab4b6a9daa89a011"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989739e4dbf6a471ada4875dc6ff924a3e", "guid": "bfdfe7dc352907fc980b868725387e98615ad6c8a17ee1f04050629a5a2a260a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833761f0ac4a36e3323c230a645997a8d", "guid": "bfdfe7dc352907fc980b868725387e98fd9db82003eb1b08fd6976b7ba6920aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98826744f08ccb874153cb81486e6349e2", "guid": "bfdfe7dc352907fc980b868725387e9836872f22a07d006d803b66800b0fd898"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821dfb4086198a16d92d2d2fadcfba08f", "guid": "bfdfe7dc352907fc980b868725387e985cf4803afdb0ccdba56bf5e79afdbebc"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d56474ef5850c087d24d24a69093581f", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}