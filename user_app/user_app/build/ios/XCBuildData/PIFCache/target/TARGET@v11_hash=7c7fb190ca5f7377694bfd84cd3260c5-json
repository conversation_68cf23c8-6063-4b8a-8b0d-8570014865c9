{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9865d1b2729ebbc240050c8b1e5e24894c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9806c6b6029c5ac4d3c3582a4ed0134603", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981036a964d36baa2bc0eed7839ea67625", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e51c294835e4bd680fc7e11c21b35fa9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981036a964d36baa2bc0eed7839ea67625", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989f4f26e3fc5651297512794d883c28fc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c3e0ae610791d1317bb82bf74f2cab2a", "guid": "bfdfe7dc352907fc980b868725387e98396f6653cf31f4d76b6e905c25061c66", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c196b151006a656a964ab5e46f5f3aee", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9883a86f256640ebaa291f819521c6e3d9", "guid": "bfdfe7dc352907fc980b868725387e98684035e4ea355f85d2ce8d31f363385e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb68809eb469950a2717373ce4380213", "guid": "bfdfe7dc352907fc980b868725387e98fd16d57c27b1eabf50e1127dac248f6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c76504a31e4df496efce41f3fc9207c6", "guid": "bfdfe7dc352907fc980b868725387e98099041aadcb8f294d91191753f71f191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d113bf3f91575d9bf987ffaf27e3d9d9", "guid": "bfdfe7dc352907fc980b868725387e98c9e2bfccc9d3168312d5399f50ad0189"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe93a44ea36a99467d9748cbb12c5f3", "guid": "bfdfe7dc352907fc980b868725387e988618bac04edc65d700f12e4fef9996f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef2cb5276171c3b2296d4f5263eecbe9", "guid": "bfdfe7dc352907fc980b868725387e98a52da68d8516919e8b56ad37a7585c33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84cd89510fc2432c1312b09b369503b", "guid": "bfdfe7dc352907fc980b868725387e982c6f683c11c783ea7c53c8ae9f01a9d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c44778eefa7b901b7aa99fb4519ed235", "guid": "bfdfe7dc352907fc980b868725387e9826576b4dd9473bcd5de7e38ff87cac48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828b86aa91b3cc7863ea0a58b4830f529", "guid": "bfdfe7dc352907fc980b868725387e98417a795a29daa4ca9c7d5b2ff57dc77d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98445d047ffa69cba0a22637b25d6642bc", "guid": "bfdfe7dc352907fc980b868725387e98b2c0cb42cb72376425ca2039edf8674d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ccbbe2c58a54b8127e3d8815dd9c70b", "guid": "bfdfe7dc352907fc980b868725387e983bd059666059b28f7879cbc2abdc80e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891171f1938f4acbcc624d6f5ee7b3db2", "guid": "bfdfe7dc352907fc980b868725387e98d120525366c3bfce54f642bbc73fb0e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d48d7479782be5f3ede7c6eb060a2b5", "guid": "bfdfe7dc352907fc980b868725387e98848ddb021e97391bbad139799c78c251"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98559bd4be8dc00661bcfb0643d68b6d48", "guid": "bfdfe7dc352907fc980b868725387e98cfd9c8b20e785095fc127688014d495c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f525f5790fe15add99214e255d576e", "guid": "bfdfe7dc352907fc980b868725387e98f1493ec0eb6d58c0d721b6199c39a0ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884b6fa42d306121b1f98e016d7df5853", "guid": "bfdfe7dc352907fc980b868725387e9837f78fd17e1c370a7cfc02ee2fe62ea8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277b5aa15b7222818bb2caf82a3a2943", "guid": "bfdfe7dc352907fc980b868725387e9854c019db9b52e754fdfd9f8cdbd2ff73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846db9567a0bf09283f9a3d708579e772", "guid": "bfdfe7dc352907fc980b868725387e98e9c95d12b8d16254c4aab1a051d984b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892cfb46f1d98c4ee6767af36784c66b5", "guid": "bfdfe7dc352907fc980b868725387e98581d39c3161c6c3e032d35d1b76a8619"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bce909d4e864b4beea08deca75a5ea3", "guid": "bfdfe7dc352907fc980b868725387e9882905c16e3c5bc5378f36931f9db88b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b25cc3dfcaeed2a8171d991958d45226", "guid": "bfdfe7dc352907fc980b868725387e982347108c81ad1ce5fe318c74c43cef2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878fb23aea54637222c1a4b857e2c165c", "guid": "bfdfe7dc352907fc980b868725387e98375972ea135116562a0694429b1fdec1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1e7830d2875fb65ce2a62a1b73c9ee5", "guid": "bfdfe7dc352907fc980b868725387e984b87eb81c95dda6b336e93fd3e1e61b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833c4795b136aa5ae4a950d07e38d083a", "guid": "bfdfe7dc352907fc980b868725387e9810c73ec6b5a6079ff7c31a0f3e587829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea82fb917dcd3e5d1bb60d6237933925", "guid": "bfdfe7dc352907fc980b868725387e98d8bfd5dbcfbd0459ab80b44c50668754"}], "guid": "bfdfe7dc352907fc980b868725387e9811301d7d9c817a1ed5b418f942ed4f4a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d56474ef5850c087d24d24a69093581f", "guid": "bfdfe7dc352907fc980b868725387e98ceea6fa24e98c6f64e4280c8e3fa9f34"}], "guid": "bfdfe7dc352907fc980b868725387e989edb21284df659215822536b08cf133e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9823fed06f1349f8d23a81b328256b280a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98df71c66ecc82a9796c50f296fb0a215b", "name": "HydraAsync", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9874406eff01050d7992f58c088679c282", "name": "Hydra.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}