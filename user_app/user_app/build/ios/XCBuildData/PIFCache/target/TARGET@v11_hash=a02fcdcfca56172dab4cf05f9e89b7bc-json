{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825af2e8b77c60b89160d21b6778e64b6", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9866b580db64b7c7b02cb19fab09567245", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fd725d784442277b416f143cfee8cbcf", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98758a7ebe28887a2617ae62919697910a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fd725d784442277b416f143cfee8cbcf", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98dd648399661ef316d424433cfabebdd8", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986195f63a7ee12da4060dacd7c59eb536", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9826ef18db335ac6ebd666559aa571171a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98071fc12ef56b7b4773cc683913b1f25e", "guid": "bfdfe7dc352907fc980b868725387e980e8bb1c86b307254c7f96b9dcbf85eef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814c6a45779212229b27dba1c1c4af93c", "guid": "bfdfe7dc352907fc980b868725387e98ec2d1ad8552753c2c8250c3df06dd0c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871cdc06fb7963aa5f8e759d300090348", "guid": "bfdfe7dc352907fc980b868725387e981a4efdc2e34d7eb875746e8ee8ff6fc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f73d10121f4dc4ea2b6345164fbd5502", "guid": "bfdfe7dc352907fc980b868725387e98ab2779b68cacb26318193d4986e27648"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de6045bdbac61b69bca50d95055774c1", "guid": "bfdfe7dc352907fc980b868725387e98a13a2bb54df9422dd2a7f90816892c53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c301f39f182a2aa356d56f138990dc7", "guid": "bfdfe7dc352907fc980b868725387e98c3be59ac69f5f00890b3a302112ea7b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f6f67cfcfd4c995296f658654b24ae3", "guid": "bfdfe7dc352907fc980b868725387e98179161d7cab21e5988690040d00b1f25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821e2401726d2dc7536728a5b1d3817c0", "guid": "bfdfe7dc352907fc980b868725387e987db455a98cef57f5f8e274b5f0ea4867"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7b9925b18b2f5f1be6e96eea00c734b", "guid": "bfdfe7dc352907fc980b868725387e980820d7ec6b8ce427eb7aaee269cbc29b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4722d56897144c9adea0bd426eadd5", "guid": "bfdfe7dc352907fc980b868725387e988de088a6f93cdd861785face1e0d93e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98364276d7ff27fd48da5e700fa0a2bdd4", "guid": "bfdfe7dc352907fc980b868725387e98c22569498efed1ded128c970293f2455"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98052933e66305c14adf0186da2c4015eb", "guid": "bfdfe7dc352907fc980b868725387e985ccac05731dd6d2ddaf92b83eabd95ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cca27b3f3338173933629b37493e9342", "guid": "bfdfe7dc352907fc980b868725387e98a871cd8509782a79cdd070690ef14bc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c839a6faab9a35f0baa175dc7322802", "guid": "bfdfe7dc352907fc980b868725387e985fdd867c2c084f35e46b1545af8ee3e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a9cbd3128fcd4a34629d27f1ce106e8", "guid": "bfdfe7dc352907fc980b868725387e9887f108d96d68a2b8dda786b095a5436d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fe64a00b392cd2d708340d3acbd08c5", "guid": "bfdfe7dc352907fc980b868725387e983e8124875fd2b93f536bc9d76781b50c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810ee3c51b17f49e352f69614dbe3601b", "guid": "bfdfe7dc352907fc980b868725387e9888fd396d9bfffa3fe9dd669caf3ac000"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5e9969ecd873f8703af862db9eb75fc", "guid": "bfdfe7dc352907fc980b868725387e98706b33edb8c5bee058356b24f0e47650"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af27babfa5dc754af6a5e8588458f187", "guid": "bfdfe7dc352907fc980b868725387e983d92987d346c4bf94a5c43a008cccdc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805e78741f4f61b6ee747596819433211", "guid": "bfdfe7dc352907fc980b868725387e98834e248957d5b2b3b71c5bf3cf7e0f87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeebe025e243613deacba7657b2c6424", "guid": "bfdfe7dc352907fc980b868725387e98f9f22df1b8f21137505ba0d6688f3d5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859551b608f2890d786dc11ad779aaa33", "guid": "bfdfe7dc352907fc980b868725387e985511c8fe518cc5d08bce4ad3aede4571"}], "guid": "bfdfe7dc352907fc980b868725387e98286565c4b4695a14a1ef3f4193df424e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}