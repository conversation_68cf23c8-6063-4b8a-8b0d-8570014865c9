{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bf920419db7ed90c73a414ffef45c1fe", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984aa4ba801a16b3fca2ccc669bd6389ae", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a7b567267109524bc1ff9cbf4d9b854c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9867e1c21b83e57b7f3234c63ebed41516", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a7b567267109524bc1ff9cbf4d9b854c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c3ee34aec936aac2f5fc413d7fe06308", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a3c2b4e75475f961520bb4dfc64717ff", "guid": "bfdfe7dc352907fc980b868725387e98fa81972c6692ec257aee3a16abe30989", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ba577d3aefa9c7bc4223c7ded7228d76", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986f60b734bc6e3dba7d9ebce7381e147f", "guid": "bfdfe7dc352907fc980b868725387e98e814e44113ebfe0380b8b457d9afaec0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6c20888e6b645898a99b75e63e2183f", "guid": "bfdfe7dc352907fc980b868725387e988ab06785aac719581e0c9a0613b2d00e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98075fd8f5b26be5c819f0fed10313378f", "guid": "bfdfe7dc352907fc980b868725387e9818f088a667f23674d4b5accc4e83d2e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9203c904d64a474061aa3a00c5068e4", "guid": "bfdfe7dc352907fc980b868725387e9872b83e405eb6a68910b6ee48a88ab4a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a5319c0b975a7d75f14956bc87dfba3", "guid": "bfdfe7dc352907fc980b868725387e989d885ca130ee7c966dd7ede732d41eda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e06e5627ea87fec534de5b0366dbdcf4", "guid": "bfdfe7dc352907fc980b868725387e989cf6edbc27312359a5a40bf36b31084f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3ed819fc9042652bc4c4cfc629c7181", "guid": "bfdfe7dc352907fc980b868725387e986fb06cc503d4d841935a8fe5b2e6d7c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b18e37981278e5a8279e320af8eabc88", "guid": "bfdfe7dc352907fc980b868725387e981500644020d0093cf426e84848d98985"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1eac36ff5560fb0f52fc9bd49359cf9", "guid": "bfdfe7dc352907fc980b868725387e985d5285ca84965383b263096ecf48fdb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d91b11facc262868d10646a12d69f3c", "guid": "bfdfe7dc352907fc980b868725387e98198b9357406f7c8e4958731a42e89eeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb1bbc77acc982cfa8fc4ceb8217ae51", "guid": "bfdfe7dc352907fc980b868725387e9819ff66c5cf7c780b7d05ecbe7823677a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832411733fbb1c9721b710813acb358fb", "guid": "bfdfe7dc352907fc980b868725387e9878fbcd89621dcd6a37dc05e0eb30f9ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b37cabaed6ce2098449b5f701ebf8128", "guid": "bfdfe7dc352907fc980b868725387e98455d1d67a465eb61ac9bdf74c6101ee1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec65d850bc23e8fa2f1c28632063eec5", "guid": "bfdfe7dc352907fc980b868725387e98473f4a7ce90fa29133842eaf492a7e53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8ebad644cc8b497d2995592415300b", "guid": "bfdfe7dc352907fc980b868725387e98856c30cac20d3866b8c65c185cbaa0b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e5ec3feb4554ebbb460dface8279a62", "guid": "bfdfe7dc352907fc980b868725387e98fb1e70bcd013ca231f0585043bbe7462"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd52b879796a2cac642916ee83afe2d9", "guid": "bfdfe7dc352907fc980b868725387e98f7e29eadd9588bd539bcd7115380ad4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980420e161727ebc475a34f769d1d7ccd5", "guid": "bfdfe7dc352907fc980b868725387e989d795a550993b7796b41e8f991f427a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c956ab5eb1f77c271ddd1f6d4e1ac66", "guid": "bfdfe7dc352907fc980b868725387e98d26476ec4726645e746b83a4b1350c48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817b54af4e8c58f611202a981a6402a53", "guid": "bfdfe7dc352907fc980b868725387e985c3530a3729415bf4dd7d011c6431914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e820d6d3fd89fbd47ba85845f0c38c5", "guid": "bfdfe7dc352907fc980b868725387e986a224be82d569fdb3dad03bbe231b7fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a54033a7cae6f5c840620ba2d1ada99", "guid": "bfdfe7dc352907fc980b868725387e98a317719c402f718910e661f8c0f27eba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3cf88e29149cb9a10d1631e8b4edc78", "guid": "bfdfe7dc352907fc980b868725387e98dbdf83d4a568f43b980d3e36b2f2fc8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d43d8dccb457081efa7336191cc97422", "guid": "bfdfe7dc352907fc980b868725387e98001d2936b52ab759615ad587d49bdcfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cdfd39b4c46671c2d79bcc5006aeb30", "guid": "bfdfe7dc352907fc980b868725387e9802321fdbf676742a6fcf2563cc378249"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847858785bcc46f3696e63f280687cd95", "guid": "bfdfe7dc352907fc980b868725387e98c321217f81fb2831162286ac9234dca3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d3d8c85e3bb4b26c0a78597473eff3b", "guid": "bfdfe7dc352907fc980b868725387e984f9735695791d7c17f10e674ea3b9f56"}], "guid": "bfdfe7dc352907fc980b868725387e9852ac1f0af139f4316c133c28d40b72ec", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987245d663eb126d58dc3bffa3f993fb1d", "guid": "bfdfe7dc352907fc980b868725387e98d07e47aca046fb6dd9fee82848f5ae31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813d077fbf01bac8382743594b271c07f", "guid": "bfdfe7dc352907fc980b868725387e981a78393967f6c42895d42de9e254dcb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56474ef5850c087d24d24a69093581f", "guid": "bfdfe7dc352907fc980b868725387e981bf09f4ae211a816ff5310f94754dc94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f426257763bc4b24489402369e6806", "guid": "bfdfe7dc352907fc980b868725387e98ffbdc2c1bdcd4afebe877be4fc572021"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0372a857a824418dfb3d5a853ed269f", "guid": "bfdfe7dc352907fc980b868725387e98b445fbf1b69daeb3b1469f69d494db52"}], "guid": "bfdfe7dc352907fc980b868725387e984ceeeb0c221230917447400c60d6e41a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985175ddd3211b46407d9486ffbe659b82", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e9826a25a2fb42d98c6a39a0c5e6457f512", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}