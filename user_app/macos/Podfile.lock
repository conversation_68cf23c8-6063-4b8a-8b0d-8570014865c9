PODS:
  - file_picker (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - HydraAsync (2.0.6)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - tencent_cloud_chat_sdk (8.0.0):
    - FlutterMacOS
    - HydraAsync
    - TXIMSDK_Plus_Mac (= 8.6.7019)
  - TXIMSDK_Plus_Mac (8.6.7019)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - file_picker (from `Flutter/ephemeral/.symlinks/plugins/file_picker/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/darwin`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - tencent_cloud_chat_sdk (from `Flutter/ephemeral/.symlinks/plugins/tencent_cloud_chat_sdk/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - HydraAsync
    - TXIMSDK_Plus_Mac

EXTERNAL SOURCES:
  file_picker:
    :path: Flutter/ephemeral/.symlinks/plugins/file_picker/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/darwin
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  tencent_cloud_chat_sdk:
    :path: Flutter/ephemeral/.symlinks/plugins/tencent_cloud_chat_sdk/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  file_picker: 7584aae6fa07a041af2b36a2655122d42f578c1a
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  HydraAsync: 8d589bd725b0224f899afafc9a396327405f8063
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  share_plus: 510bf0af1a42cd602274b4629920c9649c52f4cc
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  tencent_cloud_chat_sdk: 5fdd0713374018299507b70963ddef864eca5d89
  TXIMSDK_Plus_Mac: 8a18065c41549664f2884fe9e4317950fc81283b
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.16.2
