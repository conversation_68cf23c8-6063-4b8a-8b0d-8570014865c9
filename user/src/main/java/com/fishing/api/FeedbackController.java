package com.fishing.api;

import com.fishing.config.CurrentUser;
import com.fishing.dto.feedback.FeedbackCreateDto;
import com.fishing.service.FeedbackService;
import com.fishing.vo.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


/**
 * 用户反馈控制器
 */
@Tag(name = "用户反馈", description = "用户反馈相关接口")
@RestController
@RequestMapping("/feedback")
@RequiredArgsConstructor
public class FeedbackController {

    private final FeedbackService feedbackService;

    @PostMapping
    @Operation(summary = "提交用户反馈")
    public ResponseEntity<ApiResponse<Void>> submitFeedback(
            @Valid @RequestBody FeedbackCreateDto dto,
            @CurrentUser Long userId) {
        feedbackService.createFeedback(userId, dto);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    @GetMapping("/history")
    @Operation(summary = "获取用户反馈历史")
    public ResponseEntity<ApiResponse<java.util.List<java.util.Map<String, Object>>>> getFeedbackHistory(
            @CurrentUser Long userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            // TODO: 实现反馈历史查询逻辑
            java.util.List<java.util.Map<String, Object>> feedbackList = java.util.List.of(
                java.util.Map.of(
                    "id", 1L,
                    "type", "bug",
                    "title", "应用闪退问题",
                    "content", "在使用钓点推荐功能时，应用经常闪退",
                    "status", "处理中",
                    "submitTime", "2024-07-20 14:30:00",
                    "reply", ""
                ),
                java.util.Map.of(
                    "id", 2L,
                    "type", "feature",
                    "title", "希望增加夜钓模式",
                    "content", "建议增加夜钓专用的界面模式，方便夜间使用",
                    "status", "已回复",
                    "submitTime", "2024-07-18 09:15:00",
                    "reply", "感谢您的建议，我们会在下个版本考虑加入夜钓模式功能。"
                )
            );
            
            return ResponseEntity.ok(ApiResponse.success(feedbackList));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(500, "获取反馈历史失败"));
        }
    }

    @PostMapping("/{feedbackId}/helpful")
    @Operation(summary = "标记反馈是否有帮助")
    public ResponseEntity<ApiResponse<String>> markFeedbackHelpful(
            @PathVariable Long feedbackId,
            @RequestBody java.util.Map<String, Boolean> request,
            @CurrentUser Long userId) {
        try {
            Boolean isHelpful = request.get("helpful");
            if (isHelpful == null) {
                return ResponseEntity.ok(ApiResponse.error(400, "请提供有效的参数"));
            }
            
            // TODO: 实现标记逻辑
            String message = isHelpful ? "感谢您的反馈！" : "我们会继续改进服务";
            
            return ResponseEntity.ok(ApiResponse.success(message));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(500, "操作失败"));
        }
    }
}
