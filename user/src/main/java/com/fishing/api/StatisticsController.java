package com.fishing.api;

import com.fishing.config.CurrentUser;
import com.fishing.dto.user.UserStatsDto;
import com.fishing.service.IUserStatsService;
import com.fishing.vo.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户统计信息控制器
 */
@Slf4j
@Tag(name = "用户统计", description = "用户统计信息相关接口")
@RestController
@RequestMapping("/statistics")
@RequiredArgsConstructor
public class StatisticsController {

    private final IUserStatsService userStatsService;

    @GetMapping
    @Operation(summary = "获取用户统计信息", description = "从Redis缓存获取用户统计数据，缓存未命中时从数据库查询")
    public ResponseEntity<ApiResponse<UserStatistics>> getUserStatistics(
            @CurrentUser Long userId) {
        
        try {
            UserStatsDto stats = userStatsService.getUserStats(userId);
            
            UserStatistics statistics = new UserStatistics(
                    stats.getMomentsCount(),
                    stats.getFollowersCount(),
                    stats.getFollowingCount(),
                    stats.getSpotsCount()
            );
            
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            log.error("获取用户统计信息时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "获取统计信息失败"));
        }
    }

    /**
     * 用户统计信息响应对象
     */
    public static class UserStatistics {
        private int momentCount;
        private int fansCount;
        private int followCount;
        private int spotsCount;

        public UserStatistics(int momentCount, int fansCount, int followCount, int spotsCount) {
            this.momentCount = momentCount;
            this.fansCount = fansCount;
            this.followCount = followCount;
            this.spotsCount = spotsCount;
        }

        public int getMomentCount() {
            return momentCount;
        }

        public void setMomentCount(int momentCount) {
            this.momentCount = momentCount;
        }

        public int getFansCount() {
            return fansCount;
        }

        public void setFansCount(int fansCount) {
            this.fansCount = fansCount;
        }

        public int getFollowCount() {
            return followCount;
        }

        public void setFollowCount(int followCount) {
            this.followCount = followCount;
        }

        public int getSpotsCount() {
            return spotsCount;
        }

        public void setSpotsCount(int spotsCount) {
            this.spotsCount = spotsCount;
        }
    }
}
