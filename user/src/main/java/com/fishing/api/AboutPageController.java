package com.fishing.api;

import com.fishing.config.CurrentUser;
import com.fishing.dto.about.*;
import com.fishing.service.AboutPageService;
import com.fishing.vo.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 关于页面控制器
 */
@Slf4j
@Tag(name = "关于页面", description = "关于页面相关接口")
@RestController
@RequestMapping("/about")
@RequiredArgsConstructor
public class AboutPageController {

    private final AboutPageService aboutPageService;

    @GetMapping("/info")
    @Operation(summary = "获取应用信息")
    public ResponseEntity<ApiResponse<AppInfoDto>> getAppInfo() {
        try {
            AppInfoDto appInfo = aboutPageService.getAppInfo();
            return ResponseEntity.ok(ApiResponse.success(appInfo));
        } catch (Exception e) {
            log.error("获取应用信息失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "获取应用信息失败"));
        }
    }

    @GetMapping("/version/check")
    @Operation(summary = "检查版本更新")
    public ResponseEntity<ApiResponse<VersionCheckDto>> checkVersion(
            @RequestParam String currentVersion) {
        try {
            VersionCheckDto versionInfo = aboutPageService.checkVersion(currentVersion);
            return ResponseEntity.ok(ApiResponse.success(versionInfo));
        } catch (Exception e) {
            log.error("检查版本更新失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "检查更新失败"));
        }
    }

    @PostMapping("/feedback/version")
    @Operation(summary = "版本反馈")
    public ResponseEntity<ApiResponse<String>> submitVersionFeedback(
            @RequestBody VersionFeedbackRequest feedback,
            @CurrentUser Long userId) {
        try {
            boolean success = aboutPageService.submitVersionFeedback(userId, feedback);
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("感谢您的反馈"));
            } else {
                return ResponseEntity.ok(ApiResponse.error(500, "提交反馈失败"));
            }
        } catch (Exception e) {
            log.error("提交版本反馈失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "提交反馈失败"));
        }
    }

    @GetMapping("/privacy-policy")
    @Operation(summary = "获取隐私政策")
    public ResponseEntity<ApiResponse<PrivacyPolicyDto>> getPrivacyPolicy() {
        try {
            PrivacyPolicyDto privacyPolicy = aboutPageService.getPrivacyPolicy();
            return ResponseEntity.ok(ApiResponse.success(privacyPolicy));
        } catch (Exception e) {
            log.error("获取隐私政策失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "获取隐私政策失败"));
        }
    }

    @GetMapping("/terms-of-service")
    @Operation(summary = "获取服务条款")
    public ResponseEntity<ApiResponse<TermsOfServiceDto>> getTermsOfService() {
        try {
            TermsOfServiceDto termsOfService = aboutPageService.getTermsOfService();
            return ResponseEntity.ok(ApiResponse.success(termsOfService));
        } catch (Exception e) {
            log.error("获取服务条款失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "获取服务条款失败"));
        }
    }

    @PostMapping("/share")
    @Operation(summary = "生成分享信息")
    public ResponseEntity<ApiResponse<AppShareInfoDto>> generateShareInfo(
            @CurrentUser Long userId) {
        try {
            AppShareInfoDto shareInfo = aboutPageService.generateShareInfo(userId);
            return ResponseEntity.ok(ApiResponse.success(shareInfo));
        } catch (Exception e) {
            log.error("生成分享信息失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "生成分享信息失败"));
        }
    }

    @GetMapping("/stats")
    @Operation(summary = "获取应用统计信息")
    public ResponseEntity<ApiResponse<AppStatsDto>> getAppStats() {
        try {
            AppStatsDto stats = aboutPageService.getAppStats();
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("获取应用统计失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "获取统计信息失败"));
        }
    }
}