package com.fishing.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fishing.common.result.PageResult;
import com.fishing.config.CurrentUser;
import com.fishing.custom.exception.BizException;
import com.fishing.customer.service.IUserService;
import com.fishing.dto.spot.QuerySpotDto;
import com.fishing.service.IFishingSpotQueryService;
import com.fishing.service.SpotFavoriteService;
import com.fishing.vo.ApiResponse;
import com.fishing.vo.spot.SpotDetailVO;
import com.fishing.vo.spot.SpotMapVO;
import com.fishing.vo.spot.SpotPriceVO;
import com.fishing.vo.spot.SpotSummaryVO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v1/fishing-spots")
@RequiredArgsConstructor
public class FishingSpotQueryController {

    private final IFishingSpotQueryService fishingSpotQueryService;
    private final IUserService userService;
    private final SpotFavoriteService spotFavoriteService;

    @PostMapping
    public ResponseEntity<ApiResponse<IPage<SpotSummaryVO>>> getFishingSpots(@RequestBody QuerySpotDto querySpotDto) {
        IPage<SpotSummaryVO> fishingSpots = fishingSpotQueryService.findFishingSpots(querySpotDto);
        return ResponseEntity.ok(ApiResponse.success(fishingSpots));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<SpotDetailVO>> getFishingSpotById(@PathVariable Long id) {
        Optional<SpotDetailVO> spot = fishingSpotQueryService.findById(id);
        return spot.map(s -> ResponseEntity.ok(ApiResponse.success(s)))
                .orElseGet(() -> ResponseEntity.ok(ApiResponse.error(404, "钓点不存在")));
    }

    @GetMapping("/{id}/prices")
    public ResponseEntity<ApiResponse<List<SpotPriceVO>>> getSpotPrices(@PathVariable Long id) {
        List<SpotPriceVO> prices = fishingSpotQueryService.getPricesBySpotId(id);
        return ResponseEntity.ok(ApiResponse.success(prices));
    }

    @GetMapping("/user/recent-checkins")
    public ResponseEntity<ApiResponse<PageResult<SpotSummaryVO>>> getRecentCheckins(
            @CurrentUser Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) throws BizException.InvalidOperation {
        if (userService.getById(userId) == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }
        PageResult<SpotSummaryVO> result = fishingSpotQueryService.getUserRecentCheckins(userId, page, size);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @GetMapping("/user/favorites")
    public ResponseEntity<ApiResponse<List<SpotSummaryVO>>> getFavorites(
            @CurrentUser Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) throws BizException.InvalidOperation {
        if (userService.getById(userId) == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }
        List<SpotSummaryVO> spots = fishingSpotQueryService.getUserFavorites(userId, page, size);
        return ResponseEntity.ok(ApiResponse.success(spots));
    }

    @GetMapping("/user/created")
    public ResponseEntity<ApiResponse<PageResult<SpotSummaryVO>>> getMyCreatedSpots(
            @CurrentUser Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) throws BizException.InvalidOperation {
        if (userService.getById(userId) == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }
        PageResult<SpotSummaryVO> result = fishingSpotQueryService.getUserCreatedSpots(userId, page, size);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    @GetMapping("/user/{targetUserId}/created")
    public ResponseEntity<ApiResponse<PageResult<SpotSummaryVO>>> getUserCreatedSpots(
            @PathVariable Long targetUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) throws BizException.InvalidOperation {
        if (userService.getById(targetUserId) == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }
        PageResult<SpotSummaryVO> result = fishingSpotQueryService.getUserCreatedSpots(targetUserId, page, size);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @GetMapping("/nearby")
    public ResponseEntity<ApiResponse<List<SpotSummaryVO>>> getNearbySpots(
            @RequestParam double latitude,
            @RequestParam double longitude,
            @RequestParam(defaultValue = "5.0") double radius) {
        List<SpotSummaryVO> spots = fishingSpotQueryService.searchFishingSpots(
                null, 0, 10, latitude, longitude, radius
        );
        return ResponseEntity.ok(ApiResponse.success(spots));
    }

    /**
     * 获取附近钓点的地图数据
     *
     * @param latitude  纬度 (必需，范围 -90 到 90)
     * @param longitude 经度 (必需，范围 -180 到 180)
     * @param radius    搜索半径，单位公里 (可选，默认50km)
     * @param page      页码，从0开始 (可选，默认0)
     * @param size      每页大小 (可选，默认50)
     * @return 地图钓点数据分页结果
     */
    @GetMapping("/nearby/map")
    public ResponseEntity<ApiResponse<IPage<SpotMapVO>>> getNearbyMapSpots(
            @RequestParam double latitude,
            @RequestParam double longitude,
            @RequestParam(defaultValue = "50.0") double radius,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {

        try {
            // 参数验证
            if (latitude < -90 || latitude > 90) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "纬度必须在-90到90之间"));
            }

            if (longitude < -180 || longitude > 180) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "经度必须在-180到180之间"));
            }

            if (radius <= 0 || radius > 1000) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "搜索半径必须在0到1000公里之间"));
            }

            if (page < 0) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "页码不能小于0"));
            }

            if (size <= 0 || size > 200) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "每页大小必须在1到200之间"));
            }

            // 调用服务获取附近的地图钓点
            IPage<SpotMapVO> mapSpots = fishingSpotQueryService.findNearbyMapSpots(
                    new BigDecimal(latitude),
                    new BigDecimal(longitude),
                    radius,
                    page,
                    size
            );

            return ResponseEntity.ok(ApiResponse.success(mapSpots));

        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(400, e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error(500, "服务器内部错误"));
        }
    }

    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<SpotSummaryVO>>> searchFishingSpots(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Double latitude,
            @RequestParam(required = false) Double longitude,
            @RequestParam(required = false) Double radius) {

        boolean hasGeoSearch = latitude != null && longitude != null;
        List<SpotSummaryVO> spots;
        if (hasGeoSearch) {
            spots = fishingSpotQueryService.searchFishingSpots(
                    query, page, size, latitude, longitude, radius != null ? radius : 50.0);
        } else {
            spots = fishingSpotQueryService.searchFishingSpots(query, page, size);
        }
        return ResponseEntity.ok(ApiResponse.success(spots));
    }

    @GetMapping("/{id}/is-favorite")
    public ResponseEntity<ApiResponse<Boolean>> checkIsFavorite(@CurrentUser Long userId, @PathVariable Long id) throws BizException.InvalidOperation {
        if (userService.getById(userId) == null) {
            throw new BizException.InvalidOperation("用户不存在");
        }
        boolean isFavorite = spotFavoriteService.isFavorite(id, userId);
        return ResponseEntity.ok(ApiResponse.success(isFavorite));
    }

}
