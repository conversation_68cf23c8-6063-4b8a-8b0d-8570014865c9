package com.fishing.api;

import com.fishing.config.CurrentUser;
import com.fishing.domain.UserSettings;
import com.fishing.service.UserSettingsService;
import com.fishing.vo.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 用户设置管理控制器
 */
@Slf4j
@Tag(name = "用户设置", description = "用户个人设置相关接口")
@RestController
@RequestMapping("/settings")
@RequiredArgsConstructor
public class UserSettingsController {

    private final UserSettingsService userSettingsService;

    @GetMapping
    @Operation(summary = "获取用户设置")
    public ResponseEntity<ApiResponse<com.fishing.domain.UserSettings>> getUserSettings(@CurrentUser Long userId) {

        try {
            com.fishing.domain.UserSettings settings = userSettingsService.getUserSettings(userId);
            return ResponseEntity.ok(ApiResponse.success(settings));
        } catch (Exception e) {
            log.error("获取用户设置时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "获取设置失败"));
        }
    }

    @PutMapping
    @Operation(summary = "更新用户设置")
    public ResponseEntity<ApiResponse<String>> updateUserSettings(
            @CurrentUser Long userId,
            @RequestBody com.fishing.domain.UserSettings settings) {
        
        try {
            boolean success = userSettingsService.updateUserSettings(userId, settings);
            
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("设置更新成功"));
            } else {
                return ResponseEntity.internalServerError().body(ApiResponse.error(500, "更新设置失败"));
            }
        } catch (Exception e) {
            log.error("更新用户设置时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "更新设置失败"));
        }
    }

    @PostMapping("/privacy")
    @Operation(summary = "更新隐私设置")
    public ResponseEntity<ApiResponse<String>> updatePrivacySettings(
            @CurrentUser Long userId,
            @RequestBody PrivacySettingsRequest request) {
        
        try {
            boolean success = userSettingsService.updatePrivacySettings(
                userId,
                request.getProfileVisibility(),
                request.getMomentVisibility(),
                request.isAllowFollow(),
                request.isAllowMessage()
            );
            
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("隐私设置更新成功"));
            } else {
                return ResponseEntity.internalServerError().body(ApiResponse.error(500, "更新隐私设置失败"));
            }
        } catch (Exception e) {
            log.error("更新隐私设置时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "更新隐私设置失败"));
        }
    }

    @PostMapping("/notification")
    @Operation(summary = "更新通知设置")
    public ResponseEntity<ApiResponse<String>> updateNotificationSettings(
            @CurrentUser Long userId,
            @RequestBody NotificationSettingsRequest request) {
        
        try {
            boolean success = userSettingsService.updateNotificationSettings(
                userId,
                request.isMomentNotification(),
                request.isCommentNotification(),
                request.isFollowNotification(),
                request.isSystemNotification()
            );
            
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("通知设置更新成功"));
            } else {
                return ResponseEntity.internalServerError().body(ApiResponse.error(500, "更新通知设置失败"));
            }
        } catch (Exception e) {
            log.error("更新通知设置时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "更新通知设置失败"));
        }
    }

    @GetMapping("/notification-preferences")
    @Operation(summary = "获取用户通知偏好")
    public ResponseEntity<ApiResponse<Map<String, Boolean>>> getNotificationPreferences(@CurrentUser Long userId) {
        
        try {
            Map<String, Boolean> preferences = userSettingsService.getUserNotificationPreferences(userId);
            return ResponseEntity.ok(ApiResponse.success(preferences));
        } catch (Exception e) {
            log.error("获取通知偏好时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "获取通知偏好失败"));
        }
    }

    @GetMapping("/cache/info")
    @Operation(summary = "获取缓存信息")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCacheInfo(@CurrentUser Long userId) {
        try {
            // TODO: 实现缓存信息获取逻辑
            Map<String, Object> cacheInfo = Map.of(
                "imageCache", "45.2MB",
                "dataCache", "12.8MB",
                "logCache", "5.1MB",
                "totalCache", "63.1MB",
                "lastClearTime", "2024-07-20 10:30:00"
            );
            return ResponseEntity.ok(ApiResponse.success(cacheInfo));
        } catch (Exception e) {
            log.error("获取缓存信息时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "获取缓存信息失败"));
        }
    }

    @PostMapping("/cache/clear")
    @Operation(summary = "清理缓存")
    public ResponseEntity<ApiResponse<Map<String, Object>>> clearCache(
            @CurrentUser Long userId,
            @RequestBody Map<String, Object> request) {
        try {
            String cacheType = (String) request.get("type");
            
            // TODO: 实现缓存清理逻辑
            long clearedSize = 0L;
            String message = "";
            
            switch (cacheType) {
                case "image":
                    clearedSize = 45 * 1024 * 1024L; // 45MB
                    message = "图片缓存清理完成";
                    break;
                case "data":
                    clearedSize = 12 * 1024 * 1024L; // 12MB
                    message = "数据缓存清理完成";
                    break;
                case "all":
                    clearedSize = 63 * 1024 * 1024L; // 63MB
                    message = "所有缓存清理完成";
                    break;
                default:
                    return ResponseEntity.ok(ApiResponse.error(400, "无效的缓存类型"));
            }
            
            Map<String, Object> result = Map.of(
                "clearedSize", clearedSize,
                "message", message,
                "timestamp", System.currentTimeMillis()
            );
            
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("清理缓存时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "清理缓存失败"));
        }
    }

    /**
     * 用户设置请求对象
     */
    public static class UserSettingsRequest {
        private boolean notificationEnabled;
        private String privacyLevel;
        private String language;
        private boolean autoSaveLocation;
        private boolean showOnlineStatus;

        // Getters and Setters
        public boolean isNotificationEnabled() { return notificationEnabled; }
        public void setNotificationEnabled(boolean notificationEnabled) { this.notificationEnabled = notificationEnabled; }

        public String getPrivacyLevel() { return privacyLevel; }
        public void setPrivacyLevel(String privacyLevel) { this.privacyLevel = privacyLevel; }

        public String getLanguage() { return language; }
        public void setLanguage(String language) { this.language = language; }

        public boolean isAutoSaveLocation() { return autoSaveLocation; }
        public void setAutoSaveLocation(boolean autoSaveLocation) { this.autoSaveLocation = autoSaveLocation; }

        public boolean isShowOnlineStatus() { return showOnlineStatus; }
        public void setShowOnlineStatus(boolean showOnlineStatus) { this.showOnlineStatus = showOnlineStatus; }

        @Override
        public String toString() {
            return "UserSettingsRequest{" +
                    "notificationEnabled=" + notificationEnabled +
                    ", privacyLevel='" + privacyLevel + '\'' +
                    ", language='" + language + '\'' +
                    ", autoSaveLocation=" + autoSaveLocation +
                    ", showOnlineStatus=" + showOnlineStatus +
                    '}';
        }
    }

    /**
     * 隐私设置请求
     */
    public static class PrivacySettingsRequest {
        private String profileVisibility;
        private String momentVisibility;
        private boolean allowFollow;
        private boolean allowMessage;

        // Getters and Setters
        public String getProfileVisibility() { return profileVisibility; }
        public void setProfileVisibility(String profileVisibility) { this.profileVisibility = profileVisibility; }

        public String getMomentVisibility() { return momentVisibility; }
        public void setMomentVisibility(String momentVisibility) { this.momentVisibility = momentVisibility; }

        public boolean isAllowFollow() { return allowFollow; }
        public void setAllowFollow(boolean allowFollow) { this.allowFollow = allowFollow; }

        public boolean isAllowMessage() { return allowMessage; }
        public void setAllowMessage(boolean allowMessage) { this.allowMessage = allowMessage; }

        @Override
        public String toString() {
            return "PrivacySettingsRequest{" +
                    "profileVisibility='" + profileVisibility + '\'' +
                    ", momentVisibility='" + momentVisibility + '\'' +
                    ", allowFollow=" + allowFollow +
                    ", allowMessage=" + allowMessage +
                    '}';
        }
    }

    /**
     * 通知设置请求
     */
    public static class NotificationSettingsRequest {
        private boolean momentNotification;
        private boolean commentNotification;
        private boolean followNotification;
        private boolean systemNotification;

        // Getters and Setters
        public boolean isMomentNotification() { return momentNotification; }
        public void setMomentNotification(boolean momentNotification) { this.momentNotification = momentNotification; }

        public boolean isCommentNotification() { return commentNotification; }
        public void setCommentNotification(boolean commentNotification) { this.commentNotification = commentNotification; }

        public boolean isFollowNotification() { return followNotification; }
        public void setFollowNotification(boolean followNotification) { this.followNotification = followNotification; }

        public boolean isSystemNotification() { return systemNotification; }
        public void setSystemNotification(boolean systemNotification) { this.systemNotification = systemNotification; }

        @Override
        public String toString() {
            return "NotificationSettingsRequest{" +
                    "momentNotification=" + momentNotification +
                    ", commentNotification=" + commentNotification +
                    ", followNotification=" + followNotification +
                    ", systemNotification=" + systemNotification +
                    '}';
        }
    }
}