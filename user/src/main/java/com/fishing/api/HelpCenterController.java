package com.fishing.api;

import com.fishing.config.CurrentUser;
import com.fishing.dto.help.*;
import com.fishing.service.HelpCenterService;
import com.fishing.vo.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 帮助中心控制器
 */
@Slf4j
@Tag(name = "帮助中心", description = "帮助中心相关接口")
@RestController
@RequestMapping("/help")
@RequiredArgsConstructor
public class HelpCenterController {

    private final HelpCenterService helpCenterService;

    @GetMapping("/categories")
    @Operation(summary = "获取帮助分类")
    public ResponseEntity<ApiResponse<List<HelpCategoryDto>>> getHelpCategories() {
        try {
            List<HelpCategoryDto> categories = helpCenterService.getHelpCategories();
            return ResponseEntity.ok(ApiResponse.success(categories));
        } catch (Exception e) {
            log.error("获取帮助分类失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "获取帮助分类失败"));
        }
    }

    @GetMapping("/categories/{categoryId}/questions")
    @Operation(summary = "获取分类下的问题列表")
    public ResponseEntity<ApiResponse<List<HelpQuestionDto>>> getCategoryQuestions(
            @PathVariable String categoryId) {
        try {
            List<HelpQuestionDto> questions = helpCenterService.getCategoryQuestions(categoryId);
            return ResponseEntity.ok(ApiResponse.success(questions));
        } catch (Exception e) {
            log.error("获取问题列表失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "获取问题列表失败"));
        }
    }

    @GetMapping("/search")
    @Operation(summary = "搜索帮助内容")
    public ResponseEntity<ApiResponse<List<HelpQuestionDto>>> searchHelp(
            @RequestParam String keyword) {
        try {
            List<HelpQuestionDto> results = helpCenterService.searchHelp(keyword);
            return ResponseEntity.ok(ApiResponse.success(results));
        } catch (Exception e) {
            log.error("搜索帮助内容失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "搜索失败"));
        }
    }

    @PostMapping("/questions/{questionId}/helpful")
    @Operation(summary = "标记问题是否有帮助")
    public ResponseEntity<ApiResponse<String>> markQuestionHelpful(
            @PathVariable String questionId,
            @RequestBody MarkQuestionHelpfulRequest request,
            @CurrentUser Long userId) {
        try {
            if (request.getHelpful() == null) {
                return ResponseEntity.ok(ApiResponse.error(400, "请提供有效的参数"));
            }
            
            boolean success = helpCenterService.markQuestionHelpful(questionId, request.getHelpful(), userId);
            if (success) {
                String message = request.getHelpful() ? "感谢您的反馈！" : "已记录您的反馈，我们会改进内容";
                return ResponseEntity.ok(ApiResponse.success(message));
            } else {
                return ResponseEntity.ok(ApiResponse.error(500, "操作失败"));
            }
        } catch (Exception e) {
            log.error("标记问题失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "操作失败"));
        }
    }

    @PostMapping("/questions/{questionId}/view")
    @Operation(summary = "记录问题查看次数")
    public ResponseEntity<ApiResponse<String>> recordQuestionView(
            @PathVariable String questionId,
            @CurrentUser Long userId) {
        try {
            boolean success = helpCenterService.recordQuestionView(questionId, userId);
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("记录成功"));
            } else {
                return ResponseEntity.ok(ApiResponse.error(500, "记录失败"));
            }
        } catch (Exception e) {
            log.error("记录查看次数失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "记录失败"));
        }
    }

    @GetMapping("/questions/{questionId}/stats")
    @Operation(summary = "获取问题统计信息")
    public ResponseEntity<ApiResponse<QuestionStatsDto>> getQuestionStats(
            @PathVariable String questionId) {
        try {
            QuestionStatsDto stats = helpCenterService.getQuestionStats(questionId);
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("获取问题统计失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "获取统计信息失败"));
        }
    }

    @GetMapping("/user/feedback-stats")
    @Operation(summary = "获取用户反馈统计")
    public ResponseEntity<ApiResponse<UserFeedbackStatsDto>> getUserFeedbackStats(
            @CurrentUser Long userId) {
        try {
            UserFeedbackStatsDto stats = helpCenterService.getUserFeedbackStats(userId);
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("获取用户反馈统计失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "获取统计信息失败"));
        }
    }
}