package com.fishing.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fishing.config.CurrentUser;
import com.fishing.domain.UserAlbum;
import com.fishing.dto.album.AlbumStatsDto;
import com.fishing.dto.album.ImageDetailsDto;
import com.fishing.service.UserAlbumService;
import com.fishing.vo.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户相册管理控制器
 */
@Slf4j
@Tag(name = "用户相册", description = "用户相册管理相关接口")
@RestController
@RequestMapping("/album")
@RequiredArgsConstructor
public class UserAlbumController {

    private final UserAlbumService userAlbumService;

    @GetMapping
    @Operation(summary = "获取用户相册列表")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUserAlbum(
            @CurrentUser Long userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer pageSize) {
        
        try {
            IPage<UserAlbum> albumPage = userAlbumService.getUserAlbumPage(userId, page, pageSize);
            
            Map<String, Object> result = new HashMap<>();
            result.put("images", albumPage.getRecords());
            result.put("total", albumPage.getTotal());
            result.put("page", albumPage.getCurrent());
            result.put("pageSize", albumPage.getSize());
            result.put("pages", albumPage.getPages());
            
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("获取用户相册时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "获取相册失败"));
        }
    }

    @PostMapping
    @Operation(summary = "上传图片到相册")
    public ResponseEntity<ApiResponse<String>> uploadToAlbum(
            @CurrentUser Long userId,
            @RequestBody AddToAlbumRequest request) {
        
        try {
            if (request.getImageUrl() == null || request.getImageUrl().isEmpty()) {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, "图片URL不能为空"));
            }
            
            boolean success = userAlbumService.addToAlbum(
                userId, 
                request.getImageUrl(), 
                request.getDescription(), 
                request.getTags()
            );
            
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("上传成功"));
            } else {
                return ResponseEntity.internalServerError().body(ApiResponse.error(500, "上传失败"));
            }
        } catch (Exception e) {
            log.error("上传图片到相册时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "上传失败"));
        }
    }

    @DeleteMapping("/{imageId}")
    @Operation(summary = "删除相册中的图片")
    public ResponseEntity<ApiResponse<String>> deleteFromAlbum(
            @CurrentUser Long userId,
            @PathVariable Long imageId) {
        
        try {
            boolean success = userAlbumService.deleteFromAlbum(userId, imageId);
            
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("删除成功"));
            } else {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, "删除失败，图片不存在或无权限"));
            }
        } catch (Exception e) {
            log.error("删除相册图片时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "删除失败"));
        }
    }

    @GetMapping("/stats")
    @Operation(summary = "获取相册统计信息")
    public ResponseEntity<ApiResponse<AlbumStatsDto>> getAlbumStats(@CurrentUser Long userId) {
        
        try {
            AlbumStatsDto stats = userAlbumService.getAlbumStats(userId);
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("获取相册统计信息时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "获取统计信息失败"));
        }
    }

    @GetMapping("/recent")
    @Operation(summary = "获取最近上传的图片")
    public ResponseEntity<ApiResponse<List<UserAlbum>>> getRecentImages(
            @CurrentUser Long userId,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        try {
            List<UserAlbum> recentImages = userAlbumService.getRecentImages(userId, limit);
            return ResponseEntity.ok(ApiResponse.success(recentImages));
        } catch (Exception e) {
            log.error("获取最近图片时发生错误", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "获取最近图片失败"));
        }
    }

    @PostMapping("/{imageId}/share")
    @Operation(summary = "分享相册图片")
    public ResponseEntity<ApiResponse<Map<String, Object>>> shareImage(
            @CurrentUser Long userId,
            @PathVariable Long imageId,
            @RequestBody Map<String, String> request) {
        try {
            String shareType = request.get("shareType");
            
            // TODO: 实现图片分享逻辑
            Map<String, Object> shareInfo = Map.of(
                "shareUrl", "https://example.com/share/image/" + imageId,
                "shareText", "分享了一张精彩的钓鱼照片",
                "imageUrl", "https://example.com/images/" + imageId + ".jpg"
            );
            
            return ResponseEntity.ok(ApiResponse.success(shareInfo));
        } catch (Exception e) {
            log.error("分享图片失败", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "分享失败"));
        }
    }

    @PostMapping("/{imageId}/save")
    @Operation(summary = "保存图片到本地")
    public ResponseEntity<ApiResponse<String>> saveImageToLocal(
            @CurrentUser Long userId,
            @PathVariable Long imageId) {
        try {
            // TODO: 实现图片保存逻辑
            boolean success = userAlbumService.saveImageToLocal(userId, imageId);
            
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("保存成功"));
            } else {
                return ResponseEntity.internalServerError().body(ApiResponse.error(500, "保存失败"));
            }
        } catch (Exception e) {
            log.error("保存图片失败", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "保存失败"));
        }
    }

    @PostMapping("/batch/delete")
    @Operation(summary = "批量删除相册图片")
    public ResponseEntity<ApiResponse<Boolean>> batchDeleteImages(
            @CurrentUser Long userId,
            @RequestBody Map<String, List<Long>> request) {
        try {
            List<Long> imageIds = request.get("imageIds");
            if (imageIds == null || imageIds.isEmpty()) {
                return ResponseEntity.ok(ApiResponse.error(400, "请选择要删除的图片"));
            }
            
            boolean result = userAlbumService.batchDeleteImages(userId, imageIds);
            return ResponseEntity.ok(ApiResponse.success(result));
        } catch (Exception e) {
            log.error("批量删除图片失败", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "批量删除失败"));
        }
    }

    @GetMapping("/{imageId}/details")
    @Operation(summary = "获取图片详细信息")
    public ResponseEntity<ApiResponse<ImageDetailsDto>> getImageDetails(
            @CurrentUser Long userId,
            @PathVariable Long imageId) {
        try {
            ImageDetailsDto imageDetails = userAlbumService.getImageDetails(userId, imageId);
            if (imageDetails == null) {
                return ResponseEntity.ok(ApiResponse.error(404, "图片不存在"));
            }
            
            return ResponseEntity.ok(ApiResponse.success(imageDetails));
        } catch (Exception e) {
            log.error("获取图片详情失败", e);
            return ResponseEntity.internalServerError().body(ApiResponse.error(500, "获取图片详情失败"));
        }
    }

    /**
     * 添加到相册请求DTO
     */
    public static class AddToAlbumRequest {
        private String imageUrl;
        private String description;
        private String tags;

        // Getters and Setters
        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getTags() { return tags; }
        public void setTags(String tags) { this.tags = tags; }
    }
}