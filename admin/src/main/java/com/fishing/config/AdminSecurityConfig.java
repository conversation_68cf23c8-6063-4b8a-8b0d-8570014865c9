package com.fishing.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Admin 模块安全配置
 * 提供 PasswordEncoder bean 用于密码加密
 *
 * <AUTHOR>
 */
@Configuration
public class AdminSecurityConfig {

    /**
     * 密码编码器
     * 使用 BCrypt 算法进行密码加密
     *
     * @return PasswordEncoder
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
