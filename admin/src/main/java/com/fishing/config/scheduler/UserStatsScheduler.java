package com.fishing.config.scheduler;

import com.fishing.service.IUserStatsAdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户统计定时任务调度器（Admin Module）
 * 负责定期预热热点用户、数据一致性校验、缓存清理等任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "app.module", havingValue = "admin", matchIfMissing = true)
public class UserStatsScheduler {

    private final IUserStatsAdminService userStatsAdminService;

    /**
     * 每天凌晨2点执行全量数据刷新
     * 在业务低峰期重新计算所有用户的统计数据
     */
    @Scheduled(cron = "${stats.schedule.daily-refresh:0 0 2 * * ?}")
    public void dailyStatsRefresh() {
        log.info("开始执行每日统计数据全量刷新任务");
        long startTime = System.currentTimeMillis();
        
        try {
            userStatsAdminService.refreshAllUserStats();
            long endTime = System.currentTimeMillis();
            log.info("每日统计数据全量刷新任务完成，耗时: {} ms", endTime - startTime);
        } catch (Exception e) {
            log.error("每日统计数据全量刷新任务失败", e);
        }
    }

    /**
     * 每30分钟预热热点用户统计数据
     * 识别最近活跃的用户并预热其统计数据
     */
    @Scheduled(cron = "${stats.schedule.hot-users-preload:0 */30 * * * ?}")
    public void preloadHotUsers() {
        log.info("开始执行热点用户统计数据预热任务");
        long startTime = System.currentTimeMillis();
        
        try {
            // 获取热点用户列表（最多1000个）
            List<Long> hotUsers = userStatsAdminService.getHotUsers(1000);
            
            if (!hotUsers.isEmpty()) {
                userStatsAdminService.preloadHotUsers(hotUsers);
                long endTime = System.currentTimeMillis();
                log.info("热点用户统计数据预热任务完成，预热了 {} 个用户，耗时: {} ms", 
                        hotUsers.size(), endTime - startTime);
            } else {
                log.info("没有发现热点用户，跳过预热任务");
            }
        } catch (Exception e) {
            log.error("热点用户统计数据预热任务失败", e);
        }
    }

    /**
     * 每天凌晨4点执行数据一致性校验
     * 检查缓存与数据库的统计数据是否一致，并自动修复
     */
    @Scheduled(cron = "${stats.schedule.consistency-check:0 0 4 * * ?}")
    public void validateDataConsistency() {
        log.info("开始执行数据一致性校验任务");
        long startTime = System.currentTimeMillis();
        
        try {
            userStatsAdminService.validateAndFixStatsConsistency();
            long endTime = System.currentTimeMillis();
            log.info("数据一致性校验任务完成，耗时: {} ms", endTime - startTime);
        } catch (Exception e) {
            log.error("数据一致性校验任务失败", e);
        }
    }

    /**
     * 每天凌晨3点清理过期缓存
     * 删除超过TTL时间的统计缓存数据，释放Redis内存
     */
    @Scheduled(cron = "${stats.schedule.cache-cleanup:0 0 3 * * ?}")
    public void cleanExpiredCache() {
        log.info("开始执行过期缓存清理任务");
        long startTime = System.currentTimeMillis();
        
        try {
            userStatsAdminService.cleanExpiredCache();
            long endTime = System.currentTimeMillis();
            log.info("过期缓存清理任务完成，耗时: {} ms", endTime - startTime);
        } catch (Exception e) {
            log.error("过期缓存清理任务失败", e);
        }
    }

    /**
     * 每10分钟执行快速预热
     * 预热最近10分钟内活跃的少量用户
     */
    @Scheduled(cron = "${stats.schedule.quick-preload:0 */10 * * * ?}")
    public void quickPreload() {
        log.debug("开始执行快速预热任务");
        
        try {
            // 获取少量最活跃用户进行快速预热
            List<Long> recentActiveUsers = userStatsAdminService.getHotUsers(50);
            
            if (!recentActiveUsers.isEmpty()) {
                userStatsAdminService.preloadHotUsers(recentActiveUsers);
                log.debug("快速预热任务完成，预热了 {} 个用户", recentActiveUsers.size());
            }
        } catch (Exception e) {
            log.error("快速预热任务失败", e);
        }
    }

    /**
     * 每小时输出缓存健康状态
     * 监控Redis缓存的使用情况和性能指标
     */
    @Scheduled(cron = "${stats.schedule.health-monitor:0 0 * * * ?}")
    public void monitorCacheHealth() {
        log.debug("开始执行缓存健康状态监控");
        
        try {
            var healthMetrics = userStatsAdminService.getRedisHealthMetrics();
            log.info("缓存健康状态: {}", healthMetrics);
        } catch (Exception e) {
            log.error("缓存健康状态监控失败", e);
        }
    }
}