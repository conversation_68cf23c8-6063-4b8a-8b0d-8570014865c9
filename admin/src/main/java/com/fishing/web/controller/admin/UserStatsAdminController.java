package com.fishing.web.controller.admin;

import com.fishing.service.IUserStatsAdminService;
import com.fishing.vo.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户统计管理控制器（Admin Module）
 * 提供统计数据预热、一致性校验、缓存管理等运维功能
 */
@Slf4j
@Tag(name = "用户统计管理", description = "用户统计数据预热和缓存管理")
@RestController
@RequestMapping("/admin/user-stats")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
public class UserStatsAdminController {

    private final IUserStatsAdminService userStatsAdminService;

    @PostMapping("/preload/hot-users")
    @Operation(summary = "预热热点用户统计数据", description = "批量预热最活跃用户的统计数据到Redis缓存")
    public ResponseEntity<ApiResponse<String>> preloadHotUsers(
            @Parameter(description = "预热用户数量限制", example = "1000")
            @RequestParam(defaultValue = "1000") int limit) {
        
        try {
            List<Long> hotUsers = userStatsAdminService.getHotUsers(limit);
            userStatsAdminService.preloadHotUsers(hotUsers);
            
            String message = String.format("成功预热 %d 个热点用户的统计数据", hotUsers.size());
            log.info(message);
            return ResponseEntity.ok(ApiResponse.success(message));
            
        } catch (Exception e) {
            log.error("预热热点用户统计数据失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error(500, "预热失败: " + e.getMessage()));
        }
    }

    @PostMapping("/preload/users")
    @Operation(summary = "预热指定用户统计数据", description = "预热指定用户列表的统计数据到Redis缓存")
    public ResponseEntity<ApiResponse<String>> preloadSpecificUsers(
            @Parameter(description = "用户ID列表")
            @RequestBody List<Long> userIds) {
        
        try {
            if (userIds == null || userIds.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "用户ID列表不能为空"));
            }
            
            userStatsAdminService.preloadHotUsers(userIds);
            
            String message = String.format("成功预热 %d 个指定用户的统计数据", userIds.size());
            log.info(message);
            return ResponseEntity.ok(ApiResponse.success(message));
            
        } catch (Exception e) {
            log.error("预热指定用户统计数据失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error(500, "预热失败: " + e.getMessage()));
        }
    }

    @PostMapping("/preload/user/{userId}")
    @Operation(summary = "预热单个用户统计数据", description = "预热指定用户的统计数据到Redis缓存")
    public ResponseEntity<ApiResponse<String>> preloadUserStats(
            @Parameter(description = "用户ID")
            @PathVariable Long userId) {
        
        try {
            userStatsAdminService.refreshUserStatsToRedis(userId);
            
            String message = String.format("成功预热用户 %d 的统计数据", userId);
            log.info(message);
            return ResponseEntity.ok(ApiResponse.success(message));
            
        } catch (Exception e) {
            log.error("预热用户 {} 统计数据失败", userId, e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error(500, "预热失败: " + e.getMessage()));
        }
    }

    @PostMapping("/refresh/all")
    @Operation(summary = "全量刷新统计数据", description = "全量刷新所有用户的统计数据（建议在低峰期执行）")
    public ResponseEntity<ApiResponse<String>> refreshAllStats() {
        
        try {
            userStatsAdminService.refreshAllUserStats();
            
            String message = "全量刷新统计数据任务已启动，请查看日志了解进度";
            log.info(message);
            return ResponseEntity.ok(ApiResponse.success(message));
            
        } catch (Exception e) {
            log.error("全量刷新统计数据失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error(500, "刷新失败: " + e.getMessage()));
        }
    }

    @PostMapping("/validate-consistency")
    @Operation(summary = "数据一致性校验", description = "校验并修复缓存与数据库之间的统计数据不一致")
    public ResponseEntity<ApiResponse<String>> validateConsistency() {
        
        try {
            userStatsAdminService.validateAndFixStatsConsistency();
            
            String message = "数据一致性校验任务已启动，请查看日志了解结果";
            log.info(message);
            return ResponseEntity.ok(ApiResponse.success(message));
            
        } catch (Exception e) {
            log.error("数据一致性校验失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error(500, "校验失败: " + e.getMessage()));
        }
    }

    @PostMapping("/clean-expired")
    @Operation(summary = "清理过期缓存", description = "清理超过TTL时间的统计缓存数据")
    public ResponseEntity<ApiResponse<String>> cleanExpiredCache() {
        
        try {
            userStatsAdminService.cleanExpiredCache();
            
            String message = "过期缓存清理任务已启动，请查看日志了解结果";
            log.info(message);
            return ResponseEntity.ok(ApiResponse.success(message));
            
        } catch (Exception e) {
            log.error("清理过期缓存失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error(500, "清理失败: " + e.getMessage()));
        }
    }

    @GetMapping("/health")
    @Operation(summary = "缓存健康检查", description = "获取Redis缓存的健康状态和性能指标")
    public ResponseEntity<ApiResponse<Map<String, Object>>> healthCheck() {
        
        try {
            Map<String, Object> healthMetrics = userStatsAdminService.getRedisHealthMetrics();
            return ResponseEntity.ok(ApiResponse.success(healthMetrics));
            
        } catch (Exception e) {
            log.error("获取缓存健康状态失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error(500, "健康检查失败: " + e.getMessage()));
        }
    }

    @GetMapping("/hot-users")
    @Operation(summary = "获取热点用户列表", description = "获取当前识别的热点用户ID列表")
    public ResponseEntity<ApiResponse<List<Long>>> getHotUsers(
            @Parameter(description = "返回用户数量限制", example = "100")
            @RequestParam(defaultValue = "100") int limit) {
        
        try {
            List<Long> hotUsers = userStatsAdminService.getHotUsers(limit);
            return ResponseEntity.ok(ApiResponse.success(hotUsers));
            
        } catch (Exception e) {
            log.error("获取热点用户列表失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error(500, "获取失败: " + e.getMessage()));
        }
    }
}