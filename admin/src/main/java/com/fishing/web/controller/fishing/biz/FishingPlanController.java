package com.fishing.web.controller.fishing.biz;

import com.fishing.common.annotation.Log;
import com.fishing.common.core.controller.BaseController;
import com.fishing.common.core.domain.AjaxResult;
import com.fishing.common.core.page.TableDataInfo;
import com.fishing.common.enums.BusinessType;
import com.fishing.datasource.annotation.DataSource;
import com.fishing.datasource.enums.DataSourceType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 钓鱼计划管理Controller
 */
@DataSource(DataSourceType.FISHING_BIZ)
@RequiredArgsConstructor
@RestController
@RequestMapping("/fishing-biz/fishing-plan")
public class FishingPlanController extends BaseController {

    /**
     * 查询钓鱼计划列表
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:fishing-plan:list')")
    @GetMapping("/list")
    public TableDataInfo list() {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(200);
        rspData.setMsg("功能开发中，请稍后");
        rspData.setRows(List.of());
        rspData.setTotal(0L);
        return rspData;
    }

    /**
     * 获取钓鱼计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:fishing-plan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success("功能开发中，请稍后");
    }

    /**
     * 新增钓鱼计划
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:fishing-plan:add')")
    @Log(title = "钓鱼计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Map<String, Object> params) {
        return success("功能开发中，请稍后");
    }

    /**
     * 修改钓鱼计划
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:fishing-plan:edit')")
    @Log(title = "钓鱼计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Map<String, Object> params) {
        return success("功能开发中，请稍后");
    }

    /**
     * 删除钓鱼计划
     */
    @Operation(summary = "删除钓鱼计划", description = "批量删除钓鱼计划")
    @PreAuthorize("@ss.hasPermi('fishing-biz:fishing-plan:remove')")
    @Log(title = "钓鱼计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@Parameter(description = "计划ID数组") @PathVariable Long[] ids) {
        return success("功能开发中，请稍后");
    }

    /**
     * 获取钓鱼计划统计信息
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:fishing-plan:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        return success("功能开发中，请稍后");
    }
}