package com.fishing.web.controller.fishing.biz;

import com.fishing.common.annotation.Log;
import com.fishing.common.core.controller.BaseController;
import com.fishing.common.core.domain.AjaxResult;
import com.fishing.common.core.page.TableDataInfo;
import com.fishing.common.enums.BusinessType;
import com.fishing.datasource.annotation.DataSource;
import com.fishing.datasource.enums.DataSourceType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 通知管理Controller
 */
@DataSource(DataSourceType.FISHING_BIZ)
@RequiredArgsConstructor
@RestController
@RequestMapping("/fishing-biz/notification")
public class NotificationController extends BaseController {

    /**
     * 查询通知列表
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:notification:list')")
    @GetMapping("/list")
    public TableDataInfo list() {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(200);
        rspData.setMsg("功能开发中，请稍后");
        rspData.setRows(List.of());
        rspData.setTotal(0L);
        return rspData;
    }

    /**
     * 获取通知详细信息
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:notification:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success("功能开发中，请稍后");
    }

    /**
     * 新增通知
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:notification:add')")
    @Log(title = "通知管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Map<String, Object> params) {
        return success("功能开发中，请稍后");
    }

    /**
     * 修改通知
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:notification:edit')")
    @Log(title = "通知管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Map<String, Object> params) {
        return success("功能开发中，请稍后");
    }

    /**
     * 删除通知
     */
    @Operation(summary = "删除通知", description = "批量删除通知")
    @PreAuthorize("@ss.hasPermi('fishing-biz:notification:remove')")
    @Log(title = "通知管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@Parameter(description = "通知ID数组") @PathVariable Long[] ids) {
        return success("功能开发中，请稍后");
    }

    /**
     * 发送通知
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:notification:send')")
    @Log(title = "发送通知", businessType = BusinessType.OTHER)
    @PostMapping("/send")
    public AjaxResult sendNotification(@RequestBody Map<String, Object> params) {
        return success("功能开发中，请稍后");
    }

    /**
     * 获取通知统计信息
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:notification:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        return success("功能开发中，请稍后");
    }

    /**
     * 获取通知模板列表
     */
    @PreAuthorize("@ss.hasPermi('fishing-biz:notification:query')")
    @GetMapping("/templates")
    public AjaxResult getTemplates() {
        return success("功能开发中，请稍后");
    }
}