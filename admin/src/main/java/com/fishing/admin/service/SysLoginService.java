package com.fishing.admin.service;

import com.fishing.common.core.domain.entity.SysUser;
import com.fishing.common.core.domain.model.LoginBody;
import org.springframework.stereotype.Service;

/**
 * 登录校验方法
 */
@Service
public class SysLoginService {

    /**
     * 登录验证
     */
    public String login(String username, String password, String code, String uuid) {
        // 临时实现，返回空token
        return "temporary-token";
    }

    /**
     * 登录验证
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    public String login(LoginBody loginBody) {
        // 临时实现，返回空token
        return "temporary-token";
    }

    /**
     * 校验验证码
     * 
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     */
    public void validateCaptcha(String username, String code, String uuid) {
        // 临时实现，跳过验证码校验
    }

    /**
     * 登录前置校验
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 临时实现，跳过前置校验
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        // 临时实现，跳过记录
    }
}