package com.fishing.admin.service;

import com.fishing.common.core.domain.entity.SysUser;
import com.fishing.common.core.domain.model.LoginBody;
import com.fishing.common.core.domain.model.LoginUser;
import com.fishing.common.utils.StringUtils;
import com.fishing.framework.web.service.SysPermissionService;
import com.fishing.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * 登录校验方法
 */
@Service
public class SysLoginService {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPermissionService permissionService;

    /**
     * 登录验证
     */
    public String login(String username, String password, String code, String uuid) {
        // 简化实现：直接验证用户名密码
        SysUser user = userService.selectUserByUserName(username);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 创建LoginUser
        LoginUser loginUser = new LoginUser(user.getUserId(), user.getDeptId(), user, getMenuPermissions(user));

        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 登录验证
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    public String login(LoginBody loginBody) {
        return login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(), loginBody.getUuid());
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     */
    public void validateCaptcha(String username, String code, String uuid) {
        // 临时实现，跳过验证码校验
    }

    /**
     * 登录前置校验
     * @param username 用户名
     * @param password 用户密码
     */
    public void loginPreCheck(String username, String password) {
        // 临时实现，跳过前置校验
    }

    /**
     * 获取菜单权限
     */
    private Set<String> getMenuPermissions(SysUser user) {
        Set<String> perms = permissionService.getMenuPermission(user);
        return perms;
    }

    /**
     * 记录登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        // 临时实现，跳过记录
    }
}