package com.fishing.admin.service;

import com.fishing.common.core.domain.entity.SysUser;
import com.fishing.common.core.domain.model.LoginUser;
import org.springframework.stereotype.Service;

/**
 * Token验证处理
 */
@Service
public class TokenService {

    /**
     * 创建令牌
     */
    public String createToken(SysUser user) {
        // 临时实现，返回简单token
        return "token-" + user.getUserId();
    }

    /**
     * 创建令牌
     *
     * @param loginUser 用户信息
     * @return 令牌
     */
    public String createToken(LoginUser loginUser) {
        // 临时实现，返回简单token
        return "token-" + loginUser.getUserId();
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser) {
        // 临时实现，什么都不做
    }

    /**
     * 获取登录缓存
     */
    public String getLoginCache(String key) {
        // 临时实现，返回空
        return null;
    }

    /**
     * 删除登录缓存
     */
    public void deleteLoginCache(String key) {
        // 临时实现，什么都不做
    }
}