ruoyi:
  name: Fishing
  version: 0.0.1
  copyrightYear: 2023
  demoEnabled: true
  profile: uploadPath
  addressEnabled: false
  captchaType: math

fishing:
  name: Fishing
  version: 0.0.1
  copyrightYear: 2023
  demoEnabled: true
  profile: uploadPath
  addressEnabled: false
  captchaType: math

# 开发环境配置
server:
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    # 连接超时时间（毫秒）
    connection-timeout: 20000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 应用模块标识
app:
  module: admin

# 用户统计配置
stats:
  preload:
    # 热点用户预热数量限制
    hot-users-limit: 1000
    # 刷新批次大小
    refresh-batch-size: 100
    # 是否启用一致性检查
    consistency-check-enabled: true
  schedule:
    # 每日全量刷新时间 (凌晨2点)
    daily-refresh: "0 0 2 * * ?"
    # 热点用户预热时间 (每30分钟)
    hot-users-preload: "0 */30 * * * ?"
    # 数据一致性校验时间 (凌晨4点)
    consistency-check: "0 0 4 * * ?"
    # 过期缓存清理时间 (凌晨3点)
    cache-cleanup: "0 0 3 * * ?"
    # 快速预热时间 (每10分钟)
    quick-preload: "0 */10 * * * ?"
    # 健康状态监控时间 (每小时)
    health-monitor: "0 0 * * * ?"

# 日志配置
logging:
  level:
    com.fishing: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

spring:
  messages:
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 20MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  config:
    import: classpath:algolia-config.yml

token:
  header: Authorization
  secret: abcdefghijklmnopqrstuvwxyz
  expireTime: 30

mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.fishing.**.domain,com.fishing.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*,/fishing-biz/*

aliyun-oss:
  base-url: https://img.insight-into.life
  bucket-name: fishing-server
  endpoint: https://oss-cn-chengdu.aliyuncs.com
  access-key-id: LTAI5tEpRPUMdZYiF865G8wY
  access-key-secret: ******************************

---
spring:
  config:
    activate:
      on-profile: dev
    import: classpath:application-dev.yml
---
spring:
  config:
    activate:
      on-profile: prod
    import: classpath:application-prod.yml
