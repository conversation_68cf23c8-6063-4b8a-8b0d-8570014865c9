-- 应用版本管理表
CREATE TABLE app_versions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    version_name VARCHAR(20) NOT NULL COMMENT '版本名称，如1.2.0',
    version_code INT NOT NULL COMMENT '版本代码',
    build_number VARCHAR(20) COMMENT '构建号',
    description TEXT COMMENT '版本描述',
    whats_new JSON COMMENT '更新内容列表',
    download_url VARCHAR(500) COMMENT '下载地址',
    update_size VARCHAR(20) COMMENT '更新包大小',
    is_force_update TINYINT DEFAULT 0 COMMENT '是否强制更新：1-是，0-否',
    platform VARCHAR(20) DEFAULT 'all' COMMENT '平台：ios, android, all',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    release_time DATETIME COMMENT '发布时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_version_platform (version_name, platform),
    INDEX idx_version_code (version_code),
    INDEX idx_status (status),
    INDEX idx_release_time (release_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用版本管理表';

-- 版本反馈表
CREATE TABLE version_feedback (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    feedback_type VARCHAR(50) NOT NULL COMMENT '反馈类型：bug, suggestion, other',
    content TEXT NOT NULL COMMENT '反馈内容',
    current_version VARCHAR(20) COMMENT '当前版本',
    device_info JSON COMMENT '设备信息',
    contact_info VARCHAR(200) COMMENT '联系信息',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '处理状态：pending, processing, resolved, closed',
    admin_reply TEXT COMMENT '管理员回复',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_type (feedback_type),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本反馈表';

-- 用户缓存管理表
CREATE TABLE user_cache_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    cache_type VARCHAR(20) NOT NULL COMMENT '缓存类型：image, data, video, log',
    cache_size BIGINT DEFAULT 0 COMMENT '缓存大小（字节）',
    file_count INT DEFAULT 0 COMMENT '文件数量',
    last_clear_time DATETIME COMMENT '最后清理时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_cache_type (user_id, cache_type),
    INDEX idx_user_id (user_id),
    INDEX idx_cache_type (cache_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户缓存信息表';

-- 缓存清理记录表
CREATE TABLE cache_clear_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    cache_type VARCHAR(20) NOT NULL COMMENT '缓存类型',
    before_size BIGINT NOT NULL COMMENT '清理前大小（字节）',
    after_size BIGINT NOT NULL COMMENT '清理后大小（字节）',
    freed_size BIGINT NOT NULL COMMENT '释放大小（字节）',
    clear_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '清理时间',
    INDEX idx_user_id (user_id),
    INDEX idx_clear_time (clear_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缓存清理记录表';