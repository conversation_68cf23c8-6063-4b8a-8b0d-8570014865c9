-- 新增功能数据库Schema初始化脚本
-- 适用于钓鱼之旅应用的新增功能
-- 创建时间: 2024-07-25

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================
-- 1. 帮助中心功能相关表
-- ============================================

-- 帮助中心分类表
DROP TABLE IF EXISTS help_categories;
CREATE TABLE help_categories (
    id VARCHAR(50) PRIMARY KEY COMMENT '分类ID',
    title VARCHAR(100) NOT NULL COMMENT '分类标题',
    icon VARCHAR(50) COMMENT '图标名称',
    description VARCHAR(200) COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帮助中心分类表';

-- 帮助问题表
DROP TABLE IF EXISTS help_questions;
CREATE TABLE help_questions (
    id VARCHAR(50) PRIMARY KEY COMMENT '问题ID',
    category_id VARCHAR(50) NOT NULL COMMENT '分类ID',
    question TEXT NOT NULL COMMENT '问题内容',
    answer TEXT NOT NULL COMMENT '答案内容',
    sort_order INT DEFAULT 0 COMMENT '排序',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    helpful_count INT DEFAULT 0 COMMENT '有用次数',
    not_helpful_count INT DEFAULT 0 COMMENT '无用次数',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category_id (category_id),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帮助问题表';

-- 用户帮助反馈表
DROP TABLE IF EXISTS user_help_feedback;
CREATE TABLE user_help_feedback (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    question_id VARCHAR(50) NOT NULL COMMENT '问题ID',
    is_helpful TINYINT NOT NULL COMMENT '是否有用：1-有用，0-无用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_user_question (user_id, question_id),
    INDEX idx_question_id (question_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户帮助反馈表';

-- 问题查看记录表
DROP TABLE IF EXISTS help_question_views;
CREATE TABLE help_question_views (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    question_id VARCHAR(50) NOT NULL COMMENT '问题ID',
    view_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '查看时间',
    INDEX idx_question_id (question_id),
    INDEX idx_user_id (user_id),
    INDEX idx_view_time (view_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问题查看记录表';

-- ============================================
-- 2. 版本管理和反馈相关表
-- ============================================

-- 应用版本管理表
DROP TABLE IF EXISTS app_versions;
CREATE TABLE app_versions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    version_name VARCHAR(20) NOT NULL COMMENT '版本名称，如1.2.0',
    version_code INT NOT NULL COMMENT '版本代码',
    build_number VARCHAR(20) COMMENT '构建号',
    description TEXT COMMENT '版本描述',
    whats_new JSON COMMENT '更新内容列表',
    download_url VARCHAR(500) COMMENT '下载地址',
    update_size VARCHAR(20) COMMENT '更新包大小',
    is_force_update TINYINT DEFAULT 0 COMMENT '是否强制更新：1-是，0-否',
    platform VARCHAR(20) DEFAULT 'all' COMMENT '平台：ios, android, all',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    release_time DATETIME COMMENT '发布时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_version_platform (version_name, platform),
    INDEX idx_version_code (version_code),
    INDEX idx_status (status),
    INDEX idx_release_time (release_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用版本管理表';

-- 版本反馈表
DROP TABLE IF EXISTS version_feedback;
CREATE TABLE version_feedback (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    feedback_type VARCHAR(50) NOT NULL COMMENT '反馈类型：bug, suggestion, other',
    content TEXT NOT NULL COMMENT '反馈内容',
    current_version VARCHAR(20) COMMENT '当前版本',
    device_info JSON COMMENT '设备信息',
    contact_info VARCHAR(200) COMMENT '联系信息',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '处理状态：pending, processing, resolved, closed',
    admin_reply TEXT COMMENT '管理员回复',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_user_id (user_id),
    INDEX idx_type (feedback_type),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本反馈表';

-- ============================================
-- 3. 缓存管理相关表
-- ============================================

-- 用户缓存管理表
DROP TABLE IF EXISTS user_cache_info;
CREATE TABLE user_cache_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    cache_type VARCHAR(20) NOT NULL COMMENT '缓存类型：image, data, video, log',
    cache_size BIGINT DEFAULT 0 COMMENT '缓存大小（字节）',
    file_count INT DEFAULT 0 COMMENT '文件数量',
    last_clear_time DATETIME COMMENT '最后清理时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_cache_type (user_id, cache_type),
    INDEX idx_user_id (user_id),
    INDEX idx_cache_type (cache_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户缓存信息表';

-- 缓存清理记录表
DROP TABLE IF EXISTS cache_clear_logs;
CREATE TABLE cache_clear_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    cache_type VARCHAR(20) NOT NULL COMMENT '缓存类型',
    before_size BIGINT NOT NULL COMMENT '清理前大小（字节）',
    after_size BIGINT NOT NULL COMMENT '清理后大小（字节）',
    freed_size BIGINT NOT NULL COMMENT '释放大小（字节）',
    clear_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '清理时间',
    INDEX idx_user_id (user_id),
    INDEX idx_clear_time (clear_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缓存清理记录表';

-- ============================================
-- 4. 相册功能增强相关表
-- ============================================

-- 图片分享记录表
DROP TABLE IF EXISTS image_share_logs;
CREATE TABLE image_share_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '分享用户ID',
    image_id BIGINT NOT NULL COMMENT '图片ID',
    share_type VARCHAR(20) NOT NULL COMMENT '分享类型：wechat, weibo, qq, other',
    share_url VARCHAR(500) COMMENT '分享链接',
    share_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分享时间',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    INDEX idx_user_id (user_id),
    INDEX idx_image_id (image_id),
    INDEX idx_share_type (share_type),
    INDEX idx_share_time (share_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片分享记录表';

-- 图片查看记录表
DROP TABLE IF EXISTS image_view_logs;
CREATE TABLE image_view_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT COMMENT '查看用户ID（可为空，表示匿名查看）',
    image_id BIGINT NOT NULL COMMENT '图片ID',
    view_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '查看时间',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    INDEX idx_user_id (user_id),
    INDEX idx_image_id (image_id),
    INDEX idx_view_time (view_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片查看记录表';

-- 图片元数据扩展表
DROP TABLE IF EXISTS user_album_metadata;
CREATE TABLE user_album_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    album_id BIGINT NOT NULL COMMENT '相册图片ID',
    file_size BIGINT COMMENT '文件大小（字节）',
    image_width INT COMMENT '图片宽度',
    image_height INT COMMENT '图片高度',
    image_format VARCHAR(10) COMMENT '图片格式：JPEG, PNG, WebP等',
    exif_data JSON COMMENT 'EXIF数据',
    upload_ip VARCHAR(50) COMMENT '上传IP',
    original_filename VARCHAR(255) COMMENT '原始文件名',
    checksum VARCHAR(64) COMMENT '文件校验和',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_album_id (album_id),
    INDEX idx_file_size (file_size),
    INDEX idx_format (image_format)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户相册元数据表';

-- ============================================
-- 5. 统计和配置相关表
-- ============================================

-- 应用统计表
DROP TABLE IF EXISTS app_statistics;
CREATE TABLE app_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    total_users INT DEFAULT 0 COMMENT '总用户数',
    total_spots INT DEFAULT 0 COMMENT '总钓点数',
    total_moments INT DEFAULT 0 COMMENT '总动态数',
    daily_active_users INT DEFAULT 0 COMMENT '日活跃用户数',
    new_users_today INT DEFAULT 0 COMMENT '今日新增用户',
    new_spots_today INT DEFAULT 0 COMMENT '今日新增钓点',
    new_moments_today INT DEFAULT 0 COMMENT '今日新增动态',
    total_downloads INT DEFAULT 0 COMMENT '总下载量',
    average_rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均评分',
    total_reviews INT DEFAULT 0 COMMENT '总评论数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_stat_date (stat_date),
    INDEX idx_stat_date (stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用统计表';

-- 用户统计表
DROP TABLE IF EXISTS user_statistics;
CREATE TABLE user_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    following_count INT DEFAULT 0 COMMENT '关注数',
    followers_count INT DEFAULT 0 COMMENT '粉丝数',
    moments_count INT DEFAULT 0 COMMENT '动态数',
    spots_count INT DEFAULT 0 COMMENT '钓点数',
    likes_received_count INT DEFAULT 0 COMMENT '获赞数',
    comments_received_count INT DEFAULT 0 COMMENT '获评论数',
    bookmarks_count INT DEFAULT 0 COMMENT '收藏数',
    album_count INT DEFAULT 0 COMMENT '相册图片数',
    total_views INT DEFAULT 0 COMMENT '总浏览量',
    last_active_time DATETIME COMMENT '最后活跃时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_followers_count (followers_count),
    INDEX idx_moments_count (moments_count),
    INDEX idx_last_active (last_active_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户统计表';

-- 系统配置表
DROP TABLE IF EXISTS system_config;
CREATE TABLE system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型：string, number, boolean, json',
    category VARCHAR(50) COMMENT '配置分类',
    description VARCHAR(200) COMMENT '配置描述',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开：1-是，0-否',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_category (category),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ============================================
-- 6. 初始化数据
-- ============================================

-- 插入帮助中心分类
INSERT INTO help_categories (id, title, icon, description, sort_order) VALUES
('account', '账号相关', 'person_outline', '注册、登录、密码等', 1),
('fishing_spot', '钓点功能', 'location_on_outlined', '添加、收藏、分享钓点', 2),
('community', '社区功能', 'forum_outlined', '动态、关注、互动', 3),
('other', '其他问题', 'help_outline', '常见问题、联系客服', 4);

-- 插入帮助问题
INSERT INTO help_questions (id, category_id, question, answer, sort_order) VALUES
('account_1', 'account', '如何注册账号？', '您可以通过手机号码注册账号。在登录页面点击"注册"，输入手机号码并获取验证码，设置密码后即可完成注册。', 1),
('account_2', 'account', '忘记密码怎么办？', '在登录页面点击"忘记密码"，输入您的手机号码，获取验证码后即可重置密码。', 2),
('account_3', 'account', '如何修改个人信息？', '进入"我的"页面，点击"设置"，然后选择"编辑资料"即可修改您的个人信息。', 3),
('account_4', 'account', '如何注销账号？', '进入"设置"-"账号与安全"-"注销账号"，按照提示操作即可。注意：注销后所有数据将无法恢复。', 4),
('spot_1', 'fishing_spot', '如何添加钓点？', '在钓点页面点击"+"按钮，选择位置并填写钓点信息，包括名称、描述、鱼种等信息。', 1),
('spot_2', 'fishing_spot', '如何收藏钓点？', '在钓点详情页面点击收藏按钮（心形图标）即可收藏该钓点。', 2),
('spot_3', 'fishing_spot', '钓点信息不准确怎么办？', '您可以在钓点详情页面点击"反馈"按钮，告诉我们具体的问题，我们会及时处理。', 3),
('spot_4', 'fishing_spot', '如何分享钓点给朋友？', '在钓点详情页面点击分享按钮，选择分享方式即可将钓点分享给朋友。', 4),
('community_1', 'community', '如何发布动态？', '在社区页面点击"+"按钮，选择动态类型，添加图片和文字描述，然后发布即可。', 1),
('community_2', 'community', '如何关注其他用户？', '在用户主页点击"关注"按钮即可关注该用户，关注后可以在动态中看到他们的最新内容。', 2),
('community_3', 'community', '如何举报不当内容？', '在动态详情页面点击右上角的"..."按钮，选择"举报"，选择举报原因并提交。', 3),
('community_4', 'community', '评论被删除了是什么原因？', '评论可能因违反社区规范被系统或管理员删除。请确保您的评论内容健康、友善。', 4),
('other_1', 'other', '应用闪退怎么办？', '请尝试重启应用或重启手机。如果问题持续存在，请通过"反馈与建议"联系我们。', 1),
('other_2', 'other', '如何联系客服？', '您可以通过"我的"页面中的"反馈与建议"功能联系我们，我们会及时回复。', 2),
('other_3', 'other', '应用支持哪些设备？', '应用支持iOS 12.0以上和Android 6.0以上的设备。', 3),
('other_4', 'other', '如何清理缓存？', '进入"设置"-"通用设置"-"清理缓存"，点击确认即可清理应用缓存。', 4);

-- 插入应用版本信息
INSERT INTO app_versions (version_name, version_code, build_number, description, whats_new, download_url, update_size, is_force_update, platform, status, release_time) VALUES
('1.2.0', 120, '20240115', '优化了钓点推荐算法，修复了已知问题，提升了应用性能。', 
 JSON_ARRAY('优化钓点推荐算法', '修复动态分享bug', '提升应用启动速度', '优化用户界面体验'), 
 'https://www.fishingtrip.com/download/v1.2.0', '25.6MB', 0, 'all', 1, '2024-01-15 10:00:00'),
('1.3.0', 130, '20240724', '新增社交功能，优化用户体验。', 
 JSON_ARRAY('新增私信功能', '优化相册管理', '增强搜索功能', '修复已知问题'), 
 'https://www.fishingtrip.com/download/v1.3.0', '28.2MB', 0, 'all', 1, '2024-07-24 10:00:00');

-- 插入系统配置
INSERT INTO system_config (config_key, config_value, config_type, category, description, is_public) VALUES
('app.name', '钓鱼之旅', 'string', 'app', '应用名称', 1),
('app.version', '1.2.0', 'string', 'app', '当前版本', 1),
('app.build_number', '20240115', 'string', 'app', '构建号', 0),
('app.website', 'https://www.fishingtrip.com', 'string', 'app', '官网地址', 1),
('app.support_email', '<EMAIL>', 'string', 'app', '支持邮箱', 1),
('app.customer_service', '400-123-4567', 'string', 'app', '客服电话', 1),
('cache.default_size_limit', '104857600', 'number', 'cache', '默认缓存大小限制（字节）', 0),
('cache.auto_clear_days', '7', 'number', 'cache', '自动清理缓存天数', 0),
('privacy.policy_version', '1.2', 'string', 'legal', '隐私政策版本', 1),
('terms.service_version', '1.2', 'string', 'legal', '服务条款版本', 1);

-- 插入今日应用统计示例数据
INSERT INTO app_statistics (stat_date, total_users, total_spots, total_moments, daily_active_users, new_users_today, new_spots_today, new_moments_today, total_downloads, average_rating, total_reviews) VALUES
(CURDATE(), 150000, 8500, 320000, 25000, 850, 45, 1500, 500000, 4.6, 12500);

SET FOREIGN_KEY_CHECKS = 1;