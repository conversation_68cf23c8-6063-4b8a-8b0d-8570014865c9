-- 帮助中心分类表
CREATE TABLE help_categories (
    id VARCHAR(50) PRIMARY KEY COMMENT '分类ID',
    title VARCHAR(100) NOT NULL COMMENT '分类标题',
    icon VARCHAR(50) COMMENT '图标名称',
    description VARCHAR(200) COMMENT '分类描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帮助中心分类表';

-- 帮助问题表
CREATE TABLE help_questions (
    id VARCHAR(50) PRIMARY KEY COMMENT '问题ID',
    category_id VARCHAR(50) NOT NULL COMMENT '分类ID',
    question TEXT NOT NULL COMMENT '问题内容',
    answer TEXT NOT NULL COMMENT '答案内容',
    sort_order INT DEFAULT 0 COMMENT '排序',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    helpful_count INT DEFAULT 0 COMMENT '有用次数',
    not_helpful_count INT DEFAULT 0 COMMENT '无用次数',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    FOREIGN KEY (category_id) REFERENCES help_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帮助问题表';

-- 用户帮助反馈表
CREATE TABLE user_help_feedback (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    question_id VARCHAR(50) NOT NULL COMMENT '问题ID',
    is_helpful TINYINT NOT NULL COMMENT '是否有用：1-有用，0-无用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_user_question (user_id, question_id),
    INDEX idx_question_id (question_id),
    INDEX idx_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户帮助反馈表';

-- 问题查看记录表
CREATE TABLE help_question_views (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    question_id VARCHAR(50) NOT NULL COMMENT '问题ID',
    view_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '查看时间',
    INDEX idx_question_id (question_id),
    INDEX idx_user_id (user_id),
    INDEX idx_view_time (view_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问题查看记录表';