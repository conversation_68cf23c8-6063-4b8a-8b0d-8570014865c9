-- 应用统计表
CREATE TABLE app_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    total_users INT DEFAULT 0 COMMENT '总用户数',
    total_spots INT DEFAULT 0 COMMENT '总钓点数',
    total_moments INT DEFAULT 0 COMMENT '总动态数',
    daily_active_users INT DEFAULT 0 COMMENT '日活跃用户数',
    new_users_today INT DEFAULT 0 COMMENT '今日新增用户',
    new_spots_today INT DEFAULT 0 COMMENT '今日新增钓点',
    new_moments_today INT DEFAULT 0 COMMENT '今日新增动态',
    total_downloads INT DEFAULT 0 COMMENT '总下载量',
    average_rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '平均评分',
    total_reviews INT DEFAULT 0 COMMENT '总评论数',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_stat_date (stat_date),
    INDEX idx_stat_date (stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='应用统计表';

-- 用户统计表
CREATE TABLE user_statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    following_count INT DEFAULT 0 COMMENT '关注数',
    followers_count INT DEFAULT 0 COMMENT '粉丝数',
    moments_count INT DEFAULT 0 COMMENT '动态数',
    spots_count INT DEFAULT 0 COMMENT '钓点数',
    likes_received_count INT DEFAULT 0 COMMENT '获赞数',
    comments_received_count INT DEFAULT 0 COMMENT '获评论数',
    bookmarks_count INT DEFAULT 0 COMMENT '收藏数',
    album_count INT DEFAULT 0 COMMENT '相册图片数',
    total_views INT DEFAULT 0 COMMENT '总浏览量',
    last_active_time DATETIME COMMENT '最后活跃时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_followers_count (followers_count),
    INDEX idx_moments_count (moments_count),
    INDEX idx_last_active (last_active_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户统计表';

-- 系统配置表
CREATE TABLE system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型：string, number, boolean, json',
    category VARCHAR(50) COMMENT '配置分类',
    description VARCHAR(200) COMMENT '配置描述',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开：1-是，0-否',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_category (category),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 用户行为日志表
CREATE TABLE user_behavior_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT COMMENT '用户ID（可为空，表示匿名用户）',
    action_type VARCHAR(50) NOT NULL COMMENT '行为类型',
    action_target VARCHAR(100) COMMENT '行为目标',
    target_id BIGINT COMMENT '目标ID',
    action_data JSON COMMENT '行为数据',
    session_id VARCHAR(100) COMMENT '会话ID',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    platform VARCHAR(20) COMMENT '平台：ios, android, web',
    app_version VARCHAR(20) COMMENT '应用版本',
    action_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '行为时间',
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_action_time (action_time),
    INDEX idx_platform (platform)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户行为日志表';

-- 插入初始配置数据
INSERT INTO system_config (config_key, config_value, config_type, category, description, is_public) VALUES
('app.name', '钓鱼之旅', 'string', 'app', '应用名称', 1),
('app.version', '1.2.0', 'string', 'app', '当前版本', 1),
('app.build_number', '20240115', 'string', 'app', '构建号', 0),
('app.website', 'https://www.fishingtrip.com', 'string', 'app', '官网地址', 1),
('app.support_email', '<EMAIL>', 'string', 'app', '支持邮箱', 1),
('app.customer_service', '400-123-4567', 'string', 'app', '客服电话', 1),
('cache.default_size_limit', '104857600', 'number', 'cache', '默认缓存大小限制（字节）', 0),
('cache.auto_clear_days', '7', 'number', 'cache', '自动清理缓存天数', 0);