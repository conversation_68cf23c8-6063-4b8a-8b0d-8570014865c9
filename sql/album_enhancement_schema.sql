-- 图片分享记录表
CREATE TABLE image_share_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '分享用户ID',
    image_id BIGINT NOT NULL COMMENT '图片ID',
    share_type VARCHAR(20) NOT NULL COMMENT '分享类型：wechat, weibo, qq, other',
    share_url VARCHAR(500) COMMENT '分享链接',
    share_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分享时间',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    INDEX idx_user_id (user_id),
    INDEX idx_image_id (image_id),
    INDEX idx_share_type (share_type),
    INDEX idx_share_time (share_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片分享记录表';

-- 图片查看记录表
CREATE TABLE image_view_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT COMMENT '查看用户ID（可为空，表示匿名查看）',
    image_id BIGINT NOT NULL COMMENT '图片ID',
    view_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '查看时间',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    INDEX idx_user_id (user_id),
    INDEX idx_image_id (image_id),
    INDEX idx_view_time (view_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片查看记录表';

-- 图片元数据扩展表
CREATE TABLE user_album_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    album_id BIGINT NOT NULL COMMENT '相册图片ID',
    file_size BIGINT COMMENT '文件大小（字节）',
    image_width INT COMMENT '图片宽度',
    image_height INT COMMENT '图片高度',
    image_format VARCHAR(10) COMMENT '图片格式：JPEG, PNG, WebP等',
    exif_data JSON COMMENT 'EXIF数据',
    upload_ip VARCHAR(50) COMMENT '上传IP',
    original_filename VARCHAR(255) COMMENT '原始文件名',
    checksum VARCHAR(64) COMMENT '文件校验和',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_album_id (album_id),
    INDEX idx_file_size (file_size),
    INDEX idx_format (image_format)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户相册元数据表';

-- 图片标签表
CREATE TABLE image_tags (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    tag_name VARCHAR(50) NOT NULL COMMENT '标签名称',
    tag_color VARCHAR(10) COMMENT '标签颜色',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    created_by BIGINT COMMENT '创建者用户ID',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_tag_name (tag_name),
    INDEX idx_usage_count (usage_count),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片标签表';

-- 图片标签关联表
CREATE TABLE image_tag_relations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    album_id BIGINT NOT NULL COMMENT '相册图片ID',
    tag_id BIGINT NOT NULL COMMENT '标签ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_album_tag (album_id, tag_id),
    INDEX idx_album_id (album_id),
    INDEX idx_tag_id (tag_id),
    FOREIGN KEY (tag_id) REFERENCES image_tags(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图片标签关联表';