-- 钓点签到功能数据库表
-- 创建时间: 2024-07-26
-- 说明: 用于记录用户在钓点的签到记录

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================
-- 钓点签到记录表
-- ============================================

-- 钓点签到记录表
DROP TABLE IF EXISTS spot_checkins;
CREATE TABLE spot_checkins (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    spot_id BIGINT NOT NULL COMMENT '钓点ID',
    checkin_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '签到时间',
    comment VARCHAR(500) COMMENT '签到评论',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    
    -- 索引优化
    INDEX idx_user_id (user_id),
    INDEX idx_spot_id (spot_id), 
    INDEX idx_checkin_time (checkin_time),
    INDEX idx_user_spot_time (user_id, spot_id, checkin_time)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钓点签到记录表';

-- ============================================
-- 示例数据 (可选)
-- ============================================

-- 可根据需要插入测试数据
-- INSERT INTO spot_checkins (user_id, spot_id, checkin_time, comment) VALUES
-- (1, 1, NOW(), '今天天气不错，适合钓鱼'),
-- (2, 1, NOW() - INTERVAL 1 HOUR, '钓点环境很好'),
-- (1, 2, NOW() - INTERVAL 1 DAY, '昨天在这里钓到了大鱼');

SET FOREIGN_KEY_CHECKS = 1;