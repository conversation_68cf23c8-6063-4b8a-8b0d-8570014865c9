package com.fishing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fishing.common.result.PageResult;
import com.fishing.domain.spot.FishingSpot;
import com.fishing.dto.spot.QuerySpotDto;
import com.fishing.vo.spot.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface IFishingSpotQueryService extends IService<FishingSpot> {
    List<FishingSpot> findNearbySpots(BigDecimal latitude, BigDecimal longitude, Double radius);

    IPage<SpotSummaryVO> findFishingSpots(QuerySpotDto querySpotDto);

    Optional<SpotDetailVO> findById(Long id);

    List<SpotPriceVO> getPricesBySpotId(Long id);

    List<SpotFacilityVO> getSpotFacilities(Long id);

    PageResult<SpotSummaryVO> getUserRecentCheckins(Long userId, int page, int size);

    List<SpotSummaryVO> getUserFavorites(Long userId, int page, int size);

    PageResult<SpotSummaryVO> getUserCreatedSpots(Long userId, int page, int size);

    List<SpotSummaryVO> searchFishingSpots(String query, int page, int size);

    List<SpotSummaryVO> searchFishingSpots(String query, int page, int size, double latitude, double longitude, double radiusKm);

    /**
     * 获取附近钓点的地图数据 - 返回 SpotMapVO 格式
     *
     * @param latitude  纬度
     * @param longitude 经度
     * @param radius    搜索半径（公里）
     * @param page      页码（0-based）
     * @param size      每页大小
     * @return 地图钓点数据分页结果
     */
    IPage<SpotMapVO> findNearbyMapSpots(BigDecimal latitude, BigDecimal longitude, Double radius, int page, int size);

    // Additional methods needed by admin controller
    List<Long> getFishTypeIds(Long spotId);

    List<String> getSpotImages(Long spotId);

    // SpotSummaryVO conversion methods for Algolia sync
    List<SpotSummaryVO> convertToSpotSummaryVO(List<FishingSpot> fishingSpots);

    SpotSummaryVO convertToSpotSummaryVO(FishingSpot fishingSpot);
}
