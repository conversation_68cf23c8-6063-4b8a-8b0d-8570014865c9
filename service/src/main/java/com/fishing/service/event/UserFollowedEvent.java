package com.fishing.service.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户关注事件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserFollowedEvent {
    
    /**
     * 关注者用户ID
     */
    private Long followerUserId;
    
    /**
     * 被关注者用户ID
     */
    private Long followedUserId;
    
    /**
     * 事件时间戳
     */
    private Long timestamp;
    
    public UserFollowedEvent(Long followerUserId, Long followedUserId) {
        this.followerUserId = followerUserId;
        this.followedUserId = followedUserId;
        this.timestamp = System.currentTimeMillis();
    }
}