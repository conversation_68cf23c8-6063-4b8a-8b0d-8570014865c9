package com.fishing.service.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 动态创建事件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MomentCreatedEvent {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 动态ID
     */
    private Long momentId;
    
    /**
     * 事件时间戳
     */
    private Long timestamp;
    
    public MomentCreatedEvent(Long userId, Long momentId) {
        this.userId = userId;
        this.momentId = momentId;
        this.timestamp = System.currentTimeMillis();
    }
}