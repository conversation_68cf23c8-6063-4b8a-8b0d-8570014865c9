package com.fishing.service.event;

import com.fishing.service.IUserStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 用户统计事件处理器
 * 监听业务事件，实时更新Redis中的统计数据
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserStatsEventHandler {

    private final IUserStatsService userStatsService;

    /**
     * 处理用户关注事件
     */
    @Async
    @EventListener
    public void handleUserFollowedEvent(UserFollowedEvent event) {
        try {
            // 被关注用户的粉丝数+1
            userStatsService.incrementFollowerCount(event.getFollowedUserId(), 1);
            // 关注用户的关注数+1
            userStatsService.incrementFollowingCount(event.getFollowerUserId(), 1);
            
            log.debug("用户关注事件处理完成: {} 关注了 {}", 
                    event.getFollowerUserId(), event.getFollowedUserId());
        } catch (Exception e) {
            log.error("处理用户关注事件失败", e);
        }
    }

    /**
     * 处理用户取消关注事件
     */
    @Async
    @EventListener
    public void handleUserUnfollowedEvent(UserUnfollowedEvent event) {
        try {
            // 被取消关注用户的粉丝数-1
            userStatsService.incrementFollowerCount(event.getFollowedUserId(), -1);
            // 取消关注用户的关注数-1
            userStatsService.incrementFollowingCount(event.getFollowerUserId(), -1);
            
            log.debug("用户取消关注事件处理完成: {} 取消关注了 {}", 
                    event.getFollowerUserId(), event.getFollowedUserId());
        } catch (Exception e) {
            log.error("处理用户取消关注事件失败", e);
        }
    }

    /**
     * 处理动态创建事件
     */
    @Async
    @EventListener
    public void handleMomentCreatedEvent(MomentCreatedEvent event) {
        try {
            // 用户动态数+1
            userStatsService.incrementMomentCount(event.getUserId(), 1);
            
            log.debug("动态创建事件处理完成: 用户 {} 创建了动态 {}", 
                    event.getUserId(), event.getMomentId());
        } catch (Exception e) {
            log.error("处理动态创建事件失败", e);
        }
    }

    /**
     * 处理动态删除事件
     */
    @Async
    @EventListener
    public void handleMomentDeletedEvent(MomentDeletedEvent event) {
        try {
            // 用户动态数-1
            userStatsService.incrementMomentCount(event.getUserId(), -1);
            
            log.debug("动态删除事件处理完成: 用户 {} 删除了动态 {}", 
                    event.getUserId(), event.getMomentId());
        } catch (Exception e) {
            log.error("处理动态删除事件失败", e);
        }
    }

    /**
     * 处理钓点创建事件
     */
    @Async
    @EventListener
    public void handleFishingSpotCreatedEvent(FishingSpotCreatedEvent event) {
        try {
            // 用户钓点数+1
            userStatsService.incrementSpotCount(event.getUserId(), 1);
            
            log.debug("钓点创建事件处理完成: 用户 {} 创建了钓点 {}", 
                    event.getUserId(), event.getSpotId());
        } catch (Exception e) {
            log.error("处理钓点创建事件失败", e);
        }
    }

    /**
     * 处理钓点删除事件
     */
    @Async
    @EventListener
    public void handleFishingSpotDeletedEvent(FishingSpotDeletedEvent event) {
        try {
            // 用户钓点数-1
            userStatsService.incrementSpotCount(event.getUserId(), -1);
            
            log.debug("钓点删除事件处理完成: 用户 {} 删除了钓点 {}", 
                    event.getUserId(), event.getSpotId());
        } catch (Exception e) {
            log.error("处理钓点删除事件失败", e);
        }
    }
}