package com.fishing.service.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户取消关注事件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserUnfollowedEvent {
    
    /**
     * 取消关注者用户ID
     */
    private Long followerUserId;
    
    /**
     * 被取消关注者用户ID
     */
    private Long followedUserId;
    
    /**
     * 事件时间戳
     */
    private Long timestamp;
    
    public UserUnfollowedEvent(Long followerUserId, Long followedUserId) {
        this.followerUserId = followerUserId;
        this.followedUserId = followedUserId;
        this.timestamp = System.currentTimeMillis();
    }
}