package com.fishing.service.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 钓点创建事件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FishingSpotCreatedEvent {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 钓点ID
     */
    private Long spotId;
    
    /**
     * 事件时间戳
     */
    private Long timestamp;
    
    public FishingSpotCreatedEvent(Long userId, Long spotId) {
        this.userId = userId;
        this.spotId = spotId;
        this.timestamp = System.currentTimeMillis();
    }
}