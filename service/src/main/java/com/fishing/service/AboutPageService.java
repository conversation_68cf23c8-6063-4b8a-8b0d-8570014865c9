package com.fishing.service;

import com.fishing.dto.about.*;

/**
 * 关于页面服务接口
 */
public interface AboutPageService {

    /**
     * 获取应用信息
     */
    AppInfoDto getAppInfo();

    /**
     * 检查版本更新
     */
    VersionCheckDto checkVersion(String currentVersion);

    /**
     * 提交版本反馈
     */
    boolean submitVersionFeedback(Long userId, VersionFeedbackRequest feedback);

    /**
     * 获取隐私政策
     */
    PrivacyPolicyDto getPrivacyPolicy();

    /**
     * 获取服务条款
     */
    TermsOfServiceDto getTermsOfService();

    /**
     * 生成分享信息
     */
    AppShareInfoDto generateShareInfo(Long userId);

    /**
     * 获取应用统计信息
     */
    AppStatsDto getAppStats();
}