package com.fishing.service;

import com.fishing.domain.UserSettings;
import com.fishing.dto.settings.CacheInfoDto;
import com.fishing.dto.settings.ClearCacheResultDto;
import com.fishing.dto.user.UserStatsDto;

import java.util.Map;

/**
 * 用户设置服务接口
 */
public interface UserSettingsService {

    /**
     * 根据用户ID获取用户设置
     */
    UserSettings getUserSettings(Long userId);

    /**
     * 更新用户设置
     */
    boolean updateUserSettings(Long userId, UserSettings settings);

    /**
     * 初始化默认用户设置
     */
    boolean initDefaultSettings(Long userId);

    /**
     * 更新隐私设置
     */
    boolean updatePrivacySettings(Long userId, String profileVisibility, String momentVisibility, 
                                 Boolean allowFollow, Boolean allowMessage);

    /**
     * 更新通知设置
     */
    boolean updateNotificationSettings(Long userId, Boolean momentNotification, Boolean commentNotification,
                                     Boolean followNotification, Boolean systemNotification);

    /**
     * 更新语言设置
     */
    boolean updateLanguage(Long userId, String language);

    /**
     * 更新隐私级别
     */
    boolean updatePrivacyLevel(Long userId, String privacyLevel);

    /**
     * 批量更新通知开关
     */
    boolean batchUpdateNotificationEnabled(Long[] userIds, Boolean enabled);

    /**
     * 获取用户通知偏好
     */
    Map<String, Boolean> getUserNotificationPreferences(Long userId);

    /**
     * 检查用户是否允许接收某类型通知
     */
    boolean isNotificationEnabled(Long userId, String notificationType);

    /**
     * 获取允许接收通知的用户ID列表
     */
    Long[] getNotificationEnabledUserIds();

    /**
     * 统计各隐私级别的用户数量
     */
    Map<String, Long> getPrivacyLevelStats();

    /**
     * 重置用户设置为默认值
     */
    boolean resetToDefaultSettings(Long userId);

    /**
     * 获取用户缓存信息
     */
    CacheInfoDto getCacheInfo(Long userId);

    /**
     * 清理用户缓存
     * @param userId 用户ID
     * @param cacheType 缓存类型: image, data, log, all
     * @return 清理结果包含清理大小和消息
     */
    ClearCacheResultDto clearCache(Long userId, String cacheType);

    /**
     * 获取用户统计信息
     */
    UserStatsDto getUserStats(Long userId);

    /**
     * 验证用户密码
     */
    boolean validatePassword(Long userId, String password);
}