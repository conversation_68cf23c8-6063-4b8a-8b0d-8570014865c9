package com.fishing.service;

import com.fishing.dto.user.UserStatsDto;

/**
 * 用户统计服务接口
 */
public interface IUserStatsService {

    /**
     * 获取用户统计数据
     * 优先从Redis缓存获取，缓存未命中时从数据库查询并缓存
     * 
     * @param userId 用户ID
     * @return 用户统计数据
     */
    UserStatsDto getUserStats(Long userId);

    /**
     * 增加用户粉丝数
     * 实时更新Redis缓存
     * 
     * @param userId 用户ID
     * @param delta 增量（可为负数）
     */
    void incrementFollowerCount(Long userId, int delta);

    /**
     * 增加用户关注数
     * 实时更新Redis缓存
     * 
     * @param userId 用户ID
     * @param delta 增量（可为负数）
     */
    void incrementFollowingCount(Long userId, int delta);

    /**
     * 增加用户动态数
     * 实时更新Redis缓存
     * 
     * @param userId 用户ID
     * @param delta 增量（可为负数）
     */
    void incrementMomentCount(Long userId, int delta);

    /**
     * 增加用户钓点数
     * 实时更新Redis缓存
     * 
     * @param userId 用户ID
     * @param delta 增量（可为负数）
     */
    void incrementSpotCount(Long userId, int delta);
}