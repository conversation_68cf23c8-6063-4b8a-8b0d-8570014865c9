package com.fishing.service;

import com.fishing.dto.help.*;

import java.util.List;

/**
 * 帮助中心服务接口
 */
public interface HelpCenterService {

    /**
     * 获取帮助分类列表
     */
    List<HelpCategoryDto> getHelpCategories();

    /**
     * 根据分类ID获取问题列表
     */
    List<HelpQuestionDto> getCategoryQuestions(String categoryId);

    /**
     * 搜索帮助内容
     */
    List<HelpQuestionDto> searchHelp(String keyword);

    /**
     * 标记问题是否有帮助
     */
    boolean markQuestionHelpful(String questionId, boolean isHelpful, Long userId);

    /**
     * 记录问题查看次数
     */
    boolean recordQuestionView(String questionId, Long userId);

    /**
     * 获取问题统计信息
     */
    QuestionStatsDto getQuestionStats(String questionId);

    /**
     * 获取用户反馈统计
     */
    UserFeedbackStatsDto getUserFeedbackStats(Long userId);
}