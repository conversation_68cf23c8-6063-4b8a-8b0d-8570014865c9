package com.fishing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.domain.UserAlbum;
import com.fishing.dto.album.*;

import java.util.List;

/**
 * 用户相册服务接口
 */
public interface UserAlbumService {

    /**
     * 分页查询用户相册
     */
    IPage<UserAlbum> getUserAlbumPage(Long userId, Integer page, Integer pageSize);

    /**
     * 添加图片到相册
     */
    boolean addToAlbum(Long userId, String imageUrl, String description, String tags);

    /**
     * 从相册删除图片
     */
    boolean deleteFromAlbum(Long userId, Long imageId);

    /**
     * 更新图片信息
     */
    boolean updateAlbumImage(Long userId, Long imageId, String description, String tags);

    /**
     * 获取用户相册统计信息
     */
    AlbumStatsDto getAlbumStats(Long userId);

    /**
     * 获取用户最近上传的图片
     */
    List<UserAlbum> getRecentImages(Long userId, Integer limit);

    /**
     * 根据标签搜索图片
     */
    List<UserAlbum> searchByTags(Long userId, String tags);

    /**
     * 设置图片公开/私密状态
     */
    boolean updateImagePrivacy(Long userId, Long imageId, Integer isPublic);

    /**
     * 批量删除图片（简单版本）
     */
    boolean batchDeleteImages(Long userId, List<Long> imageIds);

    /**
     * 获取用户相册占用空间
     */
    Long getTotalFileSize(Long userId);

    /**
     * 保存图片到本地
     */
    boolean saveImageToLocal(Long userId, Long imageId);

    /**
     * 批量删除图片（返回详细结果）
     */
    BatchDeleteResultDto batchDeleteImagesWithResult(Long userId, List<Long> imageIds);

    /**
     * 获取图片详细信息
     */
    ImageDetailsDto getImageDetails(Long userId, Long imageId);

    /**
     * 分享图片
     */
    ImageShareInfoDto shareImage(Long userId, Long imageId, String shareType);

    /**
     * 获取图片分享统计
     */
    ImageShareStatsDto getImageShareStats(Long imageId);
}