package com.fishing.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fishing.domain.user.Follow;
import com.fishing.dto.follow.AttentionRequest;
import com.fishing.dto.follow.FansRequest;
import com.fishing.vo.user.UserVo;

/**
 * 用户关注服务接口
 */
public interface IFollowService extends IService<Follow> {

    /**
     * 关注用户
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     */
    void followUser(Long followerId, Long followedId);

    /**
     * 取消关注用户
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     */
    void unfollowUser(Long followerId, Long followedId);

    /**
     * 检查是否关注某用户
     * @param followerId 关注者ID
     * @param followedId 被关注者ID
     * @return 是否关注
     */
    boolean isFollowing(Long followerId, Long followedId);

    /**
     * 获取粉丝列表
     * @param request 粉丝列表请求
     * @return 粉丝列表分页响应
     */
    Page<UserVo> getFansList(FansRequest request);

    /**
     * 获取关注列表
     * @param request 关注列表请求
     * @return 关注列表分页响应
     */
    Page<UserVo> getFollowingList(AttentionRequest request);

    /**
     * 获取用户粉丝数量
     * @param userId 用户ID
     * @return 粉丝数量
     */
    int getFollowerCount(Long userId);

    /**
     * 获取用户关注数量
     * @param userId 用户ID
     * @return 关注数量
     */
    int getFollowingCount(Long userId);
}