package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fishing.datasource.annotation.DataSource;
import com.fishing.datasource.enums.DataSourceType;
import com.fishing.domain.user.Follow;
import com.fishing.dto.follow.AttentionRequest;
import com.fishing.dto.follow.FansRequest;
import com.fishing.mapper.FollowMapper;
import com.fishing.service.IFollowService;
import com.fishing.service.IUserInfoService;
import com.fishing.service.event.UserFollowedEvent;
import com.fishing.service.event.UserUnfollowedEvent;
import com.fishing.vo.user.UserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户关注服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DataSource(DataSourceType.FISHING_BIZ)
public class FollowServiceImpl extends ServiceImpl<FollowMapper, Follow> implements IFollowService {

    private final IUserInfoService userInfoService;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    @Transactional
    public void followUser(Long followerId, Long followedId) {
        if (followerId.equals(followedId)) {
            throw new IllegalArgumentException("不能关注自己");
        }

        // 检查是否已经关注
        if (isFollowing(followerId, followedId)) {
            log.warn("用户 {} 已经关注了用户 {}", followerId, followedId);
            return;
        }

        // 创建关注关系
        Follow follow = new Follow();
        follow.setFollowerId(followerId);
        follow.setFollowedId(followedId);
        follow.setCreatedAt(LocalDateTime.now());

        save(follow);
        log.info("用户 {} 关注了用户 {}", followerId, followedId);
        
        // 发布关注事件，更新统计缓存
        eventPublisher.publishEvent(new UserFollowedEvent(followerId, followedId));
    }

    @Override
    @Transactional
    public void unfollowUser(Long followerId, Long followedId) {
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Follow::getFollowerId, followerId)
               .eq(Follow::getFollowedId, followedId);

        boolean removed = remove(wrapper);
        if (removed) {
            log.info("用户 {} 取消关注了用户 {}", followerId, followedId);
            
            // 发布取消关注事件，更新统计缓存
            eventPublisher.publishEvent(new UserUnfollowedEvent(followerId, followedId));
        } else {
            log.warn("用户 {} 没有关注用户 {}，无法取消关注", followerId, followedId);
        }
    }

    @Override
    public boolean isFollowing(Long followerId, Long followedId) {
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Follow::getFollowerId, followerId)
               .eq(Follow::getFollowedId, followedId);
        return count(wrapper) > 0;
    }

    @Override
    public Page<UserVo> getFansList(FansRequest request) {
        Long userId = request.getUserId();
        
        // 使用 Lambda 查询获取粉丝列表（查询关注了该用户的人）
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Follow::getFollowedId, userId)
               .orderByDesc(Follow::getCreatedAt);
        
        // 分页查询
        Page<Follow> page = new Page<>(request.getPageNum() == 0 ? 1 : request.getPageNum(), request.getPageSize()); // MyBatis Plus 使用 1-based 分页，客户端0-based
        Page<Follow> followPage = page(page, wrapper);
        
        // 获取粉丝用户ID列表
        List<Long> followerIds = followPage.getRecords().stream()
                .map(Follow::getFollowerId)
                .collect(Collectors.toList());
        
        // 获取粉丝用户信息
        List<UserVo> followers = userInfoService.getBy(followerIds);

        // 构建 Page<UserVo> 响应
        Page<UserVo> result = new Page<>(followPage.getCurrent(), followPage.getSize());
        result.setTotal(followPage.getTotal());
        result.setRecords(followers);

        return result;
    }

    @Override
    public Page<UserVo> getFollowingList(AttentionRequest request) {
        Long userId = request.getUserId();
        log.info("获取用户 {} 的关注列表", userId);
        
        // 使用 Lambda 查询获取关注列表（查询该用户关注的人）
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Follow::getFollowerId, userId)
               .orderByDesc(Follow::getCreatedAt);
        
        // 分页查询
        Page<Follow> page = new Page<>(request.getPageNum() == 0 ? 1 : request.getPageNum(), request.getPageSize()); // MyBatis Plus 使用 1-based 分页，客户端0-based
        Page<Follow> followPage = page(page, wrapper);
        log.info("查询到 {} 条关注记录", followPage.getRecords().size());
        
        // 获取关注的用户ID列表
        List<Long> followingIds = followPage.getRecords().stream()
                .map(Follow::getFollowedId)
                .collect(Collectors.toList());
        log.info("关注的用户ID列表: {}", followingIds);
        
        // 获取关注的用户信息
        List<UserVo> following = userInfoService.getBy(followingIds);
        log.info("查询到 {} 个用户信息", following.size());

        // 构建 Page<UserVo> 响应
        Page<UserVo> result = new Page<>(followPage.getCurrent(), followPage.getSize());
        result.setTotal(followPage.getTotal());
        result.setRecords(following);

        return result;
    }

    @Override
    public int getFollowerCount(Long userId) {
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Follow::getFollowedId, userId);
        return Math.toIntExact(count(wrapper));
    }

    @Override
    public int getFollowingCount(Long userId) {
        LambdaQueryWrapper<Follow> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Follow::getFollowerId, userId);
        return Math.toIntExact(count(wrapper));
    }
}