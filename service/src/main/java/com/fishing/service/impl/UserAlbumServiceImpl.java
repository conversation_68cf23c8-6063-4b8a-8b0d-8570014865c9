package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.domain.UserAlbum;
import com.fishing.dto.album.*;
import com.fishing.mapper.UserAlbumMapper;
import com.fishing.service.UserAlbumService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户相册服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAlbumServiceImpl implements UserAlbumService {

    private final UserAlbumMapper userAlbumMapper;

    @Override
    public IPage<UserAlbum> getUserAlbumPage(Long userId, Integer page, Integer pageSize) {
        Page<UserAlbum> pageParam = new Page<>(page, pageSize);
        return userAlbumMapper.selectUserAlbumPage(pageParam, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addToAlbum(Long userId, String imageUrl, String description, String tags) {
        UserAlbum album = new UserAlbum();
        album.setUserId(userId);
        album.setImageUrl(imageUrl);
        album.setDescription(description);
        album.setTags(tags);
        album.setIsPublic(1); // 默认公开
        album.setCreateTime(LocalDateTime.now());
        album.setUpdateTime(LocalDateTime.now());

        return userAlbumMapper.insert(album) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFromAlbum(Long userId, Long imageId) {
        LambdaQueryWrapper<UserAlbum> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAlbum::getUserId, userId)
               .eq(UserAlbum::getId, imageId);

        return userAlbumMapper.delete(wrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAlbumImage(Long userId, Long imageId, String description, String tags) {
        LambdaUpdateWrapper<UserAlbum> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(UserAlbum::getUserId, userId)
               .eq(UserAlbum::getId, imageId)
               .set(UserAlbum::getDescription, description)
               .set(UserAlbum::getTags, tags)
               .set(UserAlbum::getUpdateTime, LocalDateTime.now());

        return userAlbumMapper.update(null, wrapper) > 0;
    }

    @Override
    public AlbumStatsDto getAlbumStats(Long userId) {
        // 图片总数
        Long totalCount = userAlbumMapper.countByUserId(userId);
        
        // 占用空间
        Long totalSize = userAlbumMapper.getTotalFileSize(userId);
        
        // 公开图片数量
        LambdaQueryWrapper<UserAlbum> publicWrapper = new LambdaQueryWrapper<>();
        publicWrapper.eq(UserAlbum::getUserId, userId)
                    .eq(UserAlbum::getIsPublic, 1);
        Long publicCount = userAlbumMapper.selectCount(publicWrapper);
        
        return AlbumStatsDto.builder()
            .totalCount(totalCount)
            .totalSize(totalSize != null ? totalSize : 0)
            .publicCount(publicCount)
            .privateCount(totalCount - publicCount)
            .build();
    }

    @Override
    public List<UserAlbum> getRecentImages(Long userId, Integer limit) {
        return userAlbumMapper.selectRecentImages(userId, limit);
    }

    @Override
    public List<UserAlbum> searchByTags(Long userId, String tags) {
        return userAlbumMapper.selectByTags(userId, tags);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateImagePrivacy(Long userId, Long imageId, Integer isPublic) {
        LambdaUpdateWrapper<UserAlbum> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(UserAlbum::getUserId, userId)
               .eq(UserAlbum::getId, imageId)
               .set(UserAlbum::getIsPublic, isPublic)
               .set(UserAlbum::getUpdateTime, LocalDateTime.now());

        return userAlbumMapper.update(null, wrapper) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteImages(Long userId, List<Long> imageIds) {
        if (imageIds == null || imageIds.isEmpty()) {
            return true;
        }

        LambdaQueryWrapper<UserAlbum> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAlbum::getUserId, userId)
               .in(UserAlbum::getId, imageIds);

        return userAlbumMapper.delete(wrapper) > 0;
    }

    @Override
    public Long getTotalFileSize(Long userId) {
        return userAlbumMapper.getTotalFileSize(userId);
    }

    @Override
    public boolean saveImageToLocal(Long userId, Long imageId) {
        try {
            // 验证用户权限
            LambdaQueryWrapper<UserAlbum> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserAlbum::getUserId, userId)
                   .eq(UserAlbum::getId, imageId);
            
            UserAlbum album = userAlbumMapper.selectOne(wrapper);
            if (album == null) {
                log.warn("图片不存在或无权限: userId={}, imageId={}", userId, imageId);
                return false;
            }
            
            // TODO: 实现实际的图片保存到本地逻辑
            // 1. 从OSS下载图片
            // 2. 保存到用户设备本地存储
            // 3. 更新保存状态
            
            log.info("保存图片到本地: userId={}, imageId={}, imageUrl={}", 
                    userId, imageId, album.getImageUrl());
            
            return true;
        } catch (Exception e) {
            log.error("保存图片到本地失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchDeleteResultDto batchDeleteImagesWithResult(Long userId, List<Long> imageIds) {
        if (imageIds == null || imageIds.isEmpty()) {
            return BatchDeleteResultDto.builder()
                .success(true)
                .requestedCount(0)
                .existingCount(0)
                .deletedCount(0)
                .failedCount(0)
                .message("没有要删除的图片")
                .failedImageIds(new ArrayList<>())
                .build();
        }
        
        try {
            // 查询要删除的图片
            LambdaQueryWrapper<UserAlbum> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserAlbum::getUserId, userId)
                       .in(UserAlbum::getId, imageIds);
            
            List<UserAlbum> albumsToDelete = userAlbumMapper.selectList(queryWrapper);
            int existingCount = albumsToDelete.size();
            
            // 删除图片
            LambdaQueryWrapper<UserAlbum> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(UserAlbum::getUserId, userId)
                        .in(UserAlbum::getId, imageIds);
            
            int deletedRows = userAlbumMapper.delete(deleteWrapper);
            
            // TODO: 删除OSS上的实际文件
            for (UserAlbum album : albumsToDelete) {
                log.info("删除相册图片: userId={}, imageId={}, imageUrl={}", 
                        userId, album.getId(), album.getImageUrl());
                // 实际项目中应该调用OSS删除API
            }
            
            // 计算失败的图片ID
            List<Long> failedImageIds = new ArrayList<>();
            if (deletedRows < imageIds.size()) {
                List<Long> deletedIds = albumsToDelete.stream()
                    .map(UserAlbum::getId)
                    .toList();
                failedImageIds = imageIds.stream()
                    .filter(id -> !deletedIds.contains(id))
                    .toList();
            }
            
            return BatchDeleteResultDto.builder()
                .success(true)
                .requestedCount(imageIds.size())
                .existingCount(existingCount)
                .deletedCount(deletedRows)
                .failedCount(imageIds.size() - deletedRows)
                .message(String.format("成功删除 %d 张图片", deletedRows))
                .failedImageIds(failedImageIds)
                .build();
            
        } catch (Exception e) {
            log.error("批量删除图片失败", e);
            return BatchDeleteResultDto.builder()
                .success(false)
                .requestedCount(imageIds.size())
                .existingCount(0)
                .deletedCount(0)
                .failedCount(imageIds.size())
                .message("删除失败: " + e.getMessage())
                .failedImageIds(imageIds)
                .build();
        }
    }

    @Override
    public ImageDetailsDto getImageDetails(Long userId, Long imageId) {
        try {
            LambdaQueryWrapper<UserAlbum> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserAlbum::getUserId, userId)
                   .eq(UserAlbum::getId, imageId);
            
            UserAlbum album = userAlbumMapper.selectOne(wrapper);
            if (album == null) {
                return null;
            }
            
            return ImageDetailsDto.builder()
                .id(album.getId())
                .imageUrl(album.getImageUrl())
                .description(album.getDescription())
                .tags(album.getTags())
                .isPublic(album.getIsPublic() == 1)
                .createTime(album.getCreateTime().toString())
                .updateTime(album.getUpdateTime().toString())
                .fileSize(calculateImageFileSize(album.getImageUrl()))
                .dimensions(getImageDimensions(album.getImageUrl()))
                .format(getImageFormat(album.getImageUrl()))
                .shareCount(getShareCount(imageId))
                .viewCount(getViewCount(imageId))
                .build();
        } catch (Exception e) {
            log.error("获取图片详情失败", e);
            return null;
        }
    }

    @Override
    public ImageShareInfoDto shareImage(Long userId, Long imageId, String shareType) {
        try {
            // 验证图片存在性和权限
            LambdaQueryWrapper<UserAlbum> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserAlbum::getUserId, userId)
                   .eq(UserAlbum::getId, imageId);
            
            UserAlbum album = userAlbumMapper.selectOne(wrapper);
            if (album == null) {
                return ImageShareInfoDto.builder()
                    .success(false)
                    .message("图片不存在或无权限")
                    .build();
            }
            
            // 生成分享信息
            ImageShareInfoDto.ImageShareInfoDtoBuilder builder = ImageShareInfoDto.builder()
                .success(true)
                .imageId(imageId)
                .imageUrl(album.getImageUrl())
                .shareUrl(generateShareUrl(imageId))
                .shareText(generateShareText(album))
                .qrCodeUrl(generateQRCodeUrl(imageId));
            
            // 根据分享类型添加特定信息
            switch (shareType.toLowerCase()) {
                case "wechat":
                    builder.title("分享了一张精彩的钓鱼照片")
                           .miniProgramPath("/pages/album/detail?id=" + imageId);
                    break;
                case "weibo":
                    builder.hashtags("#钓鱼之旅 #钓鱼照片");
                    break;
                case "qq":
                    builder.title("钓鱼之旅 - 精彩照片分享");
                    break;
            }
            
            // TODO: 记录分享行为到数据库
            recordShareAction(userId, imageId, shareType);
            
            log.info("生成图片分享信息: userId={}, imageId={}, shareType={}", 
                    userId, imageId, shareType);
            
            return builder.build();
        } catch (Exception e) {
            log.error("生成分享信息失败", e);
            return ImageShareInfoDto.builder()
                .success(false)
                .message("生成分享信息失败")
                .build();
        }
    }

    @Override
    public ImageShareStatsDto getImageShareStats(Long imageId) {
        try {
            // TODO: 从数据库获取实际的分享统计数据
            // 这里提供模拟数据
            return ImageShareStatsDto.builder()
                .totalShares((int) Math.round(Math.random() * 100))
                .wechatShares((int) Math.round(Math.random() * 50))
                .weiboShares((int) Math.round(Math.random() * 30))
                .qqShares((int) Math.round(Math.random() * 20))
                .lastShareTime(LocalDateTime.now().minusHours(2).toString())
                .shareUsers((int) Math.round(Math.random() * 80))
                .build();
        } catch (Exception e) {
            log.error("获取图片分享统计失败", e);
            return ImageShareStatsDto.builder()
                .totalShares(0)
                .wechatShares(0)
                .weiboShares(0)
                .qqShares(0)
                .shareUsers(0)
                .build();
        }
    }

    // 辅助方法
    
    private String calculateImageFileSize(String imageUrl) {
        // TODO: 实现实际的文件大小计算
        long sizeBytes = (long) (Math.random() * 5 * 1024 * 1024); // 0-5MB
        return formatFileSize(sizeBytes);
    }
    
    private String getImageDimensions(String imageUrl) {
        // TODO: 实现实际的图片尺寸获取
        int width = 800 + (int) (Math.random() * 1200);
        int height = 600 + (int) (Math.random() * 1200);
        return width + "x" + height;
    }
    
    private String getImageFormat(String imageUrl) {
        // 从URL推断图片格式
        if (imageUrl.toLowerCase().contains(".jpg") || imageUrl.toLowerCase().contains(".jpeg")) {
            return "JPEG";
        } else if (imageUrl.toLowerCase().contains(".png")) {
            return "PNG";
        } else if (imageUrl.toLowerCase().contains(".webp")) {
            return "WebP";
        }
        return "Unknown";
    }
    
    private int getShareCount(Long imageId) {
        // TODO: 从数据库获取实际的分享次数
        return (int) (Math.random() * 50);
    }
    
    private int getViewCount(Long imageId) {
        // TODO: 从数据库获取实际的查看次数
        return (int) (Math.random() * 200);
    }
    
    private String generateShareUrl(Long imageId) {
        return "https://www.fishingtrip.com/share/image/" + imageId;
    }
    
    private String generateShareText(UserAlbum album) {
        String description = album.getDescription();
        if (description != null && !description.trim().isEmpty()) {
            return "分享了一张精彩的钓鱼照片：" + description.substring(0, Math.min(50, description.length()));
        }
        return "分享了一张精彩的钓鱼照片";
    }
    
    private String generateQRCodeUrl(Long imageId) {
        return "https://www.fishingtrip.com/qr/image/" + imageId + ".png";
    }
    
    private void recordShareAction(Long userId, Long imageId, String shareType) {
        // TODO: 记录分享行为到数据库
        log.info("记录分享行为: userId={}, imageId={}, shareType={}, time={}", 
                userId, imageId, shareType, LocalDateTime.now());
    }
    
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + "B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1fKB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1fMB", bytes / (1024.0 * 1024));
        } else {
            return String.format("%.1fGB", bytes / (1024.0 * 1024 * 1024));
        }
    }
}