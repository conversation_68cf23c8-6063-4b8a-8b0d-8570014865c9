package com.fishing.service.impl;

import com.fishing.dto.help.*;
import com.fishing.service.HelpCenterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 帮助中心服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HelpCenterServiceImpl implements HelpCenterService {

    // 内存缓存，实际项目中应该使用Redis或数据库
    private final Map<String, Integer> questionViewCounts = new ConcurrentHashMap<>();
    private final Map<String, Map<Long, Boolean>> questionHelpfulMap = new ConcurrentHashMap<>();
    
    @Override
    public List<HelpCategoryDto> getHelpCategories() {
        return List.of(
            HelpCategoryDto.builder()
                .id("account")
                .title("账号相关")
                .icon("person_outline")
                .description("注册、登录、密码等")
                .questionCount(4)
                .build(),
            HelpCategoryDto.builder()
                .id("fishing_spot")
                .title("钓点功能")
                .icon("location_on_outlined")
                .description("添加、收藏、分享钓点")
                .questionCount(4)
                .build(),
            HelpCategoryDto.builder()
                .id("community")
                .title("社区功能")
                .icon("forum_outlined")
                .description("动态、关注、互动")
                .questionCount(4)
                .build(),
            HelpCategoryDto.builder()
                .id("other")
                .title("其他问题")
                .icon("help_outline")
                .description("常见问题、联系客服")
                .questionCount(4)
                .build()
        );
    }

    @Override
    public List<HelpQuestionDto> getCategoryQuestions(String categoryId) {
        return switch (categoryId) {
            case "account" -> getAccountQuestions();
            case "fishing_spot" -> getFishingSpotQuestions();
            case "community" -> getCommunityQuestions();
            case "other" -> getOtherQuestions();
            default -> List.of();
        };
    }

    @Override
    public List<HelpQuestionDto> searchHelp(String keyword) {
        // 简单的关键词搜索实现
        // 实际项目中应该使用Elasticsearch或其他搜索引擎
        List<HelpQuestionDto> allQuestions = List.of();
        
        // 组合所有分类的问题
        allQuestions = new java.util.ArrayList<>();
        allQuestions.addAll(getAccountQuestions());
        allQuestions.addAll(getFishingSpotQuestions());
        allQuestions.addAll(getCommunityQuestions());
        allQuestions.addAll(getOtherQuestions());
        
        return allQuestions.stream()
            .filter(question -> {
                String questionText = question.getQuestion();
                String answerText = question.getAnswer();
                return questionText.toLowerCase().contains(keyword.toLowerCase()) ||
                       answerText.toLowerCase().contains(keyword.toLowerCase());
            })
            .map(question -> {
                // 添加分类信息
                String categoryId = determineCategoryId(question.getId());
                String categoryTitle = determineCategoryTitle(question.getId());
                return HelpQuestionDto.builder()
                    .id(question.getId())
                    .question(question.getQuestion())
                    .answer(question.getAnswer())
                    .categoryId(categoryId)
                    .categoryTitle(categoryTitle)
                    .build();
            })
            .toList();
    }

    @Override
    public boolean markQuestionHelpful(String questionId, boolean isHelpful, Long userId) {
        try {
            // 实际项目中应该保存到数据库
            questionHelpfulMap.computeIfAbsent(questionId, k -> new ConcurrentHashMap<>())
                .put(userId, isHelpful);
            
            log.info("用户标记问题有用性: questionId={}, userId={}, helpful={}", 
                    questionId, userId, isHelpful);
            return true;
        } catch (Exception e) {
            log.error("标记问题有用性失败", e);
            return false;
        }
    }

    @Override
    public boolean recordQuestionView(String questionId, Long userId) {
        try {
            // 增加查看次数
            questionViewCounts.merge(questionId, 1, Integer::sum);
            
            // 实际项目中应该记录用户的查看历史到数据库
            log.info("记录问题查看: questionId={}, userId={}, totalViews={}", 
                    questionId, userId, questionViewCounts.get(questionId));
            return true;
        } catch (Exception e) {
            log.error("记录问题查看失败", e);
            return false;
        }
    }

    @Override
    public QuestionStatsDto getQuestionStats(String questionId) {
        // 查看次数
        int viewCount = questionViewCounts.getOrDefault(questionId, 0);
        
        // 有用性统计
        Map<Long, Boolean> helpfulData = questionHelpfulMap.getOrDefault(questionId, new java.util.HashMap<>());
        long helpfulCount = helpfulData.values().stream().mapToLong(helpful -> helpful ? 1 : 0).sum();
        long notHelpfulCount = helpfulData.size() - helpfulCount;
        
        return QuestionStatsDto.builder()
            .viewCount(viewCount)
            .helpfulCount(helpfulCount)
            .notHelpfulCount(notHelpfulCount)
            .totalFeedback((long) helpfulData.size())
            .helpfulRate(helpfulData.size() > 0 ? (double) helpfulCount / helpfulData.size() : 0.0)
            .build();
    }

    @Override
    public UserFeedbackStatsDto getUserFeedbackStats(Long userId) {
        // 统计用户的反馈次数
        long totalFeedback = questionHelpfulMap.values().stream()
            .mapToLong(userFeedback -> userFeedback.containsKey(userId) ? 1 : 0)
            .sum();
        
        long helpfulFeedback = questionHelpfulMap.values().stream()
            .mapToLong(userFeedback -> 
                userFeedback.containsKey(userId) && userFeedback.get(userId) ? 1 : 0)
            .sum();
        
        return UserFeedbackStatsDto.builder()
            .totalFeedback(totalFeedback)
            .helpfulFeedback(helpfulFeedback)
            .notHelpfulFeedback(totalFeedback - helpfulFeedback)
            .lastFeedbackTime(LocalDateTime.now().toString())
            .build();
    }

    private List<HelpQuestionDto> getAccountQuestions() {
        return List.of(
            HelpQuestionDto.builder()
                .id("account_1")
                .question("如何注册账号？")
                .answer("您可以通过手机号码注册账号。在登录页面点击\"注册\"，输入手机号码并获取验证码，设置密码后即可完成注册。")
                .build(),
            HelpQuestionDto.builder()
                .id("account_2")
                .question("忘记密码怎么办？")
                .answer("在登录页面点击\"忘记密码\"，输入您的手机号码，获取验证码后即可重置密码。")
                .build(),
            HelpQuestionDto.builder()
                .id("account_3")
                .question("如何修改个人信息？")
                .answer("进入\"我的\"页面，点击\"设置\"，然后选择\"编辑资料\"即可修改您的个人信息。")
                .build(),
            HelpQuestionDto.builder()
                .id("account_4")
                .question("如何注销账号？")
                .answer("进入\"设置\"-\"账号与安全\"-\"注销账号\"，按照提示操作即可。注意：注销后所有数据将无法恢复。")
                .build()
        );
    }

    private List<HelpQuestionDto> getFishingSpotQuestions() {
        return List.of(
            HelpQuestionDto.builder()
                .id("spot_1")
                .question("如何添加钓点？")
                .answer("在钓点页面点击\"+\"按钮，选择位置并填写钓点信息，包括名称、描述、鱼种等信息。")
                .build(),
            HelpQuestionDto.builder()
                .id("spot_2")
                .question("如何收藏钓点？")
                .answer("在钓点详情页面点击收藏按钮（心形图标）即可收藏该钓点。")
                .build(),
            HelpQuestionDto.builder()
                .id("spot_3")
                .question("钓点信息不准确怎么办？")
                .answer("您可以在钓点详情页面点击\"反馈\"按钮，告诉我们具体的问题，我们会及时处理。")
                .build(),
            HelpQuestionDto.builder()
                .id("spot_4")
                .question("如何分享钓点给朋友？")
                .answer("在钓点详情页面点击分享按钮，选择分享方式即可将钓点分享给朋友。")
                .build()
        );
    }

    private List<HelpQuestionDto> getCommunityQuestions() {
        return List.of(
            HelpQuestionDto.builder()
                .id("community_1")
                .question("如何发布动态？")
                .answer("在社区页面点击\"+\"按钮，选择动态类型，添加图片和文字描述，然后发布即可。")
                .build(),
            HelpQuestionDto.builder()
                .id("community_2")
                .question("如何关注其他用户？")
                .answer("在用户主页点击\"关注\"按钮即可关注该用户，关注后可以在动态中看到他们的最新内容。")
                .build(),
            HelpQuestionDto.builder()
                .id("community_3")
                .question("如何举报不当内容？")
                .answer("在动态详情页面点击右上角的\"...\"按钮，选择\"举报\"，选择举报原因并提交。")
                .build(),
            HelpQuestionDto.builder()
                .id("community_4")
                .question("评论被删除了是什么原因？")
                .answer("评论可能因违反社区规范被系统或管理员删除。请确保您的评论内容健康、友善。")
                .build()
        );
    }

    private List<HelpQuestionDto> getOtherQuestions() {
        return List.of(
            HelpQuestionDto.builder()
                .id("other_1")
                .question("应用闪退怎么办？")
                .answer("请尝试重启应用或重启手机。如果问题持续存在，请通过\"反馈与建议\"联系我们。")
                .build(),
            HelpQuestionDto.builder()
                .id("other_2")
                .question("如何联系客服？")
                .answer("您可以通过\"我的\"页面中的\"反馈与建议\"功能联系我们，我们会及时回复。")
                .build(),
            HelpQuestionDto.builder()
                .id("other_3")
                .question("应用支持哪些设备？")
                .answer("应用支持iOS 12.0以上和Android 6.0以上的设备。")
                .build(),
            HelpQuestionDto.builder()
                .id("other_4")
                .question("如何清理缓存？")
                .answer("进入\"设置\"-\"通用设置\"-\"清理缓存\"，点击确认即可清理应用缓存。")
                .build()
        );
    }

    private String determineCategoryId(String questionId) {
        if (questionId.startsWith("account_")) return "account";
        if (questionId.startsWith("spot_")) return "fishing_spot";
        if (questionId.startsWith("community_")) return "community";
        if (questionId.startsWith("other_")) return "other";
        return "unknown";
    }

    private String determineCategoryTitle(String questionId) {
        return switch (determineCategoryId(questionId)) {
            case "account" -> "账号相关";
            case "fishing_spot" -> "钓点功能";
            case "community" -> "社区功能";
            case "other" -> "其他问题";
            default -> "未知分类";
        };
    }
}