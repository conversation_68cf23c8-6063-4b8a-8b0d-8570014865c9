package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fishing.common.redis.UserStatsRedisKeys;
import com.fishing.datasource.annotation.DataSource;
import com.fishing.datasource.enums.DataSourceType;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.user.Follow;
import com.fishing.dto.user.UserStatsDto;
import com.fishing.service.IFollowService;
import com.fishing.service.MomentService;
import com.fishing.service.IUserStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;

/**
 * 用户统计服务实现（User Module）
 * 负责获取用户统计数据和实时更新Redis缓存
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DataSource(DataSourceType.FISHING_BIZ)
public class UserStatsServiceImpl implements IUserStatsService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final IFollowService followService;
    private final MomentService momentService;

    @Override
    public UserStatsDto getUserStats(Long userId) {
        String key = UserStatsRedisKeys.getUserStatsKey(userId);
        Map<Object, Object> stats = redisTemplate.opsForHash().entries(key);
        
        if (stats.isEmpty()) {
            log.info("缓存未命中，从数据库加载用户 {} 的统计数据", userId);
            return loadStatsFromDatabase(userId);
        }
        
        log.debug("从缓存获取用户 {} 的统计数据", userId);
        return convertToDto(userId, stats);
    }

    @Override
    public void incrementFollowerCount(Long userId, int delta) {
        String key = UserStatsRedisKeys.getUserStatsKey(userId);
        redisTemplate.opsForHash().increment(key, UserStatsRedisKeys.FOLLOWERS_COUNT, delta);
        redisTemplate.opsForHash().put(key, UserStatsRedisKeys.LAST_UPDATED, System.currentTimeMillis());
        redisTemplate.expire(key, Duration.ofDays(UserStatsRedisKeys.DEFAULT_EXPIRE_DAYS));
        
        log.debug("用户 {} 粉丝数增加 {}", userId, delta);
    }

    @Override
    public void incrementFollowingCount(Long userId, int delta) {
        String key = UserStatsRedisKeys.getUserStatsKey(userId);
        redisTemplate.opsForHash().increment(key, UserStatsRedisKeys.FOLLOWING_COUNT, delta);
        redisTemplate.opsForHash().put(key, UserStatsRedisKeys.LAST_UPDATED, System.currentTimeMillis());
        redisTemplate.expire(key, Duration.ofDays(UserStatsRedisKeys.DEFAULT_EXPIRE_DAYS));
        
        log.debug("用户 {} 关注数增加 {}", userId, delta);
    }

    @Override
    public void incrementMomentCount(Long userId, int delta) {
        String key = UserStatsRedisKeys.getUserStatsKey(userId);
        redisTemplate.opsForHash().increment(key, UserStatsRedisKeys.MOMENTS_COUNT, delta);
        redisTemplate.opsForHash().put(key, UserStatsRedisKeys.LAST_UPDATED, System.currentTimeMillis());
        redisTemplate.expire(key, Duration.ofDays(UserStatsRedisKeys.DEFAULT_EXPIRE_DAYS));
        
        log.debug("用户 {} 动态数增加 {}", userId, delta);
    }

    @Override
    public void incrementSpotCount(Long userId, int delta) {
        String key = UserStatsRedisKeys.getUserStatsKey(userId);
        redisTemplate.opsForHash().increment(key, UserStatsRedisKeys.SPOTS_COUNT, delta);
        redisTemplate.opsForHash().put(key, UserStatsRedisKeys.LAST_UPDATED, System.currentTimeMillis());
        redisTemplate.expire(key, Duration.ofDays(UserStatsRedisKeys.DEFAULT_EXPIRE_DAYS));
        
        log.debug("用户 {} 钓点数增加 {}", userId, delta);
    }

    /**
     * 从数据库加载统计数据并写入Redis缓存
     */
    private UserStatsDto loadStatsFromDatabase(Long userId) {
        try {
            // 查询数据库获取各项统计
            int followersCount = followService.getFollowerCount(userId);
            int followingCount = followService.getFollowingCount(userId);
            int momentsCount = momentService.getUserMomentCount(userId);
            int spotsCount = getSpotsCountFromDatabase(userId);
            
            UserStatsDto stats = UserStatsDto.builder()
                    .userId(userId)
                    .followersCount(followersCount)
                    .followingCount(followingCount)
                    .momentsCount(momentsCount)
                    .spotsCount(spotsCount)
                    .lastUpdated(System.currentTimeMillis())
                    .build();
            
            // 写入Redis缓存
            writeStatsToCache(stats);
            
            log.info("用户 {} 统计数据已从数据库加载并缓存", userId);
            return stats;
            
        } catch (Exception e) {
            log.error("加载用户 {} 统计数据失败", userId, e);
            return UserStatsDto.empty(userId);
        }
    }

    /**
     * 从数据库查询用户钓点数量
     */
    private int getSpotsCountFromDatabase(Long userId) {
        // TODO: 实现钓点数量查询逻辑
        // 这里需要调用钓点服务查询用户创建的钓点数量
        return 0;
    }

    /**
     * 将统计数据写入Redis缓存
     */
    private void writeStatsToCache(UserStatsDto stats) {
        String key = UserStatsRedisKeys.getUserStatsKey(stats.getUserId());
        
        Map<String, Object> cacheData = Map.of(
                UserStatsRedisKeys.FOLLOWERS_COUNT, stats.getFollowersCount(),
                UserStatsRedisKeys.FOLLOWING_COUNT, stats.getFollowingCount(),
                UserStatsRedisKeys.MOMENTS_COUNT, stats.getMomentsCount(),
                UserStatsRedisKeys.SPOTS_COUNT, stats.getSpotsCount(),
                UserStatsRedisKeys.LAST_UPDATED, stats.getLastUpdated()
        );
        
        redisTemplate.opsForHash().putAll(key, cacheData);
        redisTemplate.expire(key, Duration.ofDays(UserStatsRedisKeys.DEFAULT_EXPIRE_DAYS));
    }

    /**
     * 将Redis数据转换为DTO
     */
    private UserStatsDto convertToDto(Long userId, Map<Object, Object> stats) {
        return UserStatsDto.builder()
                .userId(userId)
                .followersCount(getIntValue(stats, UserStatsRedisKeys.FOLLOWERS_COUNT))
                .followingCount(getIntValue(stats, UserStatsRedisKeys.FOLLOWING_COUNT))
                .momentsCount(getIntValue(stats, UserStatsRedisKeys.MOMENTS_COUNT))
                .spotsCount(getIntValue(stats, UserStatsRedisKeys.SPOTS_COUNT))
                .lastUpdated(getLongValue(stats, UserStatsRedisKeys.LAST_UPDATED))
                .build();
    }

    private Integer getIntValue(Map<Object, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            return Integer.parseInt((String) value);
        }
        return 0;
    }

    private Long getLongValue(Map<Object, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof String) {
            return Long.parseLong((String) value);
        }
        return System.currentTimeMillis();
    }
}