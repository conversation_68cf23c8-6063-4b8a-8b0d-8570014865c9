package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fishing.common.result.PageResult;
import com.fishing.datasource.annotation.DataSource;
import com.fishing.datasource.enums.DataSourceType;
import com.fishing.domain.User;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.spot.*;
import com.fishing.dto.spot.QuerySpotDto;
import com.fishing.mapper.*;
import com.fishing.service.BusinessConfigService;
import com.fishing.service.IFishingSpotQueryService;
import com.fishing.service.SpotFishTypesService;
import com.fishing.vo.spot.*;
import com.fishing.vo.user.UserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.beans.BeanUtils.copyProperties;

@Service
@RequiredArgsConstructor
@Slf4j
@DataSource(DataSourceType.FISHING_BIZ)
public class FishingSpotQueryService extends ServiceImpl<FishingSpotMapper, FishingSpot> implements IFishingSpotQueryService {

    private final SpotFishTypesMapper spotFishTypesMapper;
    private final SpotImageMapper spotImageMapper;
    private final SpotFishTypesService spotFishTypesService;
    private final FishTypeMapper fishTypeMapper;
    private final SpotPriceMapper spotPriceMapper;
    private final SpotFacilityMapper spotFacilityMapper;
    private final FacilityMapper facilityMapper;
    private final SpotCheckinMapper spotCheckinMapper;
    private final SpotFavoriteMapper spotFavoriteMapper;
    private final UserMapper userMapper;
    private final MomentMapper momentMapper;
    private final BusinessConfigService businessConfigService;


    @Override
    public List<FishingSpot> findNearbySpots(BigDecimal latitude, BigDecimal longitude, Double radius) {
        return baseMapper.selectNearbySpots(latitude, longitude, radius);
    }

    @Override
    public IPage<SpotSummaryVO> findFishingSpots(QuerySpotDto querySpotDto) {
        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();
        if (querySpotDto != null) {
            if (StringUtils.hasText(querySpotDto.getFilterType())) {
                switch (querySpotDto.getFilterType()) {
                    case "官方认证":
                        queryWrapper.eq(FishingSpot::getIsOfficial, true);
                        break;
                    case "用户推荐":
                        queryWrapper.eq(FishingSpot::getIsOfficial, false);
                        break;
                    case "免费钓场":
                        queryWrapper.eq(FishingSpot::getIsPaid, false);
                        break;
                    case "付费钓场":
                        queryWrapper.eq(FishingSpot::getIsPaid, true);
                        break;
                    default:
                        break;
                }
            }

            // 按鱼类筛选
            if (querySpotDto.getFishTypes() != null && !querySpotDto.getFishTypes().isEmpty()) {
                queryWrapper.in(FishingSpot::getId, spotFishTypesService.getFishPotIdsByFishTypeNames(querySpotDto.getFishTypes()));
            }

            // 按基础设施筛选
            if (querySpotDto.getHasFacilities() != null) {
                queryWrapper.eq(FishingSpot::getHasFacilities, querySpotDto.getHasFacilities());
            }
            queryWrapper.eq(FishingSpot::getStatus, 1); // 只查询正常状态的钓点
        }

        queryWrapper.orderByDesc(FishingSpot::getCreatedAt);

        Page<FishingSpot> page = new Page<>(querySpotDto.getPageNum(), querySpotDto.getPageSize());
        IPage<FishingSpot> spotPage = baseMapper.selectPage(page, queryWrapper);

        List<SpotSummaryVO> spotDetails = convertToSpotSummaryVO(spotPage.getRecords());

        IPage<SpotSummaryVO> spotDetailVOIPage = new Page<>();
        spotDetailVOIPage.setRecords(spotDetails);
        spotDetailVOIPage.setTotal(spotPage.getTotal());
        spotDetailVOIPage.setCurrent(spotPage.getCurrent());
        spotDetailVOIPage.setSize(spotPage.getSize());
        return spotDetailVOIPage;
    }


    @Override
    public Optional<SpotDetailVO> findById(Long id) {
        FishingSpot spot = baseMapper.selectById(id);
        if (spot == null) {
            return Optional.empty();
        }

        List<Long> fishTypeIds = getFishTypeIds(id);
        List<FishType> fishTypes = new ArrayList<>();
        if (!fishTypeIds.isEmpty()) {
            fishTypes = fishTypeMapper.selectBatchIds(fishTypeIds);
        }

        List<String> imageUrls = getSpotImages(id);
        List<String> certificationDocuments = getSpotCertificationDocuments(id);
        List<SpotPriceVO> prices = getPricesBySpotId(id);
        List<SpotFacilityVO> facilities = getSpotFacilities(id);

        SpotDetailVO spotDetailVO = new SpotDetailVO();
        copyProperties(spot, spotDetailVO);

        List<FishTypeVO> fishTypeVOs = new ArrayList<>();
        for (FishType fishType : fishTypes) {
            FishTypeVO fishTypeVO = new FishTypeVO();
            copyProperties(fishType, fishTypeVO);
            fishTypeVOs.add(fishTypeVO);
        }
        spotDetailVO.setFishTypeList(fishTypeVOs);
        spotDetailVO.setImages(imageUrls);
        spotDetailVO.setCertificationDocuments(certificationDocuments);
        spotDetailVO.setPrices(prices);
        spotDetailVO.setFacilities(facilities);

        // 查询并设置创建者信息
        if (spot.getCreatedBy() != null) {
            User creator = userMapper.selectById(spot.getCreatedBy());
            if (creator != null) {
                UserVo creatorVo = new UserVo();
                copyProperties(creator, creatorVo);
                spotDetailVO.setCreator(creatorVo);
            }
        }

        return Optional.of(spotDetailVO);
    }

    @Override
    public List<SpotPriceVO> getPricesBySpotId(Long spotId) {
        if (spotId == null) {
            return new ArrayList<>();
        }

        // 获取价格数据
        List<SpotPrice> prices = getPricesBySpotId_Raw(spotId);
        if (prices.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有相关的鱼类ID
        List<Long> fishTypeIds = prices.stream()
                .map(SpotPrice::getFishTypeId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 查询鱼类信息
        Map<Long, FishType> fishTypeMap = new HashMap<>();
        if (!fishTypeIds.isEmpty()) {
            List<FishType> fishTypes = fishTypeMapper.selectBatchIds(fishTypeIds);
            fishTypeMap = fishTypes.stream().collect(Collectors.toMap(FishType::getId, ft -> ft));
        }

        // 转换为VO
        List<SpotPriceVO> result = new ArrayList<>();
        for (SpotPrice price : prices) {
            SpotPriceVO vo = new SpotPriceVO();
            copyProperties(price, vo);

            // 设置鱼类信息
            if (price.getFishTypeId() != null) {
                FishType fishType = fishTypeMap.get(price.getFishTypeId());
                if (fishType != null) {
                    FishTypeVO fishTypeVO = new FishTypeVO();
                    copyProperties(fishType, fishTypeVO);
                    vo.setFishType(fishTypeVO);
                }
            }

            result.add(vo);
        }

        return result;
    }

    @Override
    public List<SpotFacilityVO> getSpotFacilities(Long spotId) {
        if (spotId == null) {
            return new ArrayList<>();
        }

        // 查询钓点设施关联表
        LambdaQueryWrapper<SpotFacility> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotFacility::getSpotId, spotId);
        List<SpotFacility> spotFacilities = spotFacilityMapper.selectList(queryWrapper);

        if (spotFacilities.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取设施ID列表
        List<Long> facilityIds = spotFacilities.stream()
                .map(SpotFacility::getFacilityId)
                .collect(Collectors.toList());

        // 查询设施信息
        List<Facility> facilities = facilityMapper.selectBatchIds(facilityIds);

        // 组装设施VO
        Map<Long, Facility> facilityMap = facilities.stream()
                .collect(Collectors.toMap(Facility::getId, f -> f));

        List<SpotFacilityVO> result = new ArrayList<>();
        for (SpotFacility sf : spotFacilities) {
            Facility facility = facilityMap.get(sf.getFacilityId());
            if (facility != null) {
                SpotFacilityVO vo = new SpotFacilityVO();
                vo.setId(facility.getId());
                vo.setName(facility.getName());
                vo.setIcon(facility.getIcon());
                vo.setDescription(facility.getDescription());
                vo.setDetails(sf.getDetails());
                result.add(vo);
            }
        }

        return result;
    }

    @Override
    public PageResult<SpotSummaryVO> getUserRecentCheckins(Long userId, int page, int size) {
        // 查询用户的签到记录（按时间倒序）
        LambdaQueryWrapper<SpotCheckin> checkinQueryWrapper = new LambdaQueryWrapper<>();
        checkinQueryWrapper.eq(SpotCheckin::getUserId, userId)
                .orderByDesc(SpotCheckin::getCheckinTime);

        // 先查询总数
        long total = spotCheckinMapper.selectCount(checkinQueryWrapper);
        
        if (total == 0) {
            return PageResult.<SpotSummaryVO>builder()
                    .records(new ArrayList<>())
                    .total(total)
                    .current((long) page)
                    .size((long) size)
                    .pages((total + size - 1) / size)
                    .build();
        }

        // 手动分页：添加LIMIT和OFFSET
        checkinQueryWrapper.last("LIMIT " + size + " OFFSET " + (page * size));
        List<SpotCheckin> checkins = spotCheckinMapper.selectList(checkinQueryWrapper);

        // 提取钓点ID
        List<Long> spotIds = checkins.stream()
                .map(SpotCheckin::getSpotId)
                .collect(Collectors.toList());

        // 查询钓点基本信息
        List<FishingSpot> fishingSpots = baseMapper.selectBatchIds(spotIds);

        // 转换为VO
        List<SpotSummaryVO> spotSummaryVOs = convertToSpotSummaryVO(fishingSpots);

        return PageResult.<SpotSummaryVO>builder()
                .records(spotSummaryVOs)
                .total(total)
                .current((long) page)
                .size((long) size)
                .pages((total + size - 1) / size)
                .build();
    }

    @Override
    public List<SpotSummaryVO> getUserFavorites(Long userId, int page, int size) {
        // 查询用户的收藏记录
        Page<SpotFavorite> favoritePage = new Page<>(page, size);

        LambdaQueryWrapper<SpotFavorite> favoriteQueryWrapper = new LambdaQueryWrapper<>();
        favoriteQueryWrapper.eq(SpotFavorite::getUserId, userId)
                .orderByDesc(SpotFavorite::getCreatedAt);

        Page<SpotFavorite> favoriteResult = spotFavoriteMapper.selectPage(favoritePage, favoriteQueryWrapper);

        if (favoriteResult.getRecords().isEmpty()) {
            return new ArrayList<>();
        }

        // 提取钓点ID
        List<Long> spotIds = favoriteResult.getRecords().stream()
                .map(SpotFavorite::getSpotId)
                .collect(Collectors.toList());

        // 查询钓点基本信息
        List<FishingSpot> fishingSpots = baseMapper.selectBatchIds(spotIds);

        // 转换为VO
        return convertToSpotSummaryVO(fishingSpots);
    }

    @Override
    public PageResult<SpotSummaryVO> getUserCreatedSpots(Long userId, int page, int size) {
        // 查询用户创建的钓点
        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FishingSpot::getCreatedBy, userId)
                .eq(FishingSpot::getStatus, 1) // 正常状态的钓点
                .orderByDesc(FishingSpot::getCreatedAt);

        // 先查询总数
        long total = baseMapper.selectCount(queryWrapper);
        
        if (total == 0) {
            return PageResult.<SpotSummaryVO>builder()
                    .records(new ArrayList<>())
                    .total(total)
                    .current((long) page)
                    .size((long) size)
                    .pages((total + size - 1) / size)
                    .build();
        }

        // 手动分页：添加LIMIT和OFFSET
        queryWrapper.last("LIMIT " + size + " OFFSET " + (page * size));
        List<FishingSpot> spots = baseMapper.selectList(queryWrapper);

        // 转换为VO
        List<SpotSummaryVO> spotSummaryVOs = convertToSpotSummaryVO(spots);

        return PageResult.<SpotSummaryVO>builder()
                .records(spotSummaryVOs)
                .total(total)
                .current((long) page)
                .size((long) size)
                .pages((total + size - 1) / size)
                .build();
    }

    @Override
    public List<SpotSummaryVO> searchFishingSpots(String query, int page, int size) {
        if (!StringUtils.hasText(query)) {
            return new ArrayList<>();
        }

        // 构建搜索条件
        LambdaQueryWrapper<FishingSpot> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper
                        .like(FishingSpot::getName, query)
                        .or()
                        .like(FishingSpot::getAddress, query)
                        .or()
                        .like(FishingSpot::getDescription, query))
                .eq(FishingSpot::getStatus, 1) // 正常状态的钓点
                .orderByDesc(FishingSpot::getCreatedAt);

        // 分页处理 (MyBatis Plus页码是1-based，需要转换)
        Page<FishingSpot> spotPage = new Page<>(page + 1, size);
        Page<FishingSpot> spotResult = baseMapper.selectPage(spotPage, queryWrapper);

        if (spotResult.getRecords().isEmpty()) {
            return new ArrayList<>();
        }

        // 转换为VO
        return convertToSpotSummaryVO(spotResult.getRecords());
    }

    @Override
    public List<SpotSummaryVO> searchFishingSpots(String query, int page, int size, double latitude, double longitude, double radiusKm) {
        // 先获取附近的钓点
        List<FishingSpot> nearbySpots = findNearbySpots(
                new BigDecimal(latitude),
                new BigDecimal(longitude),
                radiusKm);

        // 如果没有提供搜索关键词，直接返回附近的钓点（分页处理）
        if (!StringUtils.hasText(query)) {
            int fromIndex = page * size;
            if (fromIndex >= nearbySpots.size()) {
                return new ArrayList<>();
            }

            int toIndex = Math.min(fromIndex + size, nearbySpots.size());
            List<FishingSpot> pagedSpots = nearbySpots.subList(fromIndex, toIndex);
            return convertToSpotSummaryVO(pagedSpots);
        }

        // 对附近的钓点进行关键词过滤
        List<FishingSpot> filteredSpots = nearbySpots.stream()
                .filter(spot ->
                        (spot.getName() != null && spot.getName().contains(query)) ||
                                (spot.getAddress() != null && spot.getAddress().contains(query)) ||
                                (spot.getDescription() != null && spot.getDescription().contains(query)))
                .collect(Collectors.toList());

        // 分页处理
        int fromIndex = page * size;
        if (fromIndex >= filteredSpots.size()) {
            return new ArrayList<>();
        }

        int toIndex = Math.min(fromIndex + size, filteredSpots.size());
        List<FishingSpot> pagedSpots = filteredSpots.subList(fromIndex, toIndex);

        // 转换为VO
        return convertToSpotSummaryVO(pagedSpots);
    }

    @Override
    public List<SpotSummaryVO> convertToSpotSummaryVO(List<FishingSpot> fishingSpots) {
        if (fishingSpots == null || fishingSpots.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> spotIds = fishingSpots.stream().map(FishingSpot::getId).toList();
        List<SpotFishTypes> spotFishTypeBySpotIds = spotFishTypesService.getSpotFishTypeBySpotIds(spotIds);
        List<Long> fishTypeIds = spotFishTypeBySpotIds.stream().map(SpotFishTypes::getFishTypeId).toList();
        List<FishType> fishTypes = fishTypeMapper.selectBatchIds(fishTypeIds);

        // 查询所有钓点的图片
        List<SpotImage> allSpotImages = spotImageMapper.selectList(
                new LambdaQueryWrapper<SpotImage>()
                        .in(SpotImage::getSpotId, spotIds)
                        .eq(SpotImage::getImageType, "normal")
                        .orderByAsc(SpotImage::getSortOrder));

        // 获取每个钓点的第一张图片
        Map<Long, String> mainImageMap = new HashMap<>();
        for (SpotImage image : allSpotImages) {
            mainImageMap.putIfAbsent(image.getSpotId(), image.getImageUrl());
        }

        // 获取所有钓点的价格信息
        List<SpotPrice> allPrices = getPricesBySpotIds(spotIds);
        Map<Long, String> priceTextMap = new HashMap<>();
        for (FishingSpot spot : fishingSpots) {
            if (Boolean.FALSE.equals(spot.getIsPaid())) {
                priceTextMap.put(spot.getId(), "免费");
            } else {
                List<SpotPrice> spotPrices = allPrices.stream().filter(p -> p.getSpotId().equals(spot.getId())).toList();
                if (spotPrices.isEmpty()) {
                    priceTextMap.put(spot.getId(), "付费");
                } else {
                    BigDecimal minPrice = spotPrices.stream()
                            .map(SpotPrice::getPrice)
                            .min(BigDecimal::compareTo)
                            .orElse(BigDecimal.ZERO);
                    priceTextMap.put(spot.getId(), "¥" + minPrice + "起");
                }
            }
        }

        // 获取动态相关数据
        Map<Long, Integer> recentMomentsCountMap = getRecentMomentsCountBySpotIds(spotIds);

        return fishingSpots.stream()
                .map(spot -> {
                    SpotSummaryVO summaryVO = new SpotSummaryVO();
                    copyProperties(spot, summaryVO);

                    // 设置关联的鱼类名称
                    List<SpotFishTypes> spotFishTypeBySpotId = spotFishTypeBySpotIds.stream().filter(s -> s.getSpotId().equals(spot.getId())).toList();
                    List<Long> fishTypeIdsBySpotId = spotFishTypeBySpotId.stream().map(SpotFishTypes::getFishTypeId).toList();
                    List<String> fishTypeNames = fishTypes.stream()
                            .filter(f -> fishTypeIdsBySpotId.contains(f.getId()))
                            .map(FishType::getName)
                            .collect(Collectors.toList());
                    summaryVO.setFishTypeNames(fishTypeNames);

                    // 设置主图片
                    summaryVO.setMainImage(mainImageMap.get(spot.getId()));

                    // 设置价格文本
                    summaryVO.setPriceText(priceTextMap.get(spot.getId()));

                    // 根据价格文本设置paid字段（修复逻辑错误）
                    String priceText = priceTextMap.get(spot.getId());
                    summaryVO.setPaid(priceText != null && !"免费".equals(priceText));

                    // 设置动态相关信息
                    summaryVO.setRecentMomentsCount(recentMomentsCountMap.getOrDefault(spot.getId(), 0));

                    return summaryVO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换单个FishingSpot为SpotSummaryVO（用于Algolia同步等场景）
     */
    @Override
    public SpotSummaryVO convertToSpotSummaryVO(FishingSpot fishingSpot) {
        if (fishingSpot == null) {
            return null;
        }
        List<SpotSummaryVO> result = convertToSpotSummaryVO(Arrays.asList(fishingSpot));
        return result.isEmpty() ? null : result.get(0);
    }

    @Override
    public List<Long> getFishTypeIds(Long spotId) {
        if (spotId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<SpotFishTypes> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotFishTypes::getSpotId, spotId);
        List<SpotFishTypes> associations = spotFishTypesMapper.selectList(queryWrapper);

        return associations.stream()
                .map(SpotFishTypes::getFishTypeId)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getSpotImages(Long spotId) {
        if (spotId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<SpotImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotImage::getSpotId, spotId)
                .eq(SpotImage::getImageType, "normal") // 只获取普通图片，不包括认证文件
                .orderByAsc(SpotImage::getSortOrder);
        List<SpotImage> images = spotImageMapper.selectList(queryWrapper);

        return images.stream()
                .map(SpotImage::getImageUrl)
                .collect(Collectors.toList());
    }

    private List<String> getSpotCertificationDocuments(Long spotId) {
        if (spotId == null) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<SpotImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotImage::getSpotId, spotId)
                .eq(SpotImage::getImageType, "certification")
                .orderByAsc(SpotImage::getSortOrder);
        List<SpotImage> images = spotImageMapper.selectList(queryWrapper);

        return images.stream()
                .map(SpotImage::getImageUrl)
                .collect(Collectors.toList());
    }

    private List<SpotPrice> getPricesBySpotId_Raw(Long spotId) {
        LambdaQueryWrapper<SpotPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpotPrice::getSpotId, spotId);
        return spotPriceMapper.selectList(queryWrapper);
    }

    private List<SpotPrice> getPricesBySpotIds(List<Long> spotIds) {
        if (spotIds == null || spotIds.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<SpotPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SpotPrice::getSpotId, spotIds);
        return spotPriceMapper.selectList(queryWrapper);
    }

    private Map<Long, Integer> getRecentMomentsCountBySpotIds(List<Long> spotIds) {
        if (spotIds == null || spotIds.isEmpty()) {
            return new HashMap<>();
        }

        try {
            // 从配置中获取统计时间范围
            int statDays = businessConfigService.getSpotMomentStatDays();
            LocalDateTime startTime = LocalDateTime.now().minusDays(statDays);

            // 查询指定时间范围内的动态
            LambdaQueryWrapper<Moment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Moment::getFishingSpotId, spotIds)
                    .ge(Moment::getCreatedAt, startTime);

            List<Moment> recentMoments = momentMapper.selectList(queryWrapper);

            // 按钓点ID分组并计数
            Map<Long, Integer> countMap = recentMoments.stream()
                    .filter(moment -> moment.getFishingSpotId() != null)
                    .collect(Collectors.groupingBy(
                            Moment::getFishingSpotId,
                            Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                    ));

            log.debug("获取钓点最近{}天动态数量成功，钓点数量: {}", statDays, spotIds.size());
            return countMap;
        } catch (Exception e) {
            // 记录错误日志，返回空映射
            log.error("获取钓点最近动态数量失败", e);
            return new HashMap<>();
        }
    }


    /**
     * 将FishingSpot列表转换为SpotDetailVO列表
     */
    public List<SpotDetailVO> convertToSpotDetailVO(List<FishingSpot> fishingSpots) {
        return fishingSpots.stream()
                .map(spot -> {
                    SpotDetailVO vo = new SpotDetailVO();
                    copyProperties(spot, vo);

                    // 设置鱼类信息
                    List<Long> fishTypeIds = getFishTypeIds(spot.getId());
                    List<FishType> fishTypes = new ArrayList<>();
                    if (!fishTypeIds.isEmpty()) {
                        fishTypes = fishTypeMapper.selectBatchIds(fishTypeIds);
                    }

                    List<FishTypeVO> fishTypeVOs = new ArrayList<>();
                    for (FishType fishType : fishTypes) {
                        FishTypeVO fishTypeVO = new FishTypeVO();
                        copyProperties(fishType, fishTypeVO);
                        fishTypeVOs.add(fishTypeVO);
                    }
                    vo.setFishTypeList(fishTypeVOs);

                    // 设置图片列表
                    vo.setImages(getSpotImages(spot.getId()));

                    // 设置价格信息
                    vo.setPrices(getPricesBySpotId(spot.getId()));

                    // 设置设施信息
                    vo.setFacilities(getSpotFacilities(spot.getId()));

                    // 设置创建者信息
                    if (spot.getCreatedBy() != null) {
                        User creator = userMapper.selectById(spot.getCreatedBy());
                        if (creator != null) {
                            UserVo creatorVo = new UserVo();
                            copyProperties(creator, creatorVo);
                            vo.setCreator(creatorVo);
                        }
                    }

                    return vo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public IPage<SpotMapVO> findNearbyMapSpots(BigDecimal latitude, BigDecimal longitude, Double radius, int page, int size) {
        // 参数验证
        if (latitude == null || longitude == null) {
            throw new IllegalArgumentException("经纬度不能为空");
        }

        // 纬度范围检查 (-90 到 90)
        if (latitude.compareTo(new BigDecimal("-90")) < 0 || latitude.compareTo(new BigDecimal("90")) > 0) {
            throw new IllegalArgumentException("纬度必须在-90到90之间");
        }

        // 经度范围检查 (-180 到 180)
        if (longitude.compareTo(new BigDecimal("-180")) < 0 || longitude.compareTo(new BigDecimal("180")) > 0) {
            throw new IllegalArgumentException("经度必须在-180到180之间");
        }

        if (radius == null || radius <= 0) {
            radius = 50.0; // 默认50公里半径
        }

        // 创建分页对象（MyBatis Plus使用1-based分页）
        Page<FishingSpot> pageObj = new Page<>(page + 1, size);

        // 执行分页查询
        Page<FishingSpot> resultPage = baseMapper.selectNearbyMapSpots(pageObj, latitude, longitude, radius);

        // 转换为SpotMapVO
        List<SpotMapVO> mapSpots = resultPage.getRecords().stream()
                .map(this::convertToSpotMapVO)
                .collect(Collectors.toList());

        // 构建分页结果
        Page<SpotMapVO> mapPage = new Page<>(page + 1, size);
        mapPage.setRecords(mapSpots);
        mapPage.setTotal(resultPage.getTotal());
        mapPage.setPages(resultPage.getPages());

        return mapPage;
    }

    /**
     * 将FishingSpot转换为SpotMapVO
     */
    private SpotMapVO convertToSpotMapVO(FishingSpot spot) {
        SpotMapVO mapVO = new SpotMapVO();
        mapVO.setId(spot.getId());
        mapVO.setName(spot.getName());
        mapVO.setAddress(spot.getAddress());
        mapVO.setLatitude(spot.getLatitude());
        mapVO.setLongitude(spot.getLongitude());
        mapVO.setRating(spot.getRating());

        // FishingSpot模型中没有reviewCount字段，暂时设置为0
        // 后续可以通过查询评论表来获取真实的评论数量
        mapVO.setReviewCount(0);

        mapVO.setIsPaid(Boolean.TRUE.equals(spot.getIsPaid()));
        mapVO.setHasFacilities(Boolean.TRUE.equals(spot.getHasFacilities()));

        // FishingSpot模型中没有imageUrl字段，设置为null
        // 后续可以通过查询图片表来获取第一张图片
        mapVO.setImageUrl(null);

        // 设置钓点类型
        if (Boolean.TRUE.equals(spot.getIsOfficial())) {
            mapVO.setType("官方认证");
        } else if (Boolean.TRUE.equals(spot.getIsPaid())) {
            mapVO.setType("付费钓场");
        } else {
            mapVO.setType("免费钓场");
        }

        // 处理鱼类信息 - 需要通过spot_fish_types表查询
        // 暂时设置空列表，在SQL查询中已经通过JOIN获取了鱼类信息
        // 但需要在后续优化中处理GROUP_CONCAT的结果
        mapVO.setFishTypes(new ArrayList<>());

        // 设置距离（如果有的话）
        // 距离信息在SQL查询中已经计算，但需要在FishingSpot模型中添加distance字段才能获取
        // 暂时设置为null
        mapVO.setDistance(null);

        return mapVO;
    }
}