package com.fishing.service.impl;

import com.fishing.domain.UserSettings;
import com.fishing.dto.settings.CacheInfoDto;
import com.fishing.dto.settings.ClearCacheResultDto;
import com.fishing.dto.user.UserStatsDto;
import com.fishing.mapper.UserSettingsMapper;
import com.fishing.service.UserSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户设置服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserSettingsServiceImpl implements UserSettingsService {

    private final UserSettingsMapper userSettingsMapper;

    @Override
    public UserSettings getUserSettings(Long userId) {
        UserSettings settings = userSettingsMapper.selectByUserId(userId);
        if (settings == null) {
            // 如果不存在设置，创建默认设置
            initDefaultSettings(userId);
            settings = userSettingsMapper.selectByUserId(userId);
        }
        return settings;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserSettings(Long userId, UserSettings settings) {
        settings.setUpdateTime(LocalDateTime.now());
        return userSettingsMapper.updateByUserId(userId, settings) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initDefaultSettings(Long userId) {
        UserSettings existingSettings = userSettingsMapper.selectByUserId(userId);
        if (existingSettings != null) {
            return true; // 已存在，不需要重复创建
        }

        UserSettings defaultSettings = createDefaultSettings(userId);
        return userSettingsMapper.insert(defaultSettings) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePrivacySettings(Long userId, String profileVisibility, String momentVisibility,
                                         Boolean allowFollow, Boolean allowMessage) {
        UserSettings settings = getUserSettings(userId);
        if (settings == null) {
            return false;
        }

        if (profileVisibility != null) settings.setProfileVisibility(profileVisibility);
        if (momentVisibility != null) settings.setMomentVisibility(momentVisibility);
        if (allowFollow != null) settings.setAllowFollow(allowFollow ? 1 : 0);
        if (allowMessage != null) settings.setAllowMessage(allowMessage ? 1 : 0);

        return updateUserSettings(userId, settings);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotificationSettings(Long userId, Boolean momentNotification, Boolean commentNotification,
                                              Boolean followNotification, Boolean systemNotification) {
        UserSettings settings = getUserSettings(userId);
        if (settings == null) {
            return false;
        }

        if (momentNotification != null) settings.setMomentNotification(momentNotification ? 1 : 0);
        if (commentNotification != null) settings.setCommentNotification(commentNotification ? 1 : 0);
        if (followNotification != null) settings.setFollowNotification(followNotification ? 1 : 0);
        if (systemNotification != null) settings.setSystemNotification(systemNotification ? 1 : 0);

        return updateUserSettings(userId, settings);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLanguage(Long userId, String language) {
        UserSettings settings = getUserSettings(userId);
        if (settings == null) {
            return false;
        }

        settings.setLanguage(language);
        return updateUserSettings(userId, settings);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePrivacyLevel(Long userId, String privacyLevel) {
        UserSettings settings = getUserSettings(userId);
        if (settings == null) {
            return false;
        }

        settings.setPrivacyLevel(privacyLevel);
        return updateUserSettings(userId, settings);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateNotificationEnabled(Long[] userIds, Boolean enabled) {
        return userSettingsMapper.batchUpdateNotificationSettings(userIds, enabled ? 1 : 0) > 0;
    }

    @Override
    public Map<String, Boolean> getUserNotificationPreferences(Long userId) {
        UserSettings settings = getUserSettings(userId);
        Map<String, Boolean> preferences = new HashMap<>();

        if (settings != null) {
            preferences.put("notification_enabled", settings.getNotificationEnabled() == 1);
            preferences.put("moment_notification", settings.getMomentNotification() == 1);
            preferences.put("comment_notification", settings.getCommentNotification() == 1);
            preferences.put("follow_notification", settings.getFollowNotification() == 1);
            preferences.put("system_notification", settings.getSystemNotification() == 1);
        } else {
            // 默认值
            preferences.put("notification_enabled", true);
            preferences.put("moment_notification", true);
            preferences.put("comment_notification", true);
            preferences.put("follow_notification", true);
            preferences.put("system_notification", true);
        }

        return preferences;
    }

    @Override
    public boolean isNotificationEnabled(Long userId, String notificationType) {
        UserSettings settings = getUserSettings(userId);
        if (settings == null || settings.getNotificationEnabled() != 1) {
            return false;
        }

        return switch (notificationType.toLowerCase()) {
            case "moment" -> settings.getMomentNotification() == 1;
            case "comment" -> settings.getCommentNotification() == 1;
            case "follow" -> settings.getFollowNotification() == 1;
            case "system" -> settings.getSystemNotification() == 1;
            default -> false;
        };
    }

    @Override
    public Long[] getNotificationEnabledUserIds() {
        return userSettingsMapper.selectNotificationEnabledUserIds();
    }

    @Override
    public Map<String, Long> getPrivacyLevelStats() {
        Map<String, Long> stats = new HashMap<>();
        stats.put("public", userSettingsMapper.countByPrivacyLevel("public"));
        stats.put("friends", userSettingsMapper.countByPrivacyLevel("friends"));
        stats.put("private", userSettingsMapper.countByPrivacyLevel("private"));
        return stats;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetToDefaultSettings(Long userId) {
        // 先删除现有设置
        UserSettings existingSettings = userSettingsMapper.selectByUserId(userId);
        if (existingSettings != null) {
            userSettingsMapper.deleteById(existingSettings.getId());
        }

        // 重新创建默认设置
        return initDefaultSettings(userId);
    }

    /**
     * 创建默认用户设置
     */
    private UserSettings createDefaultSettings(Long userId) {
        UserSettings settings = new UserSettings();
        settings.setUserId(userId);
        settings.setNotificationEnabled(1);
        settings.setPrivacyLevel("public");
        settings.setLanguage("zh_CN");
        settings.setAutoSaveLocation(1);
        settings.setShowOnlineStatus(1);
        settings.setProfileVisibility("public");
        settings.setMomentVisibility("public");
        settings.setAllowFollow(1);
        settings.setAllowMessage(1);
        settings.setMomentNotification(1);
        settings.setCommentNotification(1);
        settings.setFollowNotification(1);
        settings.setSystemNotification(1);
        settings.setCreateTime(LocalDateTime.now());
        settings.setUpdateTime(LocalDateTime.now());
        return settings;
    }

    // 缓存管理相关方法
    
    @Override
    public CacheInfoDto getCacheInfo(Long userId) {
        try {
            // 模拟计算缓存大小
            long imageCacheSize = calculateImageCacheSize(userId);
            long dataCacheSize = calculateDataCacheSize(userId);
            long logCacheSize = calculateLogCacheSize(userId);
            long totalSize = imageCacheSize + dataCacheSize + logCacheSize;
            
            return CacheInfoDto.builder()
                .imageCache(formatSize(imageCacheSize))
                .dataCache(formatSize(dataCacheSize))
                .videoCache(formatSize(logCacheSize)) // 使用logCache作为videoCache
                .totalCache(formatSize(totalSize))
                .imageCacheBytes(imageCacheSize)
                .dataCacheBytes(dataCacheSize)
                .videoCacheBytes(logCacheSize)
                .totalCacheBytes(totalSize)
                .lastClearTime(getLastClearTime(userId))
                .build();
        } catch (Exception e) {
            log.error("获取缓存信息失败: userId={}", userId, e);
            return getDefaultCacheInfo();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClearCacheResultDto clearCache(Long userId, String cacheType) {
        try {
            long beforeSize = getTotalCacheSize(userId);
            long clearedSize = 0L;
            String message = "";
            
            switch (cacheType.toLowerCase()) {
                case "image":
                    clearedSize = clearImageCache(userId);
                    message = "图片缓存清理完成";
                    break;
                case "data":
                    clearedSize = clearDataCache(userId);
                    message = "数据缓存清理完成";
                    break;
                case "video":
                case "log":
                    clearedSize = clearLogCache(userId);
                    message = "视频缓存清理完成";
                    break;
                case "all":
                    clearedSize = clearAllCache(userId);
                    message = "所有缓存清理完成";
                    break;
                default:
                    throw new IllegalArgumentException("无效的缓存类型: " + cacheType);
            }
            
            // 更新最后清理时间
            updateLastClearTime(userId);
            
            long afterSize = getTotalCacheSize(userId) - clearedSize;
            
            ClearCacheResultDto result = ClearCacheResultDto.builder()
                .success(true)
                .beforeSize(formatSize(beforeSize))
                .afterSize(formatSize(afterSize))
                .freedSpace(formatSize(clearedSize))
                .freedSpaceBytes(clearedSize)
                .clearTime(LocalDateTime.now().toString())
                .message(message)
                .build();
            
            log.info("缓存清理完成: userId={}, type={}, size={}", userId, cacheType, clearedSize);
            return result;
        } catch (Exception e) {
            log.error("清理缓存失败: userId={}, type={}", userId, cacheType, e);
            return ClearCacheResultDto.builder()
                .success(false)
                .beforeSize("0MB")
                .afterSize("0MB")
                .freedSpace("0MB")
                .freedSpaceBytes(0L)
                .clearTime(LocalDateTime.now().toString())
                .message("清理缓存失败: " + e.getMessage())
                .build();
        }
    }

    @Override
    public UserStatsDto getUserStats(Long userId) {
        try {
            // TODO: 从数据库获取真实的用户统计数据
            // 这里提供模拟数据
            return UserStatsDto.builder()
                .followingCount((int) (Math.random() * 100))
                .followersCount((int) (Math.random() * 200))
                .momentsCount((int) (Math.random() * 50))
                .spotsCount((int) (Math.random() * 20))
                .likesCount((int) (Math.random() * 500))
                .commentsCount((int) (Math.random() * 150))
                .bookmarksCount((int) (Math.random() * 80))
                .albumCount((int) (Math.random() * 30))
                .build();
        } catch (Exception e) {
            log.error("获取用户统计失败: userId={}", userId, e);
            return UserStatsDto.builder()
                .followingCount(0)
                .followersCount(0)
                .momentsCount(0)
                .spotsCount(0)
                .likesCount(0)
                .commentsCount(0)
                .bookmarksCount(0)
                .albumCount(0)
                .build();
        }
    }

    @Override
    public boolean validatePassword(Long userId, String password) {
        try {
            // TODO: 实现真实的密码验证逻辑
            // 这里提供模拟实现
            log.info("验证用户密码: userId={}", userId);
            
            // 模拟密码验证，实际项目中应该从数据库获取用户信息并验证密码
            // 应该使用加密后的密码进行比较
            return password != null && password.length() >= 6;
        } catch (Exception e) {
            log.error("密码验证失败: userId={}", userId, e);
            return false;
        }
    }

    private long getTotalCacheSize(Long userId) {
        return calculateImageCacheSize(userId) + calculateDataCacheSize(userId) + calculateLogCacheSize(userId);
    }

    private long calculateImageCacheSize(Long userId) {
        // 模拟计算图片缓存大小
        // 实际实现中应该统计用户相关的图片缓存文件大小
        return (long) (Math.random() * 50 * 1024 * 1024); // 0-50MB
    }

    private long calculateDataCacheSize(Long userId) {
        // 模拟计算数据缓存大小
        // 实际实现中应该统计用户相关的数据缓存大小
        return (long) (Math.random() * 15 * 1024 * 1024); // 0-15MB
    }

    private long calculateLogCacheSize(Long userId) {
        // 模拟计算日志缓存大小
        // 实际实现中应该统计用户相关的日志文件大小
        return (long) (Math.random() * 8 * 1024 * 1024); // 0-8MB
    }

    private long clearImageCache(Long userId) {
        // 实际实现中应该清理用户的图片缓存
        long sizeBefore = calculateImageCacheSize(userId);
        // TODO: 实现实际的图片缓存清理逻辑
        log.info("清理用户图片缓存: userId={}, size={}", userId, sizeBefore);
        return sizeBefore;
    }

    private long clearDataCache(Long userId) {
        // 实际实现中应该清理用户的数据缓存
        long sizeBefore = calculateDataCacheSize(userId);
        // TODO: 实现实际的数据缓存清理逻辑
        log.info("清理用户数据缓存: userId={}, size={}", userId, sizeBefore);
        return sizeBefore;
    }

    private long clearLogCache(Long userId) {
        // 实际实现中应该清理用户的日志缓存
        long sizeBefore = calculateLogCacheSize(userId);
        // TODO: 实现实际的日志缓存清理逻辑
        log.info("清理用户日志缓存: userId={}, size={}", userId, sizeBefore);
        return sizeBefore;
    }

    private long clearAllCache(Long userId) {
        long imageSize = clearImageCache(userId);
        long dataSize = clearDataCache(userId);
        long logSize = clearLogCache(userId);
        return imageSize + dataSize + logSize;
    }

    private String getLastClearTime(Long userId) {
        // 实际实现中应该从数据库或缓存中获取最后清理时间
        // TODO: 实现从数据库获取最后清理时间的逻辑
        return LocalDateTime.now().minusDays(1).toString();
    }

    private void updateLastClearTime(Long userId) {
        // 实际实现中应该更新数据库中的最后清理时间
        // TODO: 实现更新最后清理时间到数据库的逻辑
        log.info("更新最后清理时间: userId={}, time={}", userId, LocalDateTime.now());
    }

    private String formatSize(long bytes) {
        if (bytes < 1024) {
            return bytes + "B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1fKB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1fMB", bytes / (1024.0 * 1024));
        } else {
            return String.format("%.1fGB", bytes / (1024.0 * 1024 * 1024));
        }
    }

    private CacheInfoDto getDefaultCacheInfo() {
        return CacheInfoDto.builder()
            .imageCache("0MB")
            .dataCache("0MB")
            .videoCache("0MB")
            .totalCache("0MB")
            .imageCacheBytes(0L)
            .dataCacheBytes(0L)
            .videoCacheBytes(0L)
            .totalCacheBytes(0L)
            .lastClearTime("从未清理")
            .build();
    }
}