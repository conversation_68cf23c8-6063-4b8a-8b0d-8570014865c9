package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fishing.common.redis.UserStatsRedisKeys;
import com.fishing.datasource.annotation.DataSource;
import com.fishing.datasource.enums.DataSourceType;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.user.Follow;
import com.fishing.dto.user.UserStatsDto;
import com.fishing.service.IFollowService;
import com.fishing.service.MomentService;
import com.fishing.service.IUserStatsAdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户统计管理服务实现（Admin Module）
 * 负责批量预热、数据一致性校验和缓存管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DataSource(DataSourceType.FISHING_BIZ)
public class UserStatsAdminServiceImpl implements IUserStatsAdminService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final IFollowService followService;
    private final MomentService momentService;

    @Override
    public void preloadHotUsers(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            log.warn("热点用户列表为空，跳过预热");
            return;
        }

        log.info("开始预热 {} 个热点用户的统计数据", userIds.size());
        long startTime = System.currentTimeMillis();

        // 并行处理用户统计数据预热
        userIds.parallelStream().forEach(userId -> {
            try {
                refreshUserStatsToRedis(userId);
            } catch (Exception e) {
                log.error("预热用户 {} 统计数据失败", userId, e);
            }
        });

        long endTime = System.currentTimeMillis();
        log.info("热点用户预热完成，耗时 {} ms", endTime - startTime);

        // 记录热点用户列表到Redis
        recordHotUsers(userIds);
    }

    @Override
    public void refreshUserStatsToRedis(Long userId) {
        try {
            UserStatsDto stats = calculateStatsFromDatabase(userId);
            writeStatsToRedis(stats);
            log.debug("用户 {} 统计数据已刷新到Redis", userId);
        } catch (Exception e) {
            log.error("刷新用户 {} 统计数据到Redis失败", userId, e);
        }
    }

    @Override
    public void refreshAllUserStats() {
        log.info("开始全量刷新用户统计数据");
        long startTime = System.currentTimeMillis();

        try {
            // 获取所有有统计数据的用户ID
            Set<Long> allUserIds = getAllUsersWithStats();
            log.info("找到 {} 个用户需要刷新统计数据", allUserIds.size());

            // 分批处理，避免内存压力
            int batchSize = 100;
            List<List<Long>> batches = partition(new ArrayList<>(allUserIds), batchSize);

            for (List<Long> batch : batches) {
                batch.parallelStream().forEach(this::refreshUserStatsToRedis);
            }

            long endTime = System.currentTimeMillis();
            log.info("全量刷新用户统计数据完成，共处理 {} 个用户，耗时 {} ms", 
                    allUserIds.size(), endTime - startTime);
        } catch (Exception e) {
            log.error("全量刷新用户统计数据失败", e);
        }
    }

    @Override
    public void validateAndFixStatsConsistency() {
        log.info("开始数据一致性校验和修复");
        long startTime = System.currentTimeMillis();

        try {
            Set<String> cacheKeys = getAllStatsCacheKeys();
            int inconsistentCount = 0;
            int fixedCount = 0;

            for (String key : cacheKeys) {
                Long userId = extractUserIdFromKey(key);
                if (userId == null) continue;

                try {
                    // 从缓存获取数据
                    UserStatsDto cachedStats = getStatsFromCache(userId);
                    // 从数据库计算数据
                    UserStatsDto dbStats = calculateStatsFromDatabase(userId);

                    // 检查一致性
                    if (!isStatsConsistent(cachedStats, dbStats)) {
                        inconsistentCount++;
                        log.warn("发现用户 {} 统计数据不一致，缓存: {}, 数据库: {}", 
                                userId, cachedStats, dbStats);
                        
                        // 修复不一致数据
                        writeStatsToRedis(dbStats);
                        fixedCount++;
                        log.info("已修复用户 {} 的统计数据", userId);
                    }
                } catch (Exception e) {
                    log.error("校验用户 {} 数据一致性时出错", userId, e);
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("数据一致性校验完成，共检查 {} 个用户，发现 {} 个不一致，修复 {} 个，耗时 {} ms",
                    cacheKeys.size(), inconsistentCount, fixedCount, endTime - startTime);
        } catch (Exception e) {
            log.error("数据一致性校验失败", e);
        }
    }

    @Override
    public void cleanExpiredCache() {
        log.info("开始清理过期统计缓存");
        long startTime = System.currentTimeMillis();

        try {
            Set<String> cacheKeys = getAllStatsCacheKeys();
            int cleanedCount = 0;
            long currentTime = System.currentTimeMillis();
            long expireThreshold = 7 * 24 * 60 * 60 * 1000L; // 7天

            for (String key : cacheKeys) {
                try {
                    Map<Object, Object> stats = redisTemplate.opsForHash().entries(key);
                    Object lastUpdatedObj = stats.get(UserStatsRedisKeys.LAST_UPDATED);
                    
                    if (lastUpdatedObj != null) {
                        long lastUpdated = Long.parseLong(lastUpdatedObj.toString());
                        if (currentTime - lastUpdated > expireThreshold) {
                            redisTemplate.delete(key);
                            cleanedCount++;
                            log.debug("清理过期缓存: {}", key);
                        }
                    }
                } catch (Exception e) {
                    log.error("清理缓存 {} 时出错", key, e);
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("过期缓存清理完成，共清理 {} 个缓存，耗时 {} ms", 
                    cleanedCount, endTime - startTime);
        } catch (Exception e) {
            log.error("清理过期缓存失败", e);
        }
    }

    @Override
    public Map<String, Object> getRedisHealthMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        try {
            // 统计缓存键数量
            Set<String> cacheKeys = getAllStatsCacheKeys();
            metrics.put("totalCacheKeys", cacheKeys.size());
            
            // 统计热点用户数量
            Set<Object> hotUsers = redisTemplate.opsForSet().members(UserStatsRedisKeys.HOT_USERS_KEY);
            metrics.put("hotUsersCount", hotUsers != null ? hotUsers.size() : 0);
            
            // 计算缓存命中率（简化版，实际应该基于访问统计）
            metrics.put("estimatedHitRate", "95%");
            
            // Redis连接状态
            metrics.put("redisConnected", true);
            
            // 内存使用情况（需要Redis INFO命令，这里简化）
            metrics.put("memoryUsage", "正常");
            
        } catch (Exception e) {
            log.error("获取Redis健康指标失败", e);
            metrics.put("error", e.getMessage());
            metrics.put("redisConnected", false);
        }
        
        return metrics;
    }

    @Override
    public List<Long> getHotUsers(int limit) {
        try {
            // 基于多个维度识别热点用户
            Set<Long> hotUsers = new HashSet<>();
            
            // 1. 最近活跃用户（基于Redis访问记录）
            Set<Object> recentUsers = redisTemplate.opsForSet().members(UserStatsRedisKeys.HOT_USERS_KEY);
            if (recentUsers != null) {
                hotUsers.addAll(recentUsers.stream()
                        .map(obj -> Long.parseLong(obj.toString()))
                        .collect(Collectors.toSet()));
            }
            
            // 2. 粉丝数最多的用户（从数据库查询）
            List<Long> topFollowedUsers = getTopFollowedUsers(limit / 2);
            hotUsers.addAll(topFollowedUsers);
            
            // 3. 最近发布动态的用户
            List<Long> recentActiveUsers = getRecentActiveUsers(limit / 2);
            hotUsers.addAll(recentActiveUsers);
            
            return hotUsers.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取热点用户失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 从数据库计算用户统计数据
     */
    private UserStatsDto calculateStatsFromDatabase(Long userId) {
        int followersCount = followService.getFollowerCount(userId);
        int followingCount = followService.getFollowingCount(userId);
        int momentsCount = momentService.getUserMomentCount(userId);
        int spotsCount = getSpotsCountFromDatabase(userId);

        return UserStatsDto.builder()
                .userId(userId)
                .followersCount(followersCount)
                .followingCount(followingCount)
                .momentsCount(momentsCount)
                .spotsCount(spotsCount)
                .lastUpdated(System.currentTimeMillis())
                .build();
    }

    /**
     * 将统计数据写入Redis
     */
    private void writeStatsToRedis(UserStatsDto stats) {
        String key = UserStatsRedisKeys.getUserStatsKey(stats.getUserId());
        
        Map<String, Object> cacheData = Map.of(
                UserStatsRedisKeys.FOLLOWERS_COUNT, stats.getFollowersCount(),
                UserStatsRedisKeys.FOLLOWING_COUNT, stats.getFollowingCount(),
                UserStatsRedisKeys.MOMENTS_COUNT, stats.getMomentsCount(),
                UserStatsRedisKeys.SPOTS_COUNT, stats.getSpotsCount(),
                UserStatsRedisKeys.LAST_UPDATED, stats.getLastUpdated()
        );
        
        redisTemplate.opsForHash().putAll(key, cacheData);
        redisTemplate.expire(key, Duration.ofDays(UserStatsRedisKeys.DEFAULT_EXPIRE_DAYS));
    }

    /**
     * 记录热点用户列表到Redis
     */
    private void recordHotUsers(List<Long> userIds) {
        try {
            redisTemplate.delete(UserStatsRedisKeys.HOT_USERS_KEY);
            if (!userIds.isEmpty()) {
                redisTemplate.opsForSet().add(UserStatsRedisKeys.HOT_USERS_KEY, 
                        userIds.toArray());
                redisTemplate.expire(UserStatsRedisKeys.HOT_USERS_KEY, 
                        Duration.ofHours(UserStatsRedisKeys.HOT_USERS_EXPIRE_HOURS));
            }
        } catch (Exception e) {
            log.error("记录热点用户列表失败", e);
        }
    }

    /**
     * 获取所有有统计数据的用户ID
     */
    private Set<Long> getAllUsersWithStats() {
        Set<Long> userIds = new HashSet<>();
        
        // 从Follow表获取所有用户ID
        List<Follow> follows = followService.list();
        follows.forEach(follow -> {
            userIds.add(follow.getFollowerId());
            userIds.add(follow.getFollowedId());
        });
        
        // 从Moment表获取所有用户ID
        List<Moment> moments = momentService.list();
        moments.forEach(moment -> userIds.add(moment.getUserId()));
        
        return userIds;
    }

    /**
     * 获取所有统计缓存键
     */
    private Set<String> getAllStatsCacheKeys() {
        Set<String> keys = new HashSet<>();
        ScanOptions options = ScanOptions.scanOptions()
                .match(UserStatsRedisKeys.USER_STATS_PREFIX + "*")
                .count(1000)
                .build();
                
        try (Cursor<String> cursor = redisTemplate.scan(options)) {
            while (cursor.hasNext()) {
                keys.add(cursor.next());
            }
        } catch (Exception e) {
            log.error("扫描Redis键失败", e);
        }
        
        return keys;
    }

    /**
     * 从缓存键提取用户ID
     */
    private Long extractUserIdFromKey(String key) {
        try {
            return Long.parseLong(key.replace(UserStatsRedisKeys.USER_STATS_PREFIX, ""));
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 从缓存获取统计数据
     */
    private UserStatsDto getStatsFromCache(Long userId) {
        String key = UserStatsRedisKeys.getUserStatsKey(userId);
        Map<Object, Object> stats = redisTemplate.opsForHash().entries(key);
        
        if (stats.isEmpty()) {
            return null;
        }
        
        return UserStatsDto.builder()
                .userId(userId)
                .followersCount(getIntValue(stats, UserStatsRedisKeys.FOLLOWERS_COUNT))
                .followingCount(getIntValue(stats, UserStatsRedisKeys.FOLLOWING_COUNT))
                .momentsCount(getIntValue(stats, UserStatsRedisKeys.MOMENTS_COUNT))
                .spotsCount(getIntValue(stats, UserStatsRedisKeys.SPOTS_COUNT))
                .lastUpdated(getLongValue(stats, UserStatsRedisKeys.LAST_UPDATED))
                .build();
    }

    /**
     * 检查统计数据一致性
     */
    private boolean isStatsConsistent(UserStatsDto cached, UserStatsDto db) {
        if (cached == null || db == null) {
            return false;
        }
        
        return Objects.equals(cached.getFollowersCount(), db.getFollowersCount()) &&
               Objects.equals(cached.getFollowingCount(), db.getFollowingCount()) &&
               Objects.equals(cached.getMomentsCount(), db.getMomentsCount()) &&
               Objects.equals(cached.getSpotsCount(), db.getSpotsCount());
    }

    /**
     * 获取粉丝数最多的用户
     */
    private List<Long> getTopFollowedUsers(int limit) {
        // TODO: 实现获取粉丝数最多的用户
        return Collections.emptyList();
    }

    /**
     * 获取最近活跃用户
     */
    private List<Long> getRecentActiveUsers(int limit) {
        // TODO: 实现获取最近活跃用户
        return Collections.emptyList();
    }

    /**
     * 从数据库查询用户钓点数量
     */
    private int getSpotsCountFromDatabase(Long userId) {
        // TODO: 实现钓点数量查询逻辑
        return 0;
    }

    /**
     * 列表分批工具方法
     */
    private <T> List<List<T>> partition(List<T> list, int size) {
        List<List<T>> result = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            result.add(list.subList(i, Math.min(i + size, list.size())));
        }
        return result;
    }

    private Integer getIntValue(Map<Object, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            return Integer.parseInt((String) value);
        }
        return 0;
    }

    private Long getLongValue(Map<Object, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof String) {
            return Long.parseLong((String) value);
        }
        return System.currentTimeMillis();
    }
}