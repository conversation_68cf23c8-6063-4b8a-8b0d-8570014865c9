package com.fishing.service;

import java.util.List;
import java.util.Map;

/**
 * 用户统计管理服务接口（Admin Module）
 */
public interface IUserStatsAdminService {

    /**
     * 批量预热热点用户统计数据
     * 并行处理，将数据库中的统计数据写入Redis缓存
     * 
     * @param userIds 热点用户ID列表
     */
    void preloadHotUsers(List<Long> userIds);

    /**
     * 刷新单个用户统计数据到Redis
     * 从数据库重新计算并更新缓存
     * 
     * @param userId 用户ID
     */
    void refreshUserStatsToRedis(Long userId);

    /**
     * 全量刷新所有用户统计数据
     * 适合在低峰期执行，重新计算所有用户的统计数据
     */
    void refreshAllUserStats();

    /**
     * 数据一致性校验和修复
     * 比较缓存和数据库中的统计数据，修复不一致的数据
     */
    void validateAndFixStatsConsistency();

    /**
     * 清理过期的统计缓存
     * 删除超过TTL时间的缓存数据
     */
    void cleanExpiredCache();

    /**
     * 获取Redis健康指标
     * 包括缓存命中率、连接状态、内存使用等信息
     * 
     * @return 健康指标Map
     */
    Map<String, Object> getRedisHealthMetrics();

    /**
     * 识别热点用户
     * 基于多个维度识别需要预热的热点用户
     * 
     * @param limit 返回用户数量限制
     * @return 热点用户ID列表
     */
    List<Long> getHotUsers(int limit);
}