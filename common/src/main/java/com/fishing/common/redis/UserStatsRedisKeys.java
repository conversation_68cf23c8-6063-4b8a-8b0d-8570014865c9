package com.fishing.common.redis;

/**
 * 用户统计Redis键规范
 * 两个模块都使用相同的键规范确保数据一致性
 */
public class UserStatsRedisKeys {
    
    /**
     * 用户统计缓存键前缀
     */
    public static final String USER_STATS_PREFIX = "user:stats:";
    
    /**
     * Hash字段名
     */
    public static final String FOLLOWERS_COUNT = "followers_count";
    public static final String FOLLOWING_COUNT = "following_count";
    public static final String MOMENTS_COUNT = "moments_count";
    public static final String SPOTS_COUNT = "spots_count";
    public static final String LAST_UPDATED = "last_updated";
    
    /**
     * 获取用户统计缓存键
     * @param userId 用户ID
     * @return Redis键
     */
    public static String getUserStatsKey(Long userId) {
        return USER_STATS_PREFIX + userId;
    }
    
    /**
     * 热点用户预热键
     */
    public static final String HOT_USERS_KEY = "stats:hot_users";
    
    /**
     * 统计缓存默认过期时间（天）
     */
    public static final int DEFAULT_EXPIRE_DAYS = 7;
    
    /**
     * 热点用户缓存过期时间（小时）
     */
    public static final int HOT_USERS_EXPIRE_HOURS = 1;
}