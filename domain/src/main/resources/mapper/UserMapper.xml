<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fishing.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.fishing.domain.User">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="telephoneNumber" column="telephone_number" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="salt" column="salt" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="INTEGER"/>
        <result property="avatarUrl" column="avatar_url" jdbcType="VARCHAR"/>
        <result property="introduce" column="introduce" jdbcType="VARCHAR"/>
        <result property="province" column="province" jdbcType="VARCHAR"/>
        <result property="city" column="city" jdbcType="VARCHAR"/>
        <result property="county" column="county" jdbcType="VARCHAR"/>
        <result property="addressDetail" column="address_detail" jdbcType="VARCHAR"/>
        <result property="status" column="enabled" jdbcType="BOOLEAN"/>
        <result property="scope" column="scope" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>
